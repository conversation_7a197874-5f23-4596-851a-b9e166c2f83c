# Enable rewrite engine
RewriteEngine On

# Pasar las cabeceras de autenticación
RewriteCond %{HTTP:Authorization} ^(.*)
RewriteRule .* - [e=HTTP_AUTHORIZATION:%1]

# Permitir el acceso directo a los archivos PHP en el directorio api/
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirección para los logos subidos: /uploads/... -> /api/uploads/...
    RewriteRule ^uploads/(.*)$ api/uploads/$1 [L]

    # Si la solicitud es para un archivo o directorio que existe, servirlo directamente
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]
    
    # Redirigir las solicitudes a /api/*.php al archivo correspondiente
    RewriteRule ^api/(.+)\.php$ api/$1.php [L,QSA]
    
    # Redirigir todo lo demás a index.html (para el enrutamiento del frontend)
    RewriteRule ^ index.html [L]
</IfModule>
