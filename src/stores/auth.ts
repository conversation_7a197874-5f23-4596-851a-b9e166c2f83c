import { defineStore } from 'pinia';
import { getCurrentSubscription } from '@/api/subscriptionService'; // Importar el servicio
import type { Subscription as ApiSubscription } from '@/api/subscriptionService'; // Importar el tipo de la API

// Interfaz para el usuario
export interface User {
  id: number;
  email: string;
  name: string;
  roles?: string[];
  agency_id?: number | null; // Añadido ? para opcionalidad inicial
  is_agency_owner?: boolean | null; // Añadido ? para opcionalidad inicial
  agencyName?: string | null; // NUEVO CAMPO
  [key: string]: any;
}

// Interfaz para las características del plan del usuario almacenadas en el estado
export interface UserPlanFeatures {
  id: number | null; // plan_id de la suscripción
  name: string | null; // nombre del plan
  slug: string | null; // slug del plan
  max_dashboard_users: number | null;
  allow_multiple_sequences: boolean | null;
  allow_custom_domain_email: boolean | null;
  max_valoradores: number | null;
  has_analytics: boolean | null; // NUEVO: acceso a analíticas detalladas
  additional_config: Record<string, any> | null;
}

// Interfaz para el estado de autenticación
interface AuthState {
  token: string | null;
  user: User | null;
  isLoading: boolean;
  error: string | null;
  planFeatures: UserPlanFeatures | null;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    token: null,
    user: null,
    isLoading: false,
    error: null,
    planFeatures: null,
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
    currentUser: (state) => state.user,
    authError: (state) => state.error,
    isLoadingState: (state) => state.isLoading,
    getPlanFeatures: (state) => state.planFeatures,
    canUseCustomDomainEmail: (state) => !!state.planFeatures?.allow_custom_domain_email,
    maxDashboardUsers: (state) => state.planFeatures?.max_dashboard_users ?? 1,
    agencyId: (state) => state.user?.agency_id ?? null,
    isAgencyOwner: (state) => {
      const result = !!state.user && state.user.is_agency_owner === true;
      return result;
    },
    maxValoradores: (state) => state.planFeatures?.max_valoradores ?? 1,
    hasAnalytics: (state) => !!state.planFeatures?.has_analytics,
    getAgencyName: (state) => state.user?.agencyName ?? null,
  },

  actions: {
    /**
     * Obtiene y establece las características del plan del usuario.
     */
    async fetchAndSetPlanFeatures() {
      if (!this.isAuthenticated || !this.user?.id) {
        this.planFeatures = null;
        localStorage.removeItem('planFeatures');
        return;
      }
      this.isLoading = true;
      try {
        const subscriptionData: ApiSubscription = await getCurrentSubscription(); 

        if (this.user && subscriptionData.user_data_response?.agency_name) {
          this.user.agencyName = subscriptionData.user_data_response.agency_name;
          localStorage.setItem('user', JSON.stringify(this.user)); 
        }

        if (subscriptionData && subscriptionData.plan_features) {
          const apiFeatures = subscriptionData.plan_features;
          let parsedAdditionalConfig: Record<string, any> | null = null;
          if (apiFeatures.additional_config_json) {
            if (typeof apiFeatures.additional_config_json === 'string') {
              try {
                parsedAdditionalConfig = JSON.parse(apiFeatures.additional_config_json);
              } catch (e) {
                console.error('Error parsing additional_config_json:', e);
                parsedAdditionalConfig = {};
              }
            } else {
              parsedAdditionalConfig = apiFeatures.additional_config_json as unknown as Record<string, any>; 
            }
          }

          const featuresForState: UserPlanFeatures = {
            id: apiFeatures.id ?? null,
            name: apiFeatures.name ?? null,
            slug: apiFeatures.slug ?? null,
            max_dashboard_users: apiFeatures.max_dashboard_users ?? null,
            allow_multiple_sequences: apiFeatures.allow_multiple_sequences ?? false,
            allow_custom_domain_email: apiFeatures.allow_custom_domain_email ?? false,
            max_valoradores: apiFeatures.max_valoradores ?? null,
            has_analytics: apiFeatures.has_analytics ?? false,
            additional_config: parsedAdditionalConfig,
          };
          this.planFeatures = featuresForState;
          localStorage.setItem('planFeatures', JSON.stringify(featuresForState));
        } else {
          this.planFeatures = null;
          localStorage.removeItem('planFeatures');
        }
      } catch (error: any) {
        console.error('Error al cargar las características del plan:', error);
        this.error = error.message || 'Error al cargar datos del plan.';
        this.planFeatures = null;
        localStorage.removeItem('planFeatures');
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Inicia sesión y guarda el token y los datos del usuario
     */
    async login(userData: any, token: string) {
      
      this.token = token;
      this.user = {
        id: userData.id,
        uuid: userData.uuid,
        email: userData.email,
        name: userData.nombre_completo,
        roles: userData.roles,
        agency_id: userData.agency_id,
        is_agency_owner: userData.is_agency_owner,
        agencyName: userData.agency_name
      };

      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(this.user));
      
      await this.fetchAndSetPlanFeatures();
    },
    
    /**
     * Cierra la sesión del usuario
     */
    logout() {
      try {
        this.token = null;
        this.user = null;
        this.error = null;
        this.planFeatures = null; 
        
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('planFeatures'); 
        
        const appUrl = import.meta.env.VITE_APP_BASE_URL || 'https://app.inmoautomation.com';
        
        // Redirige siempre a la página de login DENTRO de la app.
        if (!window.location.href.includes('/login')) {
          window.location.href = `${appUrl}/login`;
        }
      } catch (error) {
        console.error('Error al cerrar sesión:', error);
      }
    },
    
    /**
     * Intenta restaurar la sesión desde el localStorage
     */
    async restoreSession() {
      const token = localStorage.getItem('token');
      const userDataString = localStorage.getItem('user');

      if (token && userDataString) {
        try {
          const parsedUserData: User = JSON.parse(userDataString);
          
          this.token = token;
          this.user = parsedUserData; 
          
          await this.fetchAndSetPlanFeatures();
        } catch (error) {
          console.error('[AuthStore RestoreSession Action] Error parsing user data from localStorage:', error);
          this.logout();
        }
      } else {
      }
    },
    
    /**
     * Verifica los roles del usuario con la base de datos
     */
    async verifyRolesWithDatabase() {
      if (!this.isAuthenticated || !this.user?.id) {
        return;
      }
      this.isLoading = true;
      try {
      } catch (error: any) {
        console.error('Error al verificar roles con la base de datos:', error);
        this.error = error.message || 'Error al verificar roles.';
      } finally {
        this.isLoading = false;
      }
    },
    
    /**
     * Verifica el estado de autenticación general y restaura la sesión si es necesario.
     * Se llama típicamente al inicio de la aplicación.
     */
    async checkAuth() {
      if (this.isAuthenticated) {
        if(!this.planFeatures && this.user?.id) {
            await this.fetchAndSetPlanFeatures();
        }
        return;
      }
      
      await this.restoreSession();
      if(this.isAuthenticated){
      } else {
      }
    },
  },
});

export function setupAuth() {
  const authStore = useAuthStore();
  authStore.checkAuth(); 
}
