<template>
  <div class="landing-page-wrapper">
    <HeroSection />
    <ProblemAgitationSection />
    <HowItWorksSection />
    <FeaturesSection />
    <ExplainerSection />
    <SocialProofSection />
    <PricingSection />
    <ComparisonTableSection />
    <FinalCTASection />
    <FAQSection />
  </div>
</template>

<script setup lang="ts">
// Importar todas las secciones de la landing page
import HeroSection from '@/components/landing/HeroSection.vue'
import ProblemAgitationSection from '@/components/landing/ProblemAgitationSection.vue'
import FeaturesSection from '@/components/landing/FeaturesSection.vue'
import HowItWorksSection from '@/components/landing/HowItWorksSection.vue'
import ExplainerSection from '@/components/landing/ExplainerSection.vue'
import SocialProofSection from '@/components/landing/SocialProofSection.vue'
// import OurCommitmentSection from '@/components/landing/OurCommitmentSection.vue';
import ComparisonTableSection from '@/components/landing/ComparisonTableSection.vue';
import PricingSection from '@/components/landing/PricingSection.vue'
import FinalCTASection from '@/components/landing/FinalCTASection.vue'
import FAQSection from '@/components/landing/FAQSection.vue'

// No se necesita lógica JavaScript específica en esta vista por ahora,
// ya que solo actúa como un ensamblador de las secciones.
// Cada sección maneja sus propias animaciones y lógica interna.

// Metadatos de la página (Título, descripción) ya se manejan en el router (src/router/index.ts)
// para la ruta '/'. Si se necesitaran metadatos más dinámicos o específicos aquí,
// se podría usar `useHead` de `@vueuse/head` o una lógica similar.
</script>

<style scoped>
/* Estilos específicos para LandingView si fueran necesarios. */
/* Por ejemplo, para asegurar que no haya márgenes extraños entre secciones
   o para aplicar un estilo de fondo general si las secciones no cubren todo. */
.landing-page-wrapper {
  /* Podríamos querer un color de fondo base aquí si las secciones
     no siempre tienen fondos que cubran el 100% del ancho/alto,
     o si queremos un efecto de "sobre" entre ellas.
     Sin embargo, con la alternancia de bg-impacto-blue y bg-white/gray-50,
     probablemente no sea necesario. */
  /* background-color: #f8f9fa; */ /* Ejemplo de un color de fondo muy neutro */
  overflow-x: hidden; /* Para evitar barras de scroll horizontales si alguna animación se desborda temporalmente */
}
</style>