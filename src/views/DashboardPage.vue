<template>
  <div class="min-h-screen bg-gray-100 flex flex-col">
    <!-- Header para todas las pantallas -->
    <header class="text-white py-3 px-4 md:py-4 md:px-6 flex items-center justify-between shadow-lg z-20 relative overflow-hidden" style="background-color: #051f33;">
      <!-- Fondo con patrón tecnológico sutil -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-full h-full bg-pattern-dots"></div>
      </div>
      
      <div class="flex items-center gap-2 md:gap-3 relative z-10">
        <!-- Bo<PERSON><PERSON> de menú móvil (visible solo en móvil) -->
        <button 
          @click="toggleMobileMenu" 
          class="md:hidden p-1.5 rounded-md hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-200"
          aria-label="Abrir menú"
        >
          <svg v-if="!isMobileMenuOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <img src="/img/logo-impacto-automation-valorador-white.svg" alt="Impacto Automation" class="h-7 md:h-8" />
        <span class="font-bold text-base md:text-lg tracking-tight">Panel de Control</span>
      </div>
      
      <div class="flex items-center gap-3 relative z-10">
        <!-- Indicador de usuario con icono -->
        <div class="hidden md:flex items-center gap-1.5 text-white/90 bg-white/10 px-3 py-1.5 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span class="text-sm font-medium">{{ userName }}</span>
        </div>
        
        <!-- Botón de cerrar sesión -->
        <button 
          @click="logout" 
          class="bg-impacto-orange text-white px-3 py-1.5 md:px-4 md:py-2 rounded-lg shadow-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 focus:ring-offset-impacto-blue transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center gap-1.5 font-medium"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          <span class="text-sm md:text-base">Cerrar sesión</span>
        </button>
      </div>
    </header>
    
    <div class="flex flex-1 overflow-hidden relative">
      <!-- Overlay para móvil cuando el menú está abierto -->
      <div 
        v-if="isMobileMenuOpen" 
        @click="toggleMobileMenu"
        class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-10"
      ></div>
      
      <!-- Sidebar - versión móvil (se muestra/oculta) -->
      <div 
        :class="[
          'md:hidden fixed inset-y-0 left-0 transform z-20 transition-transform duration-300 ease-in-out',
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        ]"
      >
        <DashboardSidebar @navigate="closeMobileMenu" />
      </div>
      
      <!-- Sidebar - versión desktop (siempre visible) -->
      <div class="hidden md:block">
        <DashboardSidebar />
      </div>
      
      <!-- Contenido principal -->
      <main class="flex-1 p-4 md:p-6 overflow-y-auto bg-gray-50">
        <!-- Banner de valorador inactivo -->
        <InactiveValoradorBanner />

        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'vue-router';
import DashboardSidebar from '@/components/dashboard/DashboardSidebar.vue';
import InactiveValoradorBanner from '@/components/dashboard/InactiveValoradorBanner.vue';

const auth = useAuthStore();
const router = useRouter();

// Estado para el menú móvil
const isMobileMenuOpen = ref(false);

// Nombre de usuario (extraído del store de autenticación o un valor por defecto)
const userName = computed(() => {
  return auth.user?.name || auth.user?.email || 'Usuario';
});

// Funciones para manejar el menú móvil
function toggleMobileMenu() {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
}

function closeMobileMenu() {
  isMobileMenuOpen.value = false;
}

// Cerrar sesión
function logout() {
  auth.logout();
  router.push('/login');
}
</script>

<style scoped>
.bg-impacto-blue {
  background-color: #003B6D;
}

/* Patrón de puntos para el fondo */
.bg-pattern-dots {
  background-image: radial-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 20px 20px;
}

.text-impacto-blue {
  color: #003B6D;
}
/* Add any additional styles if necessary */
</style>