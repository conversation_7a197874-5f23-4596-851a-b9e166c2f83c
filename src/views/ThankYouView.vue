<template>
    <div class="thank-you-view py-12 px-4 text-center bg-gray-100 min-h-screen flex items-center justify-center">
       <div class="bg-white p-8 rounded-lg shadow-xl">
        <h1 class="text-3xl font-bold text-impacto-orange mb-4">¡Gracias por tu Compra!</h1>
        <p class="text-gray-700 mb-2">
          Tu Valorador Inmobiliario IA está siendo configurado.
        </p>
        <p class="text-gray-700">
          Recibirás un email en breve con los detalles de acceso y la URL de tu valorador personalizado.
        </p>
        <RouterLink :to="{ name: 'Landing' }" class="mt-8 inline-block bg-impacto-blue text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
          Volver a la Página Principal
        </RouterLink>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { RouterLink } from 'vue-router';
  </script>
  
  <style scoped>
  /* Estilos específicos si son necesarios */
  </style>