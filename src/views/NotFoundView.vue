<template>
    <div class="not-found-view py-12 px-4 text-center bg-gray-100 min-h-screen flex items-center justify-center">
      <div class="bg-white p-10 rounded-lg shadow-xl">
        <svg class="mx-auto h-24 w-24 text-impacto-orange mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 8V8.01" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16.5 14.5L19 12 16.5 9.5" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M7.5 14.5L5 12 7.5 9.5" />
        </svg>
        <h1 class="text-5xl font-extrabold text-impacto-blue mb-3">404</h1>
        <h2 class="text-2xl font-semibold text-gray-700 mb-6">¡Ups! Página no Encontrada.</h2>
        <p class="text-gray-600 mb-8">
          Lo sentimos, la página que buscas no existe o ha sido movida.
        </p>
        <RouterLink
          :to="{ name: 'Landing' }"
          class="inline-block px-8 py-4 text-lg font-semibold text-white bg-impacto-orange rounded-lg shadow-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 transition-all duration-300 ease-in-out"
        >
          Volver a la Página Principal
        </RouterLink>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { RouterLink } from 'vue-router';
  </script>
  
  <style scoped>
  /* Estilos específicos si son necesarios */
  </style>