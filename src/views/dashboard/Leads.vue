<template>
  <div class="p-4 sm:p-6 md:p-8 bg-slate-50 min-h-screen">
    <!-- Encabezado -->
    <header class="mb-6 md:mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-impacto-blue">Gestión de leads</h1>
        <p class="text-gray-600 mt-1">Visualiza, gestiona y acciona tus oportunidades de negocio.</p>
      </div>
      <div class="mt-4 md:mt-0">
        <button @click="exportarLeads" 
              :disabled="isLoading || !!error || leads.length === 0"
              class="px-5 py-2.5 text-sm font-medium text-white bg-impacto-blue rounded-lg hover:bg-impacto-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Exportar listado
        </button>
      </div>
    </header>

    <!-- Componente de Estadísticas -->
    <LeadsEstadisticas 
      v-if="!isLoading && !error"
      :estadisticas="estadisticasLeads" 
    />

    <!-- Filtros Rápidos y Búsqueda -->
    <div class="mb-6 p-4 bg-white rounded-xl shadow">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 items-end">
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Buscar lead</label>
          <input type="text" id="search" v-model="searchTerm" @input="debouncedFetchLeads" placeholder="Nombre, email, dirección..."
                 class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange text-sm">
        </div>
        <div>
          <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Estado de nutrición</label>
          <select id="statusFilter" v-model="statusFilter" @change="applyStatusFilter"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange text-sm bg-white">
            <option value="">Todos los estados</option>
            <option v-for="status in leadStatuses" :key="status.value" :value="status.value">
              {{ status.text }}
            </option>
          </select>
        </div>
        <div>
          <label for="periodFilter" class="block text-sm font-medium text-gray-700 mb-1">Periodo</label>
          <select id="periodFilter" v-model="periodFilter" @change="applyPeriodFilter"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange text-sm bg-white">
            <option value="">Cualquier fecha</option>
            <option value="today">Hoy</option>
            <option value="last_week">Última semana</option>
            <option value="last_month">Último mes</option>
            <option value="last_quarter">Último trimestre</option>
            <option value="last_year">Último año</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Estado de Carga -->
    <div v-if="isLoading" class="text-center py-20 bg-white rounded-xl shadow">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-impacto-orange mx-auto"></div>
      <p class="mt-4 text-lg font-semibold text-gray-700">Cargando leads...</p>
    </div>

    <!-- Estado de Error -->
    <div v-else-if="error" class="bg-red-50 border-l-4 border-red-600 text-red-800 p-6 rounded-xl shadow">
      <h3 class="text-lg font-bold">Error al Cargar los Leads</h3>
      <p class="text-sm mt-1">{{ error }}</p>
      <button @click="fetchLeadsData" class="mt-3 px-3 py-1.5 text-sm bg-red-700 text-white rounded-md hover:bg-red-800">Reintentar</button>
    </div>

    <!-- Estado Vacío (Después de cargar, sin error) -->
    <div v-else-if="leads.length === 0" class="p-8 rounded-xl shadow bg-white text-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" /></svg>
      <h3 class="text-xl font-semibold mb-2 text-impacto-blue">No se encontraron leads</h3>
      <p class="text-gray-600">Ajusta tu búsqueda o filtros, o espera a que ingresen nuevos leads.</p>
    </div>
    
    <!-- Tabla de Leads y Paginación -->
    <div v-else class="bg-white rounded-xl shadow overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50 sticky top-0 z-10">
          <tr>
            <th @click="sortBy('vl.nombre')" class="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Nombre</th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Contacto</th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Propiedad / Valor</th>
            <th @click="sortBy('vl.fecha_creacion')" class="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">Captura</th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Estado de nutrición</th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Acciones</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="lead in leads" :key="lead.lead_id" class="hover:bg-slate-50 transition-colors duration-150">
            <td class="px-4 py-3 whitespace-nowrap">
              <a @click.prevent="openLeadDetail(lead)" href="#" class="text-sm font-medium text-impacto-blue hover:text-impacto-orange hover:underline">{{ lead.nombre }}</a>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
              <div v-if="lead.email" class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
                <a :href="'mailto:' + lead.email" class="hover:text-impacto-orange hover:underline">{{ lead.email }}</a>
              </div>
              <div v-if="lead.telefono" class="flex items-center mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" /></svg>
                <a :href="'tel:' + lead.telefono" class="hover:text-impacto-orange hover:underline">{{ lead.telefono }}</a>
              </div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">
              <div>{{ lead.tipo_propiedad || 'N/A' }}</div>
              <div class="text-xs text-gray-500 truncate max-w-xs" :title="lead.direccion_propiedad ? lead.direccion_propiedad : undefined">{{ lead.direccion_propiedad || '-' }}</div>
              <div class="font-semibold text-green-700 mt-0.5" v-if="lead.valor_estimado_min && lead.valor_estimado_max">
                {{ formatCurrency(lead.valor_estimado_min) }} - {{ formatCurrency(lead.valor_estimado_max) }}
              </div>
              <div v-else class="text-xs text-gray-500">Valor no estimado</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{{ formatDate(lead.fecha_captura) }}</td>
            <td class="px-4 py-3 whitespace-nowrap">
              <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="getNutritionStatusClass(lead.lead_estado_secuencia)">
                {{ lead.lead_estado_secuencia ? formatStatus(lead.lead_estado_secuencia) : 'Sin secuencia' }}
              </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
              <button @click="openLeadDetail(lead)" title="Ver detalles" class="text-impacto-blue hover:text-impacto-orange p-1 hover:bg-blue-100 rounded-md"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" /></svg></button>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- Paginación -->
      <div v-if="pagination.totalPages > 1" class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button @click="changePage(pagination.page - 1)" :disabled="pagination.page <= 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Anterior</button>
          <button @click="changePage(pagination.page + 1)" :disabled="pagination.page >= pagination.totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Siguiente</button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Mostrando <span class="font-medium">{{ (pagination.page - 1) * pagination.limit + 1 }}</span>
              a <span class="font-medium">{{ Math.min(pagination.page * pagination.limit, pagination.total) }}</span>
              de <span class="font-medium">{{ pagination.total }}</span> resultados
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Paginación">
              <button @click="changePage(pagination.page - 1)" :disabled="pagination.page <= 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                <span class="sr-only">Anterior</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>
              </button>
              <template v-for="p in getPageRange()" :key="p">
                  <button v-if="typeof p === 'number'" @click="changePage(p)" 
                          :class="['relative inline-flex items-center px-4 py-2 border text-sm font-medium', p === pagination.page ? 'z-10 bg-impacto-orange border-impacto-orange text-white' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50']">
                    {{ p }}
                  </button>
                  <span v-else class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-400">
                    {{ p }}
                  </span>
              </template>
              <button @click="changePage(pagination.page + 1)" :disabled="pagination.page >= pagination.totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                <span class="sr-only">Siguiente</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de detalle de lead (simplificado, se desarrollará en Fase 2) -->
    <Teleport to="body">
      <div v-if="showLeadDetailModal && selectedLead" @click.self="closeLeadDetail" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-[100]">
        <div class="bg-slate-50 rounded-xl shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-hidden flex flex-col">
          <div class="flex justify-between items-center p-4 border-b border-gray-200 bg-white sticky top-0 z-10 rounded-t-xl">
            <h2 class="text-xl font-bold text-impacto-blue">Detalle del Lead: {{ selectedLead.nombre }}</h2>
            <button @click="closeLeadDetail" class="text-gray-400 hover:text-red-600 p-1 rounded-full hover:bg-red-100">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
            </button>
          </div>
          <div class="overflow-y-auto flex-grow">
            <LeadDetalle :lead="selectedLead" @cerrar-detalle="closeLeadDetail" />
          </div>
        </div>
      </div>
    </Teleport>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watchEffect, computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useRouter, useRoute } from 'vue-router';
import { useToast } from '@/composables/useToast';
import LeadDetalle from '@/components/dashboard/leads/LeadDetalle.vue';
import LeadsEstadisticas from '@/components/dashboard/leads/LeadsEstadisticas.vue';
import { apiFetch } from '@/utils/apiFetch';

interface Lead {
  lead_id: number;
  client_id: string; 
  nombre: string;
  email: string | null;
  telefono: string | null;
  uuid: string;
  necesidad: string | null;
  estado: string;
  num_valoraciones: number;
  notas: string | null;
  valoracion_id: number | null;
  fecha_modificacion: string | null;
  lead_estado_secuencia: string | null;
  lead_id_secuencia_asignada: number | null;
  direccion_propiedad: string | null;
  tipo_propiedad: string | null;
  valor_estimado_min: number | null;
  valor_estimado_max: number | null;
  fecha_captura: string; 
  valorador_nombre?: string; 
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();
const { showToast } = useToast();

const leads = ref<Lead[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const pagination = ref<PaginationInfo>({
  total: 0,
  page: 1,
  limit: 15,
  totalPages: 0,
});

const searchTerm = ref('');
const orderBy = ref('vl.fecha_creacion'); 
const orderDir = ref('DESC');
const statusFilter = ref('');
const periodFilter = ref('');

const showLeadDetailModal = ref(false);
const selectedLead = ref<Lead | null>(null);

const leadStatuses = ref([
  { value: 'active', text: 'Activa' },
  { value: 'pending_activation', text: 'Pendiente Activación' },
  { value: 'paused_by_user', text: 'Pausada (Usuario)' },
  { value: 'paused_by_system', text: 'Pausada (Sistema)' },
  { value: 'completed', text: 'Completada' },
  { value: 'unsubscribed', text: 'Baja' },
  { value: 'error', text: 'Error' },
]);

const estadisticasLeads = computed(() => {
  const total = leads.value.length;
  
  const ahora = new Date();
  const primerDiaMesActual = new Date(ahora.getFullYear(), ahora.getMonth(), 1);
  const primerDiaMesAnterior = new Date(ahora.getFullYear(), ahora.getMonth() - 1, 1);
  const ultimoDiaMesAnterior = new Date(ahora.getFullYear(), ahora.getMonth(), 0);

  const leadsMesActual = leads.value.filter(lead => new Date(lead.fecha_captura) >= primerDiaMesActual);
  const leadsMesAnterior = leads.value.filter(lead => {
    const fecha = new Date(lead.fecha_captura);
    return fecha >= primerDiaMesAnterior && fecha <= ultimoDiaMesAnterior;
  });

  const totalMesActual = leadsMesActual.length;
  const totalMesAnterior = leadsMesAnterior.length;
  
  const incrementoTotal = totalMesAnterior > 0 
    ? Math.round(((totalMesActual - totalMesAnterior) / totalMesAnterior) * 100) 
    : (totalMesActual > 0 ? 100 : 0);

  // Lógica de conteo por estado
  const enNutricion = leads.value.filter(l => l.lead_estado_secuencia === 'active').length;
  const enEspera = leads.value.filter(l => ['pending_activation', 'paused_by_user', 'paused_by_system'].includes(l.lead_estado_secuencia || '')).length;
  const completados = leads.value.filter(l => l.lead_estado_secuencia === 'completed').length;
  const perdidos = leads.value.filter(l => ['unsubscribed', 'error'].includes(l.lead_estado_secuencia || '')).length;
  
  // Lógica de conteo por estado para el mes actual
  const enNutricionMesActual = leadsMesActual.filter(l => l.lead_estado_secuencia === 'active').length;
  const enEsperaMesActual = leadsMesActual.filter(l => ['pending_activation', 'paused_by_user', 'paused_by_system'].includes(l.lead_estado_secuencia || '')).length;
  const completadosMesActual = leadsMesActual.filter(l => l.lead_estado_secuencia === 'completed').length;
  const perdidosMesActual = leadsMesActual.filter(l => ['unsubscribed', 'error'].includes(l.lead_estado_secuencia || '')).length;
  
  // Lógica de conteo por estado para el mes anterior
  const enNutricionMesAnterior = leadsMesAnterior.filter(l => l.lead_estado_secuencia === 'active').length;
  const enEsperaMesAnterior = leadsMesAnterior.filter(l => ['pending_activation', 'paused_by_user', 'paused_by_system'].includes(l.lead_estado_secuencia || '')).length;
  const completadosMesAnterior = leadsMesAnterior.filter(l => l.lead_estado_secuencia === 'completed').length;
  const perdidosMesAnterior = leadsMesAnterior.filter(l => ['unsubscribed', 'error'].includes(l.lead_estado_secuencia || '')).length;
  
  // Cálculo de incrementos porcentuales
  const calcIncremento = (actual: number, anterior: number) => 
    anterior > 0 ? Math.round(((actual - anterior) / anterior) * 100) : (actual > 0 ? 100 : 0);

  return {
    total: {
      valor: total,
      mesActual: totalMesActual,
      incremento: incrementoTotal,
    },
    enNutricion: {
      valor: enNutricion,
      mesActual: enNutricionMesActual,
      incremento: calcIncremento(enNutricionMesActual, enNutricionMesAnterior)
    },
    enEspera: {
      valor: enEspera,
      mesActual: enEsperaMesActual,
      incremento: calcIncremento(enEsperaMesActual, enEsperaMesAnterior)
    },
    completados: {
      valor: completados,
      mesActual: completadosMesActual,
      incremento: calcIncremento(completadosMesActual, completadosMesAnterior)
    },
    perdidos: {
      valor: perdidos,
      mesActual: perdidosMesActual,
      incremento: calcIncremento(perdidosMesActual, perdidosMesAnterior)
    },
  };
});

const fetchLeadsData = async () => {
  if (!authStore.token) {
    error.value = "Usuario no autenticado.";
    isLoading.value = false;
    showToast('Debes iniciar sesión para ver los leads.', 'error');
    return;
  }
  isLoading.value = true;
  error.value = null;
  try {
    const params = new URLSearchParams({
      page: pagination.value.page.toString(),
      limit: pagination.value.limit.toString(),
      orderBy: orderBy.value,
      orderDir: orderDir.value,
      search: searchTerm.value,
      status: statusFilter.value,
      period: periodFilter.value,
    });
    const data = await apiFetch(`get_leads.php?${params.toString()}`);
    if (data.success) {
      leads.value = data.leads;
      pagination.value = {
        total: data.pagination.total_records,
        page: data.pagination.current_page,
        limit: data.pagination.limit,
        totalPages: data.pagination.total_pages,
      };
    } else {
      throw new Error(data.message || 'No se pudieron cargar los leads.');
    }
  } catch (e: any) {
    console.error("Error en fetchLeadsData:", e);
    error.value = e.message || 'Ocurrió un error al cargar los datos.';
    showToast(error.value || 'Se produjo un error desconocido.', 'error');
  } finally {
    isLoading.value = false;
  }
};

const debouncedFetchLeads = (() => {
  let timeoutId: number;
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      pagination.value.page = 1;
      fetchLeadsData();
    }, 500);
  };
})();

const sortBy = (column: string) => {
  if (orderBy.value === column) {
    orderDir.value = orderDir.value === 'ASC' ? 'DESC' : 'ASC';
  } else {
    orderBy.value = column;
    orderDir.value = 'DESC';
  }
  fetchLeadsData();
};

const changePage = (newPage: number) => {
  if (newPage >= 1 && newPage <= pagination.value.totalPages) {
    pagination.value.page = newPage;
    fetchLeadsData();
  }
};

const getPageRange = () => {
  const total = pagination.value.totalPages;
  const current = pagination.value.page;
  const delta = 2;
  const range = [];
  const rangeWithDots: (number | string)[] = [];
  let l: number | undefined;

  range.push(1);
  if (total <= 1) return range;

  for (let i = current - delta; i <= current + delta; i++) {
    if (i < total && i > 1) {
      range.push(i);
    }
  }
  range.push(total);

  for (const i of range) {
    if (l) {
      if (i - l === 2) {
        rangeWithDots.push(l + 1);
      } else if (i - l !== 1) {
        rangeWithDots.push('...');
      }
    }
    rangeWithDots.push(i);
    l = i;
  }
  return rangeWithDots;
};

const applyStatusFilter = () => {
  pagination.value.page = 1;
  fetchLeadsData();
};

const applyPeriodFilter = () => {
  pagination.value.page = 1;
  fetchLeadsData();
};

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Fecha inválida';
    return date.toLocaleDateString('es-ES', { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
  } catch (e) {
    return 'Error formato fecha';
  }
};

const formatCurrency = (value: number | null | undefined): string => {
  if (value === null || value === undefined) return 'N/A';
  return value.toLocaleString('es-ES', { style: 'currency', currency: 'EUR', minimumFractionDigits: 0, maximumFractionDigits: 0 });
};

const getNutritionStatusClass = (status: string | null | undefined): string => {
  if (!status) return 'bg-gray-100 text-gray-800';
  switch (status) {
    case 'active':
      return 'bg-sky-100 text-sky-800';
    case 'pending_activation':
    case 'paused_by_user':
    case 'paused_by_system':
      return 'bg-amber-100 text-amber-800';
    case 'completed':
      return 'bg-emerald-100 text-emerald-800';
    case 'unsubscribed':
    case 'error':
      return 'bg-rose-100 text-rose-800';
    default:
      return 'bg-slate-100 text-slate-800';
  }
};

const formatStatus = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    'active': 'Activa',
    'pending_activation': 'Pendiente Activación',
    'paused_by_user': 'Pausada (Usuario)',
    'paused_by_system': 'Pausada (Sistema)',
    'completed': 'Completada',
    'unsubscribed': 'Baja',
    'error': 'Error'
  };
  return statusMap[status] || status;
};

const openLeadDetail = (lead: Lead) => {
  selectedLead.value = lead;
  showLeadDetailModal.value = true;
};

const closeLeadDetail = () => {
  showLeadDetailModal.value = false;
  selectedLead.value = null;
  if (route.params.id || route.query.openLeadId) {
    router.push({ name: 'Leads' });
  }
};

const exportarCSV = (data: any[], filename: string) => {
  if (data.length === 0) {
    showToast('No hay datos para exportar.', 'info');
    return;
  }
  
  const headers = Object.keys(data[0]).join(',');
  const rows = data.map(obj => 
    Object.values(obj).map(val => 
      `"${String(val ?? '').replace(/"/g, '""')}"`
    ).join(',')
  ).join('\n');
  
  const csv = `${headers}\n${rows}`;
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const exportarLeads = async () => {
  showToast('Preparando exportación...', 'info');
  try {
    const data = await apiFetch('get_leads.php?export=all');
    if (data.success && data.leads) {
      // Opcional: aplanar o seleccionar campos específicos para el CSV
      const leadsParaExportar = data.leads.map((lead: any) => ({ // Cambiado a any para evitar error de tipo
        nombre: lead.nombre,
        email: lead.email,
        telefono: lead.telefono,
        fecha_captura: lead.fecha_captura,
        estado_nutricion: lead.lead_estado_secuencia,
        tipo_propiedad: lead.tipo_propiedad,
        direccion: lead.direccion_propiedad,
        valor_estimado_min: lead.valor_estimado_min,
        valor_estimado_max: lead.valor_estimado_max,
      }));

      const timestamp = new Date().toISOString().slice(0, 10);
      exportarCSV(leadsParaExportar, `leads_export_${timestamp}.csv`);
      showToast('Leads exportados correctamente.', 'success');
    } else {
      throw new Error(data.message || 'No se pudieron obtener los leads para exportar.');
    }
  } catch (e: any) {
    console.error("Error al exportar leads:", e);
    showToast(e.message || 'Error durante la exportación.', 'error');
  }
};

onMounted(() => {
  fetchLeadsData();
});

watchEffect(async () => {
  const leadIdFromQuery = route.query.openLeadId as string;
  const leadIdFromParams = route.params.id as string;
  
  let targetLeadIdStr: string | null = null;

  if (leadIdFromParams) {
    targetLeadIdStr = leadIdFromParams;
  } else if (leadIdFromQuery) {
    targetLeadIdStr = leadIdFromQuery;
  }

  if (targetLeadIdStr) {
    const targetLeadId = parseInt(targetLeadIdStr, 10);
    if (!isNaN(targetLeadId)) {
      if (leads.value.length > 0) {
        const leadToOpen = leads.value.find(v => v.lead_id === targetLeadId);
        if (leadToOpen) {
          if (selectedLead.value?.lead_id !== targetLeadId || !showLeadDetailModal.value) {
            selectedLead.value = leadToOpen;
            showLeadDetailModal.value = true;
          }
        } else {
          showToast('Lead con ID ' + targetLeadId + ' no encontrado en la lista actual.', 'warning');
          if (route.params.id || route.query.openLeadId) {
            router.replace({ name: 'Leads' });
          }
        }
      } else if (!isLoading.value && leads.value.length === 0 && !error.value) {
        showToast('No hay leads cargados para mostrar el detalle del ID ' + targetLeadId + '.', 'warning');
        if (route.params.id || route.query.openLeadId) {
            router.replace({ name: 'Leads' });
          }
      }
    }
  } else {
  }
});

</script>

<style scoped>
thead th {
  position: sticky;
  top: 0;
  z-index: 10; 
  /* background-color: theme('colors.gray.50'); Ensure this is Tailwind JIT compatible or define in tailwind.config.js */
}
</style>