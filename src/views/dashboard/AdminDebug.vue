<template>
  <div class="max-w-7xl mx-auto p-6">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">Depuración de Roles de Administrador</h1>
    
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">Estado actual del usuario</h2>
      
      <div v-if="isLoading" class="flex justify-center items-center py-6">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
      </div>
      
      <div v-else>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="font-medium">¿Usuario autenticado?</p>
            <p :class="isAuthenticated ? 'text-green-600 font-bold' : 'text-red-600 font-bold'">
              {{ isAuthenticated ? 'Sí' : 'No' }}
            </p>
          </div>
          
          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="font-medium">¿Tiene rol de administrador?</p>
            <div class="flex flex-col">
              <p :class="isAdmin ? 'text-green-600 font-bold' : 'text-red-600 font-bold'">
                {{ isAdmin ? 'Sí' : 'No' }} (localStorage)
              </p>
              <p v-if="userInfo?.hasAdminRoleInDB !== undefined" 
                :class="userInfo.hasAdminRoleInDB ? 'text-green-600 font-bold' : 'text-red-600 font-bold'">
                {{ userInfo.hasAdminRoleInDB ? 'Sí' : 'No' }} (base de datos)
              </p>
            </div>
          </div>
        </div>
        
        <div class="mb-4">
          <p class="font-medium mb-2">Información del usuario:</p>
          <div class="bg-gray-100 p-4 rounded-lg overflow-auto max-h-60">
            <div v-if="userInfo?.id" class="mb-2">
              <span class="font-semibold">ID:</span> {{ userInfo.id }}
            </div>
            <div v-if="userInfo?.email" class="mb-2">
              <span class="font-semibold">Email:</span> {{ userInfo.email }}
            </div>
            <div v-if="userInfo?.roles !== undefined" class="mb-2">
              <span class="font-semibold">Roles:</span> 
              <code class="bg-gray-200 px-2 py-1 rounded">{{ JSON.stringify(userInfo.roles) }}</code>
            </div>
            <div v-if="userInfo?.roleCheckResult" class="mt-4">
              <p class="font-semibold mb-1">Resultado de verificación con la base de datos:</p>
              <pre class="bg-gray-200 p-2 rounded text-sm">{{ JSON.stringify(userInfo.roleCheckResult, null, 2) }}</pre>
            </div>
          </div>
        </div>
        
        <div v-if="userInfo?.message" class="mb-4 p-3 bg-green-100 text-green-800 rounded-lg">
          {{ userInfo.message }}
        </div>
        
        <div v-if="userInfo?.error" class="mb-4 p-3 bg-red-100 text-red-800 rounded-lg">
          {{ userInfo.error }}
        </div>
      </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">Acciones</h2>
      
      <div class="space-y-4">
        <button 
          @click="asignarRolAdmin" 
          class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200"
          :disabled="isLoading"
        >
          Asignar rol de administrador
        </button>
        
        <button 
          @click="verificarRolAdmin" 
          class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 ml-4"
          :disabled="isLoading"
        >
          Verificar rol de administrador
        </button>
        
        <button 
          @click="async () => { await removeAdminRole(); await verificarRolAdmin(); }" 
          class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200 ml-4"
          :disabled="isLoading"
        >
          Quitar rol de administrador
        </button>
        
        <button 
          @click="irAlPanel" 
          class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors duration-200 ml-4"
          :disabled="isLoading || !isAdmin"
        >
          Ir al panel de administración
        </button>
      </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-semibold mb-4">Instrucciones</h2>
      
      <div class="prose">
        <p>Si tienes problemas para acceder al panel de administración, sigue estos pasos:</p>
        
        <ol class="list-decimal pl-6 space-y-2">
          <li>Verifica que estás autenticado en el sistema.</li>
          <li>Haz clic en "Verificar rol de administrador" para comprobar si tienes los permisos necesarios.</li>
          <li>Si no tienes el rol de administrador, haz clic en "Asignar rol de administrador".</li>
          <li>Después de asignar el rol, haz clic en "Verificar rol de administrador" nuevamente.</li>
          <li>Si ahora tienes el rol de administrador, haz clic en "Ir al panel de administración".</li>
        </ol>
        
        <p class="mt-4 text-sm text-gray-600">
          Nota: Si después de seguir estos pasos sigues sin poder acceder al panel de administración, 
          es posible que necesites cerrar sesión y volver a iniciar sesión para que los cambios surtan efecto.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useAdmin } from '@/composables/useAdmin';
import useRoles from '@/composables/useRoles';
import { useRouter } from 'vue-router';

const auth = useAuthStore();
const { isAdmin } = useAdmin();
const { isLoading, error, checkAdminRole, addAdminRole, removeAdminRole, roleCheckResult } = useRoles();
const router = useRouter();

const userInfo = ref<any>(null);

const isAuthenticated = computed(() => auth.isAuthenticated);

// Verificar el rol de administrador del usuario actual
const verificarRolAdmin = async () => {
  try {
    // Verificar si el usuario está autenticado
    if (!auth.isAuthenticated) {
      userInfo.value = { error: 'Usuario no autenticado' };
      return;
    }
    
    // Verificar rol de administrador usando el nuevo endpoint
    const hasAdminRole = await checkAdminRole();
    
    // Obtener información del usuario
    userInfo.value = {
      id: auth.user?.id,
      email: auth.user?.email,
      name: auth.user?.name,
      roles: auth.user?.roles,
      isAdmin: isAdmin.value,
      hasAdminRoleInDB: hasAdminRole,
      roleCheckResult: roleCheckResult.value
    };
    
  } catch (error: any) {
    console.error('Error al verificar rol admin:', error);
    userInfo.value = { error: error.message || 'Error desconocido' };
  }
};

// Asignar rol de administrador al usuario actual
const asignarRolAdmin = async () => {
  try {
    // Verificar si el usuario está autenticado
    if (!auth.isAuthenticated) {
      userInfo.value = { error: 'Usuario no autenticado' };
      return;
    }
    
    // Asignar rol de administrador usando el nuevo endpoint
    const result = await addAdminRole();
    
    if (result) {
      userInfo.value = {
        ...userInfo.value,
        message: 'Rol de administrador asignado correctamente en la base de datos',
        roles: auth.user?.roles
      };
    } else {
      userInfo.value = {
        ...userInfo.value,
        error: error.value || 'Error al asignar rol de administrador'
      };
    }
    
  } catch (error: any) {
    console.error('Error al asignar rol admin:', error);
    userInfo.value = { 
      ...userInfo.value,
      error: error.message || 'Error desconocido'
    };
  } finally {
    // Verificar el rol después de asignarlo
    await verificarRolAdmin();
  }
}

// Ir al panel de administración
function irAlPanel() {
  router.push({ name: 'AdminDashboard' });
}

// Al montar el componente, verificar el rol de administrador
onMounted(() => {
  verificarRolAdmin();
});
</script>

<style scoped>
.prose {
  max-width: 65ch;
  color: #374151;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose ol {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
</style>
