<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Overlay de fondo -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="$emit('close')"></div>

      <!-- Centrar el modal -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <!-- Contenido del modal -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Cancelar suscripción
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  ¿Estás seguro de que deseas cancelar tu suscripción? Esta acción no se puede deshacer.
                </p>
                
                <div class="mt-4">
                  <div class="flex items-center">
                    <input
                      id="cancel-end-period"
                      v-model="cancelAtPeriodEnd"
                      name="cancel-option"
                      type="radio"
                      :value="true"
                      class="focus:ring-impacto-blue h-4 w-4 text-impacto-blue border-gray-300"
                    />
                    <label for="cancel-end-period" class="ml-3 block text-sm font-medium text-gray-700">
                      Cancelar al final del período actual (seguirás teniendo acceso hasta entonces)
                    </label>
                  </div>
                  <div class="mt-2 flex items-center">
                    <input
                      id="cancel-immediately"
                      v-model="cancelAtPeriodEnd"
                      name="cancel-option"
                      type="radio"
                      :value="false"
                      class="focus:ring-impacto-blue h-4 w-4 text-impacto-blue border-gray-300"
                    />
                    <label for="cancel-immediately" class="ml-3 block text-sm font-medium text-gray-700">
                      Cancelar inmediatamente (perderás el acceso ahora)
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            type="button" 
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            @click="confirm"
          >
            Cancelar suscripción
          </button>
          <button 
            type="button" 
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            @click="$emit('close')"
          >
            Volver
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'confirm', cancelAtPeriodEnd: boolean): void
}>();

// Por defecto, cancelar al final del período actual
const cancelAtPeriodEnd = ref(true);

// Confirmar la cancelación
const confirm = () => {
  // Emitir el evento de confirmación con la opción seleccionada
  emit('confirm', cancelAtPeriodEnd.value);
};
</script>
