<template>
  <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200/75">
    <div class="flex justify-between items-start mb-4">
      <h2 class="text-xl font-semibold text-impacto-blue">Tu Suscripción</h2>
      <div class="flex space-x-2">
        <span 
          :class="[
            'px-3 py-1 rounded-full text-sm font-medium', 
            statusClasses[subscription.status] || 'bg-gray-100 text-gray-800'
          ]"
        >
          {{ statusLabels[subscription.status] || subscription.status }}
        </span>
        <span 
          v-if="subscription.cancelAtPeriodEnd" 
          class="px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800"
        >
          Cancelada al final del período
        </span>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ subscription.planName }}</h3>
        <p class="text-gray-600 mb-4">{{ formatPrice(subscription.price) }} / {{ subscription.billingCycle }}</p>
        
        <div class="space-y-2 mb-6">
          <p class="text-sm text-gray-600">
            <span class="font-medium">Período actual:</span> 
            {{ formatDate(subscription.currentPeriodStart) }} - {{ formatDate(subscription.currentPeriodEnd) }}
          </p>
          <p v-if="subscription.canceledAt" class="text-sm text-gray-600">
            <span class="font-medium">Cancelada el:</span> 
            {{ formatDate(subscription.canceledAt) }}
          </p>
        </div>
        
        <div class="space-y-1 mb-6">
          <h4 class="text-sm font-medium text-gray-900">Características incluidas:</h4>
          <ul class="space-y-1">
            <li v-for="(feature, index) in subscription.features" :key="index" class="text-sm text-gray-600 flex items-center">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              {{ feature }}
            </li>
          </ul>
        </div>
      </div>
      
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Método de pago</h3>
        <div v-if="subscription.paymentMethod" class="flex items-center mb-4">
          <div class="p-2 bg-gray-100 rounded mr-3">
            <img
              :src="getCardBrandImage(subscription.paymentMethod.brand)"
              :alt="subscription.paymentMethod.brand"
              class="h-6"
            >
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900">
              {{ getCardBrandLabel(subscription.paymentMethod.brand) }} terminada en {{ subscription.paymentMethod.lastFourDigits }}
            </p>
            <p class="text-xs text-gray-600">
              Expira {{ subscription.paymentMethod.expiryMonth }}/{{ subscription.paymentMethod.expiryYear }}
            </p>
          </div>
        </div>
        <div v-else class="mb-4">
          <p class="text-sm text-gray-500 italic">No hay método de pago configurado</p>
        </div>
        
        <div class="space-y-3 mt-6">
          <button 
            @click="$emit('manage')" 
            class="w-full flex justify-center py-2 px-4 border border-impacto-blue rounded-md shadow-sm text-sm font-medium text-impacto-blue bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
          >
            Gestionar método de pago
          </button>
          
          <button 
            v-if="!subscription.cancelAtPeriodEnd && subscription.status === 'active'"
            @click="$emit('cancel')" 
            class="w-full flex justify-center py-2 px-4 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Cancelar suscripción
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Subscription } from '@/api/subscriptionService';

defineProps<{
  subscription: Subscription
}>();

defineEmits<{
  (e: 'manage'): void
  (e: 'cancel'): void
}>();

// Formatear fecha
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

// Formatear precio
const formatPrice = (price: string | null | undefined): string => {
  if (!price) return '-';

  // Extraer el número del precio (puede venir como "49.00" o "49.00 EUR")
  const numericPrice = parseFloat(price.toString().replace(/[^\d.,]/g, '').replace(',', '.'));

  if (isNaN(numericPrice)) return price.toString();

  // Formatear igual que en "Próxima facturación" (con "EUR" en lugar de "€")
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'EUR',
    currencyDisplay: 'code'
  }).format(numericPrice);
};

// Clases para los diferentes estados de suscripción
const statusClasses: Record<string, string> = {
  active: 'bg-green-100 text-green-800',
  trialing: 'bg-blue-100 text-blue-800',
  past_due: 'bg-yellow-100 text-yellow-800',
  canceled: 'bg-red-100 text-red-800',
  unpaid: 'bg-red-100 text-red-800',
  incomplete: 'bg-gray-100 text-gray-800',
  incomplete_expired: 'bg-gray-100 text-gray-800'
};

// Etiquetas para los diferentes estados de suscripción
const statusLabels: Record<string, string> = {
  active: 'Activa',
  trialing: 'En prueba',
  past_due: 'Pago pendiente',
  canceled: 'Cancelada',
  unpaid: 'No pagada',
  incomplete: 'Incompleta',
  incomplete_expired: 'Expirada'
};

// Obtener imagen de la marca de la tarjeta
const getCardBrandImage = (brand: string) => {
  const brands: Record<string, string> = {
    visa: '/images/card-brands/visa.svg',
    mastercard: '/images/card-brands/mastercard.svg',
    amex: '/images/card-brands/amex.svg',
    discover: '/images/card-brands/discover.svg',
    jcb: '/images/card-brands/jcb.svg',
    diners: '/images/card-brands/diners.svg',
    unionpay: '/images/card-brands/unionpay.svg'
  };
  
  return brands[brand.toLowerCase()] || '/images/card-brands/generic.svg';
};

// Obtener etiqueta de la marca de la tarjeta
const getCardBrandLabel = (brand: string) => {
  const brands: Record<string, string> = {
    visa: 'Visa',
    mastercard: 'Mastercard',
    amex: 'American Express',
    discover: 'Discover',
    jcb: 'JCB',
    diners: 'Diners Club',
    unionpay: 'UnionPay'
  };
  
  return brands[brand.toLowerCase()] || brand;
};
</script>
