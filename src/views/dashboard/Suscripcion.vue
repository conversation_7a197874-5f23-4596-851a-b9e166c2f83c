<template>
  <div class="space-y-6">
    <!-- Encabezado de la página -->
    <div class="bg-gradient-to-r from-impacto-blue to-impacto-blue/90 rounded-xl shadow-lg p-6 border border-blue-700/20">
      <div class="flex items-center mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-impacto-orange mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
        <h2 class="text-2xl font-bold text-white">Gestión de suscripción</h2>
      </div>
      <p class="text-gray-100">
        Aqu<PERSON> podrás ver los detalles de tu plan, historial de facturación y gestionar tu suscripción.
      </p>
    </div>

    <!-- Estado de carga o error -->
    <div v-if="isLoading" class="bg-white rounded-xl shadow-lg p-6 border border-gray-200/75 flex justify-center items-center">
      <div class="flex flex-col items-center">
        <svg class="animate-spin h-10 w-10 text-impacto-blue mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="text-gray-600">Cargando datos de suscripción...</p>
      </div>
    </div>

    <div v-else-if="error" class="bg-white rounded-xl shadow-lg p-6 border border-red-200 bg-red-50">
      <div class="flex items-center text-red-700 mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="font-semibold">Error</h3>
      </div>
      <p class="text-red-600">{{ error }}</p>
      <button 
        @click="() => loadSubscriptionData()"
        class="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded-lg font-medium hover:bg-red-200 transition-colors"
      >
        Intentar de nuevo
      </button>
    </div>

    <!-- Sección de estado de suscripción (solo visible si no hay error y no está cargando) -->
    <div v-if="!isLoading && !error" class="bg-white rounded-xl shadow-lg border border-gray-200/75 overflow-hidden">
      <!-- Cabecera con gradiente y estado principal -->
      <div class="relative overflow-hidden">
        <!-- Fondo con gradiente y patrón de puntos -->
        <div class="absolute inset-0 bg-impacto-blue"></div>
        <div class="absolute inset-0 bg-pattern-dots opacity-10"></div>
        
        <!-- Contenido de la cabecera -->
        <div class="relative p-6 text-white">
          <!-- Banner de prueba gratuita si corresponde -->
          
          <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <!-- Nombre del plan y estado -->
              <div class="flex items-center">
                <h3 class="text-2xl font-bold">{{ isSubscriptionActive && subscription?.planName ? subscription.planName : 'Sin suscripción activa' }}</h3>
                <span v-if="subscription?.trialPeriod?.isInTrial" 
                      class="ml-3 inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-yellow-400 text-yellow-900">
                  {{ subscription.trialPeriod.daysRemaining }} días restantes
                </span>
              </div>
              
              <!-- Estado de la suscripción -->
              <p class="text-blue-100 mt-2 flex items-center">
                <span v-if="subscription?.trialPeriod?.isInTrial" class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Prueba gratuita hasta {{ formatDate(subscription.trialPeriod?.trialEnd || '') }}
                </span>
                <span v-else-if="isSubscriptionActive" class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Activo hasta {{ formatDate(subscription?.currentPeriodEnd || '') }}
                </span>
                <span v-else :class="`flex items-center ${subscriptionStatus.class.replace('bg-', 'text-').replace('-100', '-200')}`">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {{ subscriptionStatus.text }}
                </span>
              </p>
            </div>
            
            <!-- Botones de acción -->
            <div class="flex flex-wrap gap-2">
            </div>
          </div>
        </div>
      </div>
      
      <!-- Detalles de la suscripción -->
      <div class="p-6 space-y-6">
        <!-- Alerta de prueba gratuita si corresponde (no mostrar si la suscripción está cancelada) -->
        <div v-if="subscription?.trialPeriod?.isInTrial && !subscription?.cancelAtPeriodEnd" 
             class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 class="font-medium text-yellow-800">Estás en período de prueba gratuita</h4>
            <p class="text-yellow-700 text-sm mt-1">
              Tu prueba gratuita finaliza el {{ subscription.trialPeriod.trialEnd ? formatDate(subscription.trialPeriod.trialEnd) : 'fecha no disponible' }}.
              Después de esta fecha, se te cobrará automáticamente {{ nextBillingDetails.formattedNetPrice || '0,00 EUR' }}
              por el plan {{ subscription?.planName || 'seleccionado' }}<span v-if="nextBillingDetails.hasCredit" class="text-green-600"> (con descuento aplicado)</span>.
            </p>
          </div>
        </div>

        <!-- Información de cupón/descuento activo -->
        <div v-if="nextBillingDetails.hasCredit && isSubscriptionActive"
             class="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mt-0.5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 class="font-medium text-green-800">Descuento activo</h4>
            <p class="text-green-700 text-sm mt-1">
              Tienes un descuento de {{ nextBillingDetails.formattedBalance }} aplicado a tu suscripción.
              <span v-if="subscription?.trialPeriod?.isInTrial">
                Este descuento se aplicará cuando termine tu prueba gratuita.
              </span>
              <span v-else>
                Este descuento se está aplicando a tus facturas.
              </span>
            </p>
          </div>
        </div>

        <!-- Alerta de cancelación si corresponde -->
        <div v-if="subscription?.cancelAtPeriodEnd" 
             class="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400 mt-0.5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <div>
            <h4 class="font-medium text-red-800">Suscripción cancelada</h4>
            <p class="text-red-700 text-sm mt-1">
              Tu suscripción ha sido cancelada y finalizará el {{ formatDate(subscription.currentPeriodEnd) }}.
              Después de esta fecha, ya no tendrás acceso a los servicios del Valorador.
            </p>
          </div>
        </div>

        <!-- Notificaciones de problemas de pago -->
        <PaymentNotifications
          :subscription="subscription"
          @action="handleNotificationAction"
        />

        <!-- Información de la suscripción en tarjetas con iconos -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Tarjeta de detalles del plan -->
          <div class="bg-white rounded-xl p-5 border border-gray-200/75 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center mb-4">
              <div class="p-2 bg-impacto-blue/10 rounded-lg mr-3 group-hover:bg-impacto-blue/20 transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h4 class="font-semibold text-impacto-blue">Detalles del plan</h4>
            </div>
            <div class="space-y-3 text-sm">
              <!-- Bloque 1: Nombre del plan (solo si la suscripción está activa) -->
              <div v-if="isSubscriptionActive && subscription?.planName" class="flex justify-between items-center p-2 rounded-lg hover:bg-gray-50 transition-colors">
                <span class="text-gray-600">Plan</span>
                <div v-if="isSubscriptionActive && subscription?.planName">
                  <span class="font-medium text-gray-900">{{ subscription.planName }}</span>
                  <!-- El botón "Cambiar plan" debe estar aquí si la suscripción está activa -->
                  <button @click="showPlanSelectionModal = true" class="ml-2 text-sm text-impacto-orange hover:text-impacto-orange-dark font-medium hover:underline">
                    Cambiar plan
                  </button>
                </div>
                <!-- Si no está activa, pero tenemos un nombre de plan (ej. cancelado), lo mostramos sin el botón "Cambiar plan" -->
                <span v-else-if="!isSubscriptionActive && subscription?.planName && subscription?.status === 'canceled'" class="font-medium text-gray-500 italic">No disponible</span>
                <!-- Si no hay planName o no está activa -->
                <span v-else class="font-medium text-gray-500 italic">No disponible</span>
              </div>

              <!-- Bloque 2: Detalles del plan (Precio, Ciclo, Estado, Inicio) si la suscripción está activa -->
              <div v-if="isSubscriptionActive && subscription">
                <div class="flex justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span class="text-gray-600">Precio</span>
                  <span class="font-medium text-gray-900">{{ formatSubscriptionPrice(subscription?.price) }}</span>
                </div>
                <div class="flex justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span class="text-gray-600">Ciclo</span>
                  <span class="font-medium text-gray-900">{{ billingCycleDisplay }}</span>
                </div>
                <div class="flex justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span class="text-gray-600">Estado</span>
                  <span :class="`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${subscriptionStatus.class}`">
                    {{ subscriptionStatus.text }}
                  </span>
                </div>
                <div v-if="subscription?.startDate" class="flex justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span class="text-gray-600">Inicio</span>
                  <span class="font-medium text-gray-900">{{ formatDate(subscription.startDate) }}</span>
                </div>
              </div>
              
              <!-- Bloque 3: Mensaje "No tienes una suscripción activa" y botón "Ver planes disponibles" si NO está activa -->
              <div v-else class="p-4 my-2 bg-blue-50 border border-blue-200 rounded-lg text-center">
                <p class="text-sm text-impacto-blue font-medium mb-2">
                  No tienes una suscripción activa.
                </p>
                <button @click="showPlanSelectionModal = true" class="px-4 py-1.5 bg-impacto-orange text-white text-xs rounded-md hover:bg-orange-600 transition-colors">
                  Ver planes disponibles
                </button>
              </div>
            </div>
          </div>
          
          <!-- Tarjeta de próxima facturación -->
          <div class="bg-white rounded-xl p-5 border border-gray-200/75 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 z-10">
            <div class="flex items-center mb-4">
              <div class="p-2 bg-impacto-orange/10 rounded-lg mr-3 group-hover:bg-impacto-orange/20 transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h4 class="font-semibold text-impacto-blue">Próxima facturación</h4>
            </div>
            <div v-if="isSubscriptionActive && subscription?.nextBillingDate" class="space-y-3 text-sm">
              <div class="flex justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
                <span class="text-gray-600">Fecha</span>
                <span class="font-medium text-gray-900">{{ subscription?.nextBillingDate ? formatDate(subscription.nextBillingDate) : '-' }}</span>
              </div>
              <div class="flex justify-between items-center p-2 rounded-lg hover:bg-gray-50 transition-colors">
                <span class="text-gray-600">Importe</span>
                <div class="relative flex items-center">
                  <span class="font-medium text-gray-900">{{ nextBillingDetails.formattedNetPrice }}</span>
                  <button 
                    v-if="nextBillingDetails.hasCredit" 
                    @mouseenter="showBillingTooltip = true"
                    @mouseleave="showBillingTooltip = false"
                    @focus="showBillingTooltip = true"
                    @blur="showBillingTooltip = false"
                    class="ml-1.5 p-0.5 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-1 focus:ring-impacto-blue"
                    aria-label="Detalles del cálculo del importe"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.755 4 3.92C16 13.094 14.828 14 12.803 14c-1.139 0-1.99-.306-2.67-.825M12 18.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12 16.5a.5.5 0 01.5.5V18a.5.5 0 01-1 0v-1a.5.5 0 01.5-.5zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>
                  <!-- Tooltip -->
                  <div 
                    v-show="showBillingTooltip && nextBillingDetails.hasCredit"
                    class="absolute z-50 top-0 -translate-y-full -mt-2.5 left-1/2 transform -translate-x-1/2 w-64 p-3.5 bg-slate-700 text-slate-100 text-xs rounded-lg shadow-xl transition-opacity duration-300 pointer-events-none"
                    :class="{ 'opacity-100': showBillingTooltip, 'opacity-0': !showBillingTooltip }"
                    role="tooltip"
                  >
                    <p class="font-semibold mb-2 text-center border-b border-slate-500 pb-1.5 text-sm">Detalle del próximo cobro</p>
                    <div class="space-y-1">
                      <div class="flex justify-between"><span>Precio del plan:</span> <span class="font-medium">{{ nextBillingDetails.formattedPrice }}</span></div>
                      <div class="flex justify-between"><span>Descuento aplicado:</span> <span class="font-medium text-green-400">-{{ nextBillingDetails.formattedBalance }}</span></div>
                      <hr class="border-slate-500 my-1.5"/>
                      <div class="flex justify-between font-bold text-sm"><span>Total a pagar:</span> <span class="text-base">{{ nextBillingDetails.formattedNetPrice }}</span></div>
                    </div>
                    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-[calc(100%_-_1px)] w-3 h-3 bg-slate-700 rotate-45"></div> <!-- Flecha del tooltip -->
                  </div>
                </div>
              </div>
              <div class="flex justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
                <span class="text-gray-600">Renovación</span>
                <span v-if="subscription?.cancelAtPeriodEnd" class="font-medium text-red-600">No se renovará</span>
                <span v-else-if="subscription?.trialPeriod?.isInTrial" class="font-medium text-yellow-600">Después de prueba</span>
                <span v-else class="font-medium text-green-600">Automática</span>
              </div>
              <div class="flex justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
                <span class="text-gray-600">Periodo actual</span>
                <span class="font-medium text-gray-900">Hasta {{ subscription?.currentPeriodEnd ? formatDate(subscription.currentPeriodEnd) : '-' }}</span>
              </div>
            </div>
            <div v-else class="p-4 bg-gray-50 border border-gray-100 rounded-lg text-center">
                <p class="text-sm text-gray-500 italic">No hay información de próxima facturación.</p>
                <p v-if="!isSubscriptionActive" class="text-xs text-gray-400 mt-1">Activa una suscripción para ver estos detalles.</p>
            </div>
          </div>

          <!-- Estado detallado de la suscripción -->
          <div v-if="subscription?.detailedStatus && subscription.detailedStatus.priority !== 'low'"
               class="bg-white rounded-xl p-5 border border-gray-200/75 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center mb-4">
              <div class="p-2 rounded-lg mr-3"
                   :class="getStatusIconClasses(subscription.detailedStatus.priority)">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6"
                     :class="getStatusIconColor(subscription.detailedStatus.priority)"
                     fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path v-if="subscription.detailedStatus.priority === 'critical'"
                        stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  <path v-else-if="subscription.detailedStatus.priority === 'high'"
                        stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path v-else
                        stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 class="font-semibold"
                  :class="getStatusTitleColor(subscription.detailedStatus.priority)">
                Estado de la suscripción
              </h4>
            </div>

            <div class="p-4 rounded-lg border"
                 :class="getStatusBorderClasses(subscription.detailedStatus.priority)">
              <div class="flex items-start">
                <div class="flex-1">
                  <p class="text-sm font-medium mb-2"
                     :class="getStatusTextColor(subscription.detailedStatus.priority)">
                    {{ getStatusTitle(subscription.detailedStatus.detailed_status) }}
                  </p>
                  <p class="text-sm"
                     :class="getStatusMessageColor(subscription.detailedStatus.priority)">
                    {{ subscription.detailedStatus.user_message }}
                  </p>
                </div>
              </div>

              <!-- Botón de acción si se requiere -->
              <div v-if="subscription.detailedStatus.action_required" class="mt-4 flex justify-center">
                <button
                  @click="handleDetailedStatusAction(subscription.detailedStatus.action_required)"
                  class="px-4 py-2 rounded-lg font-medium transition-colors"
                  :class="getActionButtonClasses(subscription.detailedStatus.priority)">
                  {{ getActionButtonText(subscription.detailedStatus.action_required) }}
                </button>
              </div>
            </div>
          </div>

          <!-- Tarjeta de método de pago -->
          <div class="bg-white rounded-xl p-5 border border-gray-200/75 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center mb-4">
              <div class="p-2 bg-impacto-blue/10 rounded-lg mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <h4 class="font-semibold text-impacto-blue">Método de pago</h4>
            </div>
            <!-- Mostrar método de pago si existe -->
            <div v-if="subscription?.paymentMethod" class="space-y-3 text-sm">
              <div class="p-4 bg-white rounded-lg border border-gray-200 hover:border-impacto-blue/30 transition-colors flex items-center shadow-sm hover:shadow">
                <div class="mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-impacto-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <div class="flex-1">
                  <div class="font-medium text-gray-900">{{ (subscription.paymentMethod?.brand || 'visa').charAt(0).toUpperCase() + (subscription.paymentMethod?.brand || 'visa').slice(1) }}</div>
                  <div class="text-gray-600">•••• {{ subscription.paymentMethod?.lastFourDigits || '4242' }}</div>
                  <div class="text-gray-500 text-xs mt-1">Expira: {{ subscription.paymentMethod?.expiryMonth || '12' }}/{{ subscription.paymentMethod?.expiryYear || '2025' }}</div>
                  <!-- Mostrar fuente del método de pago si está disponible -->
                  <div v-if="subscription.paymentMethodSource" class="text-xs text-gray-400 mt-1">
                    Fuente: {{ getPaymentMethodSourceText(subscription.paymentMethodSource) }}
                  </div>
                </div>
                <!-- Indicador de estado si hay problemas -->
                <div v-if="subscription.actionRequired" class="ml-2">
                  <div class="flex items-center text-amber-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Mensaje de acción requerida -->
              <div v-if="subscription.actionRequired" class="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <div class="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-600 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div class="text-sm">
                    <p class="font-medium text-amber-800">{{ getActionRequiredTitle(subscription.actionRequired) }}</p>
                    <p class="text-amber-700 mt-1">{{ getActionRequiredMessage(subscription.actionRequired) }}</p>
                  </div>
                </div>
              </div>

              <div class="flex justify-center space-x-4 mt-3">
                <button
                  @click="openUpdatePaymentDialog()"
                  class="text-impacto-blue text-sm hover:text-impacto-blue/80 hover:underline font-medium flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  {{ subscription.actionRequired === 'update_payment_method' ? 'Agregar método de pago' : 'Actualizar método de pago' }}
                </button>
                <span class="text-gray-300">|</span>
                <!-- Botón condicional: Cancelar o Reactivar según el estado -->
                <button
                  v-if="!subscription?.cancelAtPeriodEnd"
                  @click="showCancelConfirmation = true"
                  class="text-gray-500 text-sm hover:text-gray-700 hover:underline font-medium flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Cancelar suscripción
                </button>
                <button
                  v-else
                  @click="reactivateSubscriptionAction()"
                  class="text-impacto-orange text-sm hover:text-impacto-orange/80 hover:underline font-medium flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Reactivar suscripción
                </button>
              </div>
            </div>

            <!-- Mostrar mensaje mejorado cuando no hay método de pago -->
            <div v-else class="space-y-3">
              <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div class="text-sm">
                    <p class="font-medium text-red-800">No hay método de pago configurado</p>
                    <p class="text-red-700 mt-1">
                      {{ subscription?.subscriptionRecoverable
                          ? 'Tu suscripción puede ser reactivada agregando un método de pago válido.'
                          : 'Necesitas agregar un método de pago para mantener tu suscripción activa.' }}
                    </p>
                  </div>
                </div>
              </div>

              <div class="flex justify-center">
                <button
                  @click="openUpdatePaymentDialog()"
                  class="bg-impacto-blue text-white px-4 py-2 rounded-lg hover:bg-impacto-blue/90 font-medium flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Agregar método de pago
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Eliminamos la sección de características incluidas -->
      </div>
    </div>

    <!-- Historial de facturas (solo visible si no hay error y no está cargando) -->
    <div v-if="!isLoading && !error" class="bg-white rounded-xl shadow-lg border border-gray-200/75 overflow-hidden hover:shadow-xl transition-shadow duration-300">
      <!-- Cabecera con gradiente -->
      <div class="bg-gradient-to-r from-impacto-blue to-impacto-blue/90 p-5 text-white">
        <div class="flex items-center">
          <div class="p-2 bg-impacto-orange/20 rounded-lg mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold">Historial de facturas</h3>
        </div>
        <p class="text-gray-300 mt-1 ml-11 text-sm">
          Aquí puedes ver todas tus facturas y descargarlas en formato PDF.
        </p>
      </div>
      
      <div class="p-5">
        <!-- Tabla de facturas con diseño moderno -->
        <div class="overflow-x-auto bg-white rounded-lg">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-5 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fecha</th>
                <th class="px-5 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Número</th>
                <th class="px-5 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Importe</th>
                <th class="px-5 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                <th class="px-5 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <tr v-for="invoice in invoices" :key="invoice.id" class="hover:bg-gray-50 transition-colors">
                <td class="px-5 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ formatDate(invoice.date) }}</td>
                <td class="px-5 py-4 whitespace-nowrap text-sm text-gray-700">{{ invoice.number }}</td>
                <td class="px-5 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ invoice.amount }}</td>
                <td class="px-5 py-4 whitespace-nowrap">
                  <span :class="{
                    'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium': true,
                    'bg-green-100 text-green-800': invoice.status === 'Pagado' || invoice.status === 'paid',
                    'bg-yellow-100 text-yellow-800': invoice.status === 'Pendiente' || invoice.status === 'open',
                    'bg-red-100 text-red-800': invoice.status === 'Fallido' || invoice.status === 'failed',
                    'bg-gray-100 text-gray-800': !['Pagado', 'Pendiente', 'Fallido', 'paid', 'open', 'failed'].includes(invoice.status)
                  }">
                    {{ getInvoiceStatusText(invoice.status) }}
                  </span>
                </td>
                <td class="px-5 py-4 whitespace-nowrap text-sm">
                  <button 
                    class="inline-flex items-center px-3 py-1.5 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" 
                    @click="downloadInvoice(invoice.id)"
                    :disabled="isDownloadingInvoice"
                    :class="{'opacity-50 cursor-not-allowed': isDownloadingInvoice}"
                  >
                    <svg v-if="isDownloadingInvoice && downloadingInvoiceId === invoice.id" class="animate-spin h-4 w-4 mr-1 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    PDF
                  </button>
                </td>
              </tr>
              <tr v-if="invoices.length === 0">
                <td colspan="5" class="px-5 py-8 text-center">
                  <div class="flex flex-col items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-gray-500 text-sm">No hay facturas disponibles todavía</p>
                    <p class="text-gray-400 text-xs mt-1">Las facturas aparecerán aquí una vez que se generen</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Eliminamos la tarjeta de cancelación de suscripción ya que ahora está integrada en la sección de método de pago -->
    
    <!-- Diálogo de actualización de método de pago -->
    <div v-if="showUpdatePaymentDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full overflow-hidden">
        <div class="bg-gradient-to-r from-impacto-blue to-impacto-blue/90 p-5 text-white">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="p-2 bg-white/10 rounded-lg mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold">Actualizar método de pago</h3>
            </div>
            <button @click="showUpdatePaymentDialog = false" class="text-white hover:text-gray-200">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        
        <div class="p-6 space-y-6">
          <!-- Contenedor del formulario de pago -->
          <div v-if="!updatePaymentSuccess" class="space-y-6">
            <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
              <h4 class="font-medium text-gray-900 mb-3">Información de tarjeta</h4>
              <p class="text-gray-700 mb-4">
                Introduce los datos de tu nueva tarjeta. Esta información es procesada de forma segura por Stripe y no almacenamos tus datos completos de tarjeta.
              </p>
              
              <!-- Método actual de pago si existe -->
              <div v-if="subscription?.paymentMethod" class="mb-4 p-3 bg-blue-50 border border-blue-100 rounded-lg">
                <p class="text-sm text-gray-600">Método actual:</p>
                <div class="flex items-center mt-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  <span class="text-gray-800 font-medium">{{ (subscription.paymentMethod?.brand || 'visa').charAt(0).toUpperCase() + (subscription.paymentMethod?.brand || 'visa').slice(1) }} •••• {{ subscription.paymentMethod?.lastFourDigits || '4242' }}</span>
                </div>
              </div>
              
              <!-- Contenedor para el formulario de Stripe Elements -->
              <div class="mt-4">
                <div id="card-element" class="p-3 border border-gray-300 rounded-md bg-white"></div>
                <div id="card-errors" class="mt-2 text-sm text-red-600"></div>
              </div>
            </div>
            
            <!-- Botón de error si existe -->
            <div v-if="updatePaymentError" class="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
              {{ updatePaymentError }}
            </div>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-end">
              <button 
                @click="showUpdatePaymentDialog = false" 
                class="px-5 py-3 bg-gray-50 text-gray-700 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center justify-center shadow-sm"
              >
                Cancelar
              </button>
              
              <button 
                @click="submitPaymentMethod()" 
                :disabled="isUpdatingPayment"
                class="px-5 py-3 bg-impacto-blue text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="isUpdatingPayment" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Actualizar método de pago
              </button>
            </div>
          </div>
          
          <!-- Mensaje de éxito -->
          <div v-if="updatePaymentSuccess" class="space-y-6">
            <div class="bg-green-50 rounded-lg p-5 border border-green-200 flex flex-col items-center">
              <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h4 class="text-lg font-medium text-gray-900 mb-2">Método de pago actualizado</h4>
              <p class="text-gray-700 text-center mb-4">
                Tu información de pago ha sido actualizada correctamente. Los próximos cargos se realizarán con el nuevo método de pago.
              </p>
            </div>
            
            <div class="flex justify-center">
              <button 
                @click="closeUpdatePaymentSuccess()" 
                class="px-5 py-3 bg-impacto-blue text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center shadow-sm"
              >
                Aceptar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Diálogo de confirmación de cancelación -->
    <div v-if="showCancelConfirmation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full overflow-hidden">
        <div class="bg-gradient-to-r from-red-700 to-red-900 p-5 text-white">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="p-2 bg-white/10 rounded-lg mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h3 class="text-xl font-bold">Cancelar suscripción</h3>
            </div>
            <button @click="showCancelConfirmation = false" class="text-white hover:text-gray-200">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        
        <div class="p-6 space-y-6">
          <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
            <h4 class="font-semibold text-gray-900 mb-2">Antes de cancelar tu suscripción</h4>
            <p class="text-gray-700 mb-4">
              Lamentamos que estés considerando cancelar tu suscripción al Valorador Inmobiliario IA. 
              Si tienes alguna duda o problema con el servicio, no dudes en contactar con nuestro equipo de soporte antes de cancelar.
            </p>
          </div>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-end">
            <button 
              @click="showCancelConfirmation = false" 
              class="px-5 py-3 bg-gray-50 text-gray-700 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center justify-center shadow-sm"
            >
              Volver
            </button>
            
            <!-- Eliminado el botón de portal de facturación -->
            
            <button 
              @click="cancelSubscriptionAction()" 
              :disabled="isCancellationLoading"
              class="px-5 py-3 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors flex items-center justify-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="isCancellationLoading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Confirmar cancelación
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Al final del template principal, ANTES del cierre de </div> del componente padre -->
  <PlanSelectionModal 
    :showModal="showPlanSelectionModal" 
    :currentPlanId="subscription?.planId || null" 
    :currentBillingCycle="subscription?.billingCycle?.toLowerCase() as 'monthly' | 'annual' || null" 
    :isSubscriptionActive="isSubscriptionActive"
    @close="showPlanSelectionModal = false"
    @plan-changed="onPlanSuccessfullyChanged"
    @change-error="handlePlanChangeError"
    @stripe-action-required="handlePotentialStripeAction"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue';
import { getCurrentSubscription, getInvoices, getInvoicePdfUrl, cancelSubscription, reactivateSubscription, retryPayment, confirmPayment } from '@/api/subscriptionService';
import type { Subscription, Invoice } from '@/api/subscriptionService';
import { apiFetch } from '@/utils/apiFetch';
import PlanSelectionModal from '@/components/dashboard/PlanSelectionModal.vue';
import PaymentNotifications from '@/components/dashboard/PaymentNotifications.vue';
import { useToast } from '@/composables/useToast'; // Importar useToast

// Stripe será accedido como (window as any).Stripe

// --- Interfaces para la gestión de cambio de planes ---
/*
interface PlanBenefit {
  text: string;
  icon: any; // Tipo Icon de Heroicons
  iconColor: string;
  subtext?: string;
}
*/

// Estado para la suscripción y facturas
const subscription = ref<Subscription | null>(null);
const invoices = ref<Invoice[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

// Importar y usar el sistema de toasts
const { showToast } = useToast();

// Estado para el diálogo de actualización de método de pago
const showUpdatePaymentDialog = ref(false);
const isUpdatingPayment = ref(false);
const updatePaymentSuccess = ref(false);
const updatePaymentMessage = ref('');
const updatePaymentError = ref('');
const stripe = ref<any>(null);
const cardElement = ref<any>(null);

// Estado para la cancelación de suscripción
const showCancelConfirmation = ref(false);
const cancelAtPeriodEnd = ref(true);
const isCancellationLoading = ref(false);
const cancellationSuccess = ref(false);
const cancellationMessage = ref('');

// Estado para la descarga de facturas
const isDownloadingInvoice = ref(false);
const downloadingInvoiceId = ref('');

// Ref para controlar visibilidad del modal de selección de planes
const showPlanSelectionModal = ref(false);
// Ref para el estado de carga mientras se procesa el cambio de plan (comunicación con backend)
// const isProcessingPlanChange = ref(false); // Eliminamos esta línea

// Tooltip para próxima facturación
const showBillingTooltip = ref(false);

// Calcular si la suscripción está activa
const isSubscriptionActive = computed(() => {
  return subscription.value?.status === 'active' || subscription.value?.status === 'trialing';
});

// Nueva propiedad computada para traducir el ciclo de facturación
const billingCycleDisplay = computed(() => {
  if (!subscription.value?.billingCycle) {
    return 'No disponible';
  }
  switch (subscription.value.billingCycle.toLowerCase()) {
    case 'monthly':
      return 'Mensual';
    case 'annual':
      return 'Anual';
    default:
      return subscription.value.billingCycle;
  }
});

// Calcular el estado de la suscripción para mostrar
const subscriptionStatus = computed(() => {
  if (!subscription.value) return { text: 'Desconocido', class: 'bg-gray-100 text-gray-800' };
  
  // Si tenemos un nombre de plan desde la BD, usarlo para el estado si la suscripción está cancelada pero el plan es conocido
  if (subscription.value.status === 'canceled' && subscription.value.planName) {
      return { text: `Cancelado - ${subscription.value.planName}`, class: 'bg-red-100 text-red-800' };
  }

  switch (subscription.value.status) {
    case 'active':
      return { text: 'Activo', class: 'bg-green-100 text-green-800' };
    case 'trialing':
      return { text: 'Periodo de prueba', class: 'bg-blue-100 text-blue-800' };
    case 'past_due':
      return { text: 'Pago pendiente', class: 'bg-yellow-100 text-yellow-800' };
    case 'canceled':
      return { text: 'Cancelado', class: 'bg-red-100 text-red-800' };
    default:
      return { text: subscription.value.status, class: 'bg-gray-100 text-gray-800' };
  }
});

// Nueva propiedad computada para detalles de próxima facturación
const nextBillingDetails = computed(() => {
  if (!subscription.value || !subscription.value.price) { // Usar price para asegurar que tenemos el bruto
    return {
      rawAmount: 0,
      currency: 'EUR',
      balanceAmount: 0,
      netAmount: 0,
      hasCredit: false,
      formattedPrice: '-',
      formattedBalance: '-',
      formattedNetPrice: '-'
    };
  }

  const rawAmount = parseFloat(subscription.value.price) || 0;

  const nextBillingAmountParts = subscription.value.nextBillingAmount ? subscription.value.nextBillingAmount.split(' ') : [];
  const currency = nextBillingAmountParts.length > 1 ? nextBillingAmountParts[1] : 'EUR';

  let customerBalanceInCents = 0;
  const rawCustomerBalance = subscription.value.customer_balance_cents;

  if (typeof rawCustomerBalance === 'number') {
    customerBalanceInCents = rawCustomerBalance;
  } else if (typeof rawCustomerBalance === 'string') {
    const parsedBalance = parseFloat(rawCustomerBalance);
    if (!isNaN(parsedBalance)) {
      customerBalanceInCents = parsedBalance;
    } else {
    }
  } else if (rawCustomerBalance !== null && rawCustomerBalance !== undefined) {
  }

  const balanceAmount = customerBalanceInCents < 0 ? Math.abs(customerBalanceInCents) / 100 : 0;

  const netAmount = Math.max(0, rawAmount - balanceAmount);
  const hasCredit = balanceAmount > 0;

  const formatter = new Intl.NumberFormat('es-ES', { style: 'currency', currency: currency, currencyDisplay: 'code' });

  return {
    rawAmount, // Precio bruto del plan (ej: 780)
    currency,
    balanceAmount, // Crédito como número positivo (ej: 539.99)
    netAmount, // Importe neto a pagar (ej: 240.01)
    hasCredit,
    formattedPrice: formatter.format(rawAmount), // ej: 780,00 EUR
    formattedBalance: formatter.format(balanceAmount), // ej: 539,99 EUR (se mostrará como positivo)
    formattedNetPrice: formatter.format(netAmount) // ej: 240,01 EUR
  };
});

// Función para cargar los datos de suscripción
async function loadSubscriptionData(isPostPaymentAction: boolean = false, attempt: number = 1) {
  try {
    isLoading.value = true;
    error.value = null;
    
    // Cargar la suscripción
    try {
      subscription.value = await getCurrentSubscription();
      error.value = null; 
    } catch (err: any) {
      console.error(`Error al cargar la suscripción (intento ${attempt}):`, err);
      if (isPostPaymentAction && attempt < 3) { 
        showToast(`Reintentando cargar datos de suscripción... (intento ${attempt + 1})`, 'info');
        await new Promise(resolve => setTimeout(resolve, 2500 * attempt)); 
        await loadSubscriptionData(isPostPaymentAction, attempt + 1);
        return; 
      } else {
        error.value = 'No se pudo cargar la información de suscripción. Por favor, inténtalo de nuevo más tarde.';
        if (!isPostPaymentAction || attempt >= 3) {
            showToast(error.value, 'error');
        }
      }
    }
    
    if (subscription.value || !isPostPaymentAction || (isPostPaymentAction && attempt >=3)) {
        try {
            invoices.value = await getInvoices();
        } catch (err) {
            console.error('Error al cargar las facturas:', err);
            invoices.value = [];
        }
    }
    
  } catch (err: any) { 
    console.error('Error inesperado al cargar los datos (fuera del reintento):', err);
    if (err.message?.includes('401') || err.message?.includes('autoriza')) {
      error.value = 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.';
      showToast(error.value, 'error');
      setTimeout(() => window.location.reload(), 2000);
    } else if (err.message?.includes('conexión') || err.message?.includes('network')) {
      error.value = 'No se pudo conectar con el servidor. Por favor, verifica tu conexión a internet.';
      showToast(error.value, 'error');
    } else if (!error.value) { 
      error.value = 'Ocurrió un error al cargar los datos. Por favor, inténtalo de nuevo más tarde.';
      showToast(error.value, 'error');
    }
  } finally {
    if (!isPostPaymentAction || attempt >= 3 || subscription.value) {
        isLoading.value = false;
    }
  }
}

// Función para traducir los estados de las facturas
function getInvoiceStatusText(status: string): string {
  switch (status.toLowerCase()) {
    case 'paid':
      return 'Pagado';
    case 'open':
      return 'Pendiente';
    case 'failed':
      return 'Fallido';
    case 'pagado':
      return 'Pagado';
    case 'pendiente':
      return 'Pendiente';
    case 'fallido':
      return 'Fallido';
    default:
      return status;
  }
}

// Función para formatear el precio de la suscripción
function formatSubscriptionPrice(price: string | null | undefined): string {
  if (!price) return '-';

  // Extraer el número del precio (puede venir como "49.00" o "49.00 EUR")
  const numericPrice = parseFloat(price.toString().replace(/[^\d.,]/g, '').replace(',', '.'));

  if (isNaN(numericPrice)) return price.toString();

  // Formatear igual que en "Próxima facturación" (con "EUR" en lugar de "€")
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'EUR',
    currencyDisplay: 'code'
  }).format(numericPrice);
}

// Cargar datos al montar el componente
onMounted(async () => {
  // Verificar si venimos de una redirección 3DS
  const urlParams = new URLSearchParams(window.location.search);
  const setupIntentCompleted = urlParams.get('setup_intent_completed');
  const setupIntentId = urlParams.get('setup_intent_id');

  if (setupIntentCompleted === 'true' && setupIntentId) {
    await handleSetupIntentRedirect(setupIntentId);
    // Limpiar los parámetros de la URL
    const newUrl = window.location.pathname;
    window.history.replaceState({}, document.title, newUrl);
  }

  loadSubscriptionData();
  // Considera cargar Stripe.js aquí si aún no está cargado,
  // para que esté disponible para handlePotentialStripeAction
  // ensureStripeLoaded(); // Podrías crear una función así
});

// --- Función para manejar acciones de Stripe que requieren confirmación del cliente ---
async function handlePotentialStripeAction(clientSecret: string) {
  isLoading.value = true; // Usar un loader general o uno específico para esto
  // alert('Tu cambio de plan requiere una confirmación adicional.');
  showToast('Tu cambio de plan requiere una confirmación adicional.', 'info');

  // Asegúrate de que Stripe.js esté cargado y `stripe.value` inicializado
  // Si `initStripeElements()` no ha sido llamado o no es adecuado aquí,
  // necesitarás una forma de cargar/inicializar stripe.value.
  // Por ahora, asumimos que stripe.value podría estar ya disponible si
  // el diálogo de pago se abrió antes, o que se cargará.
  
  // Ejemplo de cómo asegurar que Stripe esté cargado (debes adaptarlo):
  if (!stripe.value && window.Stripe) {
    const stripePk = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
    if (stripePk) {
      stripe.value = window.Stripe(stripePk);
    } else {
      console.error('Error: VITE_STRIPE_PUBLISHABLE_KEY no está definida.');
      // alert('Error de configuración de pago. Contacta con soporte.');
      showToast('Error de configuración de pago. Contacta con soporte.', 'error');
      isLoading.value = false;
      return;
    }
  } else if (!stripe.value) {
    // TODO: Cargar Stripe.js dinámicamente si no está en window.Stripe
    // Esto es importante si el usuario llega aquí sin haber abierto antes el diálogo de pago.
    console.error('Stripe.js no cargado y stripe.value no inicializado.');
    // alert('Error al procesar el pago. Intenta de nuevo o contacta con soporte.');
    showToast('Error al procesar el pago. Intenta de nuevo o contacta con soporte.', 'error');
    isLoading.value = false;
    return;
  }

  try {
    let result;
    if (clientSecret.startsWith('seti_')) { // Es un SetupIntent
        result = await stripe.value.confirmCardSetup(clientSecret);
    } else if (clientSecret.startsWith('pi_')) { // Es un PaymentIntent
        result = await stripe.value.confirmCardPayment(clientSecret);
    }
 else {
        throw new Error('Client secret no reconocido.');
    }

    if (result.error) {
      throw new Error(result.error.message || 'Error al confirmar la acción de Stripe.');
    }

    // Si la confirmación es exitosa (para PaymentIntent o SetupIntent)
    // alert('Acción de Stripe confirmada. Actualizando información...');
    showToast('Acción de Stripe confirmada. Actualizando información...', 'success');
    await loadSubscriptionData(true); // Recargar datos para reflejar el estado final, indicando que es post-pago

  } catch (e: any) {
    console.error('Error durante la confirmación de Stripe:', e);
    // error.value = e.message || 'Hubo un problema al confirmar tu acción con el proveedor de pagos.'; // Reemplazado
    // showToast(error.value, 'error'); // Reemplazado
    handleError(e); // Usar handleError
  } finally {
    isLoading.value = false;
  }
}

// --- Nueva Función para Manejar la Selección de Plan desde el Modal ---
// const handleSubscriptionPlanChange = async (planId: string, billingCycle: 'monthly' | 'annual') => {
//   showPlanSelectionModal.value = false; // Cerrar el modal inmediatamente
//   isProcessingPlanChange.value = true; // Activar loader específico para el cambio de plan
//   error.value = null; // Limpiar errores previos

//   try {
//     const response = await apiFetch('/api/update-subscription-plan.php', {
//       method: 'POST',
//       body: JSON.stringify({
//         new_plan_slug: planId,
//         new_billing_cycle: billingCycle,
//         // current_subscription_id: subscription.value?.id // Opcional, si el backend lo usa
//       }),
//     });

//     if (!response.success) {
//       // Si el backend devuelve un error específico, lo mostramos
//       throw new Error(response.message || 'Error al cambiar de plan.');
//     }

//     // Si la respuesta indica que se requiere una acción de pago (ej. 3D Secure)
//     if (response.payment_intent_client_secret) {
//       // Delegar a una función que maneje la confirmación de Stripe.js
//       await handlePotentialStripeAction(response.payment_intent_client_secret);
//     } else {
//       // Si no se requiere acción adicional, mostrar mensaje de éxito y recargar datos
//       showToast(response.message || 'Plan cambiado correctamente. Actualizando información...', 'success');
//       await loadSubscriptionData(true); // Recargar datos para reflejar el cambio, indicando que es post-pago
//     }

//   } catch (err: any) {    
//     console.error('Error al procesar el cambio de plan:', err);
//     handleError(err); // Usar handleError
//   } finally {
//     isProcessingPlanChange.value = false; // Desactivar loader específico
//   }
// };

// Formatear fecha
function formatDate(dateString: string | Date): string {
  const date = dateString instanceof Date ? dateString : new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

// Abrir el portal de facturación de Stripe
// Inicializar Stripe Elements cuando se abra el diálogo
async function initStripeElements() {
  if (!stripe.value) {
    try {
      // Cargar Stripe.js dinámicamente
      if (!window.Stripe) {
        const script = document.createElement('script');
        script.src = 'https://js.stripe.com/v3/';
        script.async = true;
        document.head.appendChild(script);
        
        await new Promise((resolve) => {
          script.onload = resolve;
        });
      }
      
      // Inicializar Stripe con la clave pública desde variables de entorno
      const stripePk = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
      if (!stripePk) {
        console.error('Error: VITE_STRIPE_PUBLISHABLE_KEY no está definida en el archivo .env');
        updatePaymentError.value = 'Error de configuración. Contacta con soporte.';
        return;
      }
      // Asegurarnos de que window.Stripe esté disponible
      if (typeof window.Stripe === 'function') {
        stripe.value = window.Stripe(stripePk);
      } else {
        console.error('Error: Stripe no está disponible en window');
        updatePaymentError.value = 'Error al cargar Stripe. Contacta con soporte.';
        return;
      }
      
      // Esperar al siguiente tick para asegurar que el DOM está actualizado
      await nextTick();
      
      // Inicializar Elements y crear el Card Element
      const elements = stripe.value.elements();
      cardElement.value = elements.create('card', {
        style: {
          base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
              color: '#aab7c4'
            }
          },
          invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
          }
        }
      });
      
      // Montar el Card Element en el DOM
      setTimeout(() => {
        const element = document.getElementById('card-element');
        if (element) {
          cardElement.value.mount('#card-element');
          
          // Escuchar eventos de cambio para mostrar errores
          cardElement.value.on('change', (event: any) => {
            const displayError = document.getElementById('card-errors');
            if (displayError) {
              displayError.textContent = event.error ? event.error.message : '';
            }
          });
        }
      }, 100);
      
    } catch (err) {
      console.error('Error al inicializar Stripe:', err);
      updatePaymentError.value = 'No se pudo inicializar el formulario de pago. Por favor, inténtalo de nuevo más tarde.';
    }
  }
}

// Limpiar el formulario cuando se cierre el diálogo
function cleanupStripeElements() {
  if (cardElement.value) {
    cardElement.value.unmount();
    cardElement.value = null;
  }
}

// Manejar la apertura del diálogo de actualización de pago
function openUpdatePaymentDialog() {
  showUpdatePaymentDialog.value = true;
  updatePaymentSuccess.value = false;
  updatePaymentError.value = '';
  
  // Inicializar Stripe Elements
  nextTick(() => {
    initStripeElements();
  });
}

// Función eliminada para evitar advertencia TS6133

// Cerrar el mensaje de éxito y recargar los datos
function closeUpdatePaymentSuccess() {
  showUpdatePaymentDialog.value = false;
  cleanupStripeElements();
  loadSubscriptionData(); // Recargar los datos de suscripción
}

// Enviar el nuevo método de pago con soporte para 3DS
async function submitPaymentMethod() {
  try {
    isUpdatingPayment.value = true;
    updatePaymentError.value = '';

    // Verificar que Stripe está inicializado
    if (!stripe.value) {
      throw new Error('El sistema de pago no está inicializado correctamente');
    }

    // Paso 1: Crear SetupIntent en el servidor
    const setupResponse = await apiFetch('update-payment-method-v2.php', {
      method: 'POST',
      body: JSON.stringify({
        action: 'create_setup_intent'
      })
    });

    if (!setupResponse.success) {
      throw new Error(setupResponse.message || 'No se pudo inicializar la actualización del método de pago');
    }

    const { setup_intent_client_secret, setup_intent_id } = setupResponse;

    // Paso 2: Confirmar SetupIntent con Stripe (esto manejará 3DS automáticamente)
    const confirmResult = await stripe.value.confirmCardSetup(setup_intent_client_secret, {
      payment_method: {
        card: cardElement.value,
      },
      return_url: `${window.location.origin}/suscripcion?setup_intent_completed=true&setup_intent_id=${setup_intent_id}`,
    });

    if (confirmResult.error) {
      throw new Error(confirmResult.error.message || 'Error al procesar la tarjeta');
    }

    // Paso 3: Si llegamos aquí, el SetupIntent fue exitoso (sin 3DS o 3DS completado)
    if (confirmResult.setupIntent.status === 'succeeded') {
      // Confirmar en el servidor
      const confirmResponse = await apiFetch('update-payment-method-v2.php', {
        method: 'POST',
        body: JSON.stringify({
          action: 'confirm_setup_intent',
          setup_intent_id: setup_intent_id
        })
      });

      if (!confirmResponse.success) {
        throw new Error(confirmResponse.message || 'No se pudo completar la actualización del método de pago');
      }

      // Mostrar mensaje de éxito
      updatePaymentSuccess.value = true;
      updatePaymentMessage.value = 'Método de pago actualizado correctamente';

      // Mostrar toast de éxito
      showToast('Método de pago actualizado correctamente', 'success');

    } else {
      throw new Error('El SetupIntent no se completó correctamente. Estado: ' + confirmResult.setupIntent.status);
    }

  } catch (err: any) {
    console.error('Error al actualizar el método de pago:', err);
    updatePaymentError.value = err.message || 'Error al actualizar el método de pago. Por favor, inténtalo de nuevo.';

    // Mostrar toast de error
    showToast(updatePaymentError.value, 'error');

    // Si el error es de autenticación, forzar recarga de la página
    if (err.message?.includes('401') || err.message?.includes('No autorizado')) {
      console.warn('Error de autenticación, recargando la página...');
      setTimeout(() => window.location.reload(), 2000);
    }
  } finally {
    isUpdatingPayment.value = false;
  }
}

// Manejar redirección después de 3DS
async function handleSetupIntentRedirect(setupIntentId: string) {
  try {
    isLoading.value = true;

    // Inicializar Stripe si no está disponible
    if (!stripe.value) {
      const stripePk = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
      if (stripePk && window.Stripe) {
        stripe.value = window.Stripe(stripePk);
      } else {
        throw new Error('No se pudo inicializar Stripe');
      }
    }

    // Recuperar el SetupIntent para verificar su estado
    const { setupIntent, error } = await stripe.value.retrieveSetupIntent(setupIntentId);

    if (error) {
      throw new Error(error.message || 'Error al verificar el estado del SetupIntent');
    }

    if (setupIntent.status === 'succeeded') {
      // Confirmar en el servidor
      const confirmResponse = await apiFetch('update-payment-method-v2.php', {
        method: 'POST',
        body: JSON.stringify({
          action: 'confirm_setup_intent',
          setup_intent_id: setupIntentId
        })
      });

      if (confirmResponse.success) {
        showToast('Método de pago actualizado correctamente tras autenticación', 'success');
      } else {
        throw new Error(confirmResponse.message || 'No se pudo completar la actualización');
      }
    } else if (setupIntent.status === 'requires_payment_method') {
      showToast('La autenticación falló. Por favor, intenta con otro método de pago.', 'error');
    } else {
      showToast('Estado inesperado del SetupIntent: ' + setupIntent.status, 'warning');
    }

  } catch (err: any) {
    console.error('Error al manejar redirección 3DS:', err);
    showToast(err.message || 'Error al procesar la autenticación', 'error');
  } finally {
    isLoading.value = false;
  }
}

// Cancelar la suscripción
async function cancelSubscriptionAction() {
  try {
    isCancellationLoading.value = true;
    error.value = '';
    cancellationMessage.value = '';
    
    // Verificar si hay una suscripción activa para cancelar
    if (!subscription.value?.id) {
      throw new Error('No hay una suscripción activa para cancelar');
    }
    
    // Verificar si la suscripción ya está cancelada
    if (subscription.value.cancelAtPeriodEnd) {
      throw new Error('Esta suscripción ya está cancelada');
    }
    
    // Enviar la solicitud de cancelación a la API
    const result = await cancelSubscription(cancelAtPeriodEnd.value);
    
    if (result.success) {
      // Actualizar el estado de la suscripción en la UI
      if (subscription.value) {
        subscription.value.cancelAtPeriodEnd = result.cancel_at_period_end;
        subscription.value.status = result.subscription_status;
      }
      
      // Mostrar mensaje de éxito
      cancellationSuccess.value = true;
      cancellationMessage.value = result.message || 'Tu suscripción ha sido cancelada correctamente.';
      
      // Ocultar el diálogo de confirmación después de un momento
      setTimeout(() => {
        showCancelConfirmation.value = false;
      }, 2000);
      
      // Recargar los datos después de un momento para reflejar los cambios
      setTimeout(() => {
        loadSubscriptionData();
      }, 2500);
    } else {
      throw new Error(result.message || 'No se pudo cancelar la suscripción');
    }
    
  } catch (err: any) {
    console.error('Error al cancelar la suscripción:', err);
    cancellationSuccess.value = false;
    
    // Mensajes de error más específicos
    if (err.message?.includes('No autorizado') || err.message?.includes('401')) {
      error.value = 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.';
      // Recargar la página para forzar el inicio de sesión
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } else {
      error.value = 'Error al cancelar la suscripción: ' + 
        (err.message || 'Por favor, inténtalo de nuevo más tarde o contacta con soporte.');
    }
    
  } finally {
    isCancellationLoading.value = false;
  }
}

// Reactivar la suscripción previamente cancelada
async function reactivateSubscriptionAction() {
  try {
    isLoading.value = true;
    error.value = '';
    
    // Verificar si hay una suscripción para reactivar
    if (!subscription.value?.id) {
      throw new Error('No hay una suscripción para reactivar');
    }
    
    // Verificar si la suscripción está cancelada (solo se puede reactivar si está cancelada)
    if (!subscription.value.cancelAtPeriodEnd) {
      throw new Error('Esta suscripción no está cancelada y no necesita reactivación.');
    }
    
    // Enviar la solicitud de reactivación a la API
    const result = await reactivateSubscription();
    
    if (result.success) {
      // Actualizar el estado de la suscripción en la UI localmente primero
      if (subscription.value) {
        subscription.value.cancelAtPeriodEnd = result.cancel_at_period_end;
        subscription.value.status = result.subscription_status;
      }
      
      error.value = ''; // Limpiar errores previos
      const successMessage = result.message || 'Tu suscripción ha sido reactivada correctamente.';
      showToast(successMessage, 'success');
      
      // Recargar los datos para reflejar todos los cambios desde el backend
      loadSubscriptionData(true); // Indicar que es una acción que puede necesitar reintento
    } else {
      throw new Error(result.message || 'No se pudo reactivar la suscripción');
    }
    
  } catch (err: any) {
    console.error('Error al reactivar la suscripción:', err);
    
    // Mensajes de error más específicos
    if (err.message?.includes('No autorizado') || err.message?.includes('401')) {
      error.value = 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.';
      // Recargar la página para forzar el inicio de sesión
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } else {
      error.value = 'Error al reactivar la suscripción: ' + 
        (err.message || 'Por favor, inténtalo de nuevo más tarde o contacta con soporte.');
      // alert(error.value);
      showToast(error.value, 'error');
    }
    
  } finally {
    isLoading.value = false;
  }
}

// Descargar factura
async function downloadInvoice(invoiceId: string) {
  try {
    isDownloadingInvoice.value = true;
    downloadingInvoiceId.value = invoiceId;
    
    if (!invoiceId) {
      throw new Error('ID de factura no proporcionado');
    }
        
    const currentInvoice = invoices.value.find(inv => inv.id === invoiceId);
    let pdfUrl: string | { url: string } | null = currentInvoice?.pdfUrl || null;
    
    // Si no hay URL en la factura, obtenerla de la API
    if (!pdfUrl) {
      pdfUrl = await getInvoicePdfUrl(invoiceId);
    }
    
    // Verificar que la URL sea válida
    if (!pdfUrl) {
      throw new Error('URL de factura no disponible');
    }
    
    // Si pdfUrl es un objeto con propiedad url (como lo devuelve getInvoicePdfUrl)
    if (typeof pdfUrl === 'object' && 'url' in pdfUrl) {
      pdfUrl = pdfUrl.url;
    }
    
    // Verificar que ahora tenemos una URL válida como string
    if (typeof pdfUrl !== 'string' || !pdfUrl.startsWith('http')) {
      throw new Error('URL de factura no válida');
    }
        
    // Método 1: Abrir directamente en una nueva pestaña (funciona siempre)
    window.open(pdfUrl, '_blank');
    
    // Opcional: También intentar la descarga directa como respaldo
    try {
      // Fetch para obtener el blob
      const response = await fetch(pdfUrl);
      const blob = await response.blob();
      
      // Crear URL para el blob
      const blobUrl = URL.createObjectURL(blob);
      
      // Crear elemento para descargar
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `factura-${invoiceId.replace('in_', '')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Liberar la URL del blob
      setTimeout(() => URL.revokeObjectURL(blobUrl), 100);
    } catch (fetchError) {
      console.warn('No se pudo descargar directamente, pero se abrió en una nueva pestaña', fetchError);
      // No lanzamos error aquí porque ya abrimos en nueva pestaña
    }
    
  } catch (err: any) {
    console.error('Error al descargar la factura:', err);
    
    // Mensajes de error más específicos
    if (err.message?.includes('No autorizado') || err.message?.includes('401')) {
      error.value = 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.';
      // Recargar la página para forzar el inicio de sesión
      setTimeout(() => window.location.reload(), 2000);
    } else if (err.message?.includes('No encontrada') || err.message?.includes('404')) {
      error.value = 'No se pudo encontrar la factura solicitada.';
    } else if (err.message?.includes('ID de factura')) {
      error.value = 'El identificador de la factura no es válido.';
    } else if (err.message?.includes('URL de factura')) {
      error.value = 'No se pudo generar el enlace de descarga de la factura.';
    } else {
      error.value = 'Error al descargar la factura: ' + 
        (err.message || 'Por favor, inténtalo de nuevo más tarde.');
    }
    
    // Mostrar el error en la interfaz
    setTimeout(() => {
      error.value = ''; // Limpiar el mensaje después de 5 segundos
    }, 5000);
  } finally {
    isDownloadingInvoice.value = false;
    downloadingInvoiceId.value = '';
  }
}

async function handleError(err: any) {
  const errorMessage = err.message || 'Error desconocido al procesar la solicitud.';
  error.value = errorMessage;
  if (errorMessage) { // Asegurarse de que errorMessage no sea null o undefined
    showToast(errorMessage, 'error');
  }
}

// Nueva función para manejar el evento 'plan-changed' del modal
const onPlanSuccessfullyChanged = async (eventData: { message: string; newPlanId: string; newBillingCycle: string }) => {
  showToast(eventData.message || 'Plan cambiado con éxito.', 'success');
  showPlanSelectionModal.value = false; // Asegurar que el modal se cierre
  isLoading.value = true; // Mostrar loader general mientras se recargan los datos
  await loadSubscriptionData(true); // Recargar todos los datos de la suscripción
  // isLoading.value = false; // loadSubscriptionData ya maneja esto
};

// Opcional: un handler para el evento de error del modal si se emite 'change-error'
const handlePlanChangeError = (errorMessage: string) => {
  showToast(errorMessage || 'Ocurrió un error al cambiar el plan.', 'error');
  showPlanSelectionModal.value = false;
  // Podrías querer también recargar los datos aquí por si el estado es inconsistente
  // await loadSubscriptionData();
};

// Funciones helper para mostrar información de métodos de pago
const getPaymentMethodSourceText = (source: string): string => {
  switch (source) {
    case 'subscription':
      return 'Método predeterminado de suscripción';
    case 'customer':
      return 'Método predeterminado de cliente';
    case 'attached':
      return 'Método adjunto al cliente';
    default:
      return 'Fuente desconocida';
  }
};

const getActionRequiredTitle = (action: string): string => {
  switch (action) {
    case 'payment_authentication':
      return 'Autenticación requerida';
    case 'retry_payment':
      return 'Reintentar pago';
    case 'update_payment_method':
      return 'Actualizar método de pago';
    case 'reactivate_subscription':
      return 'Reactivar suscripción';
    default:
      return 'Acción requerida';
  }
};

const getActionRequiredMessage = (action: string): string => {
  switch (action) {
    case 'payment_authentication':
      return 'Tu banco requiere autenticación adicional (3D Secure) para procesar el pago. Actualiza tu método de pago para completar la autenticación.';
    case 'retry_payment':
      return 'El último intento de pago falló. Puedes reintentar el pago o actualizar tu método de pago si es necesario.';
    case 'update_payment_method':
      return 'No se pudo procesar el pago con el método actual. Por favor, agrega o actualiza tu método de pago.';
    case 'reactivate_subscription':
      return 'Tu suscripción fue cancelada pero puede ser reactivada. Contacta con soporte o actualiza tu método de pago.';
    default:
      return 'Se requiere una acción de tu parte para mantener tu suscripción activa.';
  }
};

// Funciones helper para el estado detallado de suscripción
const getStatusIconClasses = (priority: string): string => {
  switch (priority) {
    case 'critical':
      return 'bg-red-100';
    case 'high':
      return 'bg-amber-100';
    case 'medium':
      return 'bg-blue-100';
    default:
      return 'bg-gray-100';
  }
};

const getStatusIconColor = (priority: string): string => {
  switch (priority) {
    case 'critical':
      return 'text-red-600';
    case 'high':
      return 'text-amber-600';
    case 'medium':
      return 'text-blue-600';
    default:
      return 'text-gray-600';
  }
};

const getStatusTitleColor = (priority: string): string => {
  switch (priority) {
    case 'critical':
      return 'text-red-700';
    case 'high':
      return 'text-amber-700';
    case 'medium':
      return 'text-blue-700';
    default:
      return 'text-gray-700';
  }
};

const getStatusBorderClasses = (priority: string): string => {
  switch (priority) {
    case 'critical':
      return 'border-red-200 bg-red-50';
    case 'high':
      return 'border-amber-200 bg-amber-50';
    case 'medium':
      return 'border-blue-200 bg-blue-50';
    default:
      return 'border-gray-200 bg-gray-50';
  }
};

const getStatusTextColor = (priority: string): string => {
  switch (priority) {
    case 'critical':
      return 'text-red-800';
    case 'high':
      return 'text-amber-800';
    case 'medium':
      return 'text-blue-800';
    default:
      return 'text-gray-800';
  }
};

const getStatusMessageColor = (priority: string): string => {
  switch (priority) {
    case 'critical':
      return 'text-red-700';
    case 'high':
      return 'text-amber-700';
    case 'medium':
      return 'text-blue-700';
    default:
      return 'text-gray-700';
  }
};

const getActionButtonClasses = (priority: string): string => {
  switch (priority) {
    case 'critical':
      return 'bg-red-600 text-white hover:bg-red-700';
    case 'high':
      return 'bg-amber-600 text-white hover:bg-amber-700';
    case 'medium':
      return 'bg-blue-600 text-white hover:bg-blue-700';
    default:
      return 'bg-gray-600 text-white hover:bg-gray-700';
  }
};

const getStatusTitle = (detailedStatus: string): string => {
  switch (detailedStatus) {
    case 'active_healthy':
      return 'Suscripción activa';
    case 'active_with_recent_issues':
      return 'Activa con problemas recientes';
    case 'trial_active':
      return 'Período de prueba activo';
    case 'trial_ending_soon':
      return 'Período de prueba terminando';
    case 'payment_authentication_required':
      return 'Autenticación requerida';
    case 'payment_failed_recoverable':
      return 'Pago fallido - Recuperable';
    case 'payment_method_required':
      return 'Método de pago requerido';
    case 'payment_failed_multiple_attempts':
      return 'Múltiples fallos de pago';
    case 'setup_incomplete':
      return 'Configuración incompleta';
    case 'setup_expired':
      return 'Configuración expirada';
    case 'canceled_by_user':
      return 'Cancelada por usuario';
    case 'canceled_payment_failure':
      return 'Cancelada por fallo de pago';
    case 'canceled_unknown':
      return 'Suscripción cancelada';
    default:
      return 'Estado desconocido';
  }
};

const getActionButtonText = (actionRequired: string): string => {
  switch (actionRequired) {
    case 'add_payment_method':
      return 'Agregar método de pago';
    case 'update_payment_method':
      return 'Actualizar método de pago';
    case 'authenticate_payment':
      return 'Completar autenticación';
    case 'retry_payment':
      return 'Reintentar pago';
    case 'complete_setup':
      return 'Completar configuración';
    case 'restart_subscription':
      return 'Reiniciar suscripción';
    case 'reactivate_subscription':
      return 'Reactivar suscripción';
    case 'contact_support':
      return 'Contactar soporte';
    default:
      return 'Tomar acción';
  }
};

// Función para reintentar pago
const retryPaymentAction = async () => {
  if (!subscription.value) return;

  try {
    isLoading.value = true;
    showToast('Reintentando pago...', 'info');

    const result = await retryPayment();

    if (result.success) {
      if (result.status === 'paid') {
        // Pago exitoso
        showToast(result.message || 'Pago procesado exitosamente. Tu suscripción ha sido reactivada.', 'success');
        loadSubscriptionData(); // Recargar datos

      } else if (result.status === 'requires_action' && result.payment_intent) {
        // Requiere autenticación 3DS
        showToast('Tu banco requiere autenticación adicional. Completando...', 'info');

        // Verificar que Stripe esté disponible
        if (typeof (window as any).Stripe === 'undefined') {
          throw new Error('Stripe no está disponible. Por favor, recarga la página.');
        }

        // Confirmar el Payment Intent con Stripe
        const stripe = (window as any).Stripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
        const { error, paymentIntent } = await stripe.confirmCardPayment(result.payment_intent.client_secret, {
          return_url: `${window.location.origin}/suscripcion`
        });

        if (error) {
          throw new Error(error.message);
        } else if (paymentIntent && paymentIntent.status === 'succeeded') {
          // Confirmar el pago en el backend
          const confirmResult = await confirmPayment(paymentIntent.id);

          if (confirmResult.success) {
            showToast('Pago completado exitosamente con autenticación.', 'success');
            loadSubscriptionData(); // Recargar datos
          } else {
            throw new Error(confirmResult.message || 'Error al confirmar el pago');
          }
        } else {
          throw new Error('El pago no se completó correctamente');
        }
      }
    } else {
      throw new Error(result.message || 'Error al reintentar el pago');
    }

  } catch (err: any) {
    console.error('Error al reintentar pago:', err);
    showToast(err.message || 'Error al reintentar el pago. Intenta actualizar tu método de pago.', 'error');
  } finally {
    isLoading.value = false;
  }
};

const handleDetailedStatusAction = (actionRequired: string) => {
  switch (actionRequired) {
    case 'add_payment_method':
    case 'update_payment_method':
    case 'authenticate_payment':
      openUpdatePaymentDialog();
      break;
    case 'retry_payment':
      retryPaymentAction();
      break;
    case 'reactivate_subscription':
      reactivateSubscriptionAction();
      break;
    case 'contact_support':
      // Abrir enlace de soporte o modal de contacto
      window.open('mailto:<EMAIL>', '_blank');
      break;
    default:
      showToast('Acción no implementada: ' + actionRequired, 'warning');
  }
};

// Manejar acciones de las notificaciones de pago
const handleNotificationAction = (actionType: string) => {

  switch (actionType) {
    case 'update_payment_method':
    case 'add_payment_method':
    case 'authenticate_payment':
      openUpdatePaymentDialog();
      break;
    case 'retry_payment':
      retryPaymentAction();
      break;
    case 'reactivate_subscription':
      reactivateSubscriptionAction();
      break;
    case 'contact_support':
      window.open('mailto:<EMAIL>?subject=Problema con suscripción', '_blank');
      break;
    case 'complete_setup':
      // Redirigir a la página de configuración o abrir modal de método de pago
      openUpdatePaymentDialog();
      break;
    case 'restart_subscription':
      // Podríamos implementar un flujo específico para reiniciar suscripciones
      showToast('Contacta con soporte para reiniciar tu suscripción', 'info');
      window.open('mailto:<EMAIL>?subject=Reiniciar suscripción', '_blank');
      break;
    default:
      console.warn('Acción de notificación no manejada:', actionType);
      showToast('Acción no disponible: ' + actionType, 'warning');
  }
};

</script>

<style scoped>

</style>
