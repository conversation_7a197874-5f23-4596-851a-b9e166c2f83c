<template>
  <div class="space-y-8 p-4 sm:p-6 md:p-8 bg-slate-50 min-h-screen">
    <!-- Diálogo de bienvenida/onboarding (se muestra solo en la primera visita) -->
    <WelcomeOnboardingDialog 
      :is-open="showWelcomeDialog" 
      :client-identifier="clientIdentifier || undefined"
      @close="closeWelcomeDialog"
      @dont-show-again="dontShowWelcomeDialogAgain"
    />

    <!-- ESTADO DE CARGA CON SKELETON -->
    <div v-if="isLoadingConfig || (isValoradorConfigured && isLoadingStats)">
      <DashboardHomeSkeleton />
    </div>

    <!-- Estado: Valorador NO Configurado -->
    <div v-else-if="!isValoradorConfigured" class="min-h-[calc(100vh-150px)] flex flex-col items-center justify-center text-center p-6 bg-white rounded-2xl shadow-xl border border-gray-200/80">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-impacto-orange mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        </svg>
        <h2 class="text-2xl sm:text-3xl font-bold text-impacto-blue mb-3">¡Bienvenido a tu Dashboard IA!</h2>
        <p class="text-md sm:text-lg text-gray-600 mb-8 max-w-xl">Para desbloquear todo el potencial de tu agente inmobiliario IA, el primer paso es configurar tu Valorador Inmobiliario personalizado.</p>
        <router-link :to="{ name: 'DashboardConfiguracion' }"
                     class="inline-flex items-center justify-center px-8 py-3.5 text-base font-semibold text-white bg-impacto-orange rounded-xl hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 transition-all duration-200 ease-in-out transform hover:scale-105 shadow-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Configurar mi valorador ahora
        </router-link>
        <p class="text-sm text-gray-500 mt-6">Una vez configurado, aquí verás todas tus estadísticas, leads y herramientas IA.</p>
    </div>
    
    <!-- Contenido Principal del Dashboard (Valorador Configurado) -->
    <div v-else>
      <!-- Estado: Cargando Estadísticas -->
      <div v-if="isLoadingStats" class="flex flex-col items-center justify-center min-h-[60vh] text-center">
        <svg class="animate-spin h-10 w-10 text-impacto-blue mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="text-md font-semibold text-gray-600">Cargando tus estadísticas actualizadas...</p>
      </div>

      <!-- Fila Superior: Tu Herramienta Clave y el Copiloto IA -->
      <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
        <!-- Bloque Principal: Tu Valorador Inmobiliario Personalizado -->
        <div class="lg:col-span-2 bg-impacto-blue text-white rounded-2xl shadow-xl p-6 flex flex-col justify-between">
          <div>
            <h2 class="text-2xl font-bold tracking-tight mb-1">Tu valorador inmobiliario</h2>
            <p class="text-sm text-blue-200 max-w-xl mb-6">
              Potencia la captación de leads con tu herramienta exclusiva.
            </p>

            <div class="grid sm:grid-cols-2 gap-4 mb-6">
              <div class="space-y-4">
                <div class="bg-white/10 p-4 rounded-xl flex items-start text-left min-h-[100px]">
                  <div class="p-3 bg-impacto-orange/80 rounded-lg mr-3.5 shrink-0 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p class="text-3xl font-bold text-white leading-tight">{{ valoradorStatsToday.todayValuations }}</p>
                    <p class="text-xs text-blue-100 mt-0.5">Emails enviados</p>
                    <p class="text-xs text-blue-200 opacity-80">Últimos 7 días</p>
                  </div>
                </div>

                <div class="bg-white/10 p-4 rounded-xl flex items-start text-left min-h-[100px]">
                  <div class="p-3 bg-impacto-orange/80 rounded-lg mr-3.5 shrink-0 mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <p class="text-3xl font-bold text-white leading-tight">{{ valoradorStatsToday.todayLeads }}</p>
                    <p class="text-xs text-blue-100 mt-0.5">Nuevos leads captados</p>
                    <p class="text-xs text-blue-200 opacity-80">Últimos 7 días</p>
                  </div>
                </div>
              </div>

              <div class="flex flex-col space-y-3">
                <button @click="copyValoradorUrl" class="w-full inline-flex items-center justify-center px-4 py-3 text-sm font-semibold text-impacto-blue bg-white rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-impacto-blue transition-all duration-200 ease-in-out transform hover:scale-105 shadow-md">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
                  Copiar URL
                </button>
                <router-link :to="{ name: 'DashboardConfiguracion' }" class="w-full inline-flex items-center justify-center px-4 py-3 text-sm font-semibold text-white bg-impacto-orange rounded-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 focus:ring-offset-impacto-blue transition-all duration-200 ease-in-out transform hover:scale-105 shadow-md">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                  Configurar valorador
                </router-link>
                <button @click="openIntegrationModal" class="w-full inline-flex items-center justify-center px-4 py-3 text-sm font-semibold text-white bg-white/20 rounded-lg hover:bg-white/30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-impacto-blue transition-all duration-200 ease-in-out transform hover:scale-105 shadow-md">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" /></svg>
                  Integración web
                </button>
              </div>
            </div>
          </div>
          
          <div class="bg-white/10 p-3.5 rounded-xl mt-auto">
            <h4 class="text-sm font-semibold text-white mb-1 flex items-center">
              <LightBulbIcon class="h-5 w-5 mr-2 text-impacto-orange" />
              Información útil
            </h4>
            <p class="text-xs text-blue-100 leading-relaxed min-h-[30px]">{{ dashboardInsightMessage }}</p>
          </div>
        </div>

        <!-- Bloque Secundario: Centro de actividad -->
        <div class="bg-white rounded-2xl shadow-xl p-6 border border-gray-200/50 flex flex-col">
          <h3 class="text-xl font-bold text-impacto-blue mb-1">Centro de actividad</h3>
          <p class="text-sm text-gray-600 mb-5">
            {{ hasAnyData ? 'Alertas y actividad reciente de tu sistema:' : 'Activa tu valorador para ver la actividad del sistema.'}}
          </p>

          <!-- Loading state -->
          <div v-if="isLoadingActivityCenter" class="flex-grow flex items-center justify-center py-8">
            <svg class="animate-spin h-8 w-8 text-impacto-orange" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <!-- Lista de Actividades -->
          <div v-else-if="hasAnyData && activityCenterData.activities && activityCenterData.activities.length > 0"
               class="flex-grow">
            <!-- Contenedor con scroll -->
            <div class="max-h-80 overflow-y-auto space-y-3 pr-2">
              <div v-for="activity in activityCenterData.activities" :key="activity.timestamp + activity.type"
                   class="p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors">
                <div class="flex items-start justify-between">
                  <div class="flex items-start flex-1">
                    <span class="text-lg mr-3 mt-0.5 flex-shrink-0">{{ activity.icon }}</span>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center justify-between mb-1">
                        <h5 class="font-medium text-gray-800 text-sm">{{ activity.title }}</h5>
                        <span class="text-xs text-gray-500 flex-shrink-0 ml-2">{{ activity.time }}</span>
                      </div>
                      <p class="text-xs text-gray-700 mb-1 truncate">{{ activity.description }}</p>
                      <p class="text-xs text-gray-500">{{ activity.details }}</p>
                    </div>
                  </div>
                  <router-link v-if="activity.action_url"
                              :to="activity.action_url"
                              class="ml-3 px-2 py-1 text-xs font-medium text-impacto-orange hover:text-white hover:bg-impacto-orange rounded transition-colors flex-shrink-0">
                    {{ activity.action_text }}
                  </router-link>
                </div>
              </div>
            </div>
          </div>

          <!-- Estado cuando no hay actividades -->
          <div v-else-if="hasAnyData && activityCenterData.activities && activityCenterData.activities.length === 0"
               class="flex-grow flex flex-col items-center justify-center text-center p-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-slate-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
              <path stroke-linecap="round" stroke-linejoin="round" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
            </svg>
            <h4 class="font-semibold text-slate-700">Todo tranquilo</h4>
            <p class="text-sm text-slate-500">
              No hay actividad reciente en las últimas 48 horas.
            </p>
          </div>
          <div v-else class="flex-grow flex flex-col items-center justify-center text-center p-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-slate-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                <path v-if="hasAnyData" stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                <path v-else stroke-linecap="round" stroke-linejoin="round" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
              <h4 class="font-semibold text-slate-700">Sin actividad aún</h4>
              <p class="text-sm text-slate-500">
                Comparte tu valorador para generar actividad y ver alertas inteligentes.
              </p>
          </div>
        </div>
      </div>

      <!-- Fila Inferior: Visión General del Rendimiento IA y Acciones Rápidas -->
      <div class="mt-10" v-if="!isLoadingStats">
        <div v-if="!hasAnyData" class="bg-white rounded-2xl shadow-xl p-8 border border-gray-200/50 text-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-impacto-blue mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 010 3.75H5.625a1.875 1.875 0 010-3.75z" />
          </svg>
          <h3 class="text-xl font-bold text-impacto-blue mb-3">Tu dashboard está casi listo</h3>
          <p class="text-gray-600 mb-6 max-w-lg mx-auto">Una vez que tu valorador comience a recibir interacciones, aquí verás un resumen detallado del rendimiento de tus leads, emails y valoraciones.</p>
          <div class="flex flex-wrap justify-center gap-4">
            <button @click="copyValoradorUrl" class="inline-flex items-center px-6 py-3 text-sm font-semibold text-white bg-impacto-orange rounded-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 shadow-md">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
              Copiar URL para compartir
            </button>
          </div>
        </div>
        
        <div v-else>
          <h3 class="text-xl font-bold text-impacto-blue mb-6 flex items-center capitalize-first">
            <ChartBarSquareIcon class="h-6 w-6 mr-2.5 text-impacto-orange" />
            Visión general del rendimiento
          </h3>

          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            <!-- Tarjeta: Rendimiento de Leads -->
            <div class="bg-white rounded-2xl shadow-xl p-6 border border-gray-200/50 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group flex flex-col">
              <div class="flex items-center justify-between mb-3">
                <h4 class="text-lg font-semibold text-gray-700 group-hover:text-impacto-blue transition-colors duration-300 capitalize-first">Leads (últimos 30 días)</h4>
                <div class="p-2.5 bg-blue-100 group-hover:bg-impacto-orange/10 rounded-lg transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-blue group-hover:text-impacto-orange transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
              <p class="text-3xl font-extrabold text-impacto-blue group-hover:text-impacto-orange transition-colors duration-300 mb-1">{{ dashboardMainStats.leads_ultimos_30_dias.value }}</p>
              <div v-if="dashboardMainStats.leads_ultimos_30_dias.changePercent !== null" 
                   :class="getChangeIndicatorClasses(dashboardMainStats.leads_ultimos_30_dias.changePercent).textClass"
                   class="text-xs font-semibold flex items-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" :d="getChangeIndicatorClasses(dashboardMainStats.leads_ultimos_30_dias.changePercent).iconPath" />
                </svg>
                {{ dashboardMainStats.leads_ultimos_30_dias.changePercent > 0 ? '+' : '' }}{{ dashboardMainStats.leads_ultimos_30_dias.changePercent }}% vs. 30 días previos
              </div>
              <p v-else class="text-xs text-gray-400 mb-3 h-[18px]">&nbsp;</p> <!-- Placeholder para mantener altura -->
              
              <div class="h-16 mb-5 bg-slate-50 rounded-lg flex items-center justify-center p-2">
                 <svg class="w-full h-full text-gray-300 group-hover:text-impacto-orange/50 transition-colors duration-300" viewBox="0 0 100 30" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
                   <path d="M0 15 L20 18 L40 12 L60 16 L80 14 L100 17" stroke="currentColor" stroke-width="2"/>
                 </svg>
              </div>

              <div class="text-xs text-gray-600 space-y-1 mb-5">
                <p><span class="font-semibold text-gray-700">{{ dashboardMainStats.leads_en_nutricion_activa }}</span> en nutrición activa</p>
              </div>
              <router-link :to="{ name: 'Leads' }" class="mt-auto w-full text-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-lg text-white bg-impacto-blue hover:bg-impacto-blue-dark group-hover:bg-impacto-orange transition-colors duration-300 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-impacto-blue focus:ring-offset-2">
                Gestionar leads
              </router-link>
            </div>

            <!-- Tarjeta: Rendimiento de Emails -->
            <div class="bg-white rounded-2xl shadow-xl p-6 border border-gray-200/50 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group flex flex-col">
              <div class="flex items-center justify-between mb-3">
                <h4 class="text-lg font-semibold text-gray-700 group-hover:text-impacto-blue transition-colors duration-300 capitalize-first">Emails (últimos 30 días)</h4>
                <div class="p-2.5 bg-blue-100 group-hover:bg-impacto-orange/10 rounded-lg transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-blue group-hover:text-impacto-orange transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <p class="text-3xl font-extrabold text-impacto-blue group-hover:text-impacto-orange transition-colors duration-300 mb-1">{{ dashboardMainStats.emails_enviados_ultimos_30_dias.value }}</p>
              <div v-if="dashboardMainStats.emails_enviados_ultimos_30_dias.changePercent !== null" 
                   :class="getChangeIndicatorClasses(dashboardMainStats.emails_enviados_ultimos_30_dias.changePercent).textClass"
                   class="text-xs font-semibold flex items-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" :d="getChangeIndicatorClasses(dashboardMainStats.emails_enviados_ultimos_30_dias.changePercent).iconPath" />
                </svg>
                {{ dashboardMainStats.emails_enviados_ultimos_30_dias.changePercent > 0 ? '+' : '' }}{{ dashboardMainStats.emails_enviados_ultimos_30_dias.changePercent }}% vs. 30 días previos
              </div>
              <p v-else class="text-xs text-gray-400 mb-3 h-[18px]">&nbsp;</p> <!-- Placeholder para mantener altura -->
              
              <div class="h-16 mb-5 bg-slate-50 rounded-lg flex items-center justify-center p-2">
                 <svg class="w-full h-full text-gray-300 group-hover:text-impacto-orange/50 transition-colors duration-300" viewBox="0 0 100 30" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
                   <path d="M0 15 L20 12 L40 18 L60 14 L80 17 L100 13" stroke="currentColor" stroke-width="2"/>
                 </svg>
              </div>

              <!-- Datos adicionales de emails eliminados - solo datos reales -->
              <router-link :to="{ name: 'Emails' }" class="mt-auto w-full text-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-lg text-white bg-impacto-blue hover:bg-impacto-blue-dark group-hover:bg-impacto-orange transition-colors duration-300 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-impacto-blue focus:ring-offset-2">
                Ver estrategia de emails
              </router-link>
            </div>
            
            <!-- Tarjeta: Rendimiento de Valoraciones -->
            <div class="bg-white rounded-2xl shadow-xl p-6 border border-gray-200/50 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group flex flex-col">
              <div class="flex items-center justify-between mb-3">
                <h4 class="text-lg font-semibold text-gray-700 group-hover:text-impacto-blue transition-colors duration-300 capitalize-first">Valoraciones (últimos 30 días)</h4>
                <div class="p-2.5 bg-blue-100 group-hover:bg-impacto-orange/10 rounded-lg transition-colors duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-blue group-hover:text-impacto-orange transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
              <p class="text-3xl font-extrabold text-impacto-blue group-hover:text-impacto-orange transition-colors duration-300 mb-1">{{ dashboardMainStats.valoraciones_ultimos_30_dias.value }}</p>
              <div v-if="dashboardMainStats.valoraciones_ultimos_30_dias.changePercent !== null" 
                   :class="getChangeIndicatorClasses(dashboardMainStats.valoraciones_ultimos_30_dias.changePercent).textClass"
                   class="text-xs font-semibold flex items-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" :d="getChangeIndicatorClasses(dashboardMainStats.valoraciones_ultimos_30_dias.changePercent).iconPath" />
                </svg>
                {{ dashboardMainStats.valoraciones_ultimos_30_dias.changePercent > 0 ? '+' : '' }}{{ dashboardMainStats.valoraciones_ultimos_30_dias.changePercent }}% vs. 30 días previos
              </div>
              <p v-else class="text-xs text-gray-400 mb-3 h-[18px]">&nbsp;</p> <!-- Placeholder para mantener altura -->

              <div class="h-16 mb-5 bg-slate-50 rounded-lg flex items-center justify-center p-2">
                 <svg class="w-full h-full text-gray-300 group-hover:text-impacto-orange/50 transition-colors duration-300" viewBox="0 0 100 30" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
                   <path d="M0 12 L20 15 L40 10 L60 18 L80 15 L100 20" stroke="currentColor" stroke-width="2"/>
                 </svg>
              </div>

              <!-- Datos adicionales de valoraciones eliminados - solo datos reales -->
              <router-link :to="{ name: 'Valoraciones' }" class="mt-auto w-full text-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-lg text-white bg-impacto-blue hover:bg-impacto-blue-dark group-hover:bg-impacto-orange transition-colors duration-300 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-impacto-blue focus:ring-offset-2">
                Analizar valoraciones
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <IntegrationModal 
      :is-open="showIntegrationModal" 
      :client-identifier="clientIdentifier"
      :api-key="apiKey" 
      @close="closeIntegrationModal" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  LightBulbIcon,
  ChartBarSquareIcon,
} from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import WelcomeOnboardingDialog from '@/components/dashboard/WelcomeOnboardingDialog.vue'
import DashboardHomeSkeleton from '@/components/dashboard/skeletons/DashboardHomeSkeleton.vue'
import { apiFetch } from '@/utils/apiFetch'
import IntegrationModal from '@/components/dashboard/IntegrationModal.vue'
import { useToast } from '@/composables/useToast'

const authStore = useAuthStore()
const { showToast } = useToast()
const route = useRoute()

const clientIdentifier = ref<string | null>(null)
const apiKey = ref<string | null>(null)
const isLoadingConfig = ref<boolean>(true) // Para la carga inicial de config del valorador
const isLoadingStats = ref<boolean>(false) // Para la carga de estadísticas después de config
const valoradorConfigError = ref<string | null>(null)



const showIntegrationModal = ref(false)

const isValoradorConfigured = computed(() => !!clientIdentifier.value)

const valoracionesCount = ref(0)
const leadsCount = ref(0)

// Stats para las tarjetas de "Visión General del Rendimiento"
const dashboardMainStats = ref({
  leads_ultimos_30_dias: { value: 0, changePercent: null as number | null },
  emails_enviados_ultimos_30_dias: { value: 0, changePercent: null as number | null },
  valoraciones_ultimos_30_dias: { value: 0, changePercent: null as number | null },
  leads_en_nutricion_activa: 0, // Se mantiene como un número simple
})

// Stats específicas del valorador para los bloques superiores (últimos 7 días)
const valoradorStatsToday = ref({
  todayValuations: 0,
  todayLeads: 0,
})

const hasAnyData = computed(() => {
  return valoracionesCount.value > 0 || leadsCount.value > 0 || valoradorStatsToday.value.todayValuations > 0 || valoradorStatsToday.value.todayLeads > 0
})

const dashboardInsightMessage = ref<string>('Sugerencia IA por defecto mientras carga...')

const openIntegrationModal = () => { showIntegrationModal.value = true }
const closeIntegrationModal = () => { showIntegrationModal.value = false }

const valoradorPublicUrl = computed(() => {
  if (clientIdentifier.value) {
    // Siempre usar la URL de producción ya que el valorador está desplegado
    return `https://${clientIdentifier.value}.inmoautomation.com/valora/`
  }
  return ''
})

const fetchValoradorConfig = async () => {
  isLoadingConfig.value = true
  valoradorConfigError.value = null
  clientIdentifier.value = null // Resetear antes de cargar
  apiKey.value = null

  try {
    const data = await apiFetch('valorador-config.php')
    if (data.success) {
      if (data.config && data.config.client_identifier) {
        // Valorador configurado
        clientIdentifier.value = data.config.client_identifier
        apiKey.value = data.config.uuid || data.config.api_key || null
      } else {
        // Valorador no configurado
        console.info('Valorador aún no configurado por el usuario.')
      }
    } else {
      throw new Error(data.message || 'No se pudo obtener la configuración del valorador.')
    }
  } catch (error: any) {
    console.error('Error fetching valorador config:', error)
    const errorMessage = error.message || 'Ocurrió un error al cargar la configuración.'
    valoradorConfigError.value = errorMessage
    showToast(errorMessage, 'error')
  } finally {
    isLoadingConfig.value = false
  }
}

const showWelcomeDialog = ref(false)

const checkFirstVisit = () => {
  const isFirstVisit = route.query.firstVisit === 'true'
  const hasSeenWelcome = localStorage.getItem('hasSeenWelcomeDialog') === 'true'
  if (isFirstVisit && !hasSeenWelcome) showWelcomeDialog.value = true
}

const closeWelcomeDialog = () => { showWelcomeDialog.value = false }
const dontShowWelcomeDialogAgain = () => {
  localStorage.setItem('hasSeenWelcomeDialog', 'true')
  closeWelcomeDialog()
}



onMounted(async () => {
  isLoadingConfig.value = true
  await fetchValoradorConfig()
  // MODIFICADO: Cargar estadísticas siempre para mostrar datos históricos
  await fetchDashboardStats()
  await fetchActivityCenterData()
  if (isValoradorConfigured.value) {
    await fetchDashboardInsight()
  }
  checkFirstVisit()
})

const copyValoradorUrl = async () => {
  if (!valoradorPublicUrl.value) {
    showToast('URL del valorador no disponible. Configura tu valorador primero.', 'error')
    return
  }
  try {
    await navigator.clipboard.writeText(valoradorPublicUrl.value)
    showToast('¡URL del Valorador copiada!', 'success')
  } catch (err) {
    showToast('No se pudo copiar la URL.', 'error')
  }
}

// Centro de Actividad - Interfaces
interface Activity {
  type: 'new_lead' | 'email_sent' | 'email_scheduled' | 'email_opened';
  icon: string;
  title: string;
  description: string;
  details: string;
  time: string;
  action_text: string;
  action_url: string;
  timestamp: string;
}

interface ActivityCenterData {
  activities: Activity[];
}

const isLoadingActivityCenter = ref(true);
const activityCenterData = ref<ActivityCenterData>({
  activities: []
});

// Variable iaPriorityActions eliminada - ahora usamos activityCenterData

// Datos simulados eliminados - ahora usamos solo datos reales

const fetchDashboardStats = async () => {
  if (!authStore.token) {
    isLoadingStats.value = false
    return
  }
  // MODIFICADO: Permitir carga de estadísticas incluso sin valorador configurado
  // para mostrar datos históricos
  isLoadingStats.value = true
  try {
    const data = await apiFetch('get_dashboard_stats.php')
    if (data.success && data.stats) {
      valoracionesCount.value = data.stats.valoraciones_count || 0
      leadsCount.value = data.stats.leads_count || 0

      valoradorStatsToday.value.todayValuations = data.stats.emails_enviados_ultimos_7_dias || 0
      valoradorStatsToday.value.todayLeads = data.stats.leads_ultimos_7_dias || 0

      dashboardMainStats.value.leads_ultimos_30_dias = data.stats.leads_ultimos_30_dias || { value: 0, changePercent: null }
      dashboardMainStats.value.emails_enviados_ultimos_30_dias = data.stats.emails_enviados_ultimos_30_dias || { value: 0, changePercent: null }
      dashboardMainStats.value.valoraciones_ultimos_30_dias = data.stats.valoraciones_ultimos_30_dias || { value: 0, changePercent: null }
      dashboardMainStats.value.leads_en_nutricion_activa = data.stats.leads_en_nutricion_activa || 0
    } else {
      showToast(data.message || 'Error al procesar estadísticas del dashboard.', 'warning')
    }
  } catch (e: any) {
    showToast(`Error cargando estadísticas: ${e.message || 'Desconocido'}`, 'error')
  } finally {
    isLoadingStats.value = false
  }
}

const fetchActivityCenterData = async () => {
  if (!authStore.token) {
    isLoadingActivityCenter.value = false
    return
  }

  try {
    const data = await apiFetch('get_activity_center_data.php')
    if (data.success) {
      activityCenterData.value = data.data
    } else {
      console.error('Error en la respuesta:', data.message)
    }
  } catch (error) {
    console.error('Error al cargar datos del centro de actividad:', error)
  } finally {
    isLoadingActivityCenter.value = false
  }
}

const fetchDashboardInsight = async () => {
  dashboardInsightMessage.value = "Buscando una nueva sugerencia para ti..."
  try {
    const data = await apiFetch('get_dashboard_insights.php')
    if (data.success && data.insight) {
      dashboardInsightMessage.value = data.insight
    } else {
      // Si la API no devuelve un insight (éxito pero vacío), usar un fallback.
      dashboardInsightMessage.value = "Consejo del día: ¡Comparte tu valorador en redes sociales para conseguir más leads!"
    }
  } catch (error: any) {
    console.error('Error en fetchDashboardInsight:', error)
    // En caso de error de red o de la API, usar un fallback y loguear.
    dashboardInsightMessage.value = "Consejo del día: Revisa la configuración de tus campañas de email para optimizar la apertura."
    showToast('No se pudo cargar una nueva sugerencia IA.', 'info')
  }
}

const getChangeIndicatorClasses = (changePercent: number | null) => {
  if (changePercent === null || changePercent === 0) {
    return { textClass: 'text-gray-500', iconPath: 'M5 12h14' } // Neutral icon (línea horizontal)
  }
  if (changePercent > 0) {
    return { textClass: 'text-green-600', iconPath: 'M5 10l7-7m0 0l7 7m-7-7v18' } // Flecha arriba
  } else {
    return { textClass: 'text-red-600', iconPath: 'M19 14l-7 7m0 0l-7-7m7 7V3' } // Flecha abajo
  }
}

// @ts-ignore
const isOwner = computed(() => authStore.isAgencyOwner)

</script>

<style scoped>
/* Estilos existentes */
input[type="text"]::placeholder { color: #a8b2d3; opacity: 0.8; }
input[type="text"] { min-width: 0; }
</style>
