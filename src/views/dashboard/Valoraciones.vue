<template>
  <div class="p-4 sm:p-6 md:p-8 bg-slate-50 min-h-screen">
    <!-- Encabezado con título y botones de acción -->
    <header class="mb-6 md:mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
      <!-- Tí<PERSON><PERSON> y descripción -->
      <div>
        <h1 class="text-3xl font-bold text-impacto-blue">Mis Valoraciones</h1>
        <p class="text-gray-600 mt-1">
          Gestiona y analiza todas las valoraciones inmobiliarias generadas.
        </p>
      </div>
      <!-- Botones de acción -->
      <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-3">
        <button @click="exportarTodas" 
                :disabled="isLoading || !!error || valoracionesFiltradas.length === 0"
                class="px-5 py-2.5 text-sm font-medium text-white bg-impacto-blue rounded-lg hover:bg-impacto-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Exportar listado
        </button>
        <button @click="mostrarEstadisticas = !mostrarEstadisticas" 
                :disabled="isLoading || !!error || valoraciones.length === 0" 
                class="px-5 py-2.5 text-sm font-medium text-impacto-blue bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          {{ mostrarEstadisticas ? 'Ocultar estadísticas' : 'Ver estadísticas' }}
        </button>
      </div>
    </header>

    <!-- Componente de estadísticas (colapsable) -->
    <ValoracionesEstadisticas 
      v-if="mostrarEstadisticas && !isLoading && !error && valoraciones.length > 0" 
      :estadisticas="estadisticasCalculadas"
      class="mb-6 md:mb-8"
    />

    <!-- Componente de filtros -->
    <ValoracionesFiltros 
      @filtrar="aplicarFiltros"
      class="mb-6 md:mb-8"
    />

    <!-- Estados de carga y error -->
    <div v-if="isLoading" class="text-center py-20 bg-white rounded-xl shadow">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-impacto-orange mx-auto"></div>
      <p class="mt-4 text-lg font-semibold text-gray-700">Cargando valoraciones...</p>
    </div>

    <div v-else-if="error" class="bg-red-50 border-l-4 border-red-600 text-red-800 p-6 rounded-xl shadow">
      <div class="flex items-center">
        <svg class="fill-current h-7 w-7 text-red-500 mr-3 shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-10a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm0 6a1 1 0 110-2 1 1 0 010 2z"/></svg>
        <div>
          <p class="font-bold text-lg">Error al cargar las valoraciones</p>
          <p class="text-sm mt-1">{{ error }}</p>
          <button @click="fetchValoraciones" class="mt-3 px-3 py-1.5 text-xs bg-red-700 text-white rounded-md hover:bg-red-800">Reintentar</button>
        </div>
      </div>
    </div>

    <div v-else-if="valoraciones.length === 0 && !Object.values(filtrosActuales).some(f => f && String(f).length > 0)" class="p-8 rounded-xl shadow bg-white text-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="text-xl font-semibold mb-2 text-impacto-blue">Aún no hay valoraciones</h3>
      <p class="text-gray-600 max-w-md mx-auto mb-6">
        Cuando se generen valoraciones, aparecerán aquí. Puedes <router-link :to="{ name: 'DashboardConfiguracion' }" class="font-medium text-impacto-orange hover:underline">configurar tu valorador</router-link> si aún no lo has hecho.
      </p>
       <button @click="() => { router.push({ name: 'DashboardConfiguracion' }) }" class="inline-flex items-center px-6 py-3 text-sm font-semibold text-white bg-impacto-orange rounded-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 shadow-md">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
          Ir a Configuración
        </button>
    </div>
     <div v-else-if="valoracionesFiltradas.length === 0" class="p-8 rounded-xl shadow bg-white text-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
        <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
      <h3 class="text-xl font-semibold mb-2 text-impacto-blue">Ninguna valoración coincide con tus filtros</h3>
      <p class="text-gray-600 max-w-md mx-auto">Intenta ajustar o limpiar los filtros para ver resultados.</p>
    </div>
    
    <!-- Tabla de valoraciones -->
    <ValoracionesTabla 
      v-if="!isLoading && !error && valoracionesFiltradas.length > 0"
      :valoraciones="paginaActualItems"
      :pagina-actual="paginaActual"
      :por-pagina="porPagina"
      :total-items="valoracionesFiltradas.length"
      :orden-actual="ordenActual" 
      @ver="verDetalle"
      @exportar="exportarValoracionIndividual"
      @ordenar="ordenarValoraciones"
      @pagina="cambiarPagina"
    />

    <!-- Modal de detalle de valoración -->
    <Teleport to="body">
      <div v-if="mostrarDetalle" @click.self="mostrarDetalle = false" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-[100]">
        <div class="bg-slate-50 rounded-xl shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-hidden flex flex-col">
          <div class="flex justify-between items-center p-4 border-b border-gray-200 bg-white sticky top-0 z-10 rounded-t-xl">
            <h2 class="text-xl font-bold text-impacto-blue">Detalle de la Valoración</h2>
            <button @click="mostrarDetalle = false" class="text-gray-400 hover:text-red-600 p-1 rounded-full hover:bg-red-100 focus:outline-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="overflow-y-auto flex-grow">
            <ValoracionDetalle 
              v-if="valoracionSeleccionada" 
              :valoracion="valoracionSeleccionada" 
              :lead="getLeadForValoracion(valoracionSeleccionada.id, valoracionSeleccionada.lead_id)" 
              @close="mostrarDetalle = false"
              @export="exportarValoracionIndividual"
              @verLead="verDetalleLead(valoracionSeleccionada?.lead_id)"
              @update:notasAgente="handleUpdateNotasAgente"
            />
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watchEffect } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useRouter, useRoute } from 'vue-router';
import { useToast } from '@/composables/useToast';
import ValoracionesFiltros from '@/components/dashboard/valoraciones/ValoracionesFiltros.vue';
import ValoracionesEstadisticas from '@/components/dashboard/valoraciones/ValoracionesEstadisticas.vue';
import ValoracionesTabla from '@/components/dashboard/valoraciones/ValoracionesTabla.vue';
import ValoracionDetalle from '@/components/dashboard/valoraciones/ValoracionDetalle.vue';
import { apiFetch } from '@/utils/apiFetch';

// Definir interfaces
interface Lead {
  id: number;
  uuid: string;
  nombre: string;
  email: string | null;
  telefono?: string | null;
  necesidad?: string | null;
  fecha_creacion: string;
}

interface Valoracion {
  id: number;
  uuid: string;
  cliente_valorador_id: number;
  client_identifier?: string;
  lead_id?: number | undefined;
  referencia_catastral?: string | null;
  direccion: string;
  latitud?: number | null;
  longitud?: number | null;
  tipo_principal: string;
  subtipo?: string | null;
  superficie: number;
  superficie_parcela?: number | null;
  habitaciones?: number | null;
  banos?: number | null;
  estado?: string | null;
  planta?: string | null;
  extras?: string | null;
  valor_estimado_min: number;
  valor_estimado_max: number;
  notas_agente?: string | null;
  // Nuevos campos de datos enriquecidos
  ano_construccion_catastro?: number | null;
  precio_m2_promedio?: number | null;
  tamano_promedio?: number | null;
  porcentaje_con_piscina?: number | null;
  porcentaje_con_parking?: number | null;
  porcentaje_con_ascensor?: number | null;
  porcentaje_con_terraza?: number | null;
  porcentaje_reformadas?: number | null;
  numero_propiedades_analizadas?: number | null;
  distancia_al_centroide_km?: number | null;
  precio_vs_zona_porcentaje?: number | null;
  tamano_vs_zona_porcentaje?: number | null;
  fecha_creacion: string;
  fecha_modificacion?: string | null | undefined;
  lead_uuid?: string | null;
  lead_nombre?: string | null;
  lead_email?: string | null;
  lead_telefono?: string | null;
  lead_necesidad?: string | null;
  lead_notas?: string | null;
  lead_fecha_creacion?: string | null;
}

interface Filtros {
  direccion: string;
  tipo_principal: string;
  periodo: string;
  fechaDesde: string | null;
  superficie_min: number | null;
  superficie_max: number | null;
  habitaciones_min: number | null;
  habitaciones_max: number | null;
  banos_min: number | null;
  banos_max: number | null;
  valor_min: number | null;
  valor_max: number | null;
  estado: string;
  planta: string;
  referencia_catastral: string;
}

interface Orden {
  campo: keyof Valoracion | 'lead_nombre'; 
  direccion: 'asc' | 'desc';
}

const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();
const { showToast } = useToast();
const valoraciones = ref<Valoracion[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const mostrarEstadisticas = ref(true);
const mostrarDetalle = ref(false);
const valoracionSeleccionada = ref<Valoracion | null>(null);

const filtrosActuales = ref<Filtros>({
  direccion: '',
  tipo_principal: '',
  periodo: '',
  fechaDesde: null,
  superficie_min: null,
  superficie_max: null,
  habitaciones_min: null,
  habitaciones_max: null,
  banos_min: null,
  banos_max: null,
  valor_min: null,
  valor_max: null,
  estado: '',
  planta: '',
  referencia_catastral: ''
});

const ordenActual = ref<Orden>({
  campo: 'fecha_creacion',
  direccion: 'desc'
});

const paginaActual = ref(1);
const porPagina = ref(10);

const valoracionesFiltradas = computed(() => {
  let items = [...valoraciones.value];

  if (filtrosActuales.value.direccion) {
    items = items.filter(v => v.direccion.toLowerCase().includes(filtrosActuales.value.direccion.toLowerCase()));
  }
  if (filtrosActuales.value.tipo_principal) {
    items = items.filter(v => v.tipo_principal === filtrosActuales.value.tipo_principal);
  }
  if (filtrosActuales.value.fechaDesde) {
    const fechaDesdeFilter = new Date(filtrosActuales.value.fechaDesde);
    items = items.filter(v => new Date(v.fecha_creacion) >= fechaDesdeFilter);
  }
  if (filtrosActuales.value.superficie_min !== null) {
    items = items.filter(v => v.superficie >= filtrosActuales.value.superficie_min!);
  }
  if (filtrosActuales.value.superficie_max !== null) {
    items = items.filter(v => v.superficie <= filtrosActuales.value.superficie_max!);
  }
  if (filtrosActuales.value.habitaciones_min !== null) {
    items = items.filter(v => v.habitaciones !== undefined && v.habitaciones !== null && v.habitaciones >= filtrosActuales.value.habitaciones_min!);
  }
  if (filtrosActuales.value.habitaciones_max !== null) {
    items = items.filter(v => v.habitaciones !== undefined && v.habitaciones !== null && v.habitaciones <= filtrosActuales.value.habitaciones_max!);
  }
  if (filtrosActuales.value.banos_min !== null) {
    items = items.filter(v => v.banos !== undefined && v.banos !== null && v.banos >= filtrosActuales.value.banos_min!);
  }
  if (filtrosActuales.value.banos_max !== null) {
    items = items.filter(v => v.banos !== undefined && v.banos !== null && v.banos <= filtrosActuales.value.banos_max!);
  }
  if (filtrosActuales.value.valor_min !== null) {
    items = items.filter(v => v.valor_estimado_min >= filtrosActuales.value.valor_min!);
  }
  if (filtrosActuales.value.valor_max !== null) {
    items = items.filter(v => v.valor_estimado_max <= filtrosActuales.value.valor_max!);
  }
  if (filtrosActuales.value.estado) {
    items = items.filter(v => v.estado === filtrosActuales.value.estado);
  }
  if (filtrosActuales.value.planta) {
    items = items.filter(v => v.planta === filtrosActuales.value.planta);
  }
  if (filtrosActuales.value.referencia_catastral) {
    items = items.filter(v => v.referencia_catastral && v.referencia_catastral.toLowerCase().includes(filtrosActuales.value.referencia_catastral.toLowerCase()));
  }

  return items.sort((a, b) => {
    const campo = ordenActual.value.campo;
    const direccion = ordenActual.value.direccion === 'asc' ? 1 : -1;
    
    let aVal: string | number | null | undefined;
    let bVal: string | number | null | undefined;

    if (campo === 'lead_nombre') {
      aVal = a.lead_nombre;
      bVal = b.lead_nombre;
    } else {
      aVal = a[campo as keyof Valoracion];
      bVal = b[campo as keyof Valoracion];
    }

    if (typeof aVal === 'number' && typeof bVal === 'number') {
      return (aVal - bVal) * direccion;
    }
    if (campo === 'fecha_creacion' || campo === 'fecha_modificacion') {
      const dateA = aVal ? new Date(aVal as string).getTime() : 0;
      const dateB = bVal ? new Date(bVal as string).getTime() : 0;
      return (dateA - dateB) * direccion;
    }
    return String(aVal ?? '').localeCompare(String(bVal ?? '')) * direccion;
  });
});

const paginaActualItems = computed(() => {
  const inicio = (paginaActual.value - 1) * porPagina.value;
  const fin = inicio + porPagina.value;
  return valoracionesFiltradas.value.slice(inicio, fin);
});

const estadisticasCalculadas = computed(() => {
  if (valoraciones.value.length === 0) {
    return {
      total: 0, totalEsteMes: 0, incremento: 0, valorMedio: 0, valorMinimo: 0, valorMaximo: 0,
      superficieMedia: 0, tipoMasComun: null, 
      zonaPrincipal: null,
      valoracionesUltimoMesPorDia: [],
      todasValoraciones: []
    };
  }
  const total = valoraciones.value.length;
  const valorMedio = valoraciones.value.reduce((sum, v) => sum + ((v.valor_estimado_min + v.valor_estimado_max) / 2), 0) / (total || 1);
  const valorMinimo = Math.min(...valoraciones.value.map(v => v.valor_estimado_min));
  const valorMaximo = Math.max(...valoraciones.value.map(v => v.valor_estimado_max));
  const superficieMedia = Math.round(valoraciones.value.reduce((sum, v) => sum + v.superficie, 0) / (total || 1));
  const tipoMasComunData = obtenerTipoMasComun(valoraciones.value);
  const incremento = calcularIncrementoMensual(valoraciones.value);
  const valoracionesUltimoMesPorDia = calcularValoracionesUltimoMesPorDia(valoraciones.value);
  const todasValoracionesParaMapa = valoraciones.value.map(v => ({
    id: v.id,
    direccion: v.direccion,
    latitud: v.latitud,
    longitud: v.longitud,
    valor_estimado_min: v.valor_estimado_min,
    valor_estimado_max: v.valor_estimado_max
  }));
  
  const hoy = new Date();
  const mesActual = hoy.getMonth();
  const anoActual = hoy.getFullYear();
  const totalEsteMes = valoraciones.value.filter(v => {
    const fechaV = new Date(v.fecha_creacion);
    return fechaV.getMonth() === mesActual && fechaV.getFullYear() === anoActual;
  }).length;

  // Lógica para Zona Principal (simplificada por ahora)
  let zonaPrincipalData: { nombre: string; cantidad: number } | null = null;
  if (valoraciones.value.length > 0) {
    const localidades = valoraciones.value.map(v => {
      // Intenta extraer una localidad de la dirección. Esto es muy básico.
      // Una mejor solución necesitaría geocodificación inversa o un campo de localidad dedicado.
      const partesDireccion = v.direccion.split(',');
      return partesDireccion.length > 1 ? partesDireccion[partesDireccion.length - 2].trim() : v.direccion; 
    });
    
    const conteoLocalidades: Record<string, number> = {};
    localidades.forEach(loc => {
      conteoLocalidades[loc] = (conteoLocalidades[loc] || 0) + 1;
    });
    
    if (Object.keys(conteoLocalidades).length > 0) {
      const zonaMasComun = Object.keys(conteoLocalidades).reduce((a, b) => conteoLocalidades[a] > conteoLocalidades[b] ? a : b);
      zonaPrincipalData = {
        nombre: zonaMasComun,
        cantidad: conteoLocalidades[zonaMasComun]
      };
    } else {
      zonaPrincipalData = { nombre: 'Varias ubicaciones', cantidad: valoraciones.value.length };
    }
  }

  return {
    total,
    totalEsteMes,
    incremento,
    valorMedio,
    valorMinimo,
    valorMaximo,
    superficieMedia,
    tipoMasComun: tipoMasComunData,
    zonaPrincipal: zonaPrincipalData,
    valoracionesUltimoMesPorDia,
    todasValoraciones: todasValoracionesParaMapa
  };
});

const fetchValoraciones = async () => {
  if (!authStore.token) {
    error.value = "Usuario no autenticado.";
    isLoading.value = false;
    return;
  }
  isLoading.value = true;
  error.value = null;
  try {
    const data = await apiFetch('get_valoraciones.php');
    if (data.success) {
      valoraciones.value = data.valoraciones.map((v: any) => ({
        ...v,
        extras: v.extras ? JSON.parse(v.extras) : []
      }));
    } else {
      throw new Error(data.message || 'Error al obtener las valoraciones del servidor.');
    }
  } catch (err: any) {
    console.error("Error al obtener valoraciones:", err);
    error.value = err.message || 'No se pudieron cargar las valoraciones.';
    if (error.value) {
      showToast(error.value, 'error');
    }
  } finally {
    isLoading.value = false;
  }
};

const getLeadForValoracion = (valoracionId: number, leadId?: number | undefined): Lead | null => {
  if (!leadId) return null;
  
  const valoracionActual = valoraciones.value.find(v => v.id === valoracionId);

  if (valoracionActual && valoracionActual.lead_id === leadId && valoracionActual.lead_nombre) {
      return {
          id: leadId,
          uuid: valoracionActual.lead_uuid || 'temp-uuid-' + leadId,
          nombre: valoracionActual.lead_nombre,
          email: valoracionActual.lead_email || null,
          telefono: valoracionActual.lead_telefono || undefined,
          necesidad: valoracionActual.lead_necesidad || 'No especificada',
          fecha_creacion: valoracionActual.lead_fecha_creacion || valoracionActual.fecha_creacion,
      } as Lead;
  }
  return null;
};

const verDetalleLead = (leadId: number | undefined) => {
  if (leadId === undefined || leadId === null) { 
    showToast('ID de lead no disponible.', 'warning');
    return;
  }
  router.push({ name: 'Leads', query: { openLeadId: leadId.toString() } });
  mostrarDetalle.value = false; 
};

const aplicarFiltros = (nuevosFiltros: Filtros) => {
  filtrosActuales.value = nuevosFiltros;
  paginaActual.value = 1;
};

const ordenarValoraciones = (orden: Orden) => {
  ordenActual.value = orden;
};

const cambiarPagina = (pagina: number) => {
  paginaActual.value = pagina;
};

const verDetalle = (valoracion: Valoracion) => {
  valoracionSeleccionada.value = valoracion;
  mostrarDetalle.value = true;
};

const exportarCSV = (data: any[], filename: string) => {
  if (data.length === 0) {
    showToast('No hay datos para exportar.', 'info');
    return;
  }
  const headers = Object.keys(data[0]).join(',');
  const rows = data.map(obj => Object.values(obj).map(val => `"${String(val ?? '').replace(/"/g, '""')}"`).join(',')).join('\n');
  const csv = `${headers}\n${rows}`;
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  showToast('Datos exportados a CSV.', 'success');
};

const exportarValoracionIndividual = (valoracion: Valoracion) => {
  if (!valoracion) return;
  const datosExportacion = {
    id: valoracion.id,
    uuid: valoracion.uuid,
    direccion: valoracion.direccion,
    ref_catastral: valoracion.referencia_catastral,
    tipo: valoracion.tipo_principal,
    subtipo: valoracion.subtipo,
    superficie_m2: valoracion.superficie,
    valor_estimado_min_eur: valoracion.valor_estimado_min,
    valor_estimado_max_eur: valoracion.valor_estimado_max,
    fecha_creacion: valoracion.fecha_creacion,
    lead_id: valoracion.lead_id,
    lead_nombre: valoracion.lead_nombre,
    lead_email: valoracion.lead_email
  };
  exportarCSV([datosExportacion], `valoracion_${valoracion.uuid}.csv`);
};

const exportarTodas = () => {
  const datos = valoracionesFiltradas.value.map(v => ({
    id: v.id, uuid: v.uuid, direccion: v.direccion, ref_catastral: v.referencia_catastral,
    tipo: v.tipo_principal, subtipo: v.subtipo, superficie_m2: v.superficie,
    valor_estimado_min_eur: v.valor_estimado_min, valor_estimado_max_eur: v.valor_estimado_max,
    fecha_creacion: v.fecha_creacion, lead_id: v.lead_id, lead_nombre: v.lead_nombre, lead_email: v.lead_email
  }));
  exportarCSV(datos, `listado_valoraciones_${new Date().toISOString().split('T')[0]}.csv`);
};

const obtenerTipoMasComun = (items: Valoracion[]): string | null => {
  if (!items.length) return null;
  const counts = items.reduce((acc, v) => {
    acc[v.tipo_principal] = (acc[v.tipo_principal] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b) || null;
};

const calcularValoracionesUltimoMesPorDia = (items: Valoracion[]): { dia: string; cantidad: number }[] => {
  const resultado: { dia: string; cantidad: number }[] = [];
  const hoy = new Date();
  const ultimoMes = new Date(hoy);
  ultimoMes.setDate(ultimoMes.getDate() - 30);

  const valoracionesDelPeriodo = items.filter(v => new Date(v.fecha_creacion) >= ultimoMes && new Date(v.fecha_creacion) <= hoy);

  const agrupadoPorDia: Record<string, number> = {};

  valoracionesDelPeriodo.forEach(v => {
    const fecha = new Date(v.fecha_creacion).toLocaleDateString('es-ES', { month: 'short', day: 'numeric' });
    agrupadoPorDia[fecha] = (agrupadoPorDia[fecha] || 0) + 1;
  });

  const diasOrdenados = Object.keys(agrupadoPorDia).sort((a, b) => {
    try {
      const dateA = new Date(a.split(' ').reverse().join(' ') + ", " + hoy.getFullYear());
      const dateB = new Date(b.split(' ').reverse().join(' ') + ", " + hoy.getFullYear());
      return dateA.getTime() - dateB.getTime();
    } catch (e) {
      return a.localeCompare(b);
    }
  });
  
  diasOrdenados.forEach(dia => {
    resultado.push({ dia: dia, cantidad: agrupadoPorDia[dia] });
  });

  return resultado;
};

const calcularIncrementoMensual = (items: Valoracion[]): number => {
  const hoy = new Date();
  const mesActual = hoy.getMonth();
  const anoActual = hoy.getFullYear();
  const valoracionesMesActual = items.filter(v => {
    const fechaV = new Date(v.fecha_creacion);
    return fechaV.getMonth() === mesActual && fechaV.getFullYear() === anoActual;
  }).length;
  const valoracionesMesAnterior = items.filter(v => {
    const fechaV = new Date(v.fecha_creacion);
    const mesV = fechaV.getMonth();
    const anoV = fechaV.getFullYear();
    if (mesActual === 0) return mesV === 11 && anoV === anoActual - 1;
    return mesV === mesActual - 1 && anoV === anoActual;
  }).length;
  if (valoracionesMesAnterior === 0) return valoracionesMesActual > 0 ? 100 : 0;
  return Math.round(((valoracionesMesActual - valoracionesMesAnterior) / valoracionesMesAnterior) * 100);
};

const handleUpdateNotasAgente = (valoracionId: number, nuevasNotas: string) => {
  const index = valoraciones.value.findIndex(v => v.id === valoracionId);
  if (index !== -1) {
    valoraciones.value[index].notas_agente = nuevasNotas;
    valoraciones.value[index].fecha_modificacion = new Date().toISOString();
    if (valoracionSeleccionada.value && valoracionSeleccionada.value.id === valoracionId) {
      valoracionSeleccionada.value.notas_agente = nuevasNotas;
      valoracionSeleccionada.value.fecha_modificacion = new Date().toISOString();
    }
  }
};

onMounted(() => {
  fetchValoraciones();
});

// Manejar el parámetro openValoracionId para abrir automáticamente una valoración
watchEffect(() => {
  const targetValoracionIdStr = route.query.openValoracionId as string;

  if (targetValoracionIdStr) {
    const targetValoracionId = parseInt(targetValoracionIdStr, 10);
    if (!isNaN(targetValoracionId)) {
      if (valoraciones.value.length > 0) {
        const valoracionToOpen = valoraciones.value.find(v => v.id === targetValoracionId);
        if (valoracionToOpen) {
          if (valoracionSeleccionada.value?.id !== targetValoracionId || !mostrarDetalle.value) {
            valoracionSeleccionada.value = valoracionToOpen;
            mostrarDetalle.value = true;
          }
        } else {
          showToast('Valoración con ID ' + targetValoracionId + ' no encontrada en la lista actual.', 'warning');
          // Limpiar el query param si la valoración no se encuentra
          router.replace({ name: 'Valoraciones' });
        }
      }
    }
  }
});
</script>

<style scoped>
/* Estilos adicionales si son necesarios */
</style>