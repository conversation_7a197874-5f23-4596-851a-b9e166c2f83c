<template>
  <div class="p-4 sm:p-6 md:p-8 bg-gray-50 min-h-screen">
    <header class="mb-6 md:mb-8">
      <h1 class="text-3xl font-bold text-[#051f33]">Actividad y rendimiento de emails</h1>
      <p class="text-gray-600 mt-1">
        Analiza la efectividad de tu estrategia de nutrición y consulta el historial de comunicaciones.
      </p>
    </header>

    <!-- Pestañas -->
    <div class="mb-6 border-b border-gray-200">
      <nav class="-mb-px flex space-x-6" aria-label="Tabs">
        <button
          @click="activeTab = 'historial'"
          :class="[
            'whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-150',
            activeTab === 'historial'
              ? 'border-impacto-orange text-impacto-orange'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
          ]"
        >
          Historial de emails
        </button>
        <button
          @click="activeTab = 'rendimiento'"
          :class="[
            'whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-150',
            activeTab === 'rendimiento'
              ? 'border-impacto-orange text-impacto-orange'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
          ]"
        >
          Rendimiento
        </button>
      </nav>
    </div>

    <!-- Contenido de las Pestañas -->
    <div>
      <EmailHistorialTab v-if="activeTab === 'historial'" />
      <EmailRendimientoTab v-if="activeTab === 'rendimiento' && hasAnalytics" />
      <AnalyticsPaymentWall v-if="activeTab === 'rendimiento' && !hasAnalytics" @upgrade-plan="openPlanSelectionModal" />
    </div>

    <!-- Modal de Selección de Planes -->
    <PlanSelectionModal
      :show-modal="showPlanModal"
      :current-plan-id="currentPlanId"
      :current-billing-cycle="currentBillingCycle"
      :is-subscription-active="isSubscriptionActive"
      :current-subscription-details="currentSubscriptionDetails"
      @close="closePlanModal"
      @plan-changed="handlePlanChanged"
      @change-initiated="handleChangeInitiated"
      @stripe-action-required="handleStripeActionRequired"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useToast } from '@/composables/useToast';

const activeTab = ref<'rendimiento' | 'historial'>('historial'); // Pestaña activa por defecto
const authStore = useAuthStore();
const { showToast } = useToast();

// Estado para el modal de selección de planes
const showPlanModal = ref(false);

// Computed para verificar si el usuario tiene acceso a analíticas
const hasAnalytics = computed(() => authStore.hasAnalytics);

// Datos de suscripción para el modal (simulados por ahora, deberían venir del store o API)
const currentPlanId = computed(() => authStore.planFeatures?.slug || null);
const currentBillingCycle = computed(() => 'monthly' as 'monthly' | 'annual' | null); // Esto debería venir de la suscripción real
const isSubscriptionActive = computed(() => true); // Esto debería venir del estado real de la suscripción
const currentSubscriptionDetails = computed(() => null); // Esto debería venir de la suscripción real

// Carga asíncrona de los componentes de las pestañas
const EmailRendimientoTab = defineAsyncComponent(() =>
  import('@/components/dashboard/emails/EmailRendimientoTab.vue')
);
const EmailHistorialTab = defineAsyncComponent(() =>
  import('@/components/dashboard/emails/EmailHistorialTab.vue')
);
const AnalyticsPaymentWall = defineAsyncComponent(() =>
  import('@/components/dashboard/common/AnalyticsPaymentWall.vue')
);
const PlanSelectionModal = defineAsyncComponent(() =>
  import('@/components/dashboard/PlanSelectionModal.vue')
);

// Funciones para manejar el modal de planes
const openPlanSelectionModal = () => {
  showPlanModal.value = true;
};

const closePlanModal = () => {
  showPlanModal.value = false;
};

const handlePlanChanged = (data: any) => {
  showToast(data.message || 'Plan actualizado correctamente', 'success');
  // Recargar las características del plan
  authStore.fetchAndSetPlanFeatures();
  closePlanModal();
};

const handleChangeInitiated = () => {
  showToast('Procesando cambio de plan...', 'info');
};

const handleStripeActionRequired = () => {
  showToast('Se requiere confirmación adicional para el cambio de plan', 'info');
  // Aquí se manejaría la acción de Stripe si fuera necesario
};

</script>

<style scoped>
/* Estilos específicos si son necesarios */
</style>
