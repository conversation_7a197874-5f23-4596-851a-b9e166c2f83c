<template>
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        Administración de Usuarios
      </h1>
      <p class="text-gray-600 mt-1">
        Gestiona todos los usuarios de la plataforma Valorador IA.
      </p>
    </div>

    <!-- Indicador de carga -->
    <div v-if="isLoading" class="flex justify-center items-center py-20">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-impacto-blue"></div>
    </div>

    <div v-else>
      <!-- Barra de acciones -->
      <div class="bg-white rounded-lg shadow-md p-4 mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div class="flex flex-col md:flex-row md:items-center gap-4">
          <!-- Búsqueda -->
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="Buscar usuarios..." 
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue w-full md:w-64"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <!-- Filtros -->
          <div class="flex items-center gap-2">
            <select 
              v-model="filterRole" 
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los roles</option>
              <option value="admin">Administradores</option>
              <option value="client">Clientes</option>
            </select>
            <select 
              v-model="filterStatus" 
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los estados</option>
              <option value="1">Activos</option>
              <option value="0">Inactivos</option>
            </select>
          </div>
        </div>
        
        <!-- Botón para añadir usuario -->
        <button 
          @click="openAddUserModal" 
          class="bg-impacto-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Añadir Usuario
        </button>
      </div>

      <!-- Tabla de usuarios -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usuario
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rol
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fecha Registro
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Último Login
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="user in paginatedUsers" :key="user.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-semibold">
                      {{ user.nombre_completo.substring(0, 2).toUpperCase() }}
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ user.nombre_completo }}</div>
                      <div class="text-sm text-gray-500">ID: {{ user.id }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ user.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span 
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                    :class="Array.isArray(user.roles) && user.roles.includes('admin') ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'"
                  >
                    {{ Array.isArray(user.roles) && user.roles.includes('admin') ? 'Administrador' : 'Cliente' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span 
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                    :class="user.activo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                  >
                    {{ user.activo ? 'Activo' : 'Inactivo' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(user.fecha_creacion) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ user.fecha_ultimo_login ? formatDate(user.fecha_ultimo_login) : 'Nunca' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end gap-2">
                    <button 
                      @click="editUser(user)" 
                      class="text-indigo-600 hover:text-indigo-900"
                      title="Editar usuario"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button 
                      @click="openUserDetailsModal(user)" 
                      class="text-blue-600 hover:text-blue-900"
                      title="Ver detalles"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Paginación -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button 
              @click="currentPage > 1 ? currentPage-- : null" 
              :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button 
              @click="currentPage < totalPages ? currentPage++ : null" 
              :disabled="currentPage === totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando <span class="font-medium">{{ (currentPage - 1) * itemsPerPage + 1 }}</span> a <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, filteredUsers.length) }}</span> de <span class="font-medium">{{ filteredUsers.length }}</span> resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button 
                  @click="currentPage > 1 ? currentPage-- : null" 
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Anterior</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
                <!-- Botones de página -->
                <template v-for="pageItem in paginationRange" :key="pageItem">
                <button 
                    v-if="typeof pageItem === 'number'"
                    @click="currentPage = pageItem"
                  :class="[
                      pageItem === currentPage ? 'z-10 bg-impacto-blue border-impacto-blue text-white' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                  ]"
                >
                    {{ pageItem }}
                </button>
                  <span
                    v-else
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                  >
                    {{ pageItem }}
                  </span>
                </template>
                <button 
                  @click="currentPage < totalPages ? currentPage++ : null" 
                  :disabled="currentPage === totalPages"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Siguiente</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal para editar usuario -->
      <div v-if="showEditModal && selectedUser" class="fixed z-10 inset-0 overflow-y-auto">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>

          <!-- Modal content -->
          <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
          <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-impacto-blue sm:mx-0 sm:h-10 sm:w-10">
                  <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                    Editar Usuario (ID: {{ selectedUser.id }})
                  </h3>
                  <div class="mt-4">
                    <form @submit.prevent="updateUser" class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700" for="nombre_completo">
                          Nombre Completo
                        </label>
                        <input v-model="selectedUser.nombre_completo" class="mt-1 shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange" id="nombre_completo" type="text" placeholder="Nombre Completo">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700" for="email">
                          Email
                        </label>
                        <input v-model="selectedUser.email" class="mt-1 shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange" id="email" type="email" placeholder="Email">
                      </div>

                      <!-- Campo para Rol -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700" for="user_role">
                          Rol
                        </label>
                        <select v-model="selectedRoleForModal" id="user_role" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm">
                          <option value="client">Cliente</option>
                          <option value="admin">Administrador</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Nota: El sistema actualmente maneja roles como un array. Al seleccionar aquí, se guardará como ["rol_seleccionado"].</p>
                      </div>

                      <!-- Campo para Estado Activo -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700">
                          Estado
                        </label>
                        <div class="mt-2 flex items-center">
                          <input type="checkbox" v-model="selectedUser.activo" id="user_status" class="h-4 w-4 text-impacto-orange border-gray-300 rounded focus:ring-impacto-orange">
                          <label for="user_status" class="ml-2 block text-sm text-gray-900">
                            Activo
                          </label>
                        </div>
                      </div>

                      <!-- Campo para Stripe Customer ID (informativo) -->
                      <div v-if="selectedUser.stripe_customer_id">
                        <label class="block text-sm font-medium text-gray-700">
                          Stripe Customer ID
                        </label>
                        <p class="mt-1 text-sm text-gray-700 bg-gray-100 p-2 rounded">{{ selectedUser.stripe_customer_id }}</p>
                      </div>

                      <div class="pt-4 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-impacto-orange text-base font-medium text-white hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange sm:ml-3 sm:w-auto sm:text-sm">
                          Guardar Cambios
                        </button>
                        <button @click="closeEditModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm">
                          Cancelar
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal para Ver Detalles de Usuario -->
      <div v-if="showUserDetailsModal && detailedUser" class="fixed z-20 inset-0 overflow-y-auto">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
          </div>
          <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
          <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6">
              <div class="sm:flex sm:items-start">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 class="text-2xl leading-6 font-bold text-gray-900" id="modal-user-details-title">
                    Detalles del Usuario: {{ detailedUser.nombre_completo }}
                  </h3>
                  <div class="mt-6 space-y-4">
                    <p><strong class="font-medium text-gray-700">ID:</strong> {{ detailedUser.id }}</p>
                    <p><strong class="font-medium text-gray-700">Email:</strong> {{ detailedUser.email }}</p>
                    <p><strong class="font-medium text-gray-700">Roles:</strong> {{ formattedDetailedUserRoles }}</p>
                    <p><strong class="font-medium text-gray-700">Estado:</strong> <span :class="statusClass">{{ statusText }}</span></p>
                    <p><strong class="font-medium text-gray-700">Fecha Registro:</strong> {{ formatDate(detailedUser.fecha_creacion) }}</p>
                    <p v-if="detailedUser.fecha_ultimo_login"><strong class="font-medium text-gray-700">Último Login:</strong> {{ formatDate(detailedUser.fecha_ultimo_login) }}</p>
                    <p v-if="detailedUser.stripe_customer_id"><strong class="font-medium text-gray-700">Stripe Customer ID:</strong> {{ detailedUser.stripe_customer_id }}</p>
                    
                    <!-- Aquí se cargarán más detalles: Config Valorador, Valoraciones, Leads -->
                    <div class="mt-6 pt-4 border-t">
                      <h4 class="text-lg font-medium text-gray-800 mb-2">Información Adicional (Próximamente)</h4>
                      <p class="text-sm text-gray-500">Configuración del valorador, historial de valoraciones y leads generados se mostrarán aquí.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button @click="closeUserDetailsModal" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-impacto-blue text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue sm:ml-3 sm:w-auto sm:text-sm">
                Cerrar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useToast } from '@/composables/useToast'; // Importar el composable local
import { apiFetch } from '@/utils/apiFetch';

const { showToast } = useToast(); // Obtener la función showToast

interface User {
  id: number;
  nombre_completo: string;
  email: string;
  roles: string | string[]; // Puede ser string desde el select, pero se enviará como array
  activo: boolean;
  fecha_creacion: string;
  fecha_ultimo_login?: string;
  stripe_customer_id?: string; // Añadido para el modal
}

const users = ref<User[]>([]);
const isLoading = ref(true);
const showEditModal = ref(false);
const selectedUser = ref<User | null>(null);

// Nueva ref para el rol seleccionado en el modal, para manejar la conversión a array
const selectedRoleForModal = ref<'client' | 'admin'>('client');

// Propiedades restauradas para búsqueda, filtros y paginación
const searchQuery = ref('');
const filterRole = ref('');
const filterStatus = ref('');
const currentPage = ref(1);
const itemsPerPage = ref(10); // O el valor que prefieras

// Nuevas refs para el modal de detalles del usuario
const showUserDetailsModal = ref(false);
const detailedUser = ref<User | null>(null);

// Función formatDate restaurada
function formatDate(dateString: string | undefined): string {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('es-ES', {
    year: 'numeric', month: 'short', day: 'numeric',
    hour: '2-digit', minute: '2-digit'
  }).format(date);
}

const filteredUsers = computed(() => {
  let result = users.value;
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(user =>
      user.nombre_completo.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query)
    );
  }
  if (filterRole.value) {
    result = result.filter(user => {
        if (Array.isArray(user.roles)) {
            return user.roles.includes(filterRole.value);
        } else if (typeof user.roles === 'string') {
            // Si roles es un string, verificar si es igual al filtro o si un array parseado lo incluye
            // Esto depende de cómo se espere que funcione el filtro con roles como string
            try {
                const parsedRoles = JSON.parse(user.roles as string);
                return Array.isArray(parsedRoles) && parsedRoles.includes(filterRole.value);
            } catch (e) {
                return user.roles === filterRole.value;
            }
        }
        return false;
    });
  }
  if (filterStatus.value !== '') {
    result = result.filter(user => user.activo === (filterStatus.value === '1'));
  }
  return result;
});

const totalPages = computed(() => {
  return Math.ceil(filteredUsers.value.length / itemsPerPage.value);
});

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredUsers.value.slice(start, end);
});

const paginationRange = computed(() => { // Restaurada para los botones de página
  const range = [];
  const total = totalPages.value;
  let start = Math.max(1, currentPage.value - 2);
  let end = Math.min(total, currentPage.value + 2);

  if (currentPage.value < 3) {
    end = Math.min(5, total);
  }
  if (currentPage.value > total - 3) {
    start = Math.max(1, total - 4);
  }

  for (let i = start; i <= end; i++) {
    range.push(i);
  }
  return range;
});

const formattedDetailedUserRoles = computed(() => {
  if (detailedUser.value && Array.isArray(detailedUser.value.roles)) {
    return detailedUser.value.roles.join(', ');
  }
  if (detailedUser.value && detailedUser.value.roles) {
    return String(detailedUser.value.roles); 
  }
  return 'N/A';
});

// NUEVAS PROPIEDADES COMPUTADAS PARA EL ESTADO
const statusClass = computed(() => {
  if (detailedUser.value) {
    return Boolean(detailedUser.value.activo) ? 'text-green-600' : 'text-red-600';
  }
  return 'text-gray-600'; // Clase por defecto o para estado desconocido
});

const statusText = computed(() => {
  if (detailedUser.value) {
    return Boolean(detailedUser.value.activo) ? 'Activo' : 'Inactivo';
  }
  return 'Desconocido'; // Texto por defecto o para estado desconocido
});

function editUser(user: User) {
  selectedUser.value = { 
    ...user, 
    activo: Boolean(user.activo),
    // Asegurar que roles sea un array para el estado interno, incluso si viene como string
    roles: Array.isArray(user.roles) ? user.roles : (typeof user.roles === 'string' ? [user.roles] : ['client'])
  };
  // Ajustar selectedRoleForModal basado en el primer rol del usuario o 'client' por defecto
  if (selectedUser.value.roles && selectedUser.value.roles.length > 0) {
    selectedRoleForModal.value = selectedUser.value.roles[0] as 'client' | 'admin';
  } else {
    selectedRoleForModal.value = 'client';
  }
  showEditModal.value = true;
}

watch(selectedRoleForModal, (newRole) => {
  if (selectedUser.value) {
    selectedUser.value.roles = [newRole];
  }
});

function closeEditModal() {
  showEditModal.value = false;
  selectedUser.value = null;
}

async function loadUsers() {
  try {
    isLoading.value = true;
    const response = await apiFetch('getAllUsers.php'); 
    if (response.data && Array.isArray(response.data)) {
        users.value = response.data.map((u: any) => {
            const cleanString = (s: any) => {
              if (s == null) return '';
              let str = String(s);

              // Paso 1: Escapar barras invertidas.
              str = str.replace(/\\/g, '\\\\');

              // Paso 2: Escapar comillas simples y dobles.
              str = str.replace(/'/g, "\\'");
              str = str.replace(/"/g, '\\"');

              // Paso 3: Reemplazar caracteres de nueva línea, separadores Unicode y otros caracteres de control con un espacio.
              str = str.replace(/[\n\r\u2028\u2029\u0000-\u001F\u007F-\u009F]/g, ' ');

              return str;
            };
            
            let processedRoles;
            if (typeof u.roles === 'string') {
                if (u.roles.startsWith('[')) {
                    try {
                        processedRoles = JSON.parse(u.roles).map(cleanString);
                    } catch (e) {
                        processedRoles = [cleanString(u.roles)];
                    }
                } else {
                    processedRoles = [cleanString(u.roles)];
                }
            } else if (Array.isArray(u.roles)) {
                processedRoles = u.roles.map(cleanString);
            } else {
                processedRoles = ['client'];
            }

            return {
                ...u, 
                nombre_completo: cleanString(u.nombre_completo),
                email: cleanString(u.email),
                activo: Boolean(u.activo),
                roles: processedRoles
            };
        });
    } else {
        users.value = [];
        console.warn('No se recibieron datos de usuarios o la estructura no es la esperada.', response);
        showToast('Error al cargar la lista de usuarios.', 'error');
    }
  } catch (error) {
    console.error('Error al cargar usuarios:', error);
    users.value = [];
    showToast('Error al cargar la lista de usuarios.', 'error');
  } finally {
    isLoading.value = false;
  }
}

async function updateUser() {
  if (!selectedUser.value) return;
  const userToUpdate = {
    ...selectedUser.value,
  };

  try {
    const data = await apiFetch('updateUser.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userToUpdate),
    });

    if (data.success) {
      closeEditModal();
      await loadUsers();
      showToast('Usuario actualizado correctamente.', 'success');
    } else {
      console.error('Error al actualizar usuario:', data.message, data.debug ? data.debug : '');
      showToast(data.message || 'Error al actualizar el usuario.', 'error');
    }
  } catch (error: any) {
    console.error('Error de red o de la API al actualizar el usuario:', error);
    showToast(error.data?.message || error.message || 'Error de conexión al actualizar el usuario.', 'error');
  }
}

// Funciones placeholder restauradas (necesitarán implementación)
function openAddUserModal() {
  // Implementar lógica para mostrar un modal de creación de usuario
}

// Función para abrir el modal de detalles del usuario
function openUserDetailsModal(user: User) {
  detailedUser.value = user;
  showUserDetailsModal.value = true;
}

// Función para cerrar el modal de detalles del usuario
function closeUserDetailsModal() {
  showUserDetailsModal.value = false;
  detailedUser.value = null;
}

onMounted(() => {
  loadUsers();
});

</script>

<style scoped>
.bg-impacto-blue {
  background-color: #003B6D;
}

.text-impacto-blue {
  color: #003B6D;
}

.border-impacto-blue {
  border-color: #003B6D;
}

.bg-impacto-orange {
  background-color: #FF6B00;
}

.text-impacto-orange {
  color: #FF6B00;
}
</style>
