<template>
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        Administración de Leads
      </h1>
      <p class="text-gray-600 mt-1">
        Gestiona todos los leads generados en la plataforma.
      </p>
    </div>

    <!-- Indicador de carga -->
    <div v-if="isLoading" class="flex justify-center items-center py-20">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-impacto-blue"></div>
    </div>

    <div v-else>
      <!-- Barra de acciones -->
      <div class="bg-white rounded-lg shadow-md p-4 mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div class="flex flex-col md:flex-row md:items-center gap-4">
          <!-- Búsqueda -->
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="Buscar por nombre o email..." 
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue w-full md:w-64"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <!-- Filtros -->
          <div class="flex items-center gap-2">
            <select 
              v-model="filterEstado" 
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los estados</option>
              <option value="nuevo">Nuevo</option>
              <option value="contactado">Contactado</option>
              <option value="calificado">Calificado</option>
              <option value="convertido">Convertido</option>
              <option value="perdido">Perdido</option>
            </select>
            <select 
              v-model="filterValoradorIdentifier" 
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los valoradores</option>
              <option v-for="valorador in listaValoradoresParaFiltro" :key="valorador.client_identifier" :value="valorador.client_identifier">
                {{ valorador.nombre_display }}
              </option>
            </select>
          </div>
        </div>
        
        <!-- Botón para exportar -->
        <button 
          @click="exportarLeads" 
          class="bg-impacto-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Exportar
        </button>
      </div>

      <!-- Tabla de leads -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  NOMBRE
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  CONTACTO
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ESTADO
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  VALORADOR
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  VALORACIONES
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  FECHA
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ACCIONES
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-if="leads.length === 0 && !isLoading">
                <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                  No se encontraron leads que coincidan con los criterios.
                </td>
              </tr>
              <tr v-for="lead in leads" :key="lead.id" class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span class="text-gray-600 font-medium">{{ getInitials(lead.nombre) }}</span>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ lead.nombre }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">{{ lead.email }}</div>
                  <div class="text-sm text-gray-500">{{ lead.telefono || 'N/A' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-0.5 text-xs rounded-full font-semibold" :class="getEstadoClass(lead.estado)">
                    {{ getEstadoLabel(lead.estado) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {{ lead.nombre_valorador_asociado || 'N/A' }}
                  <div v-if="lead.client_id" class="text-xs text-gray-500">ID: {{ lead.client_id }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-700">
                  {{ lead.num_valoraciones || 0 }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(lead.fecha_creacion) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end gap-2">
                    <button 
                      @click="verDetallesLead(lead)" 
                      class="text-blue-600 hover:text-blue-900"
                      title="Ver detalles del lead"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                    <!-- Acciones futuras: editar, eliminar -->
                    <!-- 
                    <button 
                      @click="editarLead(lead)" 
                      class="text-indigo-600 hover:text-indigo-900"
                      title="Editar lead"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button 
                      @click="eliminarLead(lead)" 
                      class="text-red-600 hover:text-red-900"
                      title="Eliminar lead"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                    -->
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Paginación -->
        <div v-if="totalPages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button 
              @click="currentPage > 1 ? currentPage-- : null" 
              :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button 
              @click="currentPage < totalPages ? currentPage++ : null" 
              :disabled="currentPage === totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando <span class="font-medium">{{ Math.max(1, (currentPage - 1) * itemsPerPage + 1) }}</span>
                a <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, totalItems) }}</span>
                de <span class="font-medium">{{ totalItems }}</span> resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button 
                  @click="currentPage > 1 ? currentPage-- : null" 
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Anterior</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
                <!-- Botones de página -->
                <template v-for="pageItem in paginationRange" :key="pageItem">
                <button 
                    v-if="typeof pageItem === 'number'"
                    @click="currentPage = pageItem"
                  :class="[
                      pageItem === currentPage ? 'z-10 bg-impacto-blue border-impacto-blue text-white' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                  ]"
                >
                    {{ pageItem }}
                </button>
                  <span
                    v-else
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                  >
                    {{ pageItem }}
                  </span>
                </template>
                <button 
                  @click="currentPage < totalPages ? currentPage++ : null" 
                  :disabled="currentPage === totalPages"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Siguiente</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de Detalle de Lead -->
  <div v-if="isLeadDetalleModalOpen && selectedLeadDetalle" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4">
    <div class="bg-white p-6 sm:p-8 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 ease-out">
      <div class="flex justify-between items-start mb-6 pb-4 border-b border-gray-200">
        <div>
          <h2 class="text-2xl font-bold text-impacto-blue flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Detalle del Lead
          </h2>
          <p class="text-sm text-gray-500 mt-1">ID Lead: {{ selectedLeadDetalle.uuid }}</p>
        </div>
        <button @click="closeLeadDetalleModal" class="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="space-y-6">
        <!-- Sección Principal: Info Lead -->
        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-impacto-blue mb-3 border-b border-gray-200 pb-2">Información del Lead</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4 text-sm">
            <div><strong class="text-gray-600">Nombre:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.nombre }}</span></div>
            <div><strong class="text-gray-600">Estado:</strong> 
              <span class="px-2 py-0.5 text-xs rounded-full font-semibold" :class="getEstadoClass(selectedLeadDetalle.estado)">
                {{ getEstadoLabel(selectedLeadDetalle.estado) }}
              </span>
            </div>
            <div><strong class="text-gray-600">Email:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.email || 'N/A' }}</span></div>
            <div><strong class="text-gray-600">Teléfono:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.telefono || 'N/A' }}</span></div>
            <div><strong class="text-gray-600">Necesidad:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.necesidad || 'N/A' }}</span></div>
            <div><strong class="text-gray-600">Fecha Creación:</strong> <span class="text-gray-800">{{ formatDate(selectedLeadDetalle.fecha_creacion) }}</span></div>
            <div><strong class="text-gray-600">Última Modificación:</strong> <span class="text-gray-800">{{ formatDate(selectedLeadDetalle.fecha_modificacion) }}</span></div>
            <div><strong class="text-gray-600">Nº Valoraciones:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.num_valoraciones ?? 0 }}</span></div>
            <div v-if="selectedLeadDetalle.valoracion_id"><strong class="text-gray-600">ID Última Valoración:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.valoracion_id }}</span></div>
          </div>
        </div>

        <!-- Sección Valorador Asociado -->
        <div v-if="selectedLeadDetalle.client_id || selectedLeadDetalle.nombre_valorador_asociado" class="bg-gray-50 p-4 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-impacto-blue mb-3 border-b border-gray-200 pb-2">Valorador Asociado</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4 text-sm">
            <div><strong class="text-gray-600">Nombre Display:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.nombre_valorador_asociado || 'N/A' }}</span></div>
            <div><strong class="text-gray-600">Client Identifier:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.client_id || 'N/A' }}</span></div>
          </div>
        </div>

        <!-- Sección Notas -->
        <div v-if="selectedLeadDetalle.notas" class="bg-gray-50 p-4 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-impacto-blue mb-2">Notas Adicionales</h3>
          <pre class="bg-white p-3 rounded-md text-xs whitespace-pre-wrap max-h-60 overflow-y-auto border border-gray-200">{{ selectedLeadDetalle.notas }}</pre>
        </div>

        <!-- Sección Seguimiento IA (si aplica) -->
        <div v-if="selectedLeadDetalle.id_secuencia_asignada || selectedLeadDetalle.estado_secuencia_lead" class="bg-gray-50 p-4 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-impacto-blue mb-3 border-b border-gray-200 pb-2">Seguimiento IA</h3>
           <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4 text-sm">
              <div><strong class="text-gray-600">ID Secuencia:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.id_secuencia_asignada || 'N/A' }}</span></div>
              <div><strong class="text-gray-600">Estado Secuencia:</strong> <span class="text-gray-800">{{ selectedLeadDetalle.estado_secuencia_lead || 'N/A' }}</span></div>
          </div>
        </div>

      </div>

      <div class="mt-8 pt-6 border-t border-gray-200 text-right">
        <button 
          @click="closeLeadDetalleModal" 
          class="bg-impacto-blue hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue shadow-md hover:shadow-lg"
        >
          Cerrar
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { apiFetch } from '@/utils/apiFetch';
import { useToast } from '@/composables/useToast';

// Definición de Interfaces
interface AdminLeadItem {
  id: number;
  uuid: string;
  client_id: string | null; 
  nombre_valorador_asociado: string | null;
  nombre: string;
  email: string | null;
  telefono: string | null;
  necesidad: string | null;
  estado: string;
  num_valoraciones: number | null;
  notas: string | null;
  valoracion_id: number | null;
  id_secuencia_asignada: number | null;
  estado_secuencia_lead: string | null;
  fecha_creacion: string;
  fecha_modificacion: string;
}

interface ValoradorParaFiltro {
  client_identifier: string; // Debe ser string, no null
  nombre_display: string;
}

interface AdminLeadsApiResponse {
  success: boolean;
  data: AdminLeadItem[];
  meta: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  message?: string;
}

const { showToast } = useToast();

const isLoading = ref(true);
const leads = ref<AdminLeadItem[]>([]);
const listaValoradoresParaFiltro = ref<ValoradorParaFiltro[]>([]);

const searchQuery = ref('');
const filterEstado = ref('');
const filterValoradorIdentifier = ref('');

const currentPage = ref(1);
const itemsPerPage = ref(15); 
const totalItems = ref(0);
const totalPages = ref(1); // Este es el ref, el computed totalPages será eliminado

// Estado para el modal de detalle de Lead
const isLeadDetalleModalOpen = ref(false);
const selectedLeadDetalle = ref<AdminLeadItem | null>(null);

// Funciones de utilidad y carga de datos
const fetchLeads = async () => {
  isLoading.value = true;
  try {
    let url = `/api/admin_get_leads.php?page=${currentPage.value}&limit=${itemsPerPage.value}`;
    if (searchQuery.value) url += `&search=${encodeURIComponent(searchQuery.value)}`;
    if (filterEstado.value) url += `&estado=${encodeURIComponent(filterEstado.value)}`;
    if (filterValoradorIdentifier.value) url += `&client_id=${encodeURIComponent(filterValoradorIdentifier.value)}`; // client_id para el backend
    
    const response = await apiFetch(url) as AdminLeadsApiResponse;

    if (response.success && response.data) {
      leads.value = response.data;
      totalItems.value = response.meta.totalItems;
      totalPages.value = response.meta.totalPages;
      // Ajustar currentPage si está fuera de rango después de filtrar/cambiar página
      if (response.meta.currentPage > response.meta.totalPages && response.meta.totalPages > 0) {
          currentPage.value = response.meta.totalPages;
      } else {
          currentPage.value = response.meta.currentPage;
      }
    } else {
      showToast(response.message || 'Error al cargar los leads.', 'error');
      leads.value = [];
      totalItems.value = 0;
      totalPages.value = 1;
    }
  } catch (error: any) {
    console.error('Error en fetchLeads:', error);
    showToast(error.message || 'Se produjo un error inesperado al cargar leads.', 'error');
    leads.value = [];
    totalItems.value = 0;
    totalPages.value = 1;
  } finally {
    isLoading.value = false;
  }
};

const fetchValoradoresParaFiltro = async () => {
  try {
    // Usamos el mismo endpoint que en AdminValoraciones, limitando a activos
    const response = await apiFetch('/api/admin_get_valoradores.php?limit=500&status=1'); 
    if (response.success && response.data) {
      // Aseguramos que client_identifier no sea null antes de mapear
      listaValoradoresParaFiltro.value = response.data
        .filter((v: any) => v.client_identifier !== null) // Filtramos los que no tienen client_identifier
        .map((v: any) => ({
          client_identifier: v.client_identifier as string, // Hacemos cast a string
          nombre_display: v.nombre_display,
      }));
    }
  } catch (error) {
    console.error("Error cargando valoradores para filtro:", error);
    // No es crítico para la carga principal de leads, no mostramos toast
  }
};

const getInitials = (name: string | null): string => {
  if (!name) return '??';
  return name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
};

const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Fecha inválida';
    return date.toLocaleDateString('es-ES', { year: 'numeric', month: 'short', day: 'numeric', hour:'2-digit', minute: '2-digit' });
  } catch (e) { return 'Fecha inválida'; }
};

const getEstadoClass = (estado: string | null): string => {
  if (!estado) return 'bg-gray-200 text-gray-800';
  const classes: Record<string, string> = {
    'nuevo': 'bg-blue-100 text-blue-800',
    'contactado': 'bg-yellow-100 text-yellow-800',
    'calificado': 'bg-green-100 text-green-800',
    'en_progreso': 'bg-purple-100 text-purple-800', // Suponiendo este estado
    'convertido': 'bg-teal-100 text-teal-800', // Suponiendo este estado
    'no_interesado': 'bg-red-100 text-red-800',
    'perdido': 'bg-orange-100 text-orange-800', // Suponiendo este estado
  };
  return classes[estado.toLowerCase()] || 'bg-gray-200 text-gray-800';
};

const getEstadoLabel = (estado: string | null): string => {
  if (!estado) return 'Desconocido';
  return estado.charAt(0).toUpperCase() + estado.slice(1).replace(/_/g, ' ');
};

const verDetallesLead = (lead: AdminLeadItem) => {
  selectedLeadDetalle.value = lead;
  isLeadDetalleModalOpen.value = true;
  // showToast(`Detalles del lead ID ${lead.id} - Próximamente`, 'info'); // Comentado para usar el modal
};

const closeLeadDetalleModal = () => {
  isLeadDetalleModalOpen.value = false;
  selectedLeadDetalle.value = null;
};

const exportarLeads = () => {
  showToast('Funcionalidad de exportar leads - Próximamente', 'info');
  // TODO: Implementar lógica de exportación
};

onMounted(() => {
  fetchLeads();
  fetchValoradoresParaFiltro();
});


// Watchers para recargar datos cuando cambian los filtros o la página
watch(currentPage, () => {
  fetchLeads();
});

// Para los filtros, reseteamos a la página 1 si no es ya la página 1
// Si ya estamos en la página 1, el fetchLeads se disparará igualmente por el cambio de filtro.
watch([searchQuery, filterEstado, filterValoradorIdentifier], () => {
  if (currentPage.value !== 1) {
    currentPage.value = 1; 
  } else {
    fetchLeads(); // Si ya estamos en la página 1, forzamos la recarga con los nuevos filtros.
  }
});


const paginationRange = computed(() => {
  const delta = 1; 
  const rangeNumbers: number[] = [];
  const left = currentPage.value - delta;
  const right = currentPage.value + delta + 1;
  let l: number | undefined = undefined;

  for (let i = 1; i <= totalPages.value; i++) {
    if (i === 1 || i === totalPages.value || (i >= left && i < right)) {
      rangeNumbers.push(i);
    }
  }
  const rangeWithDots: (number | string)[] = [];
  if (totalPages.value <= (1 + delta * 2) + 2 ) { 
     for(let i = 1; i <= totalPages.value; i++){ rangeWithDots.push(i); }
     return rangeWithDots;
  }
  for (const i of rangeNumbers) {
    if (l !== undefined) {
      if (i - l === 2) rangeWithDots.push(l + 1);
      else if (i - l !== 1) rangeWithDots.push('...');
    }
    rangeWithDots.push(i);
    l = i;
  }
  return rangeWithDots;
});

</script>

<style scoped>
.bg-impacto-blue {
  background-color: #003B6D;
}

.text-impacto-blue {
  color: #003B6D;
}

.border-impacto-blue {
  border-color: #003B6D;
}

.bg-impacto-orange {
  background-color: #FF6B00;
}

.text-impacto-orange {
  color: #FF6B00;
}
</style>