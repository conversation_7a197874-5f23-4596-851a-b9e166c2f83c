<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Plantillas HTML</h1>
            <p class="mt-1 text-sm text-gray-500">
              Gestiona las plantillas HTML para emails y reportes
            </p>
          </div>
          <button
            @click="openCreateModal"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-impacto-blue hover:bg-impacto-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
          >
            <PlusIcon class="h-5 w-5 mr-2" />
            Nueva Plantilla
          </button>
        </div>
      </div>
    </div>

    <!-- Estadísticas -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <DocumentTextIcon class="h-6 w-6 text-gray-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Total Plantillas</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ statistics.total_templates }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <CheckCircleIcon class="h-6 w-6 text-green-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Activas</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ statistics.active_templates }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <TagIcon class="h-6 w-6 text-blue-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Tipos</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ statistics.template_types }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <DocumentIcon class="h-6 w-6 text-purple-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Tamaño Promedio</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ formatBytes(statistics.avg_size) }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filtros y búsqueda -->
      <div class="bg-white shadow rounded-lg mb-6">
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Búsqueda -->
            <div class="md:col-span-2">
              <label for="search" class="block text-sm font-medium text-gray-700">Buscar</label>
              <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="search"
                  v-model="filters.search"
                  type="text"
                  placeholder="Buscar por nombre, descripción o asunto..."
                  class="focus:ring-impacto-blue focus:border-impacto-blue block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                  @input="debouncedSearch"
                >
              </div>
            </div>

            <!-- Filtro por tipo -->
            <div>
              <label for="tipo-filter" class="block text-sm font-medium text-gray-700">Tipo</label>
              <select
                id="tipo-filter"
                v-model="filters.tipo"
                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm rounded-md"
                @change="loadTemplates"
              >
                <option value="">Todos los tipos</option>
                <option
                  v-for="type in availableTypes"
                  :key="type.value"
                  :value="type.value"
                >
                  {{ type.label }} ({{ type.count }})
                </option>
              </select>
            </div>

            <!-- Filtro por estado -->
            <div>
              <label for="estado-filter" class="block text-sm font-medium text-gray-700">Estado</label>
              <select
                id="estado-filter"
                v-model="filters.activa"
                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm rounded-md"
                @change="loadTemplates"
              >
                <option value="">Todos los estados</option>
                <option value="1">Activas</option>
                <option value="0">Inactivas</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabla de plantillas -->
      <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div v-if="loading" class="p-8 text-center">
          <div class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-impacto-blue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Cargando plantillas...
          </div>
        </div>

        <ul v-else-if="templates.length > 0" role="list" class="divide-y divide-gray-200">
          <li
            v-for="template in templates"
            :key="template.id"
            class="hover:bg-gray-50"
          >
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center min-w-0 flex-1">
                <div class="flex-shrink-0">
                  <span class="text-2xl">{{ template.type_info.icon }}</span>
                </div>
                <div class="ml-4 flex-1 min-w-0">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      {{ template.nombre_interno_plantilla }}
                    </p>
                    <span
                      :class="[
                        'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                        template.status.color === 'green' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      ]"
                    >
                      {{ template.status.text }}
                    </span>
                  </div>
                  <p class="text-sm text-gray-500 truncate">{{ template.descripcion }}</p>
                  <div class="mt-1 flex items-center text-xs text-gray-400 space-x-4">
                    <span>{{ template.type_info.display_name }}</span>
                    <span>{{ formatBytes(template.html_size) }}</span>
                    <span>{{ template.usage_count }} uso(s)</span>
                    <span>{{ formatDate(template.fecha_modificacion) }}</span>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  @click="viewTemplate(template)"
                  class="inline-flex items-center p-2 border border-transparent rounded-full shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  title="Ver plantilla"
                >
                  <EyeIcon class="h-4 w-4" />
                </button>
                <button
                  @click="editTemplate(template)"
                  class="inline-flex items-center p-2 border border-transparent rounded-full shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                  title="Editar plantilla"
                >
                  <PencilIcon class="h-4 w-4" />
                </button>
                <button
                  @click="duplicateTemplate(template)"
                  class="inline-flex items-center p-2 border border-transparent rounded-full shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  title="Duplicar plantilla"
                >
                  <DocumentDuplicateIcon class="h-4 w-4" />
                </button>
                <button
                  v-if="template.can_delete"
                  @click="deleteTemplate(template)"
                  class="inline-flex items-center p-2 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  title="Eliminar plantilla"
                >
                  <TrashIcon class="h-4 w-4" />
                </button>
                <span
                  v-else
                  class="inline-flex items-center p-2 border border-gray-300 rounded-full shadow-sm text-gray-400 bg-gray-100 cursor-not-allowed"
                  title="No se puede eliminar (en uso)"
                >
                  <TrashIcon class="h-4 w-4" />
                </span>
              </div>
            </div>
          </li>
        </ul>

        <div v-else class="p-8 text-center">
          <DocumentTextIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No hay plantillas</h3>
          <p class="mt-1 text-sm text-gray-500">Comienza creando una nueva plantilla HTML.</p>
          <div class="mt-6">
            <button
              @click="openCreateModal"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-impacto-blue hover:bg-impacto-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
            >
              <PlusIcon class="h-5 w-5 mr-2" />
              Nueva Plantilla
            </button>
          </div>
        </div>
      </div>

      <!-- Paginación -->
      <div v-if="pagination.total_pages > 1" class="mt-6">
        <nav class="flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              :disabled="!pagination.has_prev"
              @click="changePage(pagination.current_page - 1)"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button
              :disabled="!pagination.has_next"
              @click="changePage(pagination.current_page + 1)"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando
                <span class="font-medium">{{ (pagination.current_page - 1) * pagination.per_page + 1 }}</span>
                a
                <span class="font-medium">{{ Math.min(pagination.current_page * pagination.per_page, pagination.total_count) }}</span>
                de
                <span class="font-medium">{{ pagination.total_count }}</span>
                resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  :disabled="!pagination.has_prev"
                  @click="changePage(pagination.current_page - 1)"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeftIcon class="h-5 w-5" />
                </button>
                <button
                  v-for="page in visiblePages"
                  :key="page"
                  @click="changePage(page)"
                  :class="[
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                    page === pagination.current_page
                      ? 'z-10 bg-impacto-blue border-impacto-blue text-white'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  ]"
                >
                  {{ page }}
                </button>
                <button
                  :disabled="!pagination.has_next"
                  @click="changePage(pagination.current_page + 1)"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRightIcon class="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </nav>
      </div>
    </div>

    <!-- Modales -->
    <TemplateFormModal
      :show="showFormModal"
      :template="selectedTemplate"
      :is-editing="isEditing"
      @close="closeFormModal"
      @saved="handleTemplateSaved"
    />

    <TemplateViewModal
      :show="showViewModal"
      :template="selectedTemplate"
      @close="closeViewModal"
      @edit="editFromView"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useToast } from '@/composables/useToast'
import { apiFetch } from '@/utils/apiFetch'
// Función debounce simple para evitar dependencia de lodash
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}
import {
  PlusIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  TagIcon,
  DocumentIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  DocumentDuplicateIcon,
  TrashIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/vue/24/outline'

import TemplateFormModal from '@/components/dashboard/admin/templates/TemplateFormModal.vue'
import TemplateViewModal from '@/components/dashboard/admin/templates/TemplateViewModal.vue'

const { showToast } = useToast()

// Estado reactivo
const loading = ref(false)
const templates = ref([])
const statistics = ref({
  total_templates: 0,
  active_templates: 0,
  template_types: 0,
  avg_size: 0
})
const availableTypes = ref([])
const pagination = ref({
  current_page: 1,
  total_pages: 1,
  total_count: 0,
  per_page: 20,
  has_next: false,
  has_prev: false
})

// Filtros
const filters = reactive({
  search: '',
  tipo: '',
  activa: ''
})

// Modales
const showFormModal = ref(false)
const showViewModal = ref(false)
const selectedTemplate = ref(null)
const isEditing = ref(false)

// Computed
const visiblePages = computed(() => {
  const current = pagination.value.current_page
  const total = pagination.value.total_pages
  const pages = []

  let start = Math.max(1, current - 2)
  let end = Math.min(total, current + 2)

  if (end - start < 4) {
    if (start === 1) {
      end = Math.min(total, start + 4)
    } else {
      start = Math.max(1, end - 4)
    }
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// Métodos
const loadTemplates = async (page = 1) => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: pagination.value.per_page.toString()
    })

    if (filters.search) params.append('search', filters.search)
    if (filters.tipo) params.append('tipo', filters.tipo)
    if (filters.activa !== '') params.append('activa', filters.activa)

    const data = await apiFetch(`admin_get_html_templates.php?${params}`)

    if (data.success) {
      templates.value = data.data.templates
      statistics.value = data.data.statistics
      availableTypes.value = data.data.available_types
      pagination.value = data.data.pagination
    } else {
      showToast('Error cargando plantillas: ' + data.message, 'error')
    }
  } catch (error) {
    showToast('Error de conexión al cargar plantillas', 'error')
    console.error('Error loading templates:', error)
  } finally {
    loading.value = false
  }
}

const debouncedSearch = debounce(() => {
  loadTemplates(1)
}, 500)

const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.total_pages) {
    loadTemplates(page)
  }
}

// Acciones de plantillas
const openCreateModal = () => {
  selectedTemplate.value = null
  isEditing.value = false
  showFormModal.value = true
}

const editTemplate = (template) => {
  selectedTemplate.value = template
  isEditing.value = true
  showFormModal.value = true
}

const viewTemplate = (template) => {
  selectedTemplate.value = template
  showViewModal.value = true
}

const editFromView = () => {
  showViewModal.value = false
  isEditing.value = true
  showFormModal.value = true
}

const duplicateTemplate = async (template) => {
  try {
    const data = await apiFetch('admin_duplicate_html_template.php', {
      method: 'POST',
      body: JSON.stringify({
        template_id: template.id
      })
    })

    if (data.success) {
      showToast('Plantilla duplicada exitosamente', 'success')
      loadTemplates(pagination.value.current_page)
    } else {
      showToast('Error duplicando plantilla: ' + data.message, 'error')
    }
  } catch (error) {
    showToast('Error de conexión al duplicar plantilla', 'error')
    console.error('Error duplicating template:', error)
  }
}

const deleteTemplate = async (template) => {
  if (!confirm(`¿Estás seguro de que quieres eliminar la plantilla "${template.nombre_interno_plantilla}"?`)) {
    return
  }

  try {
    const data = await apiFetch(`admin_delete_html_template.php?id=${template.id}`, {
      method: 'DELETE'
    })

    if (data.success) {
      showToast('Plantilla eliminada exitosamente', 'success')
      loadTemplates(pagination.value.current_page)
    } else {
      showToast('Error eliminando plantilla: ' + data.message, 'error')
    }
  } catch (error) {
    showToast('Error de conexión al eliminar plantilla', 'error')
    console.error('Error deleting template:', error)
  }
}

// Manejadores de modales
const closeFormModal = () => {
  showFormModal.value = false
  selectedTemplate.value = null
  isEditing.value = false
}

const closeViewModal = () => {
  showViewModal.value = false
  selectedTemplate.value = null
}

const handleTemplateSaved = () => {
  closeFormModal()
  loadTemplates(pagination.value.current_page)
}

// Utilidades
const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Lifecycle
onMounted(() => {
  loadTemplates()
})
</script>
