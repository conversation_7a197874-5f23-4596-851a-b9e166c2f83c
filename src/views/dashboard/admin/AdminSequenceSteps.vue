<template>
  <div class="p-8">
    <div v-if="isLoading" class="text-center">
      <p class="text-gray-600">Cargando detalles de la secuencia...</p>
    </div>
    <div v-else-if="error" class="p-6 text-center text-red-500">
      <p>Error al cargar la secuencia: {{ error }}</p>
    </div>
    <div v-else-if="sequence">
            <router-link :to="{ name: 'AdminSequences' }" class="text-blue-600 hover:text-blue-800 mb-4 inline-block">&larr; Volver a todas las secuencias</router-link>
      <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ sequence.name }}</h1>
      <p class="text-gray-600 mb-6">{{ sequence.description }}</p>

      <!-- Gestión de los pasos -->
      <div class="bg-white rounded-lg shadow-md mt-8">
        <div class="p-6 border-b flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-800">Pasos de la Secuencia</h2>
            <button @click="openAddStepModal" class="bg-impacto-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Añadir Nuevo Paso
            </button>
        </div>

        <!-- Lista de pasos -->
        <div v-if="!sequence.steps || !sequence.steps.length" class="p-6 text-center text-gray-500">
            <p>Esta secuencia todavía no tiene ningún paso. ¡Añade el primero!</p>
        </div>
        <ul v-else class="divide-y divide-gray-200">
            <li v-for="step in sequence.steps" :key="step.id" class="p-4 flex items-center justify-between hover:bg-gray-50">
                <div class="flex items-center">
                    <!-- Icono para drag-and-drop futuro -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-4 cursor-grab" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
                    <div>
                        <p class="font-semibold text-gray-800">Paso {{ step.step_order }}: {{ step.name }}</p>
                        <p class="text-sm text-gray-600">Asunto: {{ step.subject }}</p>
                        <p class="text-sm text-gray-500">
                            Enviar
                            <span class="font-medium">{{ step.delay_days }}</span>
                            días después del paso anterior.
                        </p>
                    </div>
                </div>
                <div class="flex items-center gap-4">
                     <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="step.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                        {{ step.is_active ? 'Activo' : 'Inactivo' }}
                    </span>
                    <button @click="openEditStepModal(step)" class="text-indigo-600 hover:text-indigo-900">Editar</button>
                    <button @click="deleteStep(step)" class="text-red-600 hover:text-red-900">Eliminar</button>
                </div>
            </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Modal para Pasos - Versión Robusta -->
  <step-form-modal-robust
    :show="showStepModal"
    :step="selectedStep"
    :sequence-id="sequence?.id"
    @close="showStepModal = false"
    @step-saved="handleStepSaved"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { apiFetch } from '@/utils/apiFetch';
import StepFormModalRobust from '@/components/dashboard/admin/sequences/StepFormModalRobust.vue';

const props = defineProps({
  uuid: {
    type: String,
    required: true,
  },
});

interface SequenceStep {
  id: number;
  uuid: string;
  name: string;
  subject: string;
  body: string;
  delay_days: number;
  step_order: number;
  is_active: number;
}

interface SequenceDetails {
  id: number;
  uuid: string;
  name: string;
  description: string;
  is_active: number;
  trigger_event: string;
  steps: SequenceStep[];
}

const sequence = ref<SequenceDetails | null>(null);
const showStepModal = ref(false);
const selectedStep = ref<any | null>(null);
const isSubmitting = ref(false);
const isLoading = ref(true);
const error = ref<string | null>(null);

const fetchSequenceDetails = async () => {
  isLoading.value = true;
  error.value = null;
  try {
    // Este endpoint se creará en el siguiente paso
    const response = await apiFetch(`/api/admin_get_sequence_details.php?uuid=${props.uuid}`);
    if (response.success) {
      sequence.value = response.data;
    } else {
      throw new Error(response.message || 'Error al cargar los detalles de la secuencia');
    }
  } catch (e: any) {
    error.value = e.message;
  } finally {
    isLoading.value = false;
  }
};

const handleStepSaved = async (_stepData: any) => {
  // El modal robusto maneja el guardado internamente
  showStepModal.value = false;
  await fetchSequenceDetails(); // Recargar la lista de pasos
};

const openAddStepModal = () => {
    selectedStep.value = null;
    showStepModal.value = true;
};

const openEditStepModal = (step: SequenceStep) => {
    selectedStep.value = { ...step, is_active: !!step.is_active };
    showStepModal.value = true;
};

const deleteStep = async (step: SequenceStep) => {
    if (confirm(`¿Estás seguro de que quieres eliminar el paso "${step.name}"?`)) {
        isSubmitting.value = true; // Reutilizamos para el estado de carga
        error.value = null;
        try {
            const response = await apiFetch('/api/admin_delete_sequence_step.php', {
                method: 'POST',
                body: JSON.stringify({ id: step.id })
            });

            if (!response.success) {
                throw new Error(response.message || 'Ocurrió un error al eliminar el paso.');
            }

            await fetchSequenceDetails(); // Recargar la lista de pasos
        } catch (e: any) {
            error.value = e.message;
            console.error("Error al eliminar el paso:", e);
        } finally {
            isSubmitting.value = false;
        }
    }
};

onMounted(() => {
  fetchSequenceDetails();
});
</script>
