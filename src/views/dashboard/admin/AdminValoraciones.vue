<template>
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>
        Administración de Valoraciones
      </h1>
      <p class="text-gray-600 mt-1">
        Gestiona todas las valoraciones realizadas en la plataforma.
      </p>
    </div>

    <!-- Indicador de carga -->
    <div v-if="isLoading" class="flex justify-center items-center py-20">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-impacto-blue"></div>
    </div>

    <div v-else>
      <!-- Barra de acciones -->
      <div class="bg-white rounded-lg shadow-md p-4 mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div class="flex flex-col md:flex-row md:items-center gap-4">
          <!-- Búsqueda -->
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="Buscar por dirección, ref. catastral..." 
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue w-full md:w-64"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <!-- Filtros -->
          <div class="flex items-center gap-2">
            <select 
              v-model="filterTipo" 
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los tipos</option>
              <option value="Piso">Pisos</option>
              <option value="Casa">Casas</option>
              <option value="Ático">Áticos</option>
              <option value="Chalet">Chalets</option>
              <option value="Local">Locales</option>
              <option value="Oficina">Oficinas</option>
              <option value="Garaje">Garajes</option>
              <option value="Terreno">Terrenos</option>
              <option value="Edificio">Edificios</option>
              <option value="Otro">Otro</option>
            </select>
            <select 
              v-model="filterValoradorIdentifier"
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los valoradores</option>
              <!-- TODO: Poblar dinámicamente con valoradores existentes -->
              <option v-for="valorador in listaValoradoresParaFiltro" :key="valorador.client_identifier" :value="valorador.client_identifier">
                {{ valorador.nombre_display }}
              </option>
            </select>
          </div>
        </div>
        
        <!-- Botón para exportar -->
        <button 
          @click="exportarValoraciones" 
          class="bg-impacto-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Exportar (Próximamente)
        </button>
      </div>

      <!-- Tabla de valoraciones -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Propiedad
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tipo
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor Estimado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valorador
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Lead / Usuario
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fecha
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-if="valoraciones.length === 0 && !isLoading">
                <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                  No se encontraron valoraciones que coincidan con los criterios.
                </td>
              </tr>
              <tr v-for="(valoracion) in valoraciones" :key="valoracion.id" class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <div class="flex flex-col">
                    <div class="text-sm font-medium text-gray-900">{{ valoracion.direccion }}</div>
                    <div class="text-xs text-gray-500">{{ valoracion.referencia_catastral || 'N/A' }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                    {{ valoracion.tipo_principal }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ formatCurrency(valoracion.valor_estimado_min) }} - {{ formatCurrency(valoracion.valor_estimado_max) }}</div>
                  <div class="text-xs text-gray-500">
                    <span v-if="valoracion.superficie_construida">{{ valoracion.superficie_construida }} m²</span>
                    <span v-if="valoracion.num_habitaciones"> | {{ valoracion.num_habitaciones }} hab</span>
                    <span v-if="valoracion.num_banos"> | {{ valoracion.num_banos }} b</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ valoracion.nombre_valorador || 'N/A' }}</div>
                  <div class="text-xs text-gray-500">{{ valoracion.client_id }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div v-if="valoracion.nombre_lead" class="text-sm text-gray-900">Lead: {{ valoracion.nombre_lead }}</div>
                  <div v-else-if="valoracion.nombre_usuario_solicitante" class="text-sm text-gray-900">Usuario: {{ valoracion.nombre_usuario_solicitante }}</div>
                  <div v-else class="text-sm text-gray-500">No asociado</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(valoracion.fecha_creacion_valoracion) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end gap-2">
                    <button 
                      @click="verDetalles(valoracion)" 
                      class="text-blue-600 hover:text-blue-900"
                      title="Ver detalles"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Paginación -->
        <div v-if="totalPages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button 
              @click="currentPage > 1 ? currentPage-- : null" 
              :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button 
              @click="currentPage < totalPages ? currentPage++ : null" 
              :disabled="currentPage === totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando <span class="font-medium">{{ Math.max(1, (currentPage - 1) * itemsPerPage + 1) }}</span>
                a <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, totalItems) }}</span>
                de <span class="font-medium">{{ totalItems }}</span> resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button 
                  @click="currentPage > 1 ? currentPage-- : null" 
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Anterior</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
                <template v-for="(page, index) in paginationRange" :key="index">
                  <button
                    v-if="typeof page === 'number'"
                    @click="currentPage = page" 
                    :class="[
                      page === currentPage ? 'z-10 bg-impacto-blue border-impacto-blue text-white' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                      'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                    ]"
                  >
                    {{ page }}
                  </button>
                  <span v-else class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                    {{ page }}
                  </span>
                </template>
                <button 
                  @click="currentPage < totalPages ? currentPage++ : null" 
                  :disabled="currentPage === totalPages"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Siguiente</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de detalle de valoración -->
  <div v-if="isDetalleModalOpen && selectedValoracionDetalle" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4">
    <div class="bg-white p-6 sm:p-8 rounded-xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 ease-out">
      <div class="flex justify-between items-start mb-6 pb-4 border-b border-gray-200">
        <div>
          <h2 class="text-2xl font-bold text-impacto-blue flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 mr-2 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            Detalle de la Valoración
          </h2>
          <p class="text-sm text-gray-500 mt-1">ID Valoración: {{ selectedValoracionDetalle.id }}</p>
        </div>
        <button @click="closeDetalleModal" class="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div class="space-y-6">
        <!-- Sección Principal: Propiedad y Valor -->
        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-impacto-blue mb-3 border-b border-gray-200 pb-2">Información de la Propiedad</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3 text-sm">
            <div class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Dirección:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.direccion }}</span></div>
            <div class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Ref. Catastral:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.referencia_catastral || 'N/A' }}</span></div>
            <div class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Tipo:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.tipo_principal }} <span v-if="selectedValoracionDetalle.subtipo">({{ selectedValoracionDetalle.subtipo }})</span></span></div>
            <div class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Superficie:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.superficie_construida || 'N/A' }} m²</span></div>
            <div class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Habitaciones:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.num_habitaciones ?? 'N/A' }}</span></div>
            <div class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Baños:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.num_banos ?? 'N/A' }}</span></div>
            <div class="flex items-start sm:col-span-2"><strong class="w-32 shrink-0 text-gray-600">Estado:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.estado_conservacion || 'N/A' }}</span></div>
          </div>
          <div class="mt-4 pt-4 border-t border-gray-200">
            <h4 class="text-md font-semibold text-impacto-blue mb-2">Valor Estimado</h4>
            <p class="text-2xl font-bold text-green-600">{{ formatCurrency(selectedValoracionDetalle.valor_estimado_min) }} - {{ formatCurrency(selectedValoracionDetalle.valor_estimado_max) }}</p>
          </div>
        </div>

        <!-- Sección: Origen y Fechas -->
        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
           <h3 class="text-lg font-semibold text-impacto-blue mb-3 border-b border-gray-200 pb-2">Origen y Contexto</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3 text-sm">
            <div class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Valorador:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.nombre_valorador || 'N/A' }} (ID: {{ selectedValoracionDetalle.client_id }})</span></div>
            <div v-if="selectedValoracionDetalle.nombre_lead" class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Lead:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.nombre_lead }} (ID: {{ selectedValoracionDetalle.lead_id }})</span></div>
            <div v-else-if="selectedValoracionDetalle.nombre_usuario_solicitante" class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Usuario:</strong> <span class="text-gray-800">{{ selectedValoracionDetalle.nombre_usuario_solicitante }} (ID: {{ selectedValoracionDetalle.user_id }})</span></div>
            <div v-else class="flex items-start"><strong class="w-32 shrink-0 text-gray-600">Origen:</strong> <span class="text-gray-800">No asociado</span></div>
            <div class="flex items-start sm:col-span-2"><strong class="w-32 shrink-0 text-gray-600">Fecha Valoración:</strong> <span class="text-gray-800">{{ formatDate(selectedValoracionDetalle.fecha_creacion_valoracion) }}</span></div>
          </div>
        </div>

        <!-- Sección: Datos Adicionales (Prompt) -->
        <div v-if="selectedValoracionDetalle.json_prompt_details" class="bg-gray-50 p-4 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-impacto-blue mb-2">Datos Adicionales del Prompt</h3>
          <pre class="bg-white p-3 rounded-md text-xs whitespace-pre-wrap max-h-60 overflow-y-auto border border-gray-200">{{ selectedValoracionDetalle.json_prompt_details }}</pre>
        </div>
      </div>

      <div class="mt-8 pt-6 border-t border-gray-200 text-right">
        <button 
          @click="closeDetalleModal" 
          class="bg-impacto-blue hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue shadow-md hover:shadow-lg"
        >
          Cerrar
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { apiFetch } from '@/utils/apiFetch';
import { useToast } from '@/composables/useToast';

interface AdminValoracionItem {
  id: number;
  user_id: number | null;
  client_id: string | null; // client_identifier del valorador
  lead_id: number | null;
  direccion: string;
  referencia_catastral: string | null;
  tipo_principal: string;
  subtipo: string | null;
  superficie_construida: number | null;
  superficie_util: number | null;
  num_habitaciones: number | null;
  num_banos: number | null;
  valor_estimado_min: number | null;
  valor_estimado_max: number | null;
  valor_estimado_medio: number | null;
  rango_confianza: number | null;
  estado_conservacion: string | null;
  json_prompt_details: string | null; // O podría ser un objeto si se parsea
  json_response_data: string | null; // O podría ser un objeto si se parsea
  fecha_creacion_valoracion: string;
  nombre_usuario_solicitante: string | null;
  email_usuario_solicitante: string | null;
  nombre_valorador: string | null; // Nombre display del cliente_valorador
  nombre_lead: string | null;
  email_lead: string | null;
  estado_lead: string | null;
}

interface ValoradorParaFiltro {
  client_identifier: string;
  nombre_display: string;
}

interface AdminValoracionesApiResponse {
  success: boolean;
  data: AdminValoracionItem[];
  meta: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  message?: string;
}

const { showToast } = useToast();

// Estado de carga
const isLoading = ref(true);

// Datos de valoraciones
const valoraciones = ref<AdminValoracionItem[]>([]);
const listaValoradoresParaFiltro = ref<ValoradorParaFiltro[]>([]); // Para el dropdown de filtro

// Filtros y búsqueda
const searchQuery = ref('');
const filterTipo = ref('');
const filterValoradorIdentifier = ref(''); // Usará client_identifier

// Paginación
const currentPage = ref(1);
const itemsPerPage = ref(10); // Puede ser configurable
const totalItems = ref(0);
const totalPages = ref(1);

// Estado para el modal de detalle de valoración
const isDetalleModalOpen = ref(false);
const selectedValoracionDetalle = ref<AdminValoracionItem | null>(null);

const fetchValoraciones = async () => {
  isLoading.value = true;
  try {
    let url = `/api/admin_get_valoraciones.php?page=${currentPage.value}&limit=${itemsPerPage.value}`;
    if (searchQuery.value) {
      url += `&search=${encodeURIComponent(searchQuery.value)}`;
    }
    if (filterTipo.value) {
      url += `&tipo=${encodeURIComponent(filterTipo.value)}`;
    }
    if (filterValoradorIdentifier.value) {
      url += `&valorador_id=${encodeURIComponent(filterValoradorIdentifier.value)}`; // El backend espera 'valorador_id' como client_identifier
    }

    const response = await apiFetch(url) as AdminValoracionesApiResponse;

    if (response.success && response.data) {
      valoraciones.value = response.data;
      totalItems.value = response.meta.totalItems;
      totalPages.value = response.meta.totalPages;
      // Ajustar currentPage si está fuera de rango después de filtrar/cambiar página
      if (response.meta.currentPage > response.meta.totalPages && response.meta.totalPages > 0) {
          currentPage.value = response.meta.totalPages;
      } else {
          currentPage.value = response.meta.currentPage;
      }
    } else {
      showToast(response.message || 'Error al cargar las valoraciones.', 'error');
      valoraciones.value = [];
      totalItems.value = 0;
      totalPages.value = 1;
      // currentPage.value = 1; // No resetear si es un error de carga, podría ser por filtros sin resultados
    }
  } catch (error: any) {
    console.error('Error en fetchValoraciones:', error);
    showToast(error.message || 'Se produjo un error inesperado al cargar valoraciones.', 'error');
    valoraciones.value = [];
    totalItems.value = 0;
    totalPages.value = 1;
    // currentPage.value = 1;
  } finally {
    isLoading.value = false;
  }
};

// TODO: Crear una función para cargar los valoradores para el filtro
const fetchValoradoresParaFiltro = async () => {
  try {
    // Asumimos un endpoint que devuelve una lista simple de valoradores activos
    // o podríamos obtenerlos de alguna otra manera si ya están disponibles globalmente.
    // Por ahora, usamos los que vienen en las valoraciones, aunque no es ideal.
    // Una mejor aproximación sería un endpoint específico /api/get_all_client_valoradores_simplified
    const response = await apiFetch('/api/admin_get_valoradores.php?limit=500&status=1'); //Obtener todos los activos
    if (response.success && response.data) {
      listaValoradoresParaFiltro.value = response.data.map((v: any) => ({ // 'any' provisional
        client_identifier: v.client_identifier,
        nombre_display: v.nombre_display,
      }));
    }
  } catch (error) {
    console.error("Error cargando valoradores para filtro:", error);
    // No mostrar toast por esto, es secundario
  }
};

const formatCurrency = (value: number | null): string => {
  if (value === null || value === undefined) return 'N/A';
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'EUR',
    maximumFractionDigits: 0,
  }).format(value);
};

const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Fecha inválida';
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    return 'Fecha inválida';
  }
};

const verDetalles = (valoracion: AdminValoracionItem) => {
  selectedValoracionDetalle.value = valoracion;
  isDetalleModalOpen.value = true;
  // showToast(`Detalles de valoración ID ${valoracion.id} - Próximamente`, 'info');
  // TODO: Implementar modal o navegación a página de detalles de valoración
  // Podríamos usar un modal para mostrar valoracion.json_prompt_details y valoracion.json_response_data
};

const closeDetalleModal = () => {
  isDetalleModalOpen.value = false;
  selectedValoracionDetalle.value = null;
};

const exportarValoraciones = () => {
  showToast('Funcionalidad de exportar todas las valoraciones - Próximamente', 'info');
  // TODO: Implementar lógica de exportación (CSV, Excel)
};

onMounted(() => {
  fetchValoraciones();
  fetchValoradoresParaFiltro();
});

watch(currentPage, () => {
  fetchValoraciones();
});
watch([filterTipo, filterValoradorIdentifier], () => {
  currentPage.value = 1; 
  fetchValoraciones();
});

let searchTimeout: number;
watch(searchQuery, () => {
  clearTimeout(searchTimeout);
  searchTimeout = window.setTimeout(() => {
    currentPage.value = 1; 
    fetchValoraciones();
  }, 500); 
});

const paginationRange = computed(() => {
  const delta = 1; // Menos números alrededor del actual para admin
  const rangeNumbers: number[] = [];
  const left = currentPage.value - delta;
  const right = currentPage.value + delta + 1;
  let l: number | undefined = undefined;

  for (let i = 1; i <= totalPages.value; i++) {
    if (i === 1 || i === totalPages.value || (i >= left && i < right)) {
      rangeNumbers.push(i);
    }
  }

  const rangeWithDots: (number | string)[] = [];
  if (totalPages.value <= (1 + delta * 2) + 2 ) { // Si no hay muchos puntos para omitir, mostrar todos
     for(let i = 1; i <= totalPages.value; i++){
       rangeWithDots.push(i);
     }
     return rangeWithDots;
  }


  for (const i of rangeNumbers) {
    if (l !== undefined) {
      if (i - l === 2) {
        rangeWithDots.push(l + 1);
      } else if (i - l !== 1) {
        rangeWithDots.push('...');
      }
    }
    rangeWithDots.push(i);
    l = i;
  }
  return rangeWithDots;
});
</script>

<style scoped>
/* Estilos de impacto-blue y impacto-orange como en AdminDashboard */
.bg-impacto-blue {
  background-color: #003B6D;
}
.text-impacto-blue {
  color: #003B6D;
}
.border-impacto-blue {
  border-color: #003B6D;
}
.focus\\:ring-impacto-blue:focus {
  --tw-ring-color: #003B6D;
}
.focus\\:border-impacto-blue:focus {
  border-color: #003B6D;
}

.text-impacto-orange {
  color: #FF6B00;
}

/* Ajustes menores para la tabla */
.table th, .table td {
  /* white-space: nowrap; /* Puede ser demasiado restrictivo */
}
</style>