<template>
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        Administración de Valoradores
      </h1>
      <p class="text-gray-600 mt-1">
        Gestiona todos los valoradores de la plataforma.
      </p>
    </div>

    <!-- Indicador de carga -->
    <div v-if="isLoading" class="flex justify-center items-center py-20">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-impacto-blue"></div>
    </div>

    <div v-else>
      <!-- Barra de acciones -->
      <div class="bg-white rounded-lg shadow-md p-4 mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div class="flex flex-col md:flex-row md:items-center gap-4">
          <!-- Búsqueda -->
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="Buscar valoradores..." 
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue w-full md:w-64"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <!-- Filtros -->
          <div class="flex items-center gap-2">
            <select 
              v-model="filterStatus" 
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los estados</option>
              <option value="1">Activos</option>
              <option value="0">Inactivos</option>
            </select>
          </div>
        </div>
        <button 
          @click="openCreateValoradorModal"
          class="bg-impacto-blue hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200 flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Crear Valorador
        </button>
      </div>

      <!-- Tabla de valoradores -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valorador
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usuario Asociado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Identificador Cliente
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fecha Creación
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-if="valoradores.length === 0 && !isLoading">
                <td colspan="6" class="px-6 py-10 text-center text-gray-500">
                  No se encontraron valoradores que coincidan con los criterios de búsqueda.
                </td>
              </tr>
              <tr v-for="(valorador, index) in valoradores" :key="valorador.id || index" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 rounded-lg overflow-hidden">
                      <img v-if="valorador.logo_url" :src="valorador.logo_url" alt="Logo" class="h-full w-full object-contain" />
                      <div v-else class="h-full w-full bg-gray-200 flex items-center justify-center text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ valorador.nombre_display }}</div>
                      <div class="text-sm text-gray-500">ID App: {{ valorador.id }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ valorador.nombre_usuario_asociado || 'No asignado' }}</div>
                  <div class="text-sm text-gray-500">{{ valorador.email_usuario_asociado || `User ID: ${valorador.user_id || 'N/A'}` }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">
                    <a :href="`https://${valorador.client_identifier}.inmoautomation.com/valora/`" target="_blank" class="text-blue-600 hover:text-blue-800">
                      {{ valorador.client_identifier }}
                    </a>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span 
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                    :class="valorador.activo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                    :title="valorador.activo ? 'Activo' : 'Inactivo'"
                  >
                    {{ valorador.activo ? 'Activo' : 'Inactivo' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(valorador.fecha_creacion) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end gap-2">
                    <button 
                      @click="editValoradorConfig(valorador)" 
                      class="text-indigo-600 hover:text-indigo-900"
                      title="Editar configuración"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button 
                      @click="viewValoradorStats(valorador)" 
                      class="text-blue-600 hover:text-blue-800"
                      title="Ver estadísticas"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </button>
                    <a 
                      :href="`https://${valorador.client_identifier}.inmoautomation.com/valora/`"
                      target="_blank"
                      class="text-green-600 hover:text-green-800"
                      title="Abrir valorador"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </a>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Paginación -->
        <div v-if="totalPages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button 
              @click="currentPage > 1 ? currentPage-- : null" 
              :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button 
              @click="currentPage < totalPages ? currentPage++ : null" 
              :disabled="currentPage === totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando <span class="font-medium">{{ Math.max(1, (currentPage - 1) * itemsPerPage + 1) }}</span>
                a <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, totalItems) }}</span>
                de <span class="font-medium">{{ totalItems }}</span> resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button 
                  @click="currentPage > 1 ? currentPage-- : null" 
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Anterior</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
                <template v-for="(page, index) in paginationRange" :key="index">
                  <button
                    v-if="typeof page === 'number'"
                    @click="currentPage = page" 
                    :class="[
                      page === currentPage ? 'z-10 bg-impacto-blue border-impacto-blue text-white' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                      'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                    ]"
                  >
                    {{ page }}
                  </button>
                  <span v-else class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                    {{ page }}
                  </span>
                </template>
                <button 
                  @click="currentPage < totalPages ? currentPage++ : null" 
                  :disabled="currentPage === totalPages"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Siguiente</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de edición -->
    <div v-if="isEditModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div class="bg-white p-8 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold mb-4">Editar Valorador</h2>
        <div class="mb-4">
          <label for="nombre_display" class="block text-sm font-medium text-gray-700">Nombre</label>
          <input 
            id="nombre_display"
            v-model="editFormData.nombre_display"
            class="mt-1 block w-full border-gray-300 rounded-md"
          />
        </div>
        <div class="mb-4">
          <label for="logo_url" class="block text-sm font-medium text-gray-700">Logo URL</label>
          <input 
            id="logo_url"
            v-model="editFormData.logo_url"
            class="mt-1 block w-full border-gray-300 rounded-md"
          />
        </div>
        <div class="mb-4">
          <label for="activo" class="block text-sm font-medium text-gray-700">Estado</label>
          <select 
            id="activo"
            v-model="editFormData.activo"
            class="mt-1 block w-full border-gray-300 rounded-md"
          >
            <option value="1">Activo</option>
            <option value="0">Inactivo</option>
          </select>
        </div>
        <div class="mt-4 flex justify-end">
          <button 
            @click="closeEditModal"
            class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md mr-2"
          >
            Cancelar
          </button>
          <button 
            @click="handleUpdateValorador"
            class="bg-impacto-blue hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md"
          >
            Actualizar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { apiFetch } from '@/utils/apiFetch';
import { useToast } from '@/composables/useToast';

// Interfaz para los datos de un valorador en la lista de admin
interface ValoradorAdminItem {
  id: number;
  user_id: number | null;
  client_identifier: string;
  nombre_display: string;
  logo_url: string | null;
  activo: number; // 0 para inactivo, 1 para activo
  fecha_creacion: string;
  nombre_usuario_asociado: string | null;
  email_usuario_asociado: string | null;
  // considerar añadir más campos si son necesarios para la vista o acciones
  // config_valorador_json: string | null;
  // config_email_secuencia_json: string | null;
}

interface ValoradorApiResponse {
  success: boolean;
  data: ValoradorAdminItem[];
  meta: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  message?: string;
}

const { showToast } = useToast(); // Usar showToast directamente

const isLoading = ref(true);
const valoradores = ref<ValoradorAdminItem[]>([]);
const searchQuery = ref('');
const filterStatus = ref<string>(''); 
const currentPage = ref(1);
const itemsPerPage = ref(10);
const totalItems = ref(0);
const totalPages = ref(1);

// Estado para el modal de edición
const isEditModalOpen = ref(false);
const currentValorador = ref<ValoradorAdminItem | null>(null);
const editFormData = ref({
  id: 0,
  nombre_display: '',
  logo_url: '',
  activo: 1, // Por defecto activo
});

const fetchValoradores = async () => {
  isLoading.value = true;
  try {
    let url = `/api/admin_get_valoradores.php?page=${currentPage.value}&limit=${itemsPerPage.value}`;
    if (searchQuery.value) {
      url += `&search=${encodeURIComponent(searchQuery.value)}`;
    }
    if (filterStatus.value !== '') {
      url += `&status=${filterStatus.value}`;
    }
    
    const response = await apiFetch(url) as ValoradorApiResponse;

    if (response.success && response.data) {
      valoradores.value = response.data;
      totalItems.value = response.meta.totalItems;
      totalPages.value = response.meta.totalPages;
      if (response.meta.currentPage > response.meta.totalPages && response.meta.totalPages > 0) {
        currentPage.value = response.meta.totalPages;
      } else {
        currentPage.value = response.meta.currentPage;
      }
    } else {
      showToast(response.message || 'Error al cargar los valoradores.', 'error');
      valoradores.value = [];
      totalItems.value = 0;
      totalPages.value = 1;
      currentPage.value = 1;
    }
  } catch (error: any) {
    console.error('Error en fetchValoradores:', error);
    showToast(error.message || 'Se produjo un error inesperado al cargar valoradores.', 'error');
    valoradores.value = [];
    totalItems.value = 0;
    totalPages.value = 1;
    currentPage.value = 1;
  } finally {
    isLoading.value = false;
  }
};

const formatDate = (dateString: string): string => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const editValoradorConfig = (valorador: ValoradorAdminItem) => {
  currentValorador.value = valorador;
  editFormData.value = {
    id: valorador.id,
    nombre_display: valorador.nombre_display,
    logo_url: valorador.logo_url || '',
    activo: valorador.activo,
  };
  isEditModalOpen.value = true;
  // showToast(`Editar config de ${valorador.nombre_display} (ID: ${valorador.id}) - Próximamente`, 'info');
  // TODO: Implementar navegación a página de edición o modal
};

const closeEditModal = () => {
  isEditModalOpen.value = false;
  currentValorador.value = null;
  editFormData.value = { id: 0, nombre_display: '', logo_url: '', activo: 1 };
};

const handleUpdateValorador = async () => {
  if (!currentValorador.value) return;

  try {
    const response = await apiFetch(`/api/admin_update_valorador.php`, {
      method: 'POST',
      body: JSON.stringify(editFormData.value),
    });

    if (response.success) {
      showToast(response.message || 'Valorador actualizado correctamente.', 'success');
      closeEditModal();
      fetchValoradores(); // Recargar la lista
    } else {
      showToast(response.message || 'Error al actualizar el valorador.', 'error');
    }
  } catch (error: any) {
    console.error('Error en handleUpdateValorador:', error);
    showToast(error.message || 'Error de red al actualizar el valorador.', 'error');
  }
};

const viewValoradorStats = (valorador: ValoradorAdminItem) => {
  showToast(`Ver stats de ${valorador.nombre_display} (ID: ${valorador.id}) - Próximamente`, 'info');
  // TODO: Implementar navegación a página de estadísticas específicas del valorador
};

const openCreateValoradorModal = () => {
  showToast('Abrir modal/página para crear nuevo valorador - Próximamente', 'info');
  // TODO: Implementar modal o navegación a ruta de creación
};

onMounted(fetchValoradores);

watch(currentPage, () => {
    fetchValoradores();
});
watch(filterStatus, () => {
    currentPage.value = 1; 
    fetchValoradores();
});

let searchTimeout: number;
watch(searchQuery, () => {
  clearTimeout(searchTimeout);
  searchTimeout = window.setTimeout(() => {
    currentPage.value = 1; 
    fetchValoradores();
  }, 500); 
});

const paginationRange = computed(() => {
  const delta = 2;
  const rangeNumbers: number[] = [];
  const left = currentPage.value - delta;
  const right = currentPage.value + delta + 1;
  let l: number | undefined = undefined;

  for (let i = 1; i <= totalPages.value; i++) {
    if (i === 1 || i === totalPages.value || (i >= left && i < right)) {
      rangeNumbers.push(i);
    }
  }

  const rangeWithDots: (number | string)[] = [];
  for (const i of rangeNumbers) {
    if (l !== undefined) {
      if (i - l === 2) {
        rangeWithDots.push(l + 1);
      } else if (i - l !== 1) {
        rangeWithDots.push('...');
      }
    }
    rangeWithDots.push(i);
    l = i;
  }
  return rangeWithDots;
});

</script>

<style scoped>
/* Estilos de impacto-blue y impacto-orange como en AdminDashboard */
.bg-impacto-blue {
  background-color: #003B6D;
}
.text-impacto-blue {
  color: #003B6D;
}
.border-impacto-blue {
  border-color: #003B6D;
}
.focus\:ring-impacto-blue:focus {
  --tw-ring-color: #003B6D;
}
.focus\:border-impacto-blue:focus {
  border-color: #003B6D;
}

.text-impacto-orange {
  color: #FF6B00;
}

/* Ajustes menores para la tabla */
.table th, .table td {
  white-space: nowrap; /* Prevenir saltos de línea no deseados en celdas */
}
</style>
