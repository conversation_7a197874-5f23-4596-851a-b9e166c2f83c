<template>
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        Administración de Facturación
      </h1>
      <p class="text-gray-600 mt-1">
        Gestiona las suscripciones y facturación de la plataforma.
      </p>
    </div>

    <!-- Indicador de carga -->
    <div v-if="isLoading" class="flex justify-center items-center py-20">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-impacto-blue"></div>
    </div>

    <div v-else>
      <!-- Tarjetas de estadísticas -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Ingresos Mensuales</p>
              <p class="text-2xl font-bold text-gray-800">{{ formatCurrency(estadisticas.ingresosMensuales) }}</p>
            </div>
            <div class="bg-blue-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="mt-4">
            <p class="text-sm text-gray-500 flex items-center" :class="estadisticas.crecimientoMensual >= 0 ? 'text-green-500' : 'text-red-500'">
              <span v-if="estadisticas.crecimientoMensual >= 0" class="mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </span>
              <span v-else class="mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </span>
              {{ Math.abs(estadisticas.crecimientoMensual) }}% desde el mes pasado
            </p>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Suscripciones Activas</p>
              <p class="text-2xl font-bold text-gray-800">{{ estadisticas.suscripcionesActivas }}</p>
            </div>
            <div class="bg-green-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="mt-4">
            <p class="text-sm text-gray-500 flex items-center" :class="estadisticas.crecimientoSuscripciones >= 0 ? 'text-green-500' : 'text-red-500'">
              <span v-if="estadisticas.crecimientoSuscripciones >= 0" class="mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </span>
              <span v-else class="mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </span>
              {{ Math.abs(estadisticas.crecimientoSuscripciones) }}% desde el mes pasado
            </p>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Tasa de Conversión</p>
              <p class="text-2xl font-bold text-gray-800">{{ estadisticas.tasaConversion }}%</p>
            </div>
            <div class="bg-purple-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
          <div class="mt-4">
            <p class="text-sm text-gray-500 flex items-center" :class="estadisticas.crecimientoConversion >= 0 ? 'text-green-500' : 'text-red-500'">
              <span v-if="estadisticas.crecimientoConversion >= 0" class="mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </span>
              <span v-else class="mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </span>
              {{ Math.abs(estadisticas.crecimientoConversion) }}% desde el mes pasado
            </p>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Tasa de Cancelación</p>
              <p class="text-2xl font-bold text-gray-800">{{ estadisticas.tasaCancelacion }}%</p>
            </div>
            <div class="bg-red-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          </div>
          <div class="mt-4">
            <p class="text-sm text-gray-500 flex items-center" :class="estadisticas.crecimientoCancelacion <= 0 ? 'text-green-500' : 'text-red-500'">
              <span v-if="estadisticas.crecimientoCancelacion <= 0" class="mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </span>
              <span v-else class="mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </span>
              {{ Math.abs(estadisticas.crecimientoCancelacion) }}% desde el mes pasado
            </p>
          </div>
        </div>
      </div>

      <!-- Filtros y acciones -->
      <div class="bg-white rounded-lg shadow-md p-4 mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div class="flex flex-col md:flex-row md:items-center gap-4">
          <!-- Búsqueda -->
          <div class="relative">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="Buscar por cliente..." 
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue w-full md:w-64"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <!-- Filtros -->
          <div class="flex items-center gap-2">
            <select 
              v-model="filterPlan" 
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los planes</option>
              <option v-for="plan in planesParaFiltro" :key="plan.id" :value="plan.id">
                {{ plan.nombre }}
              </option>
            </select>
            <select 
              v-model="filterEstadoSuscripcion" 
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-impacto-blue focus:border-impacto-blue"
            >
              <option value="">Todos los estados (suscripción)</option>
              <option value="active">Activa</option>
              <option value="trialing">En prueba</option>
              <option value="past_due">Pago vencido</option>
              <option value="canceled">Cancelada</option>
              <option value="incomplete">Incompleta</option>
              <!-- Otros estados de suscripción de Stripe si son relevantes -->
            </select>
          </div>
        </div>
        
        <!-- Botón para exportar -->
        <button 
          @click="exportarFacturacion" 
          class="bg-impacto-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Exportar
        </button>
      </div>

      <!-- Tabla de suscripciones -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cliente
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado Suscripción / Factura
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fecha Suscripción
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Próximo Pago / Fin Periodo
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Última Factura
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-if="facturas.length === 0 && !isLoading">
                <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                  No se encontraron facturas que coincidan con los criterios.
                </td>
              </tr>
              <tr v-for="factura in facturas" :key="factura.id" class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span class="text-gray-600 font-medium">{{ getInitials(factura.cliente_nombre) }}</span>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ factura.cliente_nombre }}</div>
                      <div class="text-sm text-gray-500">{{ factura.cliente_email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ factura.plan_nombre || 'N/A' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span v-if="factura.estado_factura" class="px-2 py-0.5 text-xs rounded-full font-semibold" :class="getEstadoFacturaClass(factura.estado_factura)">
                    Factura: {{ getEstadoFacturaLabel(factura.estado_factura) }}
                  </span>
                  <span v-else-if="factura.estado_suscripcion" class="px-2 py-0.5 text-xs rounded-full font-semibold" :class="getEstadoSuscripcionClass(factura.estado_suscripcion)">
                    Suscripción: {{ getEstadoSuscripcionLabel(factura.estado_suscripcion) }}
                  </span>
                   <span v-else class="px-2 py-0.5 text-xs rounded-full font-semibold bg-gray-100 text-gray-600">
                    N/A
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ factura.fecha_creacion_suscripcion ? formatFullDate(factura.fecha_creacion_suscripcion) : 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ factura.fecha_proximo_pago ? formatFullDate(factura.fecha_proximo_pago) : 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div v-if="factura.importe_total_factura !== null">
                    {{ formatCurrency(factura.importe_total_factura) }} ({{ factura.moneda_factura || 'EUR' }})
                    <a 
                      v-if="factura.enlace_pdf_factura" 
                      :href="factura.enlace_pdf_factura" 
                      target="_blank" 
                      class="ml-2 text-impacto-blue hover:text-blue-700 font-medium"
                      title="Ver factura PDF"
                    >
                      (Ver PDF)
                    </a>
                  </div>
                  <span v-else>N/A</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button @click="verDetallesFactura(factura)" class="text-impacto-blue hover:text-blue-700 p-1" title="Ver detalles">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button @click="gestionarSuscripcion(factura.stripe_subscription_id)" class="text-yellow-500 hover:text-yellow-700 p-1 ml-2" title="Gestionar Suscripción en Stripe">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </button>
                  <button 
                    @click="cancelarSuscripcion(factura)" 
                    class="text-red-600 hover:text-red-900"
                    title="Cancelar suscripción"
                    v-if="factura.estado_factura !== 'cancelado'"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Paginación -->
        <div v-if="totalPages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button 
              @click="currentPage > 1 ? currentPage-- : null" 
              :disabled="currentPage === 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button 
              @click="currentPage < totalPages ? currentPage++ : null" 
              :disabled="currentPage === totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando <span class="font-medium">{{ Math.max(1, (currentPage - 1) * itemsPerPage + 1) }}</span>
                a <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, totalItems) }}</span>
                de <span class="font-medium">{{ totalItems }}</span> resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button 
                  @click="currentPage > 1 ? currentPage-- : null" 
                  :disabled="currentPage === 1"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Anterior</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
                <button 
                  v-for="pageNumber in paginationRange" 
                  :key="typeof pageNumber === 'string' ? 'dots-' + pageNumber : pageNumber" 
                  @click="typeof pageNumber === 'number' ? currentPage = pageNumber : null" 
                  :disabled="typeof pageNumber === 'string'"
                  :class="[
                    pageNumber === currentPage ? 'z-10 bg-impacto-blue border-impacto-blue text-white' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                    typeof pageNumber === 'string' ? 'disabled:opacity-50 disabled:cursor-not-allowed' : ''
                  ]"
                >
                  {{ pageNumber }}
                </button>
                <button 
                  @click="currentPage < totalPages ? currentPage++ : null" 
                  :disabled="currentPage === totalPages"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span class="sr-only">Siguiente</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { apiFetch } from '@/utils/apiFetch';
import { useToast } from '@/composables/useToast';

// Definición de Interfaces
interface AdminFacturaItem {
  id: string | number; 
  cliente_id: string;
  cliente_nombre: string;
  cliente_email: string;
  plan_id: string | null; 
  plan_nombre: string | null; 
  
  estado_suscripcion: string; 
  fecha_creacion_suscripcion: string;
  fecha_proximo_pago: string | null; 
  stripe_subscription_id: string;

  estado_factura: string | null; 
  fecha_creacion_factura: string | null;
  fecha_pago_factura: string | null;
  importe_subtotal_factura: number | null;
  importe_impuestos_factura: number | null;
  importe_total_factura: number | null;
  moneda_factura: string | null;
  referencia_pago_factura: string | null; 
  enlace_pdf_factura: string | null;
}

interface PlanParaFiltro {
  id: string; 
  nombre: string;
}

interface AdminFacturacionApiResponse {
  success: boolean;
  data: AdminFacturaItem[];
  meta: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    availablePlans: PlanParaFiltro[];
  };
  message?: string;
}

interface FacturacionEstadisticas { // Mantener por si se usa para las tarjetas
  ingresosMensuales: number;
  crecimientoMensual: number;
  suscripcionesActivas: number;
  crecimientoSuscripciones: number;
  tasaConversion: number;
  crecimientoConversion: number;
  tasaCancelacion: number;
  crecimientoCancelacion: number;
}

const { showToast } = useToast();

const isLoading = ref(true);
const facturas = ref<AdminFacturaItem[]>([]); // Realmente son "Suscripciones con su última factura"
const planesParaFiltro = ref<PlanParaFiltro[]>([]);

const estadisticas = ref<FacturacionEstadisticas>({
  ingresosMensuales: 0,
  crecimientoMensual: 0,
  suscripcionesActivas: 0,
  crecimientoSuscripciones: 0,
  tasaConversion: 0,
  crecimientoConversion: 0,
  tasaCancelacion: 0,
  crecimientoCancelacion: 0,
});

const searchQuery = ref('');
const filterPlan = ref('');
const filterEstadoSuscripcion = ref(''); // Cambiado de filterEstadoFactura

const currentPage = ref(1);
const itemsPerPage = ref(15);
const totalItems = ref(0);
const totalPages = ref(1);

const fetchFacturas = async () => {
  isLoading.value = true;
  try {
    let url = `/api/admin_get_facturacion.php?page=${currentPage.value}&limit=${itemsPerPage.value}`;
    if (searchQuery.value) url += `&search=${encodeURIComponent(searchQuery.value)}`;
    if (filterPlan.value) url += `&plan_id=${encodeURIComponent(filterPlan.value)}`;
    if (filterEstadoSuscripcion.value) url += `&estado=${encodeURIComponent(filterEstadoSuscripcion.value)}`; // Cambiado para que coincida con el backend (s.estado)

    const response = await apiFetch(url) as AdminFacturacionApiResponse;

    if (response.success && response.data) {
      facturas.value = response.data;
      totalItems.value = response.meta.totalItems;
      totalPages.value = response.meta.totalPages;
      currentPage.value = response.meta.currentPage;
      
      if (response.meta.availablePlans) {
        planesParaFiltro.value = response.meta.availablePlans;
      }

      if (response.meta.currentPage > response.meta.totalPages && response.meta.totalPages > 0) {
         currentPage.value = response.meta.totalPages;
      }
    } else {
      showToast(response.message || 'Error al cargar los datos de facturación.', 'error');
      facturas.value = [];
      totalItems.value = 0;
      totalPages.value = 1;
    }
  } catch (error: any) {
    console.error('Error en fetchFacturas:', error);
    showToast(error.message || 'Se produjo un error inesperado al cargar los datos de facturación.', 'error');
    facturas.value = [];
    totalItems.value = 0;
    totalPages.value = 1;
  } finally {
    isLoading.value = false;
  }
};

const formatCurrency = (amount: number | null | undefined, currency: string = 'EUR'): string => {
  if (amount === null || amount === undefined) return 'N/A';
  return new Intl.NumberFormat('es-ES', { style: 'currency', currency: currency }).format(amount);
};

const formatFullDate = (dateString: string | null): string => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Fecha inválida';
    return date.toLocaleString('es-ES', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' });
  } catch (e) {
    return 'Fecha inválida';
  }
};

const getInitials = (name: string | null | undefined): string => {
  if (!name) return '??';
  return name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
};

const getEstadoFacturaClass = (estado: string | null): string => {
  if (!estado) return 'bg-gray-200 text-gray-800';
  const classes: Record<string, string> = {
    'paid': 'bg-green-100 text-green-800',
    'open': 'bg-yellow-100 text-yellow-800',
    'draft': 'bg-blue-100 text-blue-800',
    'void': 'bg-gray-500 text-white',
    'uncollectible': 'bg-red-100 text-red-800',
  };
  return classes[estado.toLowerCase()] || 'bg-gray-200 text-gray-800';
};

const getEstadoFacturaLabel = (estado: string | null): string => {
  if (!estado) return 'N/A';
  return estado.charAt(0).toUpperCase() + estado.slice(1).replace(/_/g, ' ');
};

// Clases y etiquetas para el estado de la suscripción
const getEstadoSuscripcionClass = (estado: string | null): string => {
  if (!estado) return 'bg-gray-200 text-gray-800';
  const classes: Record<string, string> = {
    'active': 'bg-green-100 text-green-700',
    'trialing': 'bg-blue-100 text-blue-700',
    'past_due': 'bg-yellow-100 text-yellow-700',
    'canceled': 'bg-red-100 text-red-700',
    'incomplete': 'bg-orange-100 text-orange-700',
    'incomplete_expired': 'bg-red-200 text-red-800',
    'unpaid': 'bg-red-100 text-red-700',
  };
  return classes[estado.toLowerCase()] || 'bg-gray-100 text-gray-700';
};

const getEstadoSuscripcionLabel = (estado: string | null): string => {
  if (!estado) return 'Desconocido';
  return estado.charAt(0).toUpperCase() + estado.slice(1).replace(/_/g, ' ');
};

const verDetallesFactura = (item: AdminFacturaItem) => {
  // Aquí podríamos abrir un modal con más detalles de la suscripción y la factura si es necesario
  if (item.enlace_pdf_factura) {
    window.open(item.enlace_pdf_factura, '_blank');
  } else {
    showToast('No hay PDF de factura disponible para esta entrada.', 'info');
  }
};

const cancelarSuscripcion = (item: AdminFacturaItem) => {
  if (confirm(`¿Seguro que quieres gestionar la cancelación para ${item.cliente_nombre} (Suscripción: ${item.stripe_subscription_id})? Esto podría llevar a la página de Stripe o a una acción interna.`)) {
    showToast(`Gestionar cancelación para ${item.stripe_subscription_id} - Próximamente`, 'info');
  }
};

const exportarFacturacion = () => {
  showToast('Exportar datos de facturación - Próximamente', 'info');
};

const gestionarSuscripcion = (subscriptionId: string | null) => {
  if (!subscriptionId) {
    alert('ID de suscripción no disponible.');
    return;
  }
  // Redirigir al portal de Stripe o a una página de gestión interna
  const stripeDashboardUrl = `https://dashboard.stripe.com/subscriptions/${subscriptionId}`;
  window.open(stripeDashboardUrl, '_blank');
};

onMounted(() => {
  fetchFacturas();
});

watch(currentPage, () => {
  fetchFacturas();
}, { immediate: false });

watch([searchQuery, filterPlan, filterEstadoSuscripcion], () => { // filterEstadoSuscripcion ahora
  if (currentPage.value !== 1) {
    currentPage.value = 1; 
  } else {
    fetchFacturas(); 
  }
}, { immediate: false });

const paginationRange = computed(() => {
  const delta = 1;
  const rangeNumbers: number[] = [];
  const left = currentPage.value - delta;
  const right = currentPage.value + delta + 1;
  let l: number | undefined = undefined;

  for (let i = 1; i <= totalPages.value; i++) {
    if (i === 1 || i === totalPages.value || (i >= left && i < right)) {
      rangeNumbers.push(i);
    }
  }
  const rangeWithDots: (number | string)[] = [];
  if (totalPages.value <= (1 + delta * 2) + 2) { 
    for(let i = 1; i <= totalPages.value; i++){ rangeWithDots.push(i); }
    return rangeWithDots;
  }
  for (const i of rangeNumbers) {
    if (l !== undefined) {
      if (i - l === 2) rangeWithDots.push(l + 1);
      else if (i - l !== 1) rangeWithDots.push('...');
    }
    rangeWithDots.push(i);
    l = i;
  }
  return rangeWithDots;
});

</script>

<style scoped>
/* Estilos de impacto-blue y impacto-orange como en AdminDashboard */
.bg-impacto-blue {
  background-color: #003B6D;
}

.text-impacto-blue {
  color: #003B6D;
}

.border-impacto-blue {
  border-color: #003B6D;
}

.bg-impacto-orange {
  background-color: #FF6B00;
}

.text-impacto-orange {
  color: #FF6B00;
}
</style>