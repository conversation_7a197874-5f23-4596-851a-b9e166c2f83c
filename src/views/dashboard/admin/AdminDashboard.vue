<template>
  <div class="max-w-7xl mx-auto">
    <!-- Encabezado de la página -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
        </svg>
        Panel de Administración
      </h1>
      <p class="text-gray-600 mt-1">
        Gestiona todos los aspectos de la plataforma Valorador IA desde este panel de control.
      </p>
    </div>

    <!-- Indicador de carga -->
    <div v-if="isLoading || isLoadingChart" class="flex justify-center items-center py-20">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-impacto-blue"></div>
    </div>

    <div v-else>
      <!-- Tarjetas de resumen -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <!-- Usuarios -->
        <div class="bg-white rounded-lg shadow-md p-5 border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm font-medium">Total Usuarios</p>
              <h3 class="text-2xl font-bold text-gray-800">{{ stats.totalUsuarios }}</h3>
            </div>
            <div class="p-3 bg-blue-100 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          </div>
          <div class="mt-2">
            <p class="text-xs text-gray-500">
              <span class="text-green-500 font-medium">+{{ stats.nuevosUsuarios }}</span> nuevos en los últimos 30 días
            </p>
          </div>
        </div>

        <!-- Valoradores -->
        <div class="bg-white rounded-lg shadow-md p-5 border-l-4 border-purple-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm font-medium">Valoradores Activos</p>
              <h3 class="text-2xl font-bold text-gray-800">{{ stats.valoradoresActivos }}</h3>
            </div>
            <div class="p-3 bg-purple-100 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
          </div>
          <div class="mt-2">
            <p class="text-xs text-gray-500">
              <span class="text-green-500 font-medium">{{ Math.round(stats.valoradoresActivos / stats.totalValoradores * 100) }}%</span> del total ({{ stats.totalValoradores }})
            </p>
          </div>
        </div>

        <!-- Valoraciones -->
        <div class="bg-white rounded-lg shadow-md p-5 border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm font-medium">Total Valoraciones</p>
              <h3 class="text-2xl font-bold text-gray-800">{{ stats.totalValoraciones }}</h3>
            </div>
            <div class="p-3 bg-green-100 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
            </div>
          </div>
          <div class="mt-2">
            <p class="text-xs text-gray-500">
              <span class="text-green-500 font-medium">+{{ stats.nuevasValoraciones }}</span> en los últimos 30 días
            </p>
          </div>
        </div>

        <!-- Leads -->
        <div class="bg-white rounded-lg shadow-md p-5 border-l-4 border-yellow-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm font-medium">Total Leads</p>
              <h3 class="text-2xl font-bold text-gray-800">{{ stats.totalLeads }}</h3>
            </div>
            <div class="p-3 bg-yellow-100 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
          <div class="mt-2">
            <p class="text-xs text-gray-500">
              <span class="text-green-500 font-medium">+{{ stats.nuevosLeads }}</span> en los últimos 30 días
            </p>
          </div>
        </div>
      </div>

      <!-- Gráfico de actividad reciente -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-800">Actividad Reciente</h2>
          <div class="flex items-center gap-2">
            <button 
              v-for="(period, index) in ['7D', '30D', '90D', 'YTD']" 
              :key="index"
              @click="changeChartPeriod(period)"
              class="px-3 py-1 text-xs rounded-md transition-all duration-200"
              :class="activePeriod === period ? 'bg-impacto-blue text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
            >
              {{ period }}
            </button>
          </div>
        </div>
        <div class="h-72 md:h-80 lg:h-96">
          <div v-if="isLoadingChart" class="h-full flex items-center justify-center text-gray-400">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-impacto-orange"></div>
            <p class="ml-3">Cargando datos del gráfico...</p>
          </div>
          <div v-else-if="chartError" class="h-full flex flex-col items-center justify-center text-red-500">
            <p class="font-semibold">Error al cargar el gráfico:</p>
            <p class="text-sm">{{ chartError }}</p>
            <button @click="fetchChartData(activePeriod)" class="mt-2 px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200">Reintentar</button>
          </div>
          <div v-else-if="!chartData || chartData.length === 0" class="h-full flex items-center justify-center text-gray-400">
            <p>No hay datos de actividad para el período: {{ activePeriod }}</p>
          </div>
          <!-- Componente de Gráfico SVG Básico -->
          <BasicActivityChart v-else :chart-data="chartData" />
        </div>
      </div>

      <!-- Secciones de acceso rápido -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Usuarios recientes -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="bg-blue-50 px-4 py-3 border-b border-blue-100">
            <h3 class="font-semibold text-blue-800 flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Usuarios Recientes
            </h3>
          </div>
          <div class="divide-y divide-gray-100">
            <div v-for="(user, index) in recentUsers" :key="index" class="p-4 hover:bg-gray-50 transition-colors duration-150">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-semibold text-sm">
                    {{ user.nombre_completo.substring(0, 2).toUpperCase() }}
                  </div>
                  <div>
                    <p class="font-medium text-gray-800">{{ user.nombre_completo }}</p>
                    <p class="text-sm text-gray-500">{{ user.email }}</p>
                  </div>
                </div>
                <router-link :to="`/admin/usuarios?id=${user.id}`" class="text-blue-600 hover:text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </router-link>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 text-center">
            <router-link to="/admin/usuarios" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
              Ver todos los usuarios →
            </router-link>
          </div>
        </div>

        <!-- Valoraciones recientes -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="bg-green-50 px-4 py-3 border-b border-green-100">
            <h3 class="font-semibold text-green-800 flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              Valoraciones Recientes
            </h3>
          </div>
          <div class="divide-y divide-gray-100">
            <div v-for="(valoracion, index) in recentValoraciones" :key="index" class="p-4 hover:bg-gray-50 transition-colors duration-150">
              <div class="flex items-center justify-between">
                <div>
                  <p class="font-medium text-gray-800">{{ valoracion.direccion }}</p>
                  <div class="flex items-center gap-2 mt-1">
                    <span class="text-xs px-2 py-0.5 bg-gray-100 rounded-full text-gray-600">{{ valoracion.tipo_principal }}</span>
                    <span class="text-xs text-gray-500">{{ formatCurrency(valoracion.valor_estimado_min) }} - {{ formatCurrency(valoracion.valor_estimado_max) }}</span>
                  </div>
                </div>
                <router-link :to="`/admin/valoraciones?id=${valoracion.id}`" class="text-green-600 hover:text-green-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </router-link>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 text-center">
            <router-link to="/admin/valoraciones" class="text-green-600 hover:text-green-800 text-sm font-medium">
              Ver todas las valoraciones →
            </router-link>
          </div>
        </div>

        <!-- Leads recientes -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="bg-yellow-50 px-4 py-3 border-b border-yellow-100">
            <h3 class="font-semibold text-yellow-800 flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              Leads Recientes
            </h3>
          </div>
          <div class="divide-y divide-gray-100">
            <div v-for="(lead, index) in recentLeads" :key="index" class="p-4 hover:bg-gray-50 transition-colors duration-150">
              <div class="flex items-center justify-between">
                <div>
                  <p class="font-medium text-gray-800">{{ lead.nombre }}</p>
                  <div class="flex items-center gap-2 mt-1">
                    <span class="text-xs px-2 py-0.5 rounded-full" :class="getLeadStatusClass(lead.estado)">{{ lead.estado }}</span>
                    <span class="text-xs text-gray-500">{{ lead.email }}</span>
                  </div>
                </div>
                <router-link :to="`/admin/leads?id=${lead.id}`" class="text-yellow-600 hover:text-yellow-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </router-link>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 text-center">
            <router-link to="/admin/leads" class="text-yellow-600 hover:text-yellow-800 text-sm font-medium">
              Ver todos los leads →
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { apiFetch } from '@/utils/apiFetch';
import BasicActivityChart from '@/components/dashboard/admin/BasicActivityChart.vue';

// Definición de Interfaces para tipar los datos
interface AdminUser {
  id: number;
  nombre_completo: string;
  email: string;
  // ... otros campos que pueda tener un usuario
}

interface AdminValoracion {
  id: number;
  direccion: string;
  tipo_principal: string;
  valor_estimado_min: number;
  valor_estimado_max: number;
  // ... otros campos relevantes
}

interface AdminLead {
  id: number;
  nombre: string;
  email: string;
  estado: string;
  // ... otros campos relevantes
}

// Interfaz para los datos del gráfico
interface ActivityChartDataPoint {
  date: string;
  newUsers: number;
  newValoraciones: number;
  newLeads: number;
}

// Estado de carga principal y del gráfico
const isLoading = ref(true);
const isLoadingChart = ref(true);
const chartError = ref<string | null>(null);

// Estadísticas generales
const stats = ref({
  totalUsuarios: 0,
  nuevosUsuarios: 0,
  valoradoresActivos: 0,
  totalValoradores: 0,
  totalValoraciones: 0,
  nuevasValoraciones: 0,
  totalLeads: 0,
  nuevosLeads: 0
});

const activePeriod = ref('30D');
const chartData = ref<ActivityChartDataPoint[]>([]);

// Datos de usuarios recientes - Tipado
const recentUsers = ref<AdminUser[]>([]);

// Datos de valoraciones recientes - Tipado
const recentValoraciones = ref<AdminValoracion[]>([]);

// Datos de leads recientes - Tipado
const recentLeads = ref<AdminLead[]>([]);

// Función para cargar datos del gráfico
const fetchChartData = async (period: string) => {
  isLoadingChart.value = true;
  chartError.value = null;
  try {
    const response = await apiFetch(`/api/admin_dashboard_activity_chart.php?period=${period}`);
    if (response.success && Array.isArray(response.data)) {
      chartData.value = response.data;
    } else {
      throw new Error(response.message || 'Formato de datos del gráfico incorrecto.');
    }
  } catch (err: any) {
    console.error('Error cargando datos del gráfico:', err);
    chartError.value = err.message || 'No se pudieron cargar los datos del gráfico.';
    chartData.value = []; // Limpiar datos en caso de error
  } finally {
    isLoadingChart.value = false;
  }
};

// Función para cambiar el período del gráfico y recargar datos
const changeChartPeriod = (newPeriod: string) => {
  activePeriod.value = newPeriod;
  fetchChartData(newPeriod);
};

// Función para formatear moneda
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'EUR',
    maximumFractionDigits: 0
  }).format(value);
}

// Función para obtener la clase CSS según el estado del lead
function getLeadStatusClass(status: string): string {
  const statusMap: Record<string, string> = {
    'nuevo': 'bg-blue-100 text-blue-800',
    'contactado': 'bg-yellow-100 text-yellow-800',
    'calificado': 'bg-green-100 text-green-800',
    'no_interesado': 'bg-red-100 text-red-800',
    'convertido': 'bg-purple-100 text-purple-800'
  };
  
  return statusMap[status] || 'bg-gray-100 text-gray-800';
}

// Cargar datos al montar el componente
onMounted(async () => {
  isLoading.value = true;
  try {
    const statsResponse = await apiFetch('/api/admin_dashboard_stats.php');

    if (statsResponse.success && statsResponse.stats) {
      stats.value = {
        totalUsuarios: statsResponse.stats.totalUsuarios || 0,
        nuevosUsuarios: statsResponse.stats.nuevosUsuarios || 0,
        valoradoresActivos: statsResponse.stats.valoradoresActivos || 0,
        totalValoradores: statsResponse.stats.totalValoradores || 0,
        totalValoraciones: statsResponse.stats.totalValoraciones || 0,
        nuevasValoraciones: statsResponse.stats.nuevasValoraciones || 0,
        totalLeads: statsResponse.stats.totalLeads || 0,
        nuevosLeads: statsResponse.stats.nuevosLeads || 0
      };
    } else {
      console.warn('Respuesta inesperada para estadísticas generales de Admin:', statsResponse);
    }

    const usersResponse = await apiFetch('/api/getAllUsers.php?limit=5'); 
    
    let usersData: AdminUser[] = [];
    if (usersResponse.success && Array.isArray(usersResponse.data)) { 
        usersData = usersResponse.data as AdminUser[];
    } else {
      console.warn('Respuesta inesperada o formato incorrecto para usuarios recientes (Admin):', usersResponse);
    }
    recentUsers.value = usersData;

    const valoracionesResponse = await apiFetch('/api/get_valoraciones.php?limit=5');
    if (valoracionesResponse.success && Array.isArray(valoracionesResponse.valoraciones)) {
      recentValoraciones.value = valoracionesResponse.valoraciones as AdminValoracion[];
    } else {
      console.warn('Respuesta inesperada para valoraciones recientes (Admin):', valoracionesResponse);
      recentValoraciones.value = [];
    }

    const leadsResponse = await apiFetch('/api/get_leads.php?limit=5');
    if (leadsResponse.success && Array.isArray(leadsResponse.leads)) {
      recentLeads.value = leadsResponse.leads as AdminLead[];
    } else {
      console.warn('Respuesta inesperada para leads recientes (Admin):', leadsResponse);
      recentLeads.value = [];
    }
  } catch (error) {
    console.error('Error al cargar los datos del panel de administración:', error);
  } finally {
    isLoading.value = false;
  }
  
  fetchChartData(activePeriod.value);
});
</script>

<style scoped>
.bg-impacto-blue {
  background-color: #003B6D;
}

.text-impacto-blue {
  color: #003B6D;
}

.bg-impacto-orange {
  background-color: #FF6B00;
}

.text-impacto-orange {
  color: #FF6B00;
}
</style>
