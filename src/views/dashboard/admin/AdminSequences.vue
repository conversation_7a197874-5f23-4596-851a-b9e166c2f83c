<template>
  <div class="p-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Gestión de Secuencias de Emails</h1>

    <!-- <PERSON>a de acciones -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-6 flex items-center justify-between">
      <h2 class="text-xl font-semibold text-gray-800">Listado de Secuencias</h2>
      <button @click="openCreateModal" class="bg-impacto-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        <PERSON><PERSON><PERSON>ueva Secuencia
      </button>
    </div>

    <!-- <PERSON>ten<PERSON><PERSON> de la tabla -->
    <div class="bg-white shadow-xl rounded-lg overflow-hidden">
      <div v-if="isLoading" class="p-6 text-center text-gray-500">
        <p>Cargando secuencias...</p>
      </div>
      <div v-else-if="error" class="p-6 text-center text-red-500">
        <p>Error al cargar las secuencias: {{ error }}</p>
      </div>
      <div v-else-if="sequences.length === 0" class="p-6 text-center text-gray-500">
        <p>No hay secuencias creadas todavía.</p>
      </div>
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre</th>
              <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Pasos</th>
              <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
              <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="sequence in sequences" :key="sequence.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ sequence.name }}</div>
                <div class="text-sm text-gray-500 max-w-md truncate">{{ sequence.description }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ sequence.step_count }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="sequence.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                  {{ sequence.is_active ? 'Activa' : 'Inactiva' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                <router-link :to="{ name: 'AdminSequenceSteps', params: { uuid: sequence.uuid } }" class="text-blue-600 hover:text-blue-900 font-medium">Gestionar Pasos</router-link>
                <button @click="openEditModal(sequence)" class="text-indigo-600 hover:text-indigo-900 ml-4">Ajustes</button>
                <button @click="handleDelete(sequence)" class="text-red-600 hover:text-red-900 ml-4">Eliminar</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Modal -->
    <sequence-form-modal
      :show="showModal"
      :sequence="selectedSequence"
      :is-submitting="isSubmitting"
      :plans="plans"
      @save="handleSave"
      @close="showModal = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import SequenceFormModal from '@/components/dashboard/admin/sequences/SequenceFormModal.vue';
import { apiFetch } from '@/utils/apiFetch';

// Interfaces
interface Sequence {
  id: number;
  uuid: string;
  name: string;
  description: string;
  is_active: boolean;
  trigger_event: string;
  created_at: string;
  step_count: number;
}

// State
const sequences = ref<Sequence[]>([]);
const plans = ref<any[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);
const showModal = ref(false);
const isSubmitting = ref(false);
const selectedSequence = ref<Sequence | null>(null);

// Functions
const fetchSequences = async () => {
  isLoading.value = true;
  try {
    const response = await apiFetch('/api/admin_get_sequences.php');
    if (response.success) {
      sequences.value = response.data;
    } else {
      throw new Error(response.message || 'Failed to fetch sequences.');
    }
  } catch (e: any) {
    error.value = e.message;
  } finally {
    isLoading.value = false;
  }
};

const fetchPlans = async () => {
  try {
    const response = await apiFetch('/api/get_plans.php');
    if (response.success) {
      plans.value = response.data;
    } else {
      console.error('Failed to fetch plans:', response.message);
    }
  } catch (e: any) {
    console.error('Error fetching plans:', e.message);
  }
};

const openCreateModal = () => {
  selectedSequence.value = null;
  showModal.value = true;
};

const openEditModal = (sequence: Sequence) => {
  selectedSequence.value = { ...sequence };
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  selectedSequence.value = null;
};

const handleDelete = async (sequence: Sequence) => {
  if (!confirm(`¿Estás seguro de que quieres eliminar la secuencia "${sequence.name}"? Esta acción no se puede deshacer.`)) {
    return;
  }

  isSubmitting.value = true; // Reutilizamos el estado para bloquear la UI
  error.value = null;
  try {
    const response = await apiFetch('/api/admin_delete_sequence.php', {
      method: 'POST',
      body: JSON.stringify({ id: sequence.id })
    });

    if (!response.success) {
      throw new Error(response.message || 'Ocurrió un error al eliminar la secuencia.');
    }

    await fetchSequences(); // Recargar la lista
  } catch (e: any) {
    error.value = e.message;
    console.error("Error al eliminar:", e);
    // Considerar mostrar una notificación de error al usuario
  } finally {
    isSubmitting.value = false;
  }
};

const handleSave = async (sequenceData: any) => {
  isSubmitting.value = true;
  error.value = null;
  try {
    const payload = {
      ...sequenceData,
      is_active: sequenceData.is_active ? 1 : 0
    };
    const endpoint = sequenceData.id ? '/api/admin_update_sequence.php' : '/api/admin_create_sequence.php';
    const response = await apiFetch(endpoint, { 
      method: 'POST', 
      body: JSON.stringify(payload)
    });

    if (!response.success) {
      throw new Error(response.message || 'Ocurrió un error al guardar la secuencia.');
    }
    
    closeModal();
    await fetchSequences();
  } catch (e: any) {
    error.value = e.message; // Podríamos mostrar este error en una notificación
    console.error("Error al guardar:", e);
  } finally {
    isSubmitting.value = false;
  }
};

onMounted(() => {
  fetchSequences();
  fetchPlans();
});
</script>
