<template>
  <div class="p-4 sm:p-6 md:p-8 bg-gray-50 min-h-screen">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> (Restaurado) -->
    <div class="mb-8 rounded-xl shadow-xl overflow-hidden relative bg-impacto-blue">
      <div class="absolute inset-0 bg-grid-pattern opacity-10" style="background-image: linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px); background-size: 20px 20px;"></div>
      <div class="relative p-6 md:p-8 flex flex-col md:flex-row items-center gap-4 md:gap-6">
        <div class="bg-white/10 backdrop-blur-sm p-3 md:p-4 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656-.126-1.283-.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm-9 5a2 2 0 100-4 2 2 0 000 4z" />
          </svg>
        </div>
        <div class="text-center md:text-left">
          <h1 class="text-2xl md:text-3xl font-bold text-white mb-1">Mi <span class="text-impacto-orange">Agencia</span></h1>
          <p class="text-white/80 text-sm md:text-base max-w-xl">
            Gestiona el nombre de tu agencia y los miembros de tu equipo.
          </p>
        </div>
      </div>
    </div>

    <!-- SKELETON LOADER PARA LA VISTA COMPLETA (controlado por isInitialLoading) -->
    <div v-if="isInitialLoading" class="space-y-8 animate-pulse">
      <!-- Skeleton para Nombre de Agencia -->
      <div class="bg-white shadow-xl rounded-lg p-6">
        <div class="h-6 bg-gray-200 rounded w-1/4 mb-4"></div> <!-- Skeleton para título "Datos de la Agencia" -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div class="md:col-span-2 space-y-2">
            <div class="h-4 bg-gray-200 rounded w-1/3"></div> <!-- Skeleton para label -->
            <div class="h-10 bg-gray-200 rounded"></div>    <!-- Skeleton para input -->
          </div>
          <div class="h-10 bg-gray-300 rounded"></div>        <!-- Skeleton para botón -->
        </div>
      </div>

      <!-- Skeleton para Barra de Acciones -->
      <div class="mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
        <div class="h-10 bg-gray-300 rounded w-36"></div> <!-- Skeleton para botón Añadir Usuario -->
        <div class="h-4 bg-gray-200 rounded w-24"></div>   <!-- Skeleton para contador de usuarios -->
      </div>

      <!-- Skeleton para Contenedor de Tabla -->
      <div class="bg-white shadow-xl rounded-lg overflow-hidden">
        <div class="p-6 space-y-4">
          <!-- Skeleton para fila de cabecera de tabla -->
          <div class="flex justify-between items-center">
            <div class="h-4 bg-gray-200 rounded w-1/4"></div>
            <div class="h-4 bg-gray-200 rounded w-1/4"></div>
            <div class="h-4 bg-gray-200 rounded w-1/5"></div>
            <div class="h-4 bg-gray-200 rounded w-1/6"></div>
          </div>
          <!-- Skeleton para filas de tabla (ej. 3 filas) -->
          <div v-for="i in 3" :key="`skel-row-${i}`" class="flex justify-between items-center pt-4 border-t border-gray-200">
            <div class="h-4 bg-gray-300 rounded w-1/4"></div>
            <div class="h-4 bg-gray-300 rounded w-1/4"></div>
            <div class="h-4 bg-gray-300 rounded w-1/5"></div>
            <div class="h-4 bg-gray-300 rounded w-1/6"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- CONTENIDO REAL DE LA VISTA (se muestra cuando isInitialLoading es false) -->
    <div v-else>
      <!-- Sección para Nombre de Agencia -->
      <div class="mb-8 bg-white shadow-xl rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Datos de la Agencia</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div class="md:col-span-2">
            <label for="agencyNameInput" class="block text-sm font-medium text-gray-700 mb-1">Nombre de la Agencia</label>
            <input 
              type="text" 
              id="agencyNameInput" 
              v-model="editableAgencyName" 
              class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              placeholder="Ej: Inmobiliaria Sol Brillante"
              :disabled="isLoadingAgencyName"
            />
          </div>
          <div>
            <button 
              @click="handleUpdateAgencyName"
              :disabled="isLoadingAgencyName || !isAgencyNameChanged"
              class="w-full px-4 py-2 text-sm font-medium text-white bg-impacto-blue rounded-md hover:bg-impacto-blue/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md transition-all"
            >
              <svg v-if="isLoadingAgencyName" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isLoadingAgencyName ? 'Guardando...' : 'Guardar Nombre' }}
            </button>
          </div>
        </div>
        <p v-if="agencyNameError" class="mt-2 text-xs text-red-600">{{ agencyNameError }}</p>
      </div>

      <!-- Barra de Acciones: Botón Añadir y Contador -->
      <div class="mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
        <button
          v-if="authStore.isAgencyOwner"
          @click="openAddUserModal"
          :disabled="!canAddUser || isLoading"
          class="px-4 py-2 text-sm font-medium text-white bg-impacto-blue rounded-md hover:bg-impacto-blue/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 shadow-sm hover:shadow-md transition-all"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          Añadir Usuario
        </button>
        <div v-if="authStore.isAgencyOwner && (authStore.maxDashboardUsers > 0 || authStore.maxDashboardUsers === 0 || authStore.maxDashboardUsers === null)" class="text-sm text-gray-700">
          Usuarios: {{ users.length }} / 
          <span v-if="authStore.maxDashboardUsers > 0">{{ authStore.maxDashboardUsers }}</span>
          <span v-else>Ilimitados</span>
        </div>
      </div>
      
      <div v-if="authStore.isAgencyOwner && !canAddUser && authStore.maxDashboardUsers > 0" class="mb-4 p-4 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700 rounded-md shadow-sm text-sm">
        <p><strong class="font-medium">Límite alcanzado:</strong> Has alcanzado el límite de {{ authStore.maxDashboardUsers }} usuarios para tu plan actual. Para añadir más, considera <router-link to="/suscripcion" class="font-semibold underline hover:text-yellow-800">actualizar tu plan</router-link>.</p>
      </div>

      <!-- Contenedor para la tabla y mensajes de estado -->
      <div class="bg-white shadow-xl rounded-lg overflow-hidden">
        <!-- Loader secundario (si es necesario para recargas, pero no para carga inicial con skeleton) -->
        <div v-if="isLoading && !isInitialLoading" class="text-center py-20">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto" :style="{ borderColor: '#ed8725' }"></div>
          <p class="mt-4 text-lg font-semibold text-gray-700">Cargando usuarios...</p>
          <p class="text-sm text-gray-500">Por favor, espera un momento.</p>
        </div>

        <div v-else-if="error" class="bg-red-50 border-l-4 border-red-600 text-red-800 p-6 rounded-md m-4 sm:m-6 shadow-sm">
           <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-red-600" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-bold">Error al Cargar los Usuarios del Equipo</h3>
              <p class="text-sm mt-1">{{ error }}</p>
              <button @click="fetchUsers" class="mt-3 px-3 py-1.5 text-sm bg-red-700 text-white rounded-md hover:bg-red-800 transition-colors font-medium">Reintentar</button>
            </div>
          </div>
        </div>

        <div v-else-if="!isLoading && users.length === 0" class="p-8 text-center" style="background-color: #fffaf0; border: 1px solid #f0e4d0;">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 opacity-80" style="color: #051f33;" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656-.126-1.283-.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm-9 5a2 2 0 100-4 2 2 0 000 4z" />
          </svg>
          <h3 class="text-xl font-semibold mb-2" style="color: #051f33;">Aún no hay usuarios en tu equipo</h3>
          <p class="text-gray-600 max-w-md mx-auto">
            Comienza añadiendo el primer miembro de tu equipo haciendo clic en el botón "Añadir Usuario".
          </p>
        </div>
        
        <div v-else-if="authStore.isAgencyOwner" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="user in users" :key="user.id" class="hover:bg-gray-50 transition-colors duration-150">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ user.nombre_completo }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.email }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <span :class="user.activo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                    {{ user.activo ? 'Activo' : 'Inactivo' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    @click="confirmRemoveUser(user)" 
                    class="text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed p-1 rounded-md hover:bg-red-50 transition-colors"
                    :disabled="isLoadingAction || (authStore.currentUser ? authStore.currentUser.id === user.id : false)"
                    title="Eliminar usuario"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="p-8 text-center text-gray-600">
            No tienes permisos para ver esta sección.
        </div>
      </div> <!-- Cierre del div del contenedor de la tabla y mensajes de estado -->

      <!-- Modales -->
      <!-- Modal para añadir usuario -->
      <div v-if="showAddUserModal" class="fixed inset-0 z-[100] overflow-y-auto bg-black/60 backdrop-blur-sm flex items-center justify-center p-4">
        <div class="bg-gray-50 rounded-xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-y-auto flex flex-col">
            <div class="flex justify-between items-center p-5 border-b border-gray-200 bg-white sticky top-0 z-10 rounded-t-xl">
              <h2 class="text-xl font-bold text-[#051f33]">Añadir Nuevo Usuario al Equipo</h2>
              <button @click="showAddUserModal = false" class="text-gray-400 hover:text-red-600 focus:outline-none p-1 rounded-full hover:bg-red-100 transition-colors duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <form @submit.prevent="handleAddUser" class="p-6 space-y-5">
              <div>
                <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Nombre Completo</label>
                <input v-model="newUser.fullName" type="text" name="fullName" id="fullName" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue" placeholder="Ej: Laura Martínez">
              </div>
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input v-model="newUser.email" type="email" name="email" id="email" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue" placeholder="ej: <EMAIL>">
              </div>
              <p v-if="addUserError" class="text-sm text-red-600">{{ addUserError }}</p>
              
              <div class="pt-3 flex justify-end space-x-3">
                <button 
                  type="button" 
                  @click="showAddUserModal = false" 
                  :disabled="isLoadingAction"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50"
                >
                  Cancelar
                </button>
                <button 
                  type="submit" 
                  :disabled="isLoadingAction"
                  class="px-4 py-2 text-sm font-medium text-white bg-impacto-blue rounded-md shadow-sm hover:bg-impacto-blue/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50 flex items-center justify-center"
                >
                  <svg v-if="isLoadingAction" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isLoadingAction ? 'Guardando...' : 'Guardar Usuario' }}
                </button>
              </div>
            </form>
          </div>
      </div>
      
      <!-- Modal de Confirmación para Eliminar Usuario -->
      <div v-if="userToRemove" class="fixed inset-0 z-[100] overflow-y-auto bg-black/60 backdrop-blur-sm flex items-center justify-center p-4">
          <div class="bg-white rounded-xl shadow-2xl w-full max-w-lg overflow-hidden">
              <div class="p-6 text-center">
                  <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                      <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                  </div>
                  <h3 class="text-lg font-medium text-gray-900 mb-2">
                      Eliminar Usuario
                  </h3>
                  <p class="text-sm text-gray-500 mb-6">
                      ¿Estás seguro de que quieres eliminar al usuario <strong>{{ userToRemove.nombre_completo }} ({{ userToRemove.email }})</strong>? Esta acción desactivará su cuenta y lo desvinculará de tu agencia. No se puede deshacer.
                  </p>
              </div>
              <div class="bg-gray-50 px-4 py-3 sm:px-6 flex flex-col sm:flex-row-reverse gap-3">
                  <button @click="handleRemoveUser" :disabled="isLoadingAction" class="w-full sm:w-auto inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50">
                      <svg v-if="isLoadingAction" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                         <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                         <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {{ isLoadingAction ? 'Eliminando...' : 'Sí, eliminar' }}
                  </button>
                  <button @click="userToRemove = null" :disabled="isLoadingAction" type="button" class="w-full sm:w-auto inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50">
                      Cancelar
                  </button>
              </div>
          </div>
      </div>
    </div> <!-- Cierre del div v-else principal que envuelve el contenido real -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { 
  listAgencyUsers, 
  addUserToAgency, 
  removeUserFromAgency,
  updateAgencyName,
  type AgencyUser,
  type AgencyUserActionResponse
} from '@/api/agencyUsersService';
import { useToast } from '@/composables/useToast';

const toast = useToast();
const authStore = useAuthStore();

// Estados para el nombre de la agencia
const editableAgencyName = ref('');
const originalAgencyName = ref('');
const isLoadingAgencyName = ref(false);
const agencyNameError = ref<string | null>(null);

const users = ref<AgencyUser[]>([]);
const isLoading = ref(false);
const isInitialLoading = ref(true); // NUEVO: para el skeleton de carga inicial de la vista
const isLoadingAction = ref(false); // Para modales
const error = ref<string | null>(null);
const addUserError = ref<string | null>(null);

const showAddUserModal = ref(false);
const newUser = ref<{ fullName: string; email: string }>({ fullName: '', email: '' });

const userToRemove = ref<AgencyUser | null>(null);

const isAgencyNameChanged = computed(() => editableAgencyName.value.trim() !== originalAgencyName.value.trim());

const canAddUser = computed(() => {
  if (!authStore.isAgencyOwner) return false; // Si no es owner, no puede añadir
  if (authStore.maxDashboardUsers === null || authStore.maxDashboardUsers === 0) { // 0 o null significa ilimitado
    return true;
  }
  return users.value.length < authStore.maxDashboardUsers;
});

const fetchUsers = async () => {
  if (!authStore.isAgencyOwner) {
    // No debería llegar aquí si la UI lo controla, pero como salvaguarda
    // error.value = "No tienes permiso para ver la lista de usuarios.";
    // users.value = []; // Limpiar por si acaso
    return;
  }
  isLoading.value = true;
  error.value = null;
  try {
    // Cargar nombre de la agencia al inicio
    if (authStore.getAgencyName) {
      originalAgencyName.value = authStore.getAgencyName;
      editableAgencyName.value = authStore.getAgencyName;
    } else {
      // Si no está en el store, intentar obtenerlo de la suscripción (puede que ya esté allí)
      // o considerar una llamada a un endpoint get_agency_details si fuera necesario.
      // Por ahora, asumimos que authStore se poblará correctamente.
      await authStore.fetchAndSetPlanFeatures(); // Esto podría actualizar el agencyName en user
      if (authStore.getAgencyName) {
        originalAgencyName.value = authStore.getAgencyName;
        editableAgencyName.value = authStore.getAgencyName;
      } else {
        console.warn("Nombre de agencia no disponible en authStore después de fetchAndSetPlanFeatures.");
        // Podrías dejarlo vacío o poner un placeholder si es la primera vez y no se estableció en el signup
      }
    }

    // Asumimos que listAgencyUsers ya está tipado o devuelve una estructura con { success: boolean, users?: AgencyUser[], message?: string }
    const response = await listAgencyUsers();
    if (response.success && response.users) {
      users.value = response.users;
    } else {
      throw new Error(response.message || 'No se pudieron cargar los usuarios.');
    }
  } catch (err: any) {
    error.value = err.message || 'Ocurrió un error desconocido.';
    // No mostramos toast aquí, el error se muestra en la UI
  } finally {
    isLoading.value = false;
    isInitialLoading.value = false; // Desactivar el skeleton principal aquí
  }
};

const openAddUserModal = () => {
  if (!canAddUser.value) {
    toast.showToast('Has alcanzado el límite de usuarios para tu plan. Considera actualizar tu plan para añadir más usuarios.', 'warning');
    return;
  }
  newUser.value = { fullName: '', email: '' };
  addUserError.value = null;
  showAddUserModal.value = true;
};

const handleAddUser = async () => {
  if (!newUser.value.fullName.trim() || !newUser.value.email.trim()) {
    addUserError.value = 'Nombre completo y email son obligatorios.';
    return;
  }
  isLoadingAction.value = true;
  addUserError.value = null;
  try {
    const response: AgencyUserActionResponse = await addUserToAgency(newUser.value.fullName, newUser.value.email);
    if (response.success) {
      toast.showToast(response.message || 'Usuario añadido correctamente.', 'success');
      showAddUserModal.value = false;
      await fetchUsers(); // Refrescar la lista
    } else {
      throw new Error(response.message || 'No se pudo añadir el usuario.');
    }
  } catch (err: any) {
    addUserError.value = err.message || 'Ocurrió un error al añadir el usuario.';
    // El error se muestra en el modal
  } finally {
    isLoadingAction.value = false;
  }
};

const confirmRemoveUser = (user: AgencyUser) => {
    if (authStore.currentUser && authStore.currentUser.id === user.id) {
        toast.showToast('No puedes eliminarte a ti mismo de la agencia.', 'error');
        return;
    }
    userToRemove.value = user;
};

const handleRemoveUser = async () => {
  if (!userToRemove.value) return;

  isLoadingAction.value = true;
  try {
    const response: AgencyUserActionResponse = await removeUserFromAgency(userToRemove.value.id);
    if (response.success) {
      toast.showToast(response.message || 'Usuario eliminado correctamente.', 'success');
      userToRemove.value = null; // Cierra el modal de confirmación
      await fetchUsers(); // Refrescar la lista
    } else {
      throw new Error(response.message || 'No se pudo eliminar el usuario.');
    }
  } catch (err: any) {
    toast.showToast(err.message || 'Ocurrió un error al eliminar el usuario.', 'error');
  } finally {
    isLoadingAction.value = false;
    // Asegurarse que el modal se cierre incluso si hay error, ya que el toast informa.
    // Si se quiere mantener abierto el modal en error, quitar la siguiente linea.
    if (userToRemove.value) userToRemove.value = null; 
  }
};

// Nueva función para manejar la actualización del nombre de la agencia
const handleUpdateAgencyName = async () => {
  agencyNameError.value = null;

  const currentAgencyName = editableAgencyName.value;

  if (!currentAgencyName || (typeof currentAgencyName === 'string' && currentAgencyName.trim() === '')) {
    agencyNameError.value = 'El nombre de la agencia no puede estar vacío.';
    return;
  }
  if (typeof currentAgencyName === 'string' && currentAgencyName.trim().length > 150) {
    agencyNameError.value = 'El nombre de la agencia no debe exceder los 150 caracteres.';
    return;
  }

  if (!isAgencyNameChanged.value) {
    return;
  }

  isLoadingAgencyName.value = true;
  try {
    const nameToSend = typeof currentAgencyName === 'string' ? currentAgencyName.trim() : ''; // Asegurar enviar string
    const response = await updateAgencyName(nameToSend);
    if (response.success) {
      toast.showToast('Nombre de la agencia actualizado con éxito.', 'success');
      originalAgencyName.value = currentAgencyName.trim();
      if (authStore.user) {
        authStore.user.agencyName = currentAgencyName.trim();
        localStorage.setItem('user', JSON.stringify(authStore.user));
      }
    } else {
      // El error de la API se asignará a agencyNameError
      throw new Error(response.message || 'No se pudo actualizar el nombre de la agencia.');
    }
  } catch (err: any) {
    agencyNameError.value = err.message || 'Ocurrió un error al guardar el nombre.';
    // El toast ya no es necesario aquí si el error se muestra en agencyNameError
    // toast.showToast(agencyNameError.value, 'error');
  } finally {
    isLoadingAgencyName.value = false;
  }
};

onMounted(() => {
  isInitialLoading.value = true; // Activar skeleton principal al montar
  const checkAuthAndFetch = async () => {
    if (authStore.isAgencyOwner) {
      await fetchUsers();
    } else {
      // Opcional: Redirigir o mostrar un mensaje más permanente si no es owner
      // error.value = "Acceso denegado. Debes ser propietario de la agencia.";
      // users.value = []; // Asegurar que no se muestren datos si no es owner
      console.warn("Usuario no es Propietario de Agencia, no se cargarán usuarios.");
      isInitialLoading.value = false; // Asegurarse de desactivar el skeleton si no es owner
    }
  };

  if (!authStore.isLoading) {
    checkAuthAndFetch();
  } else {
    const unwatch = watch(() => authStore.isLoading, (newIsLoading) => {
      if (!newIsLoading) {
        unwatch();
        checkAuthAndFetch();
      }
    }, { immediate: true }); // Añadido immediate: true para que se ejecute una vez al inicio si isLoading ya es false
  }
});

</script>

<style scoped>
/* Tailwind maneja la animación de pulso con `animate-pulse` */
/* Puedes añadir estilos adicionales si es necesario */
</style> 