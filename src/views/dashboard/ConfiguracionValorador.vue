<template>
  <div class="max-w-5xl mx-auto">
    <!-- En<PERSON><PERSON><PERSON>o -->
    <div class="mb-8 rounded-xl shadow-xl overflow-hidden relative" style="background-color: #051f33;">
      <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
      <div class="relative p-6 md:p-8 flex flex-col md:flex-row items-center gap-4 md:gap-6">
        <div class="bg-white/10 backdrop-blur-sm p-3 md:p-4 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 md:h-12 md:w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <div class="text-center md:text-left">
          <h1 class="text-2xl md:text-3xl font-bold text-white mb-1">Ajustes del <span style="color: #ed8725;">Valorador</span></h1>
          <p class="text-white/70 text-sm md:text-base max-w-xl">
            Define la apariencia, datos de contacto y enlaces de tu herramienta de valoración.
          </p>
        </div>
      </div>
    </div>
    
    <!-- Contenedor del formulario con sombra suave -->
    <div class="bg-white rounded-xl shadow-xl border border-gray-100 p-6 md:p-8">
      <ValoradorConfigForm @config-saved="handleConfigSaved" @config-error="handleConfigError" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import ValoradorConfigForm from '@/components/dashboard/ValoradorConfigForm.vue';
import { useToast } from '@/composables/useToast';

const { showToast } = useToast();

const handleConfigSaved = () => {
  showToast('Configuración guardada con éxito.', 'success');
};

const handleConfigError = (errorMessage: string) => {
  showToast(`Error al guardar: ${errorMessage}`, 'error');
};

onMounted(() => {
  // Lógica inicial al montar la página, si es necesaria.
});
</script>

<style scoped>
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
