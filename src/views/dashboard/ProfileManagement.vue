<template>
  <div class="space-y-6">
    <!-- Encabezado de la página -->
    <div class="bg-gradient-to-r from-impacto-blue to-impacto-blue/90 rounded-xl shadow-lg p-6 border border-blue-700/20">
      <div class="flex items-center mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-impacto-orange mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <h2 class="text-2xl font-bold text-white">Gestionar Perfil</h2>
      </div>
      <p class="text-gray-100">
        Actualiza tu información personal y gestiona la configuración de tu cuenta.
      </p>
    </div>

    <!-- Sección de Información Personal -->
    <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200/75">
      <h3 class="text-lg font-semibold text-impacto-blue mb-4">Información Personal</h3>
      <form @submit.prevent="handleUpdateProfile">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Nombre Completo</label>
            <input type="text" id="name" v-model="profile.name" :disabled="isLoadingProfile || isUpdatingProfile" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm disabled:bg-gray-100" placeholder="Tu nombre completo">
          </div>
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Correo Electrónico</label>
            <input type="email" id="email" v-model="profile.email" :disabled="isLoadingProfile || isUpdatingProfile" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm disabled:bg-gray-100" placeholder="<EMAIL>">
          </div>
        </div>
        <div v-if="profileError && !isUpdatingProfile" class="mt-4 text-sm text-red-600 bg-red-50 p-3 rounded-md">
          {{ profileError }}
        </div>
        <div class="mt-6 text-right">
          <button type="submit" :disabled="isLoadingProfile || isUpdatingProfile" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-impacto-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange disabled:opacity-50">
            <svg v-if="isUpdatingProfile" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isUpdatingProfile ? 'Guardando...' : (isLoadingProfile ? 'Cargando Perfil...' : 'Guardar Cambios') }}
          </button>
        </div>
      </form>
    </div>

    <!-- Sección de Cambio de Contraseña -->
    <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200/75">
      <div class="border-b border-gray-200 pb-4 mb-6">
        <h3 class="text-lg font-semibold leading-6 text-impacto-blue flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          Cambiar contraseña
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          Asegúrate de que tu cuenta esté usando una contraseña larga y aleatoria para mantenerla segura (mínimo 8 caracteres).
        </p>
      </div>
      
      <form @submit.prevent="handleChangePassword" class="space-y-4">
        <div>
          <label for="current-password" class="block text-sm font-medium text-gray-700">Contraseña actual</label>
          <input type="password" id="current-password" v-model="currentPassword" :disabled="isChangingPassword" autocomplete="current-password" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm disabled:bg-gray-100">
        </div>
        <div>
          <label for="new-password" class="block text-sm font-medium text-gray-700">Nueva contraseña</label>
          <input type="password" id="new-password" v-model="newPassword" :disabled="isChangingPassword" autocomplete="new-password" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm disabled:bg-gray-100">
        </div>
        <div>
          <label for="confirm-new-password" class="block text-sm font-medium text-gray-700">Confirmar nueva contraseña</label>
          <input type="password" id="confirm-new-password" v-model="confirmNewPassword" :disabled="isChangingPassword" autocomplete="new-password" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm disabled:bg-gray-100">
        </div>

        <div v-if="passwordChangeError && !isChangingPassword" class="text-sm text-red-600 bg-red-50 p-3 rounded-md">
          {{ passwordChangeError }}
        </div>
        <div v-if="passwordChangeSuccessMessage && !isChangingPassword" class="text-sm text-green-600 bg-green-50 p-3 rounded-md">
          {{ passwordChangeSuccessMessage }}
        </div>

        <div class="flex justify-end">
          <button 
            type="submit"
            :disabled="isChangingPassword"
            class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-impacto-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50"
          >
            <svg v-if="isChangingPassword" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isChangingPassword ? 'Cambiando...' : 'Cambiar contraseña' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Sección de Zona de Peligro / Eliminación de Cuenta -->
    <div class="bg-white rounded-xl shadow-lg p-6 border border-red-300 bg-red-50">
      <h3 class="text-lg font-semibold text-red-700 mb-4">Zona de Peligro</h3>
      <div class="space-y-4">
        <div>
          <h4 class="font-medium text-gray-800">Eliminar cuenta</h4>
          <p class="text-sm text-gray-600 mt-1">
            Una vez que eliminas tu cuenta, toda tu información, incluyendo valoraciones, leads y configuraciones, será eliminada permanentemente. Esta acción no se puede deshacer.
          </p>
        </div>
        <div v-if="deletionError && !isRequestingDeletion" class="text-sm text-red-700">
          Error: {{ deletionError }}
        </div>
        <div v-if="deletionSuccessMessage && !isRequestingDeletion" class="text-sm text-green-700">
          {{ deletionSuccessMessage }}
        </div>
        <div class="mt-4 text-right">
          <button @click="openDeletionModal" :disabled="isRequestingDeletion" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50">
            <svg v-if="isRequestingDeletion" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isRequestingDeletion ? 'Procesando...' : 'Solicitar Eliminación de Cuenta' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Modal de Confirmación de Eliminación -->
    <AccountDeletionModal 
      :show="showConfirmDeletionModal" 
      :is-loading="isRequestingDeletion"
      @close="showConfirmDeletionModal = false"
      @confirm="handleRequestAccountDeletion"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getUserProfile, updateUserProfile, changePassword, requestAccountDeletion } from '@/api/profileService';
import type { UserProfile } from '@/api/profileService';
import { useToast } from '@/composables/useToast';
import AccountDeletionModal from '@/components/dashboard/AccountDeletionModal.vue';

const { showToast } = useToast();

// --- Estado para la información del perfil ---
const profile = ref<UserProfile>({ name: '', email: '' });
const isLoadingProfile = ref(true);
const profileError = ref<string | null>(null);
const isUpdatingProfile = ref(false);

// --- Estado para el cambio de contraseña ---
const currentPassword = ref('');
const newPassword = ref('');
const confirmNewPassword = ref('');
const isChangingPassword = ref(false);
const passwordChangeError = ref<string | null>(null);
const passwordChangeSuccessMessage = ref<string | null>(null);

// --- Estado para la eliminación de cuenta ---
const isRequestingDeletion = ref(false);
const deletionError = ref<string | null>(null);
const deletionSuccessMessage = ref<string | null>(null);
const showConfirmDeletionModal = ref(false);


async function fetchUserProfile() {
  try {
    isLoadingProfile.value = true;
    profileError.value = null;
    const data = await getUserProfile();
    profile.value = { name: data.name, email: data.email };
  } catch (err: any) {
    const errorMessage = err.message || 'No se pudo cargar el perfil.';
    profileError.value = errorMessage;
    showToast(errorMessage, 'error');
  } finally {
    isLoadingProfile.value = false;
  }
}

async function handleUpdateProfile() {
  profileError.value = null;
  if (!profile.value.name || !profile.value.email) {
    const errorMessage = 'El nombre y el correo electrónico no pueden estar vacíos.';
    profileError.value = errorMessage;
    showToast(errorMessage, 'error');
    return;
  }
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(profile.value.email)) {
    const errorMessage = 'Por favor, introduce un correo electrónico válido.';
    profileError.value = errorMessage;
    showToast(errorMessage, 'error');
    return;
  }

  isUpdatingProfile.value = true;
  try {
    const response = await updateUserProfile(profile.value);
    if (response.success) {
      const successMessage = response.message || 'Perfil actualizado con éxito.';
      showToast(successMessage, 'success');
      if(response.user) {
        profile.value = { name: response.user.name, email: response.user.email };
      }
    } else {
      const errorMessage = response.message || 'Error al actualizar el perfil.';
      profileError.value = errorMessage;
      showToast(errorMessage, 'error');
    }
  } catch (err: any) {
    const errorMessage = err.message || 'Error desconocido al actualizar el perfil.';
    profileError.value = errorMessage;
    showToast(errorMessage, 'error');
  } finally {
    isUpdatingProfile.value = false;
  }
}

async function handleChangePassword() {
  passwordChangeError.value = null;
  passwordChangeSuccessMessage.value = null;

  if (!currentPassword.value || !newPassword.value || !confirmNewPassword.value) {
    const errorMessage = 'Todos los campos de contraseña son requeridos.';
    passwordChangeError.value = errorMessage;
    showToast(errorMessage, 'error');
    return;
  }
  if (newPassword.value !== confirmNewPassword.value) {
    const errorMessage = 'La nueva contraseña y la confirmación no coinciden.';
    passwordChangeError.value = errorMessage;
    showToast(errorMessage, 'error');
    return;
  }
  if (newPassword.value.length < 8) {
    const errorMessage = 'La nueva contraseña debe tener al menos 8 caracteres.';
    passwordChangeError.value = errorMessage;
    showToast(errorMessage, 'error');
    return;
  }

  isChangingPassword.value = true;
  try {
    const response = await changePassword({
      currentPassword: currentPassword.value,
      newPassword: newPassword.value,
    });
    if (response.success) {
      const successMessage = response.message || 'Contraseña actualizada con éxito.';
      passwordChangeSuccessMessage.value = successMessage;
      showToast(successMessage, 'success');
      currentPassword.value = '';
      newPassword.value = '';
      confirmNewPassword.value = '';
    } else {
      const errorMessage = response.message || 'Error al cambiar la contraseña.';
      passwordChangeError.value = errorMessage;
      showToast(errorMessage, 'error');
    }
  } catch (err: any) {
    const errorMessage = err.message || 'Error desconocido al cambiar la contraseña.';
    passwordChangeError.value = errorMessage;
    showToast(errorMessage, 'error');
  } finally {
    isChangingPassword.value = false;
  }
}

function openDeletionModal() {
  deletionError.value = null;
  deletionSuccessMessage.value = null;
  showConfirmDeletionModal.value = true;
}

async function handleRequestAccountDeletion() {
  deletionError.value = null;
  deletionSuccessMessage.value = null;
  isRequestingDeletion.value = true;

  try {
    const response = await requestAccountDeletion();
    if (response.success) {
      const successMessage = response.message || 'Solicitud de eliminación enviada con éxito.';
      deletionSuccessMessage.value = successMessage;
      showToast(successMessage, 'success');
      showConfirmDeletionModal.value = false;
    } else {
      const errorMessage = response.message || 'Error al procesar la solicitud de eliminación.';
      deletionError.value = errorMessage;
      showToast(errorMessage, 'error');
    }
  } catch (err: any) {
    const errorMessage = err.message || 'Error desconocido al procesar la solicitud.';
    deletionError.value = errorMessage;
    showToast(errorMessage, 'error');
  } finally {
    isRequestingDeletion.value = false;
  }
}

onMounted(() => {
  fetchUserProfile();
});

</script>

<style scoped>
/* Puedes añadir estilos específicos aquí si es necesario */
</style> 