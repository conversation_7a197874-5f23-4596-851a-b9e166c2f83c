<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <RouterLink :to="{ name: 'Landing' }">
        <img class="mx-auto h-12 w-auto" src="/img/logo-impacto-automation-valorador.svg" alt="Impacto Valorador IA" />
      </RouterLink>
      <h2 ref="pageTitleSectionRef" class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        {{ pageTitle }}
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Comienza tu prueba gratuita de 7 días. Hoy no pagas nada. Cancela cuando quieras.
      </p>
    </div>

    <!-- Pantalla de carga general para el proceso post-redirección -->
    <div v-if="isFinalizingPostRedirect" class="fixed inset-0 bg-white bg-opacity-90 flex flex-col items-center justify-center z-50">
      <svg class="animate-spin h-12 w-12 text-impacto-orange mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="text-lg font-medium text-gray-700">Finalizando tu registro...</p>
      <p class="text-sm text-gray-500">Esto puede tardar unos segundos. Por favor, espera.</p>
    </div>

    <div v-if="!isFinalizingPostRedirect" ref="formMainContainerRef" class="mt-8 sm:mx-auto sm:w-full" :class="showPlanOptions ? 'sm:max-w-3xl' : 'sm:max-w-lg'">
      <div class="bg-white py-8 px-4 shadow-xl ring-1 ring-gray-900/10 sm:rounded-xl sm:px-10">
        
        <!-- Selección de Plan (si no hay plan en URL o se ha elegido cambiar) -->
        <div v-if="showPlanOptions" ref="planOptionsSectionRef" class="mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-1 text-center">
            {{ selectedPlan ? 'Plan seleccionado - Puedes cambiarlo si quieres' : 'Elige el plan perfecto para ti' }}
          </h3>
          <p v-if="!selectedPlan" class="text-sm text-gray-500 mb-6 text-center">
            Todos los planes incluyen <strong>7 días de prueba gratuita</strong>. Pago hoy: <strong>0€</strong>.
          </p>
          <p v-else class="text-sm text-gray-500 mb-6 text-center">
            Haz clic en cualquier otro plan para cambiarlo. <strong>7 días gratis incluidos</strong>.
          </p>

          <!-- Selector de ciclo de facturación -->
          <div class="mb-8 flex justify-center items-center space-x-3">
            <span class="text-sm text-gray-700">Facturación:</span>
            <div class="relative flex items-center p-0.5 bg-impacto-blue/10 rounded-full shadow-sm">
              <button
                @click="selectedBillingCycle = 'monthly'"
                :class="['px-4 py-1.5 text-xs sm:text-sm font-medium rounded-full transition-colors duration-200 ease-in-out relative z-10',
                        selectedBillingCycle === 'monthly' ? 'bg-impacto-orange text-white shadow' : 'text-gray-600 hover:text-impacto-blue']"
              >
                Mensual
              </button>
              <button
                @click="selectedBillingCycle = 'annual'"
                :class="['px-4 py-1.5 text-xs sm:text-sm font-medium rounded-full transition-colors duration-200 ease-in-out relative z-10 flex items-center gap-1.5',
                        selectedBillingCycle === 'annual' ? 'bg-impacto-orange text-white shadow' : 'text-gray-600 hover:text-impacto-blue']"
              >
                Anual
                <span class="bg-green-500 text-white text-[0.6rem] font-bold px-1.5 py-0.5 rounded-sm leading-none">
                  AHORRO
                </span>
              </button>
            </div>
          </div>

          <div ref="planCardsGridRef" class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Plan IA Starter -->
            <div
              @click="selectPlanOnClick('ia-starter')"
              :class="['p-6 border-2 rounded-xl cursor-pointer hover:shadow-xl transition-all duration-300 flex flex-col transform hover:scale-105',
                       selectedPlan?.id === 'ia-starter' ? 'border-impacto-orange ring-4 ring-impacto-orange/20 shadow-xl bg-impacto-orange/5' : 'border-gray-200 hover:border-impacto-orange/50']"
            >
              <div class="flex items-center justify-between">
                <h4 class="text-lg font-semibold text-impacto-blue">IA Starter</h4>
                <div v-if="selectedPlan?.id === 'ia-starter'" class="flex items-center text-impacto-orange">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </div>
              <div class="mt-2 mb-3">
                <span class="text-2xl font-bold text-gray-900">
                  {{getPriceForDisplay('ia-starter').mainPrice }}
                </span>
                <span class="text-sm font-normal text-gray-500">/mes</span>
                <p v-if="selectedBillingCycle === 'annual'" class="text-xs text-gray-500">
                  Total {{ getPriceForDisplay('ia-starter').annualTotal }}€ al año
                </p>
              </div>
              <ul class="mt-auto space-y-2 text-sm text-gray-600">
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Acceso para <strong>1 usuario</strong></li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Valorador IA 100% personalizado</li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Landing page automática de valoración</li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Valoraciones ilimitadas</li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Secuencia IA básica de nutrición automática</li>
              </ul>
            </div>

            <!-- Plan IA Pro -->
            <div
              @click="selectPlanOnClick('ia-pro')"
              :class="['relative p-6 border-2 rounded-xl cursor-pointer hover:shadow-xl transition-all duration-300 flex flex-col transform hover:scale-105',
                       selectedPlan?.id === 'ia-pro' ? 'border-impacto-orange ring-4 ring-impacto-orange/20 shadow-xl bg-impacto-orange/5' : 'border-gray-200 hover:border-impacto-orange/50']"
            >
              <div v-if="planDetailsData['ia-pro'].isPopular" class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2">
                <span class="inline-flex items-center px-3 py-0.5 rounded-full text-xs font-semibold bg-impacto-orange text-white shadow-md">
                  RECOMENDADO
                </span>
              </div>
              <div class="flex items-center justify-between pt-3">
                <h4 class="text-lg font-semibold text-impacto-blue">IA Pro</h4>
                <div v-if="selectedPlan?.id === 'ia-pro'" class="flex items-center text-impacto-orange">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </div>
              <div class="mt-2 mb-3">
                <span class="text-2xl font-bold text-gray-900">
                  {{ getPriceForDisplay('ia-pro').mainPrice }}
                </span>
                <span class="text-sm font-normal text-gray-500">/mes</span>
                <p v-if="selectedBillingCycle === 'annual'" class="text-xs text-gray-500">
                   Total {{ getPriceForDisplay('ia-pro').annualTotal }}€ al año
                </p>
              </div>
              <ul class="mt-auto space-y-2 text-sm text-gray-600">
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" /><strong>Todo en IA Starter, más:</strong></li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Acceso hasta <strong>4 usuarios</strong></li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Analíticas detalladas (tráfico, conversiones, CTR, aperturas)</li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Secuencias IA múltiples según comportamiento</li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Integraciones exclusivas <span class="text-xs bg-gray-400 text-white px-1.5 py-0.5 rounded-full ml-1">Próximamente</span></li>
              </ul>
            </div>

            <!-- Plan IA Élite -->
            <div
              @click="selectPlanOnClick('ia-elite')"
              :class="['p-6 border-2 rounded-xl cursor-pointer hover:shadow-xl transition-all duration-300 flex flex-col transform hover:scale-105',
                       selectedPlan?.id === 'ia-elite' ? 'border-impacto-orange ring-4 ring-impacto-orange/20 shadow-xl bg-impacto-orange/5' : 'border-gray-200 hover:border-impacto-orange/50']"
            >
              <div class="flex items-center justify-between">
                <h4 class="text-lg font-semibold text-impacto-blue">IA Élite</h4>
                <div v-if="selectedPlan?.id === 'ia-elite'" class="flex items-center text-impacto-orange">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </div>
              <div class="mt-2 mb-3">
                <span class="text-2xl font-bold text-gray-900">
                  {{ getPriceForDisplay('ia-elite').mainPrice }}
                </span>
                <span class="text-sm font-normal text-gray-500">/mes</span>
                <p v-if="selectedBillingCycle === 'annual'" class="text-xs text-gray-500">
                   Total {{ getPriceForDisplay('ia-elite').annualTotal }}€ al año
                </p>
              </div>
              <ul class="mt-auto space-y-2 text-sm text-gray-600">
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" /><strong>Todo en IA Pro, más:</strong></li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Acceso hasta <strong>7 usuarios</strong></li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Hasta <strong>5 valoradores IA</strong> diferentes</li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Secuencias IA avanzadas de largo plazo</li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Soporte prioritario</li>
                <li><CheckIcon class="h-5 w-5 text-green-500 inline mr-2" />Configuración personalizada</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Resumen del Plan Seleccionado -->
        <div v-if="selectedPlan && !showPlanOptions" ref="planSummarySectionRef" class="mb-8 p-6 bg-gray-50 rounded-xl border border-gray-200">
          <div class="flex justify-between items-center">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Estás contratando:</h3>
              <p class="text-xl font-bold text-impacto-blue">
                {{ selectedPlan.name }} - {{ getPriceForDisplay(selectedPlan.id).mainPrice }}/mes
                <span v-if="selectedBillingCycle === 'annual'" class="text-sm font-normal text-gray-600">
                  (Total {{ getPriceForDisplay(selectedPlan.id).annualTotal }}€ al año)
                </span>
              </p>
            </div>
            <button @click="allowPlanChange" class="text-sm text-impacto-orange hover:text-white font-medium py-2 px-4 rounded-lg border-2 border-impacto-orange hover:bg-impacto-orange transition-all duration-200 transform hover:scale-105">
              ✏️ Cambiar plan
            </button>
          </div>
          <ul class="mt-4 space-y-1 text-sm text-gray-700">
            <li v-for="(benefit, index) in selectedPlan?.benefits || []" :key="index" class="flex items-start">
              <component :is="benefit.icon" :class="['flex-shrink-0 h-5 w-5 inline mr-2 mt-0.5', benefit.iconColor]" />
              <span :class="{'font-semibold': benefit.icon === StarIcon}">
                {{ benefit.text }}
                <span v-if="benefit.subtext" class="block pl-7 text-xs text-gray-500">{{ benefit.subtext }}</span>
              </span>
            </li>
          </ul>
        </div>

        <!-- Formulario de Creación de Cuenta y Pago -->
        <form @submit.prevent="handleSignup" class="space-y-8">
          <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900">1. Crear Cuenta</h3>
            <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              <div class="sm:col-span-6">
                <label for="fullName" class="block text-sm font-medium text-gray-700">Nombre completo</label>
                <input type="text" v-model="formData.fullName" id="fullName" autocomplete="name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm" />
                <p v-if="formErrors.fullName" class="mt-1 text-xs text-red-600">{{ formErrors.fullName }}</p>
              </div>

              <div class="sm:col-span-6">
                <label for="agencyName" class="block text-sm font-medium text-gray-700">Nombre de tu agencia/negocio</label>
                <input type="text" v-model="formData.agencyName" id="agencyName" autocomplete="organization" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm" placeholder="Ej: Inmobiliaria Sol Brillante" />
                <p v-if="formErrors.agencyName" class="mt-1 text-xs text-red-600">{{ formErrors.agencyName }}</p>
                <p class="mt-1 text-xs text-gray-500">Si eres un agente individual, puedes dejarlo vacío.</p>
              </div>

              <div class="sm:col-span-6">
                <label for="email" class="block text-sm font-medium text-gray-700">Dirección de email</label>
                <input type="email" v-model="formData.email" id="email" autocomplete="email" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm" />
                <p v-if="formErrors.email" class="mt-1 text-xs text-red-600">{{ formErrors.email }}</p>
              </div>

              <div class="sm:col-span-3">
                <label for="password" class="block text-sm font-medium text-gray-700">Contraseña</label>
                <input type="password" v-model="formData.password" id="password" autocomplete="new-password" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm" />
                <p v-if="formErrors.password" class="mt-1 text-xs text-red-600">{{ formErrors.password }}</p>
              </div>

              <div class="sm:col-span-3">
                <label for="passwordConfirm" class="block text-sm font-medium text-gray-700">Confirmar contraseña</label>
                <input type="password" v-model="formData.passwordConfirm" id="passwordConfirm" autocomplete="new-password" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm" />
                <p v-if="formErrors.passwordConfirm" class="mt-1 text-xs text-red-600">{{ formErrors.passwordConfirm }}</p>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900">2. Detalles de Pago</h3>
            <p class="mt-1 text-sm text-gray-500">
              Introduce tus datos de pago para iniciar tu prueba gratuita de 7 días. No se te cobrará nada hoy.
              Puedes cancelar en cualquier momento antes de que finalice la prueba si decides que no es para ti.
            </p>
            <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              <div class="sm:col-span-6">
                <label for="payment-element" class="block text-sm font-medium text-gray-700 mb-1">Datos de pago</label>
                <div id="payment-element" class="mt-1 block w-full rounded-md shadow-sm focus-within:ring-impacto-orange focus-within:border-impacto-orange"></div>
                <div v-if="isInitializingPayment" class="mt-2 text-sm text-gray-500 flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Inicializando métodos de pago...
              </div>
                <p v-if="stripeError" class="mt-1 text-xs text-red-600">{{ stripeError }}</p>
              </div>
            </div>
          </div>

          <div class="sm:col-span-6">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <input id="termsAccepted" name="termsAccepted" type="checkbox" v-model="formData.termsAccepted" class="h-4 w-4 text-impacto-orange border-gray-300 rounded focus:ring-impacto-orange">
              </div>
              <div class="ml-3 text-sm">
                <label for="termsAccepted" class="font-medium text-gray-700">Acepto los <a href="#" target="_blank" class="text-impacto-blue hover:underline">Términos de Servicio</a> y la <a href="#" target="_blank" class="text-impacto-blue hover:underline">Política de Privacidad</a>.</label>
              </div>
            </div>
            <p v-if="formErrors.termsAccepted" class="mt-1 text-xs text-red-600">{{ formErrors.termsAccepted }}</p>
          </div>

          <!-- Sección de cupón -->
          <div class="border-t border-gray-200 pt-4 mt-4">
            <div v-if="!showCouponField" class="text-center">
              <button type="button" @click="showCouponField = true" class="text-sm text-impacto-blue hover:text-impacto-orange font-medium">
                ¿Tienes un código de cupón? Haz clic aquí
              </button>
            </div>

            <div v-if="showCouponField" class="space-y-4">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-gray-900">Código de cupón</h4>
                <button type="button" @click="hideCouponField" class="text-xs text-gray-500 hover:text-gray-700">
                  Cancelar
                </button>
              </div>

              <div class="flex space-x-3">
                <div class="flex-1">
                  <input
                    type="text"
                    v-model="formData.couponCode"
                    placeholder="Ingresa tu código de descuento"
                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-orange focus:border-impacto-orange sm:text-sm"
                    :disabled="isValidatingCoupon"
                  />
                </div>
                <button
                  type="button"
                  @click="validateCoupon"
                  :disabled="!formData.couponCode.trim() || isValidatingCoupon"
                  class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-impacto-orange hover:bg-impacto-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="isValidatingCoupon">...</span>
                  <span v-else>Aplicar</span>
                </button>
              </div>

              <!-- Mensajes de estado del cupón -->
              <div v-if="couponError" class="text-sm text-red-600">
                {{ couponError }}
              </div>
              <div v-if="appliedCoupon" class="text-sm text-green-600 flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                {{ appliedCoupon.discount_text }} aplicado
                <span v-if="appliedCoupon.duration_text" class="ml-2 text-xs text-gray-500">
                  ({{ appliedCoupon.duration_text }})
                </span>
              </div>
            </div>
          </div>

          <div class="pt-2">
            <button 
              type="submit" 
              :disabled="isSubmitDisabled"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-impacto-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <span v-if="isLoading">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Procesando...
              </span>
              <span v-else>
                Iniciar prueba gratuita de 7 días (Pago hoy: 0€)
              </span>
            </button>
          </div>
        </form>

        <p class="mt-8 text-center text-xs text-gray-500">
          Al crear una cuenta, aceptas nuestros <RouterLink :to="{ name: 'TermsOfService' }" class="font-medium text-impacto-blue hover:text-impacto-orange">Términos de Servicio</RouterLink> y <RouterLink :to="{ name: 'PrivacyPolicy' }" class="font-medium text-impacto-blue hover:text-impacto-orange">Política de Privacidad</RouterLink>.
          Los detalles de tu tarjeta son necesarios para iniciar la prueba gratuita de 7 días. No se te cobrará nada hoy. Cancela cuando quieras.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick, watchEffect } from 'vue';
import { useRoute, useRouter, RouterLink, onBeforeRouteUpdate } from 'vue-router';
import { CheckIcon, StarIcon } from '@heroicons/vue/20/solid';
import gsap from 'gsap';
import { loadStripe } from '@stripe/stripe-js';
import type { Stripe, StripeElements, StripePaymentElement, StripeError } from '@stripe/stripe-js';
import { useToast } from '@/composables/useToast';
import { apiFetch } from '@/utils/apiFetch';

interface PlanBenefit {
  text: string;
  icon: any;
  iconColor: string;
  subtext?: string;
}

interface PlanOption {
  id: string; // plan_slug
  name: string;
  price_monthly: number;
  price_annual: number;
  currency: string;
  stripe_price_id_monthly: string; // Necesario si Stripe se configura desde el frontend
  stripe_price_id_annual: string;  // Necesario si Stripe se configura desde el frontend
  benefits: Array<PlanBenefit>;
  isPopular?: boolean;
}

interface FormData {
  fullName: string;
  email: string;
  password: string;
  passwordConfirm: string;
  agencyName: string; // CAMBIO: Añadido
  termsAccepted: boolean;
  couponCode: string;
  stripe?: string;
  billing_cycle?: string;
}
interface FormErrors {
  fullName?: string;
  email?: string;
  password?: string;
  passwordConfirm?: string;
  agencyName?: string; // CAMBIO: Añadido
  termsAccepted?: string;
  couponCode?: string;
  plan?: string;
  stripe?: string;
}

const route = useRoute();
const router = useRouter();
const { showToast } = useToast();

// Definir los planes disponibles
const planDetailsData: Record<string, PlanOption> = {
  'ia-starter': {
    id: 'ia-starter',
    name: 'IA Starter',
    price_monthly: 49,
    price_annual: 420, 
    currency: 'EUR',
    stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_STARTER_MONTHLY || 'price_starter_monthly_placeholder', 
    stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_STARTER_ANNUAL || 'price_starter_annual_placeholder',   
    benefits: [
      { text: 'Acceso para 1 usuario', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Valorador IA 100% personalizado', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Landing page automática de valoración', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Integración nativa para tu web', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Valoraciones ilimitadas', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Captación de leads ilimitada', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Secuencia IA básica de nutrición automática', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Exportación de leads y valoraciones', icon: CheckIcon, iconColor: 'text-green-500' },
    ]
  },
  'ia-pro': {
    id: 'ia-pro',
    name: 'IA Pro',
    price_monthly: 99,
    price_annual: 780, 
    currency: 'EUR',
    stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_PRO_MONTHLY || 'price_pro_monthly_placeholder', 
    stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_PRO_ANNUAL || 'price_pro_annual_placeholder',   
    isPopular: true,
    benefits: [
      { text: 'Todo en IA Starter, más:', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Acceso hasta 4 usuarios', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Analíticas detalladas (tráfico, conversiones, CTR, aperturas)', icon: StarIcon, iconColor: 'text-impacto-orange' },
      { text: 'Secuencias IA múltiples según comportamiento', icon: StarIcon, iconColor: 'text-impacto-orange' },
      { text: 'Integraciones exclusivas', icon: StarIcon, iconColor: 'text-impacto-orange', subtext: 'Próximamente' },
    ]
  },
  'ia-elite': {
    id: 'ia-elite',
    name: 'IA Élite',
    price_monthly: 199,
    price_annual: 1500, 
    currency: 'EUR',
    stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_ELITE_MONTHLY || 'price_elite_monthly_placeholder', 
    stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_ELITE_ANNUAL || 'price_elite_annual_placeholder',   
    benefits: [
      { text: 'Todo en IA Pro, más:', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Acceso hasta 7 usuarios', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Hasta 5 valoradores IA diferentes', icon: StarIcon, iconColor: 'text-impacto-orange' },
      { text: 'Secuencias IA avanzadas de largo plazo', icon: StarIcon, iconColor: 'text-impacto-orange' },
      { text: 'Soporte prioritario', icon: CheckIcon, iconColor: 'text-green-500' },
      { text: 'Configuración personalizada', icon: CheckIcon, iconColor: 'text-green-500' },
    ]
  }
};

const selectedPlan = ref<PlanOption | null>(null);
const selectedBillingCycle = ref<'monthly' | 'annual'>('monthly');
const showPlanOptions = ref(true);
const isLoading = ref(false);
const initiallyLoaded = ref(false);
const stripeError = ref<string | null>(null);

// Stripe refs
let stripe: Stripe | null = null;
let elements: StripeElements | null = null;
let paymentElement: StripePaymentElement | null = null;

const stripeCustomerId = ref<string | null>(null);
const setupIntentClientSecret = ref<string | null>(null);
const isInitializingPayment = ref(false);

// Estados para cupones
const appliedCoupon = ref<any>(null);
const showCouponField = ref(false);
const isValidatingCoupon = ref(false);
const couponError = ref<string>('');

// Nuevos estados para el flujo de confirmación de pago de suscripción
const isConfirmingSubscriptionPayment = ref(false);
const FINALIZATION_DATA_KEY = 'valoradorPendingFinalizationData';

// Animation Refs
const pageTitleSectionRef = ref<HTMLElement | null>(null);
const formMainContainerRef = ref<HTMLElement | null>(null);
const planOptionsSectionRef = ref<HTMLElement | null>(null);
const planCardsGridRef = ref<HTMLElement | null>(null);
const planSummarySectionRef = ref<HTMLElement | null>(null);

const formData = ref<FormData>({
  fullName: '',
  email: '',
  password: '',
  passwordConfirm: '',
  agencyName: '', // CAMBIO: Añadido
  termsAccepted: false,
  couponCode: '',
  stripe: undefined,
  billing_cycle: undefined,
});

const formErrors = ref<FormErrors>({
  fullName: '',
  email: '',
  password: '',
  passwordConfirm: '',
  agencyName: '', // CAMBIO: Añadido
  termsAccepted: '',
});

const pageTitle = computed(() => {
  if (showPlanOptions.value || !selectedPlan.value) {
    // Si estamos mostrando las opciones de plan, o si aún no hay ningún plan seleccionado (incluso si showPlanOptions es false temporalmente)
    return 'Elige tu Plan Ideal';
  }
  // Si hay un plan seleccionado y no estamos mostrando las opciones (estamos en la vista de formulario/resumen)
  return `Revisa y completa: Plan ${selectedPlan.value.name}`;
});

const selectPlanOnClick = (planId: string) => {
  const plan = planDetailsData[planId];
  if (plan) {
    selectedPlan.value = plan;
    formErrors.value.plan = undefined;
    showPlanOptions.value = false; // Ocultar opciones de plan para mostrar resumen y formulario
  }
};

const allowPlanChange = () => {
  showPlanOptions.value = true;
  // selectedPlan.value = null; // No deseleccionar, para que el título pueda ser "Elige tu nuevo plan" o similar si se desea.
};

const isSubmitDisabled = computed(() => {
  return isLoading.value ||
         !selectedPlan.value ||
         !formData.value.termsAccepted ||
         !!stripeError.value || // Disable if there's a stripe error message
         !formData.value.fullName ||
         !formData.value.email ||
         !formData.value.password ||
         !formData.value.passwordConfirm;
});

// Funciones para manejar cupones
const hideCouponField = () => {
  showCouponField.value = false;
  formData.value.couponCode = '';
  appliedCoupon.value = null;
  couponError.value = '';
};

const validateCoupon = async () => {
  if (!formData.value.couponCode.trim()) return;

  isValidatingCoupon.value = true;
  couponError.value = '';
  appliedCoupon.value = null;

  try {
    const response = await apiFetch('validate-coupon.php', {
      method: 'POST',
      body: JSON.stringify({
        coupon_code: formData.value.couponCode.trim(),
        customer_id: stripeCustomerId.value
      })
    });

    if (response.success) {
      appliedCoupon.value = response.coupon;
      couponError.value = '';
    } else {
      couponError.value = response.message || 'Código de cupón no válido';
      appliedCoupon.value = null;
    }
  } catch (error) {
    console.error('Error validating coupon:', error);
    couponError.value = 'Error al validar el cupón. Inténtalo de nuevo.';
    appliedCoupon.value = null;
  } finally {
    isValidatingCoupon.value = false;
  }
};

const validateForm = (): boolean => {
  const errors: FormErrors = {};
  if (!selectedPlan.value) errors.plan = 'Debes seleccionar un plan para continuar.';
  if (!formData.value.fullName.trim()) errors.fullName = 'El nombre completo es obligatorio.';
  
  // Validación opcional para agencyName
  if (formData.value.agencyName.trim().length > 100) {
    errors.agencyName = 'El nombre de la agencia no debe exceder los 100 caracteres.';
  }

  if (!formData.value.email.trim()) {
    errors.email = 'El email es obligatorio.';
  } else if (!/\S+@\S+\.\S+/.test(formData.value.email)) {
    errors.email = 'El formato del email no es válido.';
  }
  if (!formData.value.password) {
    errors.password = 'La contraseña es obligatoria.';
  } else if (formData.value.password.length < 8) {
    errors.password = 'La contraseña debe tener al menos 8 caracteres.';
  }
  if (formData.value.password !== formData.value.passwordConfirm) {
    errors.passwordConfirm = 'Las contraseñas no coinciden.';
  }
  
  // Stripe Element internal validation is checked during createPaymentMethod.
  // stripeError.value will be set if cardElement.on('change', ...) detects an issue OR if createPaymentMethod fails.
  if (stripeError.value) {
    errors.stripe = stripeError.value;
  }

  if (!formData.value.termsAccepted) errors.termsAccepted = 'Debes aceptar los términos y condiciones para continuar.';
  
  formErrors.value = errors;
  return Object.keys(errors).filter(key => !!errors[key as keyof FormErrors]).length === 0;
};

const isFinalizingPostRedirect = ref(false);

// NUEVA FUNCIÓN para enviar datos al servidor
const submitSignupDataToServer = async (payload: {
  fullName: string;
  email: string;
  password: string;
  plan_slug: string;
  billing_cycle: 'monthly' | 'annual';
  paymentMethodId: string;
  stripeCustomerId: string;
  nombre_agencia: string; // AÑADIDO
}) => {
  isLoading.value = true; // Usar el loader general existente o uno específico
  stripeError.value = null;

  try {
    
    const signupUrl = 'signup.php';
    
    const responseData = await apiFetch(signupUrl, {
      method: 'POST',
      body: JSON.stringify(payload),
    });

    if (responseData.success && responseData.status === 'requires_action') {
      stripeError.value = null; // Limpiar errores previos
      formErrors.value.stripe = undefined;
      isLoading.value = false; // Apagar loader general por ahora
      
      const { paymentIntentClientSecret, subscriptionId, stripeCustomerId: returnedStripeCustomerId, formDataForFinalize } = responseData;
      if (!paymentIntentClientSecret || !subscriptionId || !returnedStripeCustomerId || !formDataForFinalize) {
        // console.error('[SIGNUP RESPONSE] Faltan datos críticos en la respuesta "requires_action".');
        stripeError.value = 'Error de comunicación con el servidor (faltan datos para SCA).';
        isConfirmingSubscriptionPayment.value = false;
        return;
      }

      // Guardar datos necesarios para la finalización después de la acción de Stripe
      // Estos datos se usarán SI stripe.confirmCardPayment redirige. Si no, los usaremos directamente.
      const finalizationPayloadForStorage = {
        stripeSubscriptionId: subscriptionId,
        stripeCustomerId: returnedStripeCustomerId,
        originalFormData: { 
          ...formDataForFinalize,
          password: payload.password, // payload aquí se refiere al argumento de submitSignupDataToServer
          nombre_agencia: payload.nombre_agencia // AÑADIDO
        }
      };
      try {
        sessionStorage.setItem(FINALIZATION_DATA_KEY, JSON.stringify(finalizationPayloadForStorage));
      } catch (e) {
        // console.error('Error al guardar datos de finalización en sessionStorage:', e);
        stripeError.value = 'Error al preparar la confirmación del pago.';
        isConfirmingSubscriptionPayment.value = false;
        return;
      }
      
      if (!stripe) {
        stripeError.value = 'Stripe no está inicializado para confirmar el pago de la suscripción.';
        isConfirmingSubscriptionPayment.value = false;
        return;
      }

      // Mostrar mensaje al usuario ANTES de la posible interacción con Stripe (popup/redirección)
      isConfirmingSubscriptionPayment.value = true; // Activar loader/mensaje específico para confirmación de pago
      showToast('Se requiere un paso adicional para confirmar tu pago. Sigue las instrucciones de tu banco.', 'info', 7000);
      
      const { error: confirmError, paymentIntent: confirmedPaymentIntent } = await stripe.confirmCardPayment(paymentIntentClientSecret, {
        return_url: `${window.location.origin}/contratacion?payment_intent_completed=true`,
      });

      isConfirmingSubscriptionPayment.value = false; 

      if (confirmError) {
        // console.error('[STRIPE confirmCardPayment] Error:', confirmError);
        stripeError.value = confirmError.message || 'Error al confirmar el pago de la suscripción.';
        sessionStorage.removeItem(FINALIZATION_DATA_KEY); 
        if (stripeError.value) showToast(stripeError.value, 'error', 10000); // Asegurar que no sea null
      } else if (confirmedPaymentIntent && confirmedPaymentIntent.status === 'succeeded') {
        stripeError.value = null;
        
        const finalizationDataJSON = sessionStorage.getItem(FINALIZATION_DATA_KEY);
        sessionStorage.removeItem(FINALIZATION_DATA_KEY);

        if (!finalizationDataJSON) {
          stripeError.value = 'Faltan datos de sesión para finalizar el registro post-confirmación de pago.';
          if (stripeError.value) showToast(stripeError.value, 'error', 10000);
          return;
        }
        let finalizationPayloadRetrieved;
        try {
          finalizationPayloadRetrieved = JSON.parse(finalizationDataJSON);
        } catch (e) {
          stripeError.value = 'Error al leer datos de finalización de sesión.';
          if (stripeError.value) showToast(stripeError.value, 'error', 10000);
          return;
        }

        const { stripeSubscriptionId: subId, stripeCustomerId: custId, originalFormData: oFD } = finalizationPayloadRetrieved;

        if (!subId || !custId || !oFD || !oFD.password) {
            stripeError.value = 'Datos para finalizar el registro incompletos en sesión (post-confirmCardPayment).';
            if (stripeError.value) showToast(stripeError.value, 'error', 10000);
            return;
        }
        
        isFinalizingPostRedirect.value = true; 
        try {
          const finalizeApiPayload = {
            stripeSubscriptionId: subId,
            stripeCustomerId: custId,
            paymentIntentId: confirmedPaymentIntent.id,
            originalFormData: oFD
          };
          const finalizeResponseData = await apiFetch('finalize-registration.php', {
            method: 'POST',
            body: JSON.stringify(finalizeApiPayload)
          });
          
          // ÉXITO FINAL! Usuario y suscripción creados, token recibido de finalize-registration
          const completeUserDataFromAPI = finalizeResponseData.user; 
          
          if (!completeUserDataFromAPI || !completeUserDataFromAPI.id || !completeUserDataFromAPI.email) {
            console.error('Error: Datos de usuario incompletos o inválidos de finalize-registration (flujo handleStripeRedirect).');
            stripeError.value = 'Error crítico de datos post-finalización.';
            showToast(stripeError.value, 'error', 10000);
            isFinalizingPostRedirect.value = false;
            router.push('/login');
            return;
          }

          if (!finalizeResponseData.token || typeof finalizeResponseData.token !== 'string' || finalizeResponseData.token.split('.').length !== 3) {
            console.error('Error: Token JWT inválido de finalize-registration (flujo handleStripeRedirect).');
            stripeError.value = 'Error de autenticación post-finalización.';
            showToast(stripeError.value, 'error', 10000);
            isFinalizingPostRedirect.value = false;
            router.push('/login');
            return;
          }

          const tokenString = finalizeResponseData.token;

          // Redirigir a la página de login de la app con el token para que ella gestione la sesión
          const appUrl = import.meta.env.VITE_APP_BASE_URL || 'https://app.inmoautomation.com';
          window.location.href = `${appUrl}/login?token=${tokenString}&firstVisit=true`;
          
        } catch (networkOrFinalizeError: any) {
          console.error('[FINALIZE API] Error de red o fetch:', networkOrFinalizeError);
          stripeError.value = (networkOrFinalizeError.data && networkOrFinalizeError.data.message) || networkOrFinalizeError.message || 'Error de conexión al finalizar tu registro.';
          if (stripeError.value) showToast(stripeError.value, 'error', 10000);
          isFinalizingPostRedirect.value = false;
        }
      } else if (confirmedPaymentIntent) {
        // console.warn('[STRIPE confirmCardPayment] PaymentIntent no \'succeeded\' inmediatamente. Estado:', confirmedPaymentIntent.status, 'Intent:', confirmedPaymentIntent);
        stripeError.value = `El pago está siendo procesado (estado: ${confirmedPaymentIntent.status}). Si no se completa en breve, contacta a soporte.`;
        if (stripeError.value) showToast(stripeError.value, 'error', 10000);
      } else {
      }

    } else if (responseData.success && responseData.token) {

      const completeUserDataFromAPI = responseData.user;

      if (!completeUserDataFromAPI || !completeUserDataFromAPI.id || !completeUserDataFromAPI.email) {
        // console.error('Error: Datos de usuario incompletos o inválidos del servidor tras signup.');
        stripeError.value = 'Error de datos de usuario post-registro.';
        if (isFinalizingPostRedirect.value) showToast(stripeError.value, 'error', 10000);
        // Considerar si se debe redirigir a login o mostrar un error más persistente.
        return; 
      }
      
      // Asegurarse de que el token es una cadena válida
      if (!responseData.token || typeof responseData.token !== 'string' || responseData.token.split('.').length !== 3) {
        // console.error('Error: Token JWT inválido del servidor tras signup.');
        stripeError.value = 'Error de autenticación post-registro. Intenta iniciar sesión manualmente.';
        if (isFinalizingPostRedirect.value) showToast(stripeError.value, 'error', 10000);
        router.push('/login'); 
        return; 
      }
      
      const tokenString = responseData.token;

      // No es necesario guardar en localStorage aquí, ya que se pasará por URL
      // y la página de login de la app se encargará de guardarlo.
      
      const appUrl = import.meta.env.VITE_APP_BASE_URL || 'https://app.inmoautomation.com';
      window.location.href = `${appUrl}/login?token=${tokenString}&firstVisit=true`;

    }
  } catch (networkError: any) {
    // console.error('Error de red o fetch en submitSignupDataToServer:', networkError);
    stripeError.value = (networkError.data && networkError.data.message) || networkError.message || 'No se pudo conectar con el servidor para finalizar tu registro.';
    formErrors.value.stripe = stripeError.value || undefined;
    if (isFinalizingPostRedirect.value && stripeError.value) showToast(stripeError.value, 'error', 10000);
  } finally {
    isLoading.value = false; // Apagar loader general
    // isFinalizingPostRedirect se mantendrá true si falla en ese flujo, para que el usuario vea el error
    // Si tiene éxito, la redirección ya habrá ocurrido.
  }
};

const handleSignup = async () => {
  stripeError.value = null; 
  if (!validateForm()) {
     if (formErrors.value.stripe && paymentElement) {
        formErrors.value.stripe = undefined;
    }
    const activeErrors = Object.keys(formErrors.value).filter(k => formErrors.value[k as keyof FormErrors]);
    if (activeErrors.length > 0) return;
  }
  
  isLoading.value = true; // Activamos el loader principal

  if (!stripe || !elements || !paymentElement || !setupIntentClientSecret.value || !stripeCustomerId.value) {
    stripeError.value = 'Error al inicializar el sistema de pago. Por favor, recarga la página.';
    formErrors.value.stripe = stripeError.value || undefined;
    isLoading.value = false;
    return;
  }

  // Guardar estado del formulario en sessionStorage ANTES de la redirección de Stripe (por si acaso)
  try {
    const formStateToSave = {
      formData: formData.value,
      selectedPlanId: selectedPlan.value?.id,
      selectedBillingCycle: selectedBillingCycle.value
    };
    sessionStorage.setItem('stripeRedirectFormState', JSON.stringify(formStateToSave));
    if(stripeCustomerId.value) sessionStorage.setItem('stripeCustomerIdForRedirect', stripeCustomerId.value);
    if(setupIntentClientSecret.value) sessionStorage.setItem('setupIntentClientSecretForRedirect', setupIntentClientSecret.value);
  } catch (e) {
    console.error('Error al guardar estado del formulario en sessionStorage (handleSignup):', e);
  }

  const result = await stripe.confirmSetup({
    elements,
    confirmParams: {
      return_url: `${window.location.origin}/contratacion?setup_intent_completed=true`,
    },
  });

  if (result.error) {
    stripeError.value = result.error.message || 'Error al procesar la información de pago.';
    formErrors.value.stripe = stripeError.value || undefined;
    isLoading.value = false;
    return;
  }

  if (!(result as any).setupIntent) {
    stripeError.value = 'Respuesta inesperada de Stripe: Falta setupIntent.';
    formErrors.value.stripe = stripeError.value || undefined;
    isLoading.value = false;
    return;
  }

  const setupIntent = (result as any).setupIntent;

  if (setupIntent.status === 'succeeded') {
    const paymentMethodIdFromSetup = setupIntent.payment_method;
    if (!paymentMethodIdFromSetup) {
        stripeError.value = 'No se pudo obtener el método de pago tras la configuración (flujo normal).';
        formErrors.value.stripe = stripeError.value || undefined;
        isLoading.value = false;
        return;
    }

    if (!selectedPlan.value || !stripeCustomerId.value) {
      console.error('Faltan datos críticos (plan o customerId) antes de llamar a submitSignupDataToServer desde handleSignup');
      stripeError.value = 'Error interno. Faltan datos para completar el registro.';
    isLoading.value = false;
    return;
  }

  const payload = {
    fullName: formData.value.fullName,
    email: formData.value.email,
    password: formData.value.password,
    plan_slug: selectedPlan.value.id,
    billing_cycle: selectedBillingCycle.value,
      paymentMethodId: typeof paymentMethodIdFromSetup === 'string' ? paymentMethodIdFromSetup : paymentMethodIdFromSetup.id,
      stripeCustomerId: stripeCustomerId.value,
      nombre_agencia: formData.value.agencyName.trim(), // AÑADIDO
      coupon_code: formData.value.couponCode
    };
    // No necesitamos isFinalizingPostRedirect aquí, ya que isLoading.value maneja el loader.
    await submitSignupDataToServer(payload); // Llamar a la nueva función
    // El finally de submitSignupDataToServer apagará isLoading.value

  } else if (setupIntent && setupIntent.status === 'processing') {
    stripeError.value = 'El pago se está procesando. Espera unos momentos.';
    isLoading.value = false;
  } else {
    stripeError.value = 'El estado del pago es incierto. Intenta de nuevo o contacta a soporte.';
    isLoading.value = false;
  }
  // La limpieza de URL de Stripe (si la hubo) la maneja onMounted.
};

// Nueva función para procesar los query params
const processUrlQueryParameters = async (query: Record<string, string | undefined>) => {
  const setupIntentClientSecretFromUrl = query.setup_intent_client_secret;
  const paymentIntentClientSecretFromUrl = query.payment_intent_client_secret;
  const redirectStatus = query.redirect_status;

  const isSetupIntentRedirect = setupIntentClientSecretFromUrl && redirectStatus === 'succeeded';
  const isPaymentIntentRedirect = paymentIntentClientSecretFromUrl && redirectStatus === 'succeeded';

  if (isSetupIntentRedirect || isPaymentIntentRedirect) {
    if (!stripeError.value) {
        isFinalizingPostRedirect.value = true; 
    }
    
    await handleStripeRedirect();

  } else {
    const queryString = new URLSearchParams(query as Record<string,string>).toString(); // Cast para URLSearchParams
    if (queryString !== '') {
        if (redirectStatus && redirectStatus !== 'succeeded') {
            stripeError.value = `El proceso de pago no se completó (${redirectStatus}). Por favor, inténtalo de nuevo.`;
            showToast(stripeError.value, 'error', 10000);
            router.replace({ query: undefined }); 
        }
    } else {
        if (!isFinalizingPostRedirect.value) {
            if (pageTitleSectionRef.value) {
                gsap.fromTo(pageTitleSectionRef.value, { autoAlpha: 0, y: -30 }, { autoAlpha: 1, y: 0, duration: 0.6, ease: 'power2.out' });
            }
            if (formMainContainerRef.value) {
                gsap.fromTo(formMainContainerRef.value, { autoAlpha: 0, y: 50 }, { autoAlpha: 1, y: 0, duration: 0.7, delay: 0.2, ease: 'power2.out' });
            }
        }
    }
  }
};

// --- ANIMATIONS, STRIPE INIT & INITIAL LOGIC --- 
onMounted(async () => {
  const currentUrlParams = new URLSearchParams(window.location.search);
  const queryForProcessing: Record<string, string | undefined> = {};
  currentUrlParams.forEach((value, key) => {
    queryForProcessing[key] = value;
  });
  
  await processUrlQueryParameters(queryForProcessing); 
});

// Nuevo hook para manejar actualizaciones de ruta sin remontaje completo
onBeforeRouteUpdate(async (to) => {
  const queryForProcessingUpdate = to.query as Record<string, string | undefined>;

  // Comprobamos si hay parámetros relevantes de Stripe en la nueva ruta (to.query)
  const hasStripeParams = queryForProcessingUpdate.payment_intent_client_secret || 
                          queryForProcessingUpdate.setup_intent_client_secret || 
                          queryForProcessingUpdate.setup_intent; // setup_intent también puede indicar una redirección de SetupIntent

  if (hasStripeParams) {
    await processUrlQueryParameters(queryForProcessingUpdate);
  }
});

const handleStripeRedirect = async (/* siClientSecret: string, status: string */) => {
  // Determinar si es una redirección de SetupIntent o de PaymentIntent de factura
  const urlParams = new URLSearchParams(window.location.search);
  const setupIntentClientSecretFromUrl = urlParams.get('setup_intent_client_secret');
  const paymentIntentClientSecretFromUrl = urlParams.get('payment_intent_client_secret');

  isFinalizingPostRedirect.value = true; 

  if (!stripe) {
    const stripePk = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
    if (stripePk) stripe = await loadStripe(stripePk);
    else {
      stripeError.value = 'Clave de Stripe no configurada.';
      showToast(stripeError.value, 'error', 10000);
      isFinalizingPostRedirect.value = false; return;
    }
  }
  if (!stripe) { 
    stripeError.value = 'Error al cargar Stripe para verificar estado.';
    showToast(stripeError.value, 'error', 10000);
    isFinalizingPostRedirect.value = false; return;
  }

  // Limpiar query params de la URL lo antes posible
  router.replace({ query: undefined });

  if (setupIntentClientSecretFromUrl) {
    const storedCustomerIdForSetup = sessionStorage.getItem('stripeCustomerIdForRedirect');
    const savedFormStateForSetupJSON = sessionStorage.getItem('stripeRedirectFormState');
  
    sessionStorage.removeItem('stripeCustomerIdForRedirect');
    sessionStorage.removeItem('setupIntentClientSecretForRedirect');
    sessionStorage.removeItem('stripeRedirectFormState');

    if (!storedCustomerIdForSetup || !savedFormStateForSetupJSON) {
      stripeError.value = 'Faltan datos de sesión para completar el guardado de tarjeta post-redirección.';
      showToast(stripeError.value, 'error', 10000);
      isFinalizingPostRedirect.value = false; 
      return;
    }
  
    const { error: retrieveSetupError, setupIntent: retrievedSetupIntent } = await stripe.retrieveSetupIntent(setupIntentClientSecretFromUrl!);

    if (retrieveSetupError) {
      stripeError.value = retrieveSetupError.message || 'Error al verificar estado del guardado de tarjeta post-redirección.';
      showToast(stripeError.value, 'error', 10000);
      isFinalizingPostRedirect.value = false;
    } else if (retrievedSetupIntent && retrievedSetupIntent.status === 'succeeded') {
      stripeError.value = null;
      const paymentMethodIdFromSetup = retrievedSetupIntent.payment_method;

      if (!paymentMethodIdFromSetup) {
        stripeError.value = "El método de pago no se pudo confirmar después de la redirección del SetupIntent.";
        showToast(stripeError.value, 'error', 10000);
        isFinalizingPostRedirect.value = false;
        return;
      }
    
      try {
        const savedFormState = JSON.parse(savedFormStateForSetupJSON);
        formData.value = savedFormState.formData || formData.value;
        if (savedFormState.selectedPlanId && planDetailsData[savedFormState.selectedPlanId]) {
          selectedPlan.value = planDetailsData[savedFormState.selectedPlanId];
        }
        if (savedFormState.selectedBillingCycle) {
          selectedBillingCycle.value = savedFormState.selectedBillingCycle;
        }

        if (!formData.value.fullName || !formData.value.email || !formData.value.password || !selectedPlan.value) {
           throw new Error("Datos del formulario (SetupIntent) restaurados incompletos para finalizar el registro.");
        }

        isFinalizingPostRedirect.value = true;
        
        const payload = {
          fullName: formData.value.fullName,
          email: formData.value.email,
          password: formData.value.password,
          plan_slug: selectedPlan.value.id,
          billing_cycle: selectedBillingCycle.value,
          paymentMethodId: typeof paymentMethodIdFromSetup === 'string' ? paymentMethodIdFromSetup : paymentMethodIdFromSetup.id,
          stripeCustomerId: storedCustomerIdForSetup,
          nombre_agencia: formData.value.agencyName.trim(),
          coupon_code: formData.value.couponCode
        };
        await submitSignupDataToServer(payload); 

      } catch (e: any) {
        stripeError.value = e.message || "Error al procesar la información guardada del SetupIntent. Por favor, intenta de nuevo.";
        showToast(stripeError.value!, 'error', 10000);
        isFinalizingPostRedirect.value = false;
      }
    } else if (retrievedSetupIntent && retrievedSetupIntent.status === 'processing') {
      stripeError.value = 'El guardado de tu método de pago aún se está procesando.';
      showToast(stripeError.value, 'info', 10000);
      isFinalizingPostRedirect.value = false; 
    } else {
      stripeError.value = `Estado del guardado de método de pago incierto (${retrievedSetupIntent?.status}). Intenta de nuevo.`;
      showToast(stripeError.value, 'error', 10000);
      isFinalizingPostRedirect.value = false;
    }

  } else if (paymentIntentClientSecretFromUrl) {
    isConfirmingSubscriptionPayment.value = false;

    const finalizationDataJSON = sessionStorage.getItem(FINALIZATION_DATA_KEY);
    sessionStorage.removeItem(FINALIZATION_DATA_KEY);

    if (!finalizationDataJSON) {
      stripeError.value = 'Faltan datos de sesión para finalizar el registro post-confirmación de pago.';
      showToast(stripeError.value, 'error', 10000);
      isFinalizingPostRedirect.value = false;
      return;
    }

    let finalizationPayload;
    try {
      finalizationPayload = JSON.parse(finalizationDataJSON);
    } catch (e) {
      stripeError.value = 'Error al leer datos de finalización de sesión.';
      showToast(stripeError.value, 'error', 10000);
      isFinalizingPostRedirect.value = false;
      return;
    }

    const { stripeSubscriptionId, stripeCustomerId: customerIdForFinalize, originalFormData } = finalizationPayload;

    if (!stripeSubscriptionId || !customerIdForFinalize || !originalFormData || !originalFormData.password) {
        stripeError.value = 'Datos para finalizar el registro incompletos en sesión.';
        showToast(stripeError.value, 'error', 10000);
        isFinalizingPostRedirect.value = false;
        return;
    }
      
    const { error: retrievePaymentError, paymentIntent: retrievedPaymentIntent } = await stripe.retrievePaymentIntent(paymentIntentClientSecretFromUrl!);

    if (retrievePaymentError) {
      stripeError.value = retrievePaymentError.message || 'Error al verificar el estado del pago de la suscripción post-redirección.';
      showToast(stripeError.value, 'error', 10000);
      isFinalizingPostRedirect.value = false;
    } else if (retrievedPaymentIntent && retrievedPaymentIntent.status === 'succeeded') {
      stripeError.value = null;

      const finalizeApiPayload = {
        stripeSubscriptionId,
        stripeCustomerId: customerIdForFinalize,
        paymentIntentId: retrievedPaymentIntent.id,
        originalFormData
      };

      try {
        const finalizeResponseData = await apiFetch('finalize-registration.php', {
          method: 'POST',
          body: JSON.stringify(finalizeApiPayload)
        });
        
        // ÉXITO FINAL! Usuario y suscripción creados, token recibido de finalize-registration
        const completeUserDataFromAPI = finalizeResponseData.user; 
        
        if (!completeUserDataFromAPI || !completeUserDataFromAPI.id || !completeUserDataFromAPI.email) {
          console.error('Error: Datos de usuario incompletos o inválidos de finalize-registration (flujo handleStripeRedirect).');
          stripeError.value = 'Error crítico de datos post-finalización.';
          showToast(stripeError.value, 'error', 10000);
          isFinalizingPostRedirect.value = false;
          router.push('/login');
          return;
        }

        if (!finalizeResponseData.token || typeof finalizeResponseData.token !== 'string' || finalizeResponseData.token.split('.').length !== 3) {
          console.error('Error: Token JWT inválido de finalize-registration (flujo handleStripeRedirect).');
          stripeError.value = 'Error de autenticación post-finalización.';
          showToast(stripeError.value, 'error', 10000);
          isFinalizingPostRedirect.value = false;
          router.push('/login');
          return;
        }

        const tokenString = finalizeResponseData.token;

        // Redirigir a la página de login de la app con el token para que ella gestione la sesión
        const appUrl = import.meta.env.VITE_APP_BASE_URL || 'https://app.inmoautomation.com';
        window.location.href = `${appUrl}/login?token=${tokenString}&firstVisit=true`;
        
      } catch (networkOrFinalizeError: any) {
        console.error('[FINALIZE API] Error de red o fetch:', networkOrFinalizeError);
        stripeError.value = (networkOrFinalizeError.data && networkOrFinalizeError.data.message) || networkOrFinalizeError.message || 'Error de conexión al finalizar tu registro.';
        if (stripeError.value) showToast(stripeError.value, 'error', 10000);
        isFinalizingPostRedirect.value = false;
      }

    } else if (retrievedPaymentIntent && retrievedPaymentIntent.status === 'processing') {
      stripeError.value = 'El pago de tu suscripción aún se está procesando.';
      showToast(stripeError.value, 'info', 10000);
      isFinalizingPostRedirect.value = false;
    } else {
      stripeError.value = `El pago de la suscripción no pudo ser confirmado (estado: ${retrievedPaymentIntent?.status}). Intenta de nuevo o contacta a soporte.`;
      showToast(stripeError.value, 'error', 10000);
      isFinalizingPostRedirect.value = false;
    }
  } else {
    isFinalizingPostRedirect.value = false;
    if (pageTitleSectionRef.value) {
      gsap.fromTo(pageTitleSectionRef.value, { autoAlpha: 0, y: -30 }, { autoAlpha: 1, y: 0, duration: 0.6, ease: 'power2.out' });
    }
    if (formMainContainerRef.value) {
      gsap.fromTo(formMainContainerRef.value, { autoAlpha: 0, y: 50 }, { autoAlpha: 1, y: 0, duration: 0.7, delay: 0.2, ease: 'power2.out' });
    }
  }
};

const initializeStripeAndElements = async () => {
  if (!formData.value.fullName.trim()) {
    return; 
  }
  if (!formData.value.email.trim()) {
    return; 
  }
  if (!/\S+@\S+\.\S+/.test(formData.value.email)) {
        return;
      }
      
  if (paymentElement) {
    return;
  }
  isInitializingPayment.value = true;
  stripeError.value = null;

  try {
    const initData = await apiFetch('initialize-checkout.php', {
      method: 'POST',
      body: JSON.stringify({
        email: formData.value.email,
        fullName: formData.value.fullName,
        coupon_code: formData.value.couponCode
      }),
    });

    stripeCustomerId.value = initData.stripeCustomerId;
    setupIntentClientSecret.value = initData.setupIntentClientSecret;

    // Aplicar cupón si se devolvió uno válido
    if (initData.coupon) {
      appliedCoupon.value = initData.coupon;
    }

  const stripePk = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
  if (!stripePk) {
      console.error('VITE_STRIPE_PUBLISHABLE_KEY no está definida.');
      throw new Error('La clave pública de Stripe no está configurada.');
  }

    if (!stripe) { 
    stripe = await loadStripe(stripePk);
    }

    if (!stripe) {
      console.error('Error: Stripe.js no se pudo cargar o inicializar.');
      throw new Error('No se pudo cargar Stripe.js');
    }
    
    const currentClientSecret = setupIntentClientSecret.value;
    if (!currentClientSecret) { 
        console.error('Client secret es null o undefined ANTES de crear Stripe Elements.');
        throw new Error('Client secret para SetupIntent no disponible.');
    }

    const appearance: {
      theme: 'stripe' | 'night' | 'flat' | undefined;
      variables?: Record<string, string>;
      rules?: Record<string, Record<string, string>>;
    } = {
      theme: 'flat',
      variables: {
        fontFamily: '"Inter", ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
        fontLineHeight: '1.5',
        borderRadius: '0.375rem', 
        colorPrimary: '#DD5800', 
        colorPrimaryText: '#FFFFFF',
        colorBackground: '#FFFFFF', 
        colorText: '#1F2937', 
        colorSecondaryText: '#4B5563', 
        colorDanger: '#EF4444', 
        spacingUnit: '0.5rem', 
        colorIcon: '#6B7280', 
      },
      rules: {
        '.Tab': {
          border: '1px solid #E5E7EB',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
          backgroundColor: '#FFFFFF',
          color: '#374151',
          padding: '12px 16px',
          borderRadius: '0.375rem',
        },
        '.Tab:hover': {
          backgroundColor: '#F9FAFB',
          borderColor: '#D1D5DB',
        },
        '.Tab--selected': {
          borderColor: '#DD5800',
          backgroundColor: '#FFFFFF',
          color: '#1F2937',
          fontWeight: '500',
          boxShadow: '0 0 0 1px #DD5800, 0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        },
        '.TabIcon': {
          color: '#6B7280 !important',
        },
        '.TabIcon--selected': {
          color: '#DD5800 !important',
        },
        // Estilos específicos para iconos de tarjeta
        '.p-TabIcon': {
          color: '#6B7280 !important',
          fill: '#6B7280 !important',
        },
        '.p-TabIcon--selected': {
          color: '#DD5800 !important',
          fill: '#DD5800 !important',
        },
        '.Input': { 
          border: '1px solid #D1D5DB', 
          borderRadius: '0.375rem', 
          padding: '0.625rem 0.75rem', 
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)', 
          backgroundColor: '#FFFFFF',
          transition: 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
        },
        '.Input--invalid': { 
          borderColor: '#EF4444', 
          boxShadow: '0 0 0 1px #EF4444',
        },
        '.Input:focus': { 
          borderColor: '#DD5800', 
          boxShadow: '0 0 0 1px #DD5800, 0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        },
        '.Label': {
          color: '#374151',
          fontWeight: '500',
          marginBottom: '0.25rem',
        },
        '.TabLabel': {
          color: '#374151',
          fontWeight: '500',
          fontSize: '14px',
        },
        '.TabLabel--selected': {
          color: '#1F2937',
          fontWeight: '600',
        },
        '.Error': {
          color: '#EF4444',
          fontSize: '0.75rem',
          marginTop: '0.25rem',
        },
        // Estilos específicos para elementos de pago
        '.p-PaymentMethodSelector': {
          backgroundColor: '#FFFFFF',
        },
        '.p-PaymentMethodSelector-option': {
          backgroundColor: '#FFFFFF',
          color: '#374151',
          border: '1px solid #E5E7EB',
        },
        '.p-PaymentMethodSelector-option--selected': {
          backgroundColor: '#FFFFFF',
          color: '#1F2937',
          borderColor: '#DD5800',
          fontWeight: '500',
        },
        '.p-PaymentMethodSelector-label': {
          color: '#374151 !important',
          fontWeight: '500 !important',
        },
        '.p-PaymentMethodSelector-label--selected': {
          color: '#1F2937 !important',
          fontWeight: '600 !important',
        }
      }
    };

    elements = stripe.elements({ clientSecret: currentClientSecret, appearance });
    if (!elements) {
      console.error('Stripe Elements no pudo ser creado. Client Secret:', currentClientSecret);
      throw new Error('Error al crear Stripe Elements.');
    }

    if (paymentElement) { 
        (paymentElement as any).destroy(); 
        paymentElement = null; 
    }

    const newPaymentElement = elements.create('payment', {});

    if (!newPaymentElement) {
      console.error('PaymentElement no pudo ser creado por elements.create().');
      throw new Error('Error al crear el PaymentElement.');
    }
    paymentElement = newPaymentElement; 
    
      await nextTick();
    
    const paymentElementDiv = document.getElementById('payment-element');

    let paymentElementTyped: StripePaymentElement | null = paymentElement;

    if (paymentElementDiv && paymentElementTyped) {
      try {
        paymentElementTyped.mount(paymentElementDiv);
        
        setTimeout(() => {
            const iframe = paymentElementDiv.querySelector('iframe');
            if (iframe) {
            }
        }, 500);

        paymentElementTyped.on('change', (event) => {
          if ('error' in event && event.error) {
            const errorFromEvent = event.error as StripeError;
            if (typeof errorFromEvent.message === 'string') {
              stripeError.value = errorFromEvent.message;
              formErrors.value.stripe = errorFromEvent.message || undefined;
            }
           } else {
             stripeError.value = null;
            formErrors.value.stripe = undefined;
          }
        });

      } catch (mountError: any) {
        console.error('[MOUNTING] Error durante paymentElement.mount():', mountError);
        stripeError.value = 'Error al mostrar el formulario de pago: ' + mountError.message;
      }
    } else if (!paymentElementDiv) {
      console.error('Div para PaymentElement (#payment-element) no encontrado.');
      stripeError.value = 'Error interno: No se encontró el contenedor del formulario de pago.';
      } else {
       console.error('Error inesperado: paymentElement es null justo antes de mount.');
       stripeError.value = 'Error interno: El elemento de pago no pudo ser creado.';
    }

  } catch (error: any) {
    console.error('Error initializing Stripe Elements:', error);
    stripeError.value = (error.data && error.data.message) || error.message || 'Error al configurar el formulario de pago.';
    formErrors.value.stripe = stripeError.value ? stripeError.value : undefined;
  } finally {
    isInitializingPayment.value = false;
  }
};

// Watchers actualizados para respetar isFinalizingPostRedirect
watch([() => formData.value.email, () => formData.value.fullName], (newValues, oldValues) => {
  if (isFinalizingPostRedirect.value) {
    return;
  }
  const [newEmail, newFullName] = newValues;
  const [oldEmail, oldFullName] = oldValues;

  if (newEmail && newFullName && !isInitializingPayment.value) {
    const emailChanged = oldEmail !== newEmail;
    const fullNameChanged = oldFullName !== newFullName;

    if (paymentElement && (emailChanged || fullNameChanged)) {
      if (paymentElement) {
        (paymentElement as any).destroy();
        paymentElement = null;
      }
      initializeStripeAndElements();
    } else if (!paymentElement) {
      initializeStripeAndElements();
    }
  }
}, { deep: false, immediate: true }); 

watch(showPlanOptions, async (isShowingOptions) => {
  if (isFinalizingPostRedirect.value) {
    return;
  }
  await nextTick(); 
  if (isShowingOptions && planCardsGridRef.value?.children.length) {
    gsap.fromTo(planCardsGridRef.value.children, 
      { autoAlpha: 0, y: 20, scale: 0.95 }, 
      { autoAlpha: 1, y: 0, scale: 1, stagger: 0.1, duration: 0.4, ease: 'power1.out', delay: 0.1 }
    );
  } else if (!isShowingOptions) {
    if (formData.value.email && formData.value.fullName && !paymentElement && !isInitializingPayment.value) {
      initializeStripeAndElements();
    }
  } 
}, { deep: false });

// Mapeo de IDs de la landing page a IDs internos
const planIdMapping: Record<string, string> = {
  '1': 'ia-starter',
  '2': 'ia-pro',
  '3': 'ia-elite'
};

watchEffect(async () => {
  if (isFinalizingPostRedirect.value) return;
  if (!initiallyLoaded.value) {
    const planSlugFromRoute = route.query.plan as string;
    const cycleFromRoute = route.query.cycle as ('monthly' | 'annual' | undefined);

    // Mapear el ID de la landing al ID interno
    const mappedPlanId = planSlugFromRoute ? planIdMapping[planSlugFromRoute] || planSlugFromRoute : null;

    if (mappedPlanId && planDetailsData[mappedPlanId]) {
      selectedPlan.value = planDetailsData[mappedPlanId];
      showPlanOptions.value = false;
      if (cycleFromRoute === 'annual' || cycleFromRoute === 'monthly') {
        selectedBillingCycle.value = cycleFromRoute;
      } else {
        selectedBillingCycle.value = 'monthly';
      }

      // Limpiar los parámetros URL después de procesarlos
      router.replace({ query: {} });
    } else {
      selectedPlan.value = null;
      showPlanOptions.value = true;
      selectedBillingCycle.value = 'monthly';
    }
    await nextTick();
    initiallyLoaded.value = true;
    if (!paymentElement && formData.value.email && formData.value.fullName && !isInitializingPayment.value) {
        initializeStripeAndElements();
    }
  }
});

// Nueva función para obtener precios para las tarjetas de selección
const getPriceForDisplay = (planId: keyof typeof planDetailsData) => {
  const plan = planDetailsData[planId];
  if (!plan) return { mainPrice: 'N/A', annualTotal: 'N/A' };

  let mainPriceToShow;
  if (selectedBillingCycle.value === 'monthly') {
    mainPriceToShow = plan.price_monthly;
  } else {
    mainPriceToShow = parseFloat((plan.price_annual / 12).toFixed(2));
  }
  
  const formattedMainPrice = Number.isInteger(mainPriceToShow) ? mainPriceToShow.toFixed(0) : mainPriceToShow.toFixed(2);
  const annualTotal = Number.isInteger(plan.price_annual) ? plan.price_annual.toFixed(0) : plan.price_annual.toFixed(2);

  return {
    mainPrice: `${plan.currency === 'EUR' ? '€' : plan.currency}${formattedMainPrice}`,
    annualTotal: annualTotal
  };
};

</script>