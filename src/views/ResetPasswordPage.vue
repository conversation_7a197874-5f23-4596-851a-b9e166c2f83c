<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white p-8 rounded shadow">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900" v-if="!hasToken">
          Restablecer contraseña
        </h2>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900" v-else>
          Define tu nueva contraseña
        </h2>
      </div>

      <!-- Solicitud de enlace -->
      <form v-if="!hasToken" class="mt-8 space-y-6" @submit.prevent="requestReset">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="email" class="sr-only">Correo electrónico</label>
            <input
              id="email"
              name="email"
              type="email"
              autocomplete="email"
              required
              v-model="email"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange focus:z-10 sm:text-sm"
              placeholder="Correo electrónico"
            />
          </div>
        </div>
        <p v-if="errorMessage" class="text-sm text-red-600 text-center">{{ errorMessage }}</p>
        <p v-if="successMessage" class="text-sm text-green-600 text-center">{{ successMessage }}</p>
        <div>
          <button
            type="submit"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-impacto-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange"
            :disabled="loading"
          >
            <span v-if="!loading">Enviar enlace</span>
            <span v-else>Cargando…</span>
          </button>
        </div>
      </form>

      <!-- Formulario para establecer nueva contraseña -->
      <form v-else class="mt-8 space-y-6" @submit.prevent="performReset">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="password" class="sr-only">Nueva contraseña</label>
            <input
              id="password"
              name="password"
              type="password"
              autocomplete="new-password"
              required
              minlength="8"
              v-model="password"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange focus:z-10 sm:text-sm"
              placeholder="Nueva contraseña"
            />
          </div>
          <div>
            <label for="password-confirm" class="sr-only">Confirmar contraseña</label>
            <input
              id="password-confirm"
              name="password-confirm"
              type="password"
              autocomplete="new-password"
              required
              minlength="8"
              v-model="passwordConfirm"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange focus:z-10 sm:text-sm"
              placeholder="Confirmar contraseña"
            />
          </div>
        </div>
        <p v-if="errorMessage" class="text-sm text-red-600 text-center">{{ errorMessage }}</p>
        <p v-if="successMessage" class="text-sm text-green-600 text-center">{{ successMessage }}</p>
        <div>
          <button
            type="submit"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-impacto-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange"
            :disabled="loading"
          >
            <span v-if="!loading">Guardar nueva contraseña</span>
            <span v-else>Cargando…</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { apiFetch } from '@/utils/apiFetch';
import { useToast } from '@/composables/useToast';

const { showToast } = useToast();

const route = useRoute();
const router = useRouter();

const token = computed(() => route.query.token as string | undefined);
const hasToken = computed(() => !!token.value);

const email = ref('');
const password = ref('');
const passwordConfirm = ref('');

const loading = ref(false);
const errorMessage = ref<string | null>(null);
const successMessage = ref<string | null>(null);

async function requestReset() {
  errorMessage.value = null;
  successMessage.value = null;
  loading.value = true;
  try {
    await apiFetch('request_password_reset.php', {
      method: 'POST',
      body: JSON.stringify({ email: email.value.trim() })
    });
    // Backend siempre retorna success true para evitar enumeración
    successMessage.value = 'Si el correo está registrado, recibirás un mensaje con instrucciones.';
  } catch (e: any) {
    errorMessage.value = e?.message || 'Error solicitando restablecimiento.';
  } finally {
    loading.value = false;
  }
}

async function performReset() {
  errorMessage.value = null;
  successMessage.value = null;
  if (password.value !== passwordConfirm.value) {
    errorMessage.value = 'Las contraseñas no coinciden.';
    return;
  }
  loading.value = true;
  try {
    const res = await apiFetch('perform_password_reset.php', {
      method: 'POST',
      body: JSON.stringify({ token: token.value, password: password.value })
    });
    if (res.success) {
      successMessage.value = 'Contraseña actualizada. Ahora puedes iniciar sesión.';
      showToast('Contraseña actualizada', 'success');
      setTimeout(() => router.push('/login'), 2000);
    } else {
      throw new Error(res.message || 'Error al actualizar contraseña');
    }
  } catch (e: any) {
    errorMessage.value = e?.message || 'Error al actualizar contraseña.';
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.reset-password-page {
  max-width: 460px;
  margin: 0 auto;
}
</style>
