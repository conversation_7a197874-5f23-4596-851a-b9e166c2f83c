<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white p-8 rounded shadow">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Iniciar sesión en tu cuenta</h2>
      </div>
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="email-address" class="sr-only">Correo electrónico</label>
            <input id="email-address" name="email" type="email" autocomplete="email" required v-model="email"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange focus:z-10 sm:text-sm"
              placeholder="Correo electrónico" />
          </div>
          <div>
            <label for="password" class="sr-only">Contraseña</label>
            <input id="password" name="password" type="password" autocomplete="current-password" required v-model="password"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-impacto-orange focus:border-impacto-orange focus:z-10 sm:text-sm"
              placeholder="Contraseña" />
          </div>
        </div>
        <div class="flex justify-end mt-2">
          <RouterLink to="/reset-password" class="text-sm text-blue-600 hover:underline">¿Olvidaste tu contraseña?</RouterLink>
        </div>
        <div v-if="error" class="text-red-600 text-sm text-center">{{ error }}</div>
        <div>
          <button type="submit"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-impacto-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange">
            Iniciar sesión
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router';
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { jwtDecode } from 'jwt-decode';
import { apiFetch } from '@/utils/apiFetch';

const email = ref('');
const password = ref('');
const error = ref('');
const router = useRouter();
const route = useRoute();
const auth = useAuthStore();

onMounted(async () => {
  const token = route.query.token as string | null;
  const isFirstVisit = route.query.firstVisit === 'true';

  if (token) {
    try {
      // Decodificar el token para obtener los datos del usuario.
      // El backend debe asegurar que el JWT contiene los datos necesarios.
      const decoded: any = jwtDecode(token);

      // El backend ya devuelve un objeto 'user' en el login normal,
      // aquí replicamos esa estructura a partir del payload del token.
      const userDataFromToken = {
        id: decoded.user_id,
        email: decoded.email,
        nombre_completo: decoded.nombre_completo,
        roles: decoded.roles,
        agency_id: decoded.agency_id,
        is_agency_owner: decoded.is_agency_owner,
        agency_name: decoded.agency_name
      };

      if (!userDataFromToken.id || !userDataFromToken.email) {
        throw new Error('Token JWT inválido o no contiene la información necesaria.');
      }

      await auth.login(userDataFromToken, token);
      
      // Redirección explícita al dashboard.
      // Si es la primera visita, añadimos el query param para el onboarding.
      const redirectQuery = isFirstVisit ? { firstVisit: 'true' } : {};
      router.push({ name: 'DashboardHome', query: redirectQuery });

    } catch (e: any) {
      error.value = `Error de autenticación: ${e.message}. Por favor, inicia sesión manualmente.`;
      // Limpiamos la URL aunque haya error para no dejar el token expuesto.
      router.replace({ query: {} });
    }
  }
});

const handleLogin = async () => {
  error.value = '';
  try {
    const data = await apiFetch('login.php', {
      method: 'POST',
      body: JSON.stringify({ email: email.value, password: password.value }),
    });
    if (data.success && data.token && data.user) {
      const completeUserDataFromAPI = data.user;
      const tokenString = data.token;

      if (!completeUserDataFromAPI || !completeUserDataFromAPI.id || !completeUserDataFromAPI.email) {
        error.value = 'Datos de usuario inválidos recibidos del servidor.';
        return;
      }
      if (typeof tokenString !== 'string' || tokenString.split('.').length !== 3) {
        error.value = 'Token de autenticación inválido recibido del servidor.';
        return;
      }
      
      await auth.login(completeUserDataFromAPI, tokenString);
      // Redirigimos a la raíz, la guarda del router se encargará del resto.
      router.push('/');
    } else {
      error.value = data.message || 'Error al iniciar sesión.';
    }
  } catch (e) {
    error.value = 'No se pudo conectar con el servidor.';
  }
};
</script>

<style scoped>
</style>
