<template>
    <div class="terms-of-service-view py-12 px-4 bg-white min-h-screen">
      <div class="container mx-auto prose lg:prose-xl">
        <h1 class="text-3xl font-bold text-impacto-blue mb-6">T<PERSON>rminos de Servicio</h1>
        <p class="text-gray-700 mb-4">
          Esta es la página de Términos de Servicio. El contenido detallado se añadirá aquí.
        </p>
        <p class="text-gray-700">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
          Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
          Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
        </p>
        <div class="mt-8">
          <RouterLink :to="{ name: 'Landing' }" class="text-impacto-orange hover:underline">
            ← Volver al Inicio
          </RouterLink>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { RouterLink } from 'vue-router';
  </script>
  
  <style scoped>
  /* Estilos específicos si son necesarios */
  .prose h1 {
    color: #051f33;
  }
  </style>