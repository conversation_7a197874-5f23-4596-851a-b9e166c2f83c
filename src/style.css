/* src/style.css */

/* 1. Importación de las capas base de Tailwind CSS */
/* Estas directivas inyectarán los estilos base, de componentes y utilidades de Tailwind. */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Patrones y fondos personalizados */
.bg-pattern-dots {
  background-image: radial-gradient(circle, currentColor 1px, transparent 1px);
  background-size: 15px 15px;
}

/* 2. Definición de estilos base globales y personalizaciones */
/* Usamos la directiva @layer base para aplicar estilos a elementos HTML básicos
   o para definir estilos por defecto que queremos para toda la aplicación. */
@layer base {
  html {
    scroll-behavior: smooth; /* Habilita el desplazamiento suave para anclas en la página */
    -webkit-tap-highlight-color: transparent; /* Elimina el resaltado azul en elementos clicables en móviles (iOS Safari) */
  }

  body {
    /* 
      Aplica la fuente 'Inter' (definida como 'font-body' en tailwind.config.js) por defecto al cuerpo.
      Establece el color de fondo por defecto a blanco (#ffffff).
      Establece el color de texto por defecto a nuestro 'impacto-blue' (#051f33),
      asumiendo que el contenido principal estará sobre fondos claros, según la paleta.
      La clase 'antialiased' ya está en el body desde index.html para el suavizado de fuentes.
    */
    @apply font-body bg-white text-impacto-blue;
  }

  /* 
    Estilos base para encabezados (h1-h6).
    Aplicamos la fuente 'Poppins' (definida como 'font-sans' en tailwind.config.js) por defecto.
    Los tamaños y pesos específicos de los encabezados se manejarán usualmente con clases de Tailwind
    directamente en los componentes (ej. text-3xl, font-bold), o podrían definirse
    más específicamente aquí si hubiera un sistema tipográfico muy rígido.
    Por ahora, solo establecemos la familia de fuente.
  */
  h1, h2, h3, h4, h5, h6 {
    @apply font-sans;
    /* Ejemplo: si quisiéramos que todos los encabezados tuvieran un color específico por defecto,
       podríamos añadirlo aquí, ej: @apply text-impacto-blue;
       Pero a menudo es mejor controlarlo contextualmente. */
  }

  /*
    Podríamos añadir otros estilos base aquí, por ejemplo, para enlaces (<a>), párrafos (<p>), etc.,
    si queremos unos estilos por defecto muy específicos que no cubran las utilidades de Tailwind
    o los estilos "preflight" de Tailwind.
    Ejemplo:
    a {
      @apply text-impacto-orange hover:text-orange-400 transition-colors duration-150;
      text-decoration: none; // O alguna otra preferencia global para enlaces
    }
    Por ahora, mantendremos esto mínimo y dejaremos que las utilidades de Tailwind y los componentes lo manejen.
  */
}

/* 3. Componentes personalizados (Opcional, si se necesitan clases de componentes reutilizables) */
/* Si tuviéramos componentes de CSS complejos que no son fácilmente construibles solo con utilidades
   y queremos encapsularlos en una clase. */
/*
@layer components {
  .btn-primary-custom {
    @apply bg-impacto-orange text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-opacity-50 transition-all duration-150 ease-in-out;
  }
  .card-custom {
    @apply bg-white shadow-xl rounded-xl p-6 overflow-hidden;
  }
}
*/

/* 4. Utilidades personalizadas (Opcional, si se necesitan nuevas clases de utilidad) */
/* Si necesitamos crear nuevas clases de utilidad que Tailwind no ofrece por defecto. */
/*
@layer utilities {
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0,0,0,0.10);
  }
  .text-shadow-md {
    text-shadow: 0 2px 4px rgba(0,0,0,0.10);
  }
  .bg-hero-pattern {
    background-image: url('/img/patterns/hero-pattern.svg'); // Asegurarse que la ruta es correcta si se usa
  }
}
*/

/*
  Notas sobre las fuentes (Google Fonts):
  Las fuentes Poppins e Inter ya están enlazadas en `public/index.html` usando `<link>`.
  Esto es generalmente el método preferido para Google Fonts por eficiencia (CDN, caché).
  Tailwind CSS las utilizará gracias a la configuración en `tailwind.config.js` (theme.extend.fontFamily).
  No es necesario importarlas nuevamente aquí con @import url(...) o @font-face
  a menos que decidas alojar las fuentes localmente en `src/assets/fonts/`.
*/