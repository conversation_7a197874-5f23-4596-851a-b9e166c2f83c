// src/main.ts

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import { useAuthStore } from './stores/auth'

// Estilos globales
import './style.css'         // Importación de estilos globales y Tailwind CSS

// Lottie
import Vue3Lottie from 'vue3-lottie'

// GSAP
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
gsap.registerPlugin(ScrollTrigger);

// Click outside directive
import ClickOutside from 'click-outside-vue3'

async function initializeApp() {
  const app = createApp(App)
  const pinia = createPinia()
  app.use(pinia)

  // Restaurar la sesión de autenticación ANTES de montar el router
  // Esto asegura que los guardias de navegación tengan el estado correcto
  const authStore = useAuthStore();
  await authStore.checkAuth();

  const appType = import.meta.env.VITE_APP_TYPE || 'landing';

  // Carga dinámica del router correspondiente
  const routerModule = appType === 'app'
    ? await import('./router/app')
    : await import('./router/landing');
  
  const router = routerModule.default;

  app.use(router)
  app.use(Vue3Lottie)
  app.directive('click-outside', ClickOutside.directive)

  app.mount('#app')
}

initializeApp().catch(error => {
  console.error('Failed to initialize app:', error);
});

/*
  Notas adicionales:

  1.  GSAP (GreenSock Animation Platform):
      - La importación `import { gsap } from 'gsap'` y `import { ScrollTrigger } from 'gsap/ScrollTrigger'` trae la librería principal y el plugin ScrollTrigger.
      - `gsap.registerPlugin(ScrollTrigger)` es importante para que ScrollTrigger funcione correctamente en todo el proyecto si planeas usarlo.
      - Otras animaciones GSAP y microinteracciones se implementarán típicamente dentro de los componentes específicos o a través de composables (`src/composables/`).

  2.  Axios/Fetch:
      - La elección entre Fetch API nativa o Axios para peticiones HTTP se decidirá más adelante.
      - Si se opta por Axios y se requiere configuración global (ej. base URL, interceptors), podría inicializarse aquí o, preferiblemente, en un módulo de servicio dedicado (ej. `src/services/apiService.ts` o dentro de `src/services/onboardingService.ts`). Por ahora, no se incluye configuración de Axios.

  3.  Variables de Entorno:
      - Las variables de entorno (ej. `VITE_STRIPE_PUBLIC_KEY` de `.env` archivos) están disponibles a través de `import.meta.env`.
      - No se necesitan configuraciones especiales en `main.ts` para usarlas, pero es bueno recordar que Vite las maneja.

  4.  Otros Plugins de Vue:
      - Si en el futuro se necesitaran otros plugins de Vue (ej. Pinia para gestión de estado, Vue-i18n para internacionalización), se importarían y registrarían aquí usando `app.use(pluginName)`.
*/