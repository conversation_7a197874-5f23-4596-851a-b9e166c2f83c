export interface Plan {
  id: number;
  uuid: string;
  slug: string;
  name: string;
  short_description: string;
  price_monthly: string;
  price_annual: string;
  stripe_price_id_monthly: string;
  stripe_price_id_annual: string;
  is_active: boolean;
  display_order: number;
  max_dashboard_users: number;
  max_valoradores: number;
  default_ia_sequence_id: number | null;
  allow_multiple_sequences: boolean;
  allow_custom_domain_email: boolean;
  has_analytics: boolean;
  additional_config_json: string | null;
}

export interface UserPlanFeatures {
  id: number; // plan_id
  name: string; // nombre del plan
  slug: string; // slug del plan
  max_dashboard_users: number | null;
  allow_multiple_sequences: boolean | null;
  allow_custom_domain_email: boolean | null;
  max_valoradores: number | null;
  has_analytics: boolean | null; // acceso a analíticas detalladas
  additional_config: Record<string, any> | null;
  [key: string]: any;
}