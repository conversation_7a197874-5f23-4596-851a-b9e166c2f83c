export interface Subscription {
  id: string;
  status: string;
  planId: string;
  planName: string;
  price: string;
  billingCycle: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  canceledAt: string | null;
  paymentMethod: {
    brand: string;
    lastFourDigits: string;
    expiryMonth: number;
    expiryYear: number;
  };
  features: string[];
}
