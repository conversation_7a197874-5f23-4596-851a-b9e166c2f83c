// src/types/google.maps.d.ts
declare namespace google.maps {
    export enum SymbolPath {
        CIRCLE = 0,
        FORWARD_CLOSED_ARROW = 1,
        FORWARD_OPEN_ARROW = 2,
        BACKWARD_CLOSED_ARROW = 3,
        BACKWARD_OPEN_ARROW = 4,
    }
    // Puedes añadir más tipos de google.maps aquí si los necesitas en el futuro
    // Por ejemplo:
    // export class LatLng {
    //   constructor(lat: number, lng: number);
    //   lat(): number;
    //   lng(): number;
    // }
    // export interface MarkerOptions {
    //   position?: LatLng | LatLngLiteral;
    //   title?: string;
    //   icon?: string | Icon | Symbol;
    //   // etc.
    // }
}