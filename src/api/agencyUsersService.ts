import { apiFetch } from '@/utils/apiFetch';

// Interfaz para un usuario de la agencia (puede ser similar a la User de auth.ts pero más específica)
export interface AgencyUser {
  id: number;
  nombre_completo: string;
  email: string;
  // roles: string[]; // Si el endpoint los devuelve
  activo: boolean; // O el campo que indique si está activo/desvinculado
  fecha_creacion?: string; // Opcional
  // Otros campos relevantes que devuelva list_agency_users.php
}

// Interfaz para la respuesta de list_agency_users.php
export interface ListAgencyUsersResponse {
  success: boolean;
  users?: AgencyUser[];
  message?: string;
}

// Interfaz para la respuesta de add_user_to_agency.php y remove_user_from_agency.php
export interface AgencyUserActionResponse {
  success: boolean;
  message?: string;
  user?: AgencyUser; // remove_user podría no devolver el usuario
  // Otros campos que puedan devolver estos endpoints
}

/**
 * Lista los usuarios de la agencia del propietario actual.
 */
export async function listAgencyUsers(): Promise<ListAgencyUsersResponse> {
  try {
    return await apiFetch<ListAgencyUsersResponse>('/api/list_agency_users.php');
  } catch (error) {
    console.error('Error en listAgencyUsers:', error);
    // Devolver una respuesta de error consistente con la interfaz
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido al listar usuarios.';
    return { success: false, message: errorMessage };
  }
}

/**
 * Añade un nuevo usuario a la agencia.
 * @param fullName El nombre completo del nuevo usuario.
 * @param email El email del nuevo usuario.
 */
export async function addUserToAgency(fullName: string, email: string): Promise<AgencyUserActionResponse> {
  try {
    return await apiFetch<AgencyUserActionResponse>('/api/add_user_to_agency.php', {
      method: 'POST',
      body: JSON.stringify({ nombre_completo: fullName, email }),
    });
  } catch (error) {
    console.error('Error en addUserToAgency:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido al añadir usuario.';
    return { success: false, message: errorMessage };
  }
}

/**
 * Elimina (desvincula y desactiva) un usuario de la agencia.
 * @param userId El ID del usuario a eliminar.
 */
export async function removeUserFromAgency(userId: number): Promise<AgencyUserActionResponse> {
  try {
    return await apiFetch<AgencyUserActionResponse>('/api/remove_user_from_agency.php', {
      method: 'POST',
      body: JSON.stringify({ user_id_to_remove: userId }),
    });
  } catch (error) {
    console.error('Error en removeUserFromAgency:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido al eliminar usuario.';
    return { success: false, message: errorMessage };
  }
}

/**
 * Actualiza el nombre de la agencia.
 * @param agencyName El nuevo nombre para la agencia.
 */
export async function updateAgencyName(agencyName: string): Promise<AgencyUserActionResponse> {
  try {
    return await apiFetch<AgencyUserActionResponse>('/api/update_agency_name.php', {
      method: 'POST',
      body: JSON.stringify({ new_agency_name: agencyName }),
    });
  } catch (error) {
    console.error('Error en updateAgencyName:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido al actualizar el nombre de la agencia.';
    return { success: false, message: errorMessage };
  }
} 