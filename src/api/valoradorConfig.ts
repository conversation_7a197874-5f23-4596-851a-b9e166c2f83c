import { apiFetch } from '@/utils/apiFetch';

export async function getValoradorConfig() {
  // Usar la ruta para acceder a los scripts PHP
  const res = await apiFetch('valorador-config.php', { method: 'GET' });
  
  // Verificar si la respuesta es un objeto JSON o una respuesta HTTP
  if (typeof res === 'object' && !(res instanceof Response)) {
    // Si es un objeto JSON (ya procesado por apiFetch), devolverlo directamente
    return res;
  }
  
  try {
    // Si llegamos aquí, tenemos una respuesta HTTP
    const responseText = await res.text(); // Leer como texto primero

    if (!res.ok) {
      console.error('[getValoradorConfig] Error response from server:', responseText);
      throw new Error('No se pudo cargar la configuración. Status: ' + res.status);
    }

    try {
      return JSON.parse(responseText); // Intentar parsear el texto como JSON
    } catch (e) {
      console.error('[getValoradorConfig] Failed to parse responseText as JSON:', e);
      console.error('[getValoradorConfig] Original responseText that failed parsing:', responseText);
      throw new Error('La respuesta del servidor no es JSON válido.');
    }
  } catch (error) {
    console.error('[getValoradorConfig] Error procesando la respuesta:', error);
    // Devolver un objeto de configuración vacío para evitar errores en cascada
    return { success: true, config: {} };
  }
}

// Interface for the data coming from the Vue form
interface ValoradorFormInput {
  agencyName?: string;
  agencyLogoUrl?: string;
  desiredSubdomain?: string;
  colorPrimario?: string;
  colorSecundario?: string;
  emailNotificacionesLeads?: string;
  politicaPrivacidadUrl?: string;
  terminosUsoUrl?: string;
  ctaContactoUrl?: string;
  [key: string]: any; // Allow other properties from the form
}

export async function saveValoradorConfig(data: ValoradorFormInput | FormData) {
  let options: RequestInit;
  
  if (data instanceof FormData) {

    options = { method: 'POST', body: data };

  } else {

    const payloadToSend = { ...data };

    options = {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payloadToSend) // Enviar el objeto 'data' (ahora payloadToSend) directamente
    };
  }
  const res = await apiFetch('valorador-config.php', options);
  
  if (typeof res === 'object' && !(res instanceof Response)) {
    return res;
  }
  
  try {
    const responseText = await res.text();

    if (!res.ok) {
      console.error('[saveValoradorConfig] Error response from server. Status:', res.status, 'Body:', responseText);
      try {
          const errorJson = JSON.parse(responseText);
          throw new Error(errorJson.message || 'No se pudo guardar la configuración. Status: ' + res.status);
      } catch (e) {
          // If responseText is not JSON or empty, use it directly or a default message
          throw new Error(responseText.trim() || 'No se pudo guardar la configuración. Status: ' + res.status);
      }
    }
    
    // Si la respuesta es exitosa, intentar parsear el JSON
    try {
      return JSON.parse(responseText);
    } catch (e) {
      console.error('[saveValoradorConfig] Failed to parse responseText as JSON:', e);
      return { success: true, message: 'Configuración guardada exitosamente' };
    }
  } catch (error) {
    console.error('[saveValoradorConfig] Error procesando la respuesta:', error);
    throw error;
  }

  // El código de manejo de respuesta ya está implementado arriba
}

export async function checkClientIdentifierAvailability(clientIdentifier: string): Promise<{ success: boolean; available: boolean; message: string }> {
  try {
    const response = await apiFetch(`check-client-identifier.php?client_identifier=${encodeURIComponent(clientIdentifier)}`, {
      method: 'GET'
    });

    if (typeof response === 'object' && !(response instanceof Response)) {
      return response;
    }

    // Si es una Response, procesarla
    const responseText = await response.text();

    if (!response.ok) {
      console.error('[checkClientIdentifierAvailability] Error response:', responseText);
      return { success: false, available: false, message: 'Error verificando disponibilidad' };
    }

    try {
      return JSON.parse(responseText);
    } catch (e) {
      console.error('[checkClientIdentifierAvailability] Failed to parse JSON:', e);
      return { success: false, available: false, message: 'Error procesando respuesta' };
    }
  } catch (error) {
    console.error('[checkClientIdentifierAvailability] Error:', error);
    return { success: false, available: false, message: 'Error de conexión' };
  }
}

export async function uploadAgencyLogo(logoFileOrUrl: File | string, clientIdentifier: string): Promise<{ success: boolean; logoUrl?: string; error?: string }> {
  const formData = new FormData();
  formData.append('client_identifier', clientIdentifier);
  
  // Determinar si estamos procesando un archivo o una URL
  if (typeof logoFileOrUrl === 'string') {
    // Es una URL
    formData.append('logo_url', logoFileOrUrl);
  } else {
    // Es un archivo
    formData.append('agencyLogoFile', logoFileOrUrl);
  }

  try {
    // Usar apiFetch en lugar de fetch directamente
    const responseData = await apiFetch('upload_logo.php', {
      method: 'POST',
      body: formData,
      // apiFetch debería manejar la cabecera de Authorization.
      // NO establecer Content-Type aquí, el navegador lo manejará para FormData.
    });

    // apiFetch ya debería haber manejado el !response.ok y parseado el JSON
    // o lanzado un error si no es ok o no es JSON.
    // La estructura de responseData dependerá de cómo apiFetch devuelve los datos.
    // Asumiendo que devuelve el JSON parseado en caso de éxito:

    if (responseData.success) {
      return { success: true, logoUrl: responseData.logoUrl };
    } else {
      return { success: false, error: responseData.error || 'Error en la respuesta del servidor al procesar el logo.' };
    }

  } catch (error: any) {
    console.error('[uploadAgencyLogo] Error:', error);
    // Si apiFetch lanza un error, su mensaje ya debería ser informativo.
    return { success: false, error: error.message || 'No se pudo procesar el logo.' };
  }
}