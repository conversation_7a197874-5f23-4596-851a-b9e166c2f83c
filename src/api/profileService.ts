import { apiFetch } from '@/utils/apiFetch';

// --- Interfaces ---
export interface UserProfile {
  name: string;
  email: string;
  // Se podrían añadir más campos si es necesario, como 'avatar_url', 'last_login', etc.
}

export interface ChangePasswordPayload {
  currentPassword: string;
  newPassword: string;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
  user?: UserProfile; // Opcional, si el backend devuelve el perfil actualizado
}

export interface BasicApiResponse {
  success: boolean;
  message: string;
}

// --- Funciones del Servicio ---

/**
 * Obtiene los datos del perfil del usuario actual.
 */
export async function getUserProfile(): Promise<UserProfile> {
  try {
    const response = await apiFetch<UserProfile>('/api/profile.php');
    if (!response) {
      throw new Error('No se recibió respuesta del servidor al obtener el perfil.');
    }
    return response;
  } catch (error: any) {
    console.error('Error en profileService.getUserProfile:', error);
    throw new Error(error.message || 'No se pudo obtener la información del perfil.');
  }
}

/**
 * Actualiza los datos del perfil del usuario (nombre, email).
 * @param profileData - Los nuevos datos del perfil.
 */
export async function updateUserProfile(profileData: UserProfile): Promise<UpdateProfileResponse> {
  try {
    const response = await apiFetch<UpdateProfileResponse>('/api/update-profile.php', {
      method: 'POST',
      body: JSON.stringify(profileData),
    });
    return response;
  } catch (error: any) {
    console.error('Error en profileService.updateUserProfile:', error);
    throw new Error(error.message || 'Error al actualizar el perfil.');
  }
}

/**
 * Cambia la contraseña del usuario.
 * @param passwordData - Contraseña actual y nueva contraseña.
 */
export async function changePassword(passwordData: ChangePasswordPayload): Promise<BasicApiResponse> {
  try {
    const response = await apiFetch<BasicApiResponse>('/api/change-password.php', {
      method: 'POST',
      body: JSON.stringify(passwordData),
    });
    return response;
  } catch (error: any) {
    console.error('Error en profileService.changePassword:', error);
    throw new Error(error.message || 'Error al cambiar la contraseña.');
  }
}

/**
 * Solicita la eliminación de la cuenta del usuario.
 */
export async function requestAccountDeletion(): Promise<BasicApiResponse> {
  try {
    const response = await apiFetch<BasicApiResponse>('/api/request-deletion.php', {
      method: 'POST',
    });
    return response;
  } catch (error: any) {
    console.error('Error en profileService.requestAccountDeletion:', error);
    throw new Error(error.message || 'Error al solicitar la eliminación de la cuenta.');
  }
} 