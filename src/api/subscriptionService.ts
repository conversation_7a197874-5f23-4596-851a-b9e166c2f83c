import { apiFetch } from '@/utils/apiFetch';
import type { UserPlanFeatures as ApiUserPlanFeatures } from '@/types/plan';

// Interfaces para el frontend
export interface Subscription {
  id: string;
  status: string;
  planId: string;
  planName: string;
  price: string;
  billingCycle: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  canceledAt: string | null;
  customer_balance_cents?: number;
  paymentMethod: {
    brand: string;
    lastFourDigits: string;
    expiryMonth: number;
    expiryYear: number;
  } | null;
  features: string[];
  plan_features: ApiUserPlanFeatures | null; // Vuelve el objeto anidado
  trialPeriod: {
    isInTrial: boolean;
    trialStart: string | null;
    trialEnd: string | null;
    daysRemaining: number;
  };
  nextBillingDate: string | null;
  nextBillingAmount: string | null;
  startDate: string | null;
  renewalStatus: 'active' | 'canceled' | 'pending' | 'trial';
  // Nuevos campos para manejo mejorado de métodos de pago
  paymentMethodSource?: 'subscription' | 'customer' | 'attached' | null;
  hasPaymentMethod?: boolean;
  subscriptionRecoverable?: boolean;
  actionRequired?: 'payment_authentication' | 'retry_payment' | 'update_payment_method' | 'reactivate_subscription' | null;
  // Estado detallado de la suscripción
  detailedStatus?: {
    status: string;
    detailed_status: string;
    user_message: string;
    action_required: string | null;
    is_recoverable: boolean;
    priority: 'low' | 'medium' | 'high' | 'critical';
  };
  user_id?: number;
  agency_id?: number;
  is_agency_owner?: boolean;
  user_data_response?: {
    id?: number;
    agency_id?: number;
    agency_name?: string | null;
    email?: string;
    nombre_completo?: string;
    stripe_customer_id?: string;
    is_agency_owner?: boolean;
  };
}

export interface Invoice {
  id: string;
  number: string;
  date: string;
  amount: string;
  status: string;
  pdfUrl: string;
}

/**
 * Obtiene la información de la suscripción actual del usuario
 */
export async function getCurrentSubscription(): Promise<Subscription> {
  try {
    const response = await apiFetch<any>('subscription.php');

    if (!response || typeof response !== 'object') {
      throw new Error('La respuesta de suscripción no es un objeto válido');
    }

    // Mapear los datos de plan_features si existen
    const planFeaturesData: ApiUserPlanFeatures | null = response.plan_features ? {
      id: response.plan_features.id ?? null,
      name: response.plan_features.name ?? null,
      slug: response.plan_features.slug ?? null,
      max_dashboard_users: response.plan_features.max_dashboard_users ?? null,
      allow_multiple_sequences: response.plan_features.allow_multiple_sequences ?? null,
      allow_custom_domain_email: response.plan_features.allow_custom_domain_email ?? null,
      max_valoradores: response.plan_features.max_valoradores ?? null,
      has_analytics: response.plan_features.has_analytics ?? null,
      additional_config: response.plan_features.additional_config ?? null,
    } : null;

    return {
      ...response,
      plan_features: planFeaturesData, // Asignar el objeto anidado
      // Asegurar que los campos principales estén bien formateados
      currentPeriodStart: safeFormatISO(response.currentPeriodStart),
      currentPeriodEnd: safeFormatISO(response.currentPeriodEnd),
      nextBillingDate: safeFormatISO(response.nextBillingDate),
      startDate: safeFormatISO(response.startDate),
      canceledAt: response.canceled_at ? safeFormatISO(response.canceled_at) : null,
    } as Subscription;

  } catch (error) {
    console.error('Error al obtener la suscripción (en subscriptionService):', error);
    throw new Error('No se pudo cargar la información de la suscripción.');
  }
}

function safeFormatISO(dateInput: any): string {
  if (!dateInput) return new Date(0).toISOString();
  try {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) {
      return new Date(0).toISOString();
    }
    return date.toISOString();
  } catch {
    return new Date(0).toISOString();
  }
}

export async function getInvoices(): Promise<Invoice[]> {
  try {
    // El API devuelve {success: true, invoices: [...]}
    const response = await apiFetch<any>('invoices.php');

    // Verificar que la respuesta sea exitosa y contenga facturas
    if (!response || typeof response !== 'object' || !response.success) {
      console.error('Error en la respuesta de facturas:', response);
      return [];
    }

    const invoicesData = response.invoices;

    // Comprobar si la respuesta contiene un array de facturas
    if (!Array.isArray(invoicesData)) {
      console.error('La respuesta de facturas no contiene un array válido:', response);
      return [];
    }

    // Mapear usando los nombres de campo correctos que envía el backend
    return invoicesData.map((invoice: any) => ({
      id: invoice.id,
      number: invoice.number,
      // El backend envía 'date' como un timestamp de UNIX
      date: new Date(invoice.date * 1000).toISOString(),
      // El backend envía 'total' como número y 'currency' por separado
      amount: `${invoice.total} ${invoice.currency}`,
      status: invoice.status,
      // El backend envía la URL del PDF en el campo 'pdf_url'
      pdfUrl: invoice.pdf_url,
    }));
  } catch (error) {
    console.error('Error al obtener las facturas:', error);
    // Devolvemos un array vacío en caso de error para que la UI no se rompa
    return [];
  }
}

export async function createBillingPortalSession(): Promise<{ url: string }> {
  const response = await apiFetch('create-billing-portal-session.php', { method: 'POST' });
  return response as { url: string };
}

export async function cancelSubscription(cancelAtPeriodEnd: boolean = true): Promise<{ success: boolean; message: string; subscription_status: string; cancel_at_period_end: boolean }> {
  const response = await apiFetch('cancel-subscription.php', {
    method: 'POST',
    body: JSON.stringify({ cancel_at_period_end: cancelAtPeriodEnd }),
  });
  return response as any;
}

export async function reactivateSubscription(): Promise<{ success: boolean; message: string; subscription_status: string; cancel_at_period_end: boolean }> {
  const response = await apiFetch('reactivate-subscription.php', { method: 'POST' });
  return response as any;
}

export async function retryPayment(): Promise<{
  success: boolean;
  message?: string;
  status?: 'paid' | 'requires_action';
  payment_intent?: { id: string; client_secret: string };
  invoice_id?: string;
  error_type?: string;
}> {
  const response = await apiFetch('retry-payment.php', { method: 'POST' });
  return response as any;
}

export async function confirmPayment(paymentIntentId: string): Promise<{
  success: boolean;
  message?: string;
  status?: string;
  requires_action?: boolean;
  error_type?: string;
}> {
  const response = await apiFetch('confirm-payment.php', {
    method: 'POST',
    body: JSON.stringify({ payment_intent_id: paymentIntentId })
  });
  return response as any;
}

export async function getInvoicePdfUrl(invoiceId: string): Promise<{ url: string }> {
  const response = await apiFetch(`invoice-pdf.php?invoice_id=${invoiceId}`);
  return response as { url: string };
}