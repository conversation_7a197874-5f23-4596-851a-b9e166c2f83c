<template>
    <div v-if="showModal" class="fixed inset-0 z-50 overflow-y-auto bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 transition-opacity duration-300 ease-in-out" :class="showModal ? 'opacity-100' : 'opacity-0 pointer-events-none'">
      <div class="relative bg-white rounded-xl shadow-2xl w-full max-w-4xl transform transition-all duration-300 ease-in-out" :class="showModal ? 'scale-100 opacity-100' : 'scale-95 opacity-0'" role="dialog" aria-modal="true" aria-labelledby="modal-title">
        <!-- Cabecera del Modal -->
        <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <h3 id="modal-title" class="text-xl font-semibold text-impacto-blue">Selecciona tu Nuevo Plan</h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full focus:outline-none focus:ring-2 focus:ring-impacto-orange">
            <svg xmlns='http://www.w3.org/2000/svg' class='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
              <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12' />
            </svg>
          </button>
        </div>
  
        <!-- Contenido del Modal -->
        <div class="p-6 space-y-6">
          <p class="text-sm text-gray-600">
            Elige el plan y el ciclo de facturación que mejor se adapte a tus necesidades. Tu selección actual está resaltada.
          </p>
  
          <!-- Selector de ciclo de facturación -->
          <div class="mb-6 flex justify-center items-center space-x-3">
            <span class="text-sm text-gray-700">Ver precios en facturación:</span>
            <div class="relative flex items-center p-0.5 bg-impacto-blue/10 rounded-full shadow-sm">
              <button
                @click="selectedBillingCycleForModal = 'monthly'"
                :class="['px-4 py-1.5 text-xs sm:text-sm font-medium rounded-full transition-colors duration-200 ease-in-out relative z-10',
                        selectedBillingCycleForModal === 'monthly' ? 'bg-impacto-orange text-white shadow' : 'text-gray-600 hover:text-impacto-blue']"
              >
                Mensual
              </button>
              <button
                @click="selectedBillingCycleForModal = 'annual'"
                :class="['px-4 py-1.5 text-xs sm:text-sm font-medium rounded-full transition-colors duration-200 ease-in-out relative z-10 flex items-center gap-1.5',
                        selectedBillingCycleForModal === 'annual' ? 'bg-impacto-orange text-white shadow' : 'text-gray-600 hover:text-impacto-blue']"
              >
                Anual
                <span class="bg-green-500 text-white text-[0.6rem] font-bold px-1.5 py-0.5 rounded-sm leading-none">
                  AHORRO
                </span>
              </button>
            </div>
          </div>
  
          <!-- Grid de Planes Disponibles -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              v-for="plan in planDetailsData"
              :key="plan.id"
              :class="[
                'p-6 border rounded-lg flex flex-col transition-all duration-300 hover:shadow-xl relative',
                (props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal) ? 'border-impacto-orange ring-2 ring-impacto-orange shadow-lg bg-orange-50/30' : 'border-gray-200 bg-white',
                isProcessingChange ? 'opacity-70 cursor-not-allowed' : ''
              ]"
            >
              <div v-if="plan.isPopular && !(props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal)" 
                   class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 z-10">
                <span class="inline-flex items-center px-3 py-0.5 rounded-full text-xs font-semibold bg-impacto-orange text-white shadow-md">
                  RECOMENDADO
                </span>
              </div>
              <h4 class="text-lg font-semibold text-impacto-blue mt-1">{{ plan.name }}</h4>
              <p v-if="plan.description" class="text-xs text-gray-500 mb-3 h-10">{{ plan.description }}</p>
              
              <div class="my-3">
                <span class="text-3xl font-bold text-gray-900">
                  {{ getPriceForModalDisplay(plan.id).mainPrice }}
                </span>
                <span class="text-sm font-normal text-gray-500">{{ getPriceForModalDisplay(plan.id).billingCycleText }}</span>
                <p v-if="selectedBillingCycleForModal === 'annual'" class="text-xs text-gray-500 mt-1">
                  Total {{ getPriceForModalDisplay(plan.id).annualTotal }}€ al año
                </p>
              </div>
  
              <ul class="space-y-2 text-sm text-gray-600 mb-6 flex-grow">
                <li v-for="benefit in plan.benefits" :key="benefit.text" class="flex items-start">
                  <component :is="benefit.icon" :class="['flex-shrink-0 h-5 w-5 inline mr-2 mt-0.5', benefit.iconColor]"></component>
                  <span>{{ benefit.text }}</span>
                </li>
              </ul>
  
              <button 
                @click="selectPlanAndProceed(plan.id, selectedBillingCycleForModal)"
                :disabled="isProcessingChange || (props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal)"
                class="mt-auto w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                :class="[(props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal) ? 'bg-gray-400' : 'bg-impacto-orange hover:bg-orange-600 focus:ring-impacto-orange']"
              >
                <svg v-if="isProcessingChange" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                  <circle class='opacity-25' cx='12' cy='12' r='10' stroke='currentColor' stroke-width='4'></circle>
                  <path class='opacity-75' fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
                </svg>
                <span v-if="props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal">Plan Actual</span>
                <span v-else-if="!props.isSubscriptionActive">Suscribirse a {{ plan.name }}</span>
                <span v-else>Cambiar a {{ plan.name }}</span>
              </button>
            </div>
          </div>
        </div>
         <!-- Pie del Modal (opcional, para acciones adicionales o mensajes) -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 text-right">
          <button @click="closeModal" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue">
            Cancelar
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, watch } from 'vue';
  import { CheckIcon, StarIcon } from '@heroicons/vue/20/solid';
  
  // --- Props --- 
  const props = defineProps<{
    showModal: boolean;
    currentPlanId: string | null;
    currentBillingCycle: 'monthly' | 'annual' | null;
    isSubscriptionActive: boolean;
  }>();
  
  // --- Emits --- 
  const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'plan-selected', planId: string, billingCycle: 'monthly' | 'annual'): void;
  }>();
  
  // --- Interfaces Internas (copiadas de Suscripcion.vue o ContratacionPage.vue) ---
  interface PlanBenefit {
    text: string;
    icon: any; 
    iconColor: string;
    subtext?: string;
  }
  
  interface AvailablePlanOption {
    id: string; 
    name: string;
    price_monthly: number;
    price_annual: number; 
    currency: string;
    stripe_price_id_monthly: string; 
    stripe_price_id_annual: string;  
    benefits: Array<PlanBenefit>;
    isPopular?: boolean;
    description?: string;
  }
  
  // --- Datos de Planes (copiados de Suscripcion.vue o ContratacionPage.vue) ---
  const planDetailsData: Record<string, AvailablePlanOption> = {
    'ia-starter': {
      id: 'ia-starter',
      name: 'IA Starter',
      price_monthly: 49,
      price_annual: 420,
      currency: 'EUR',
      stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_STARTER_MONTHLY || 'price_starter_monthly_placeholder',
      stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_STARTER_ANNUAL || 'price_starter_annual_placeholder',
      benefits: [
        { text: 'Valorador IA personalizado', icon: CheckIcon, iconColor: 'text-green-500' },
        { text: 'Informes de valoración básicos', icon: CheckIcon, iconColor: 'text-green-500' },
        { text: 'Secuencia IA estándar', icon: CheckIcon, iconColor: 'text-green-500' },
        { text: '1 Usuario en Dashboard', icon: CheckIcon, iconColor: 'text-green-500' },
      ],
      description: 'Ideal para quienes comienzan y quieren automatizar la captación de leads.'
    },
    'ia-pro': {
      id: 'ia-pro',
      name: 'IA Pro',
      price_monthly: 99,
      price_annual: 780,
      currency: 'EUR',
      stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_PRO_MONTHLY || 'price_pro_monthly_placeholder',
      stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_PRO_ANNUAL || 'price_pro_annual_placeholder',
      isPopular: true,
      benefits: [
        { text: 'Todo en IA Starter', icon: CheckIcon, iconColor: 'text-green-500' },
        { text: 'Secuencias IA avanzadas', icon: StarIcon, iconColor: 'text-impacto-orange' },
        { text: 'Hasta 3 Usuarios en Dashboard', icon: CheckIcon, iconColor: 'text-green-500' },
        { text: 'Emails desde tu dominio', icon: StarIcon, iconColor: 'text-impacto-orange' },
        { text: 'Soporte prioritario', icon: CheckIcon, iconColor: 'text-green-500' },
      ],
      description: 'Perfecto para agencias en crecimiento que buscan más potencia y personalización.'
    },
    'ia-elite': {
      id: 'ia-elite',
      name: 'IA Élite',
      price_monthly: 199,
      price_annual: 1500,
      currency: 'EUR',
      stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_ELITE_MONTHLY || 'price_elite_monthly_placeholder',
      stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_ELITE_ANNUAL || 'price_elite_annual_placeholder',
      benefits: [
        { text: 'Todo en IA Pro', icon: CheckIcon, iconColor: 'text-green-500' },
        { text: 'Secuencias IA personalizables y múltiples', icon: StarIcon, iconColor: 'text-impacto-orange' },
        { text: 'Usuarios ilimitados', icon: StarIcon, iconColor: 'text-impacto-orange' },
        { text: 'Exportación de datos', icon: StarIcon, iconColor: 'text-impacto-orange' },
        { text: 'Configuración asistida', icon: CheckIcon, iconColor: 'text-green-500' },
      ],
      description: 'La solución completa para grandes agencias que requieren el máximo rendimiento y flexibilidad.'
    }
  };
  
  // --- Refs Internas del Modal ---
  const selectedBillingCycleForModal = ref<'monthly' | 'annual'>('monthly');
  const isProcessingChange = ref(false); // Para feedback visual durante la llamada a API
  
  // Inicializar el ciclo de facturación del modal con el actual si está activo
  watch(() => props.showModal, (newValue) => {
    if (newValue && props.isSubscriptionActive && props.currentBillingCycle) {
      selectedBillingCycleForModal.value = props.currentBillingCycle;
    } else if (newValue) {
      selectedBillingCycleForModal.value = 'monthly'; // Default si no hay suscripción activa
    }
  }, { immediate: true });
  
  // --- Funciones de Ayuda Internas ---
  const closeModal = () => {
    emit('close');
  };
  
  const getPriceForModalDisplay = (planId: keyof typeof planDetailsData) => {
    const plan = planDetailsData[planId];
    if (!plan) return { mainPrice: 'N/A', annualTotal: 'N/A', billingCycleText: '' };
  
    let priceToShow;
    let billingCycleText = '';
    if (selectedBillingCycleForModal.value === 'monthly') {
      priceToShow = plan.price_monthly;
      billingCycleText = '/mes';
    } else {
      priceToShow = parseFloat((plan.price_annual / 12).toFixed(2));
      billingCycleText = '/mes (fact. anual)';
    }
    
    const formattedPrice = Number.isInteger(priceToShow) ? priceToShow.toFixed(0) : priceToShow.toFixed(2);
    const annualTotal = Number.isInteger(plan.price_annual) ? plan.price_annual.toFixed(0) : plan.price_annual.toFixed(2);

    return {
      mainPrice: `${plan.currency === 'EUR' ? '€' : plan.currency}${formattedPrice}`,
      annualTotal: annualTotal,
      billingCycleText: billingCycleText
    };
  };
  
  const selectPlanAndProceed = (planId: string, newBillingCycle: 'monthly' | 'annual') => {
    if (props.currentPlanId === planId && props.currentBillingCycle === newBillingCycle) {
      // No hacer nada si es el plan y ciclo actual, el botón debería estar deshabilitado
      return;
    }
    isProcessingChange.value = true;
  };
  
  </script>
  
  <style scoped>
  /* Estilos para la transición del modal */
  .transition-opacity {
    transition-property: opacity;
  }
  .ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
  .duration-300 {
    transition-duration: 300ms;
  }
  
  .transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .transition-all {
    transition-property: all;
  }
  .scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: var(--tw-translate-x, 0) var(--tw-translate-y, 0) var(--tw-rotate, 0) var(--tw-skew-x, 0) var(--tw-skew-y, 0) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: var(--tw-translate-x, 0) var(--tw-translate-y, 0) var(--tw-rotate, 0) var(--tw-skew-x, 0) var(--tw-skew-y, 0) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .pointer-events-none {
    pointer-events: none;
  }
  
  </style> 