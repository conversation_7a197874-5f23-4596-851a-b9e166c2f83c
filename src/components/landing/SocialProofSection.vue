<template>
    <section id="social-proof" class="py-16 sm:py-24 bg-white">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-10 md:mb-14">
          <p
            ref="sectionEyebrowRef"
            class="text-sm font-semibold text-impacto-orange uppercase tracking-wider mb-2"
          >
            Qui<PERSON>es somos
          </p>
          <h2
            ref="sectionTitleRef"
            class="text-2xl sm:text-3xl font-bold text-impacto-blue tracking-tight"
          >
            Desarrollado por <span class="text-impacto-orange">Impacto Automation</span>
          </h2>
          <p class="text-gray-600 mt-4 max-w-3xl mx-auto">
            Más de 5 años especializados en marketing digital, automatización e Inteligencia Artificial para el sector inmobiliario español. Conocemos tus desafíos porque trabajamos contigo día a día.
          </p>
        </div>
  
        <!-- Historia y Conexión Emocional -->
        <div class="max-w-4xl mx-auto">
          <!-- <PERSON><PERSON><PERSON> Principal de Historia -->
          <div class="bg-gradient-to-r from-impacto-blue to-impacto-blue/90 rounded-2xl p-8 md:p-12 text-white mb-12">
            <div class="text-center max-w-4xl mx-auto">
              <h3 class="text-2xl md:text-3xl font-bold mb-6 leading-tight">
                Entendemos los <span class="text-impacto-orange">desafíos de la captación</span>
              </h3>
              <div class="space-y-4 text-lg leading-relaxed">
                <p>
                  Durante <strong>más de 5 años</strong> hemos trabajado codo a codo con decenas de agentes como tú. Conocemos la presión de generar prospectos constantemente, la dificultad de hacer seguimiento manual y la competencia cada vez más intensa.
                </p>
                <p>
                  Por eso desarrollamos <strong>InmoAutomation</strong>: para automatizar lo que más tiempo te consume y permitirte enfocarte en lo que realmente importa: cerrar ventas.
                </p>
              </div>
            </div>
          </div>

          <!-- Sección de Misión con Diseño Moderno -->
          <div class="grid md:grid-cols-2 gap-8 items-center">
            <!-- Lado Izquierdo: Misión -->
            <div>
              <div class="inline-flex items-center px-4 py-2 bg-impacto-orange/10 text-impacto-orange rounded-full text-sm font-semibold mb-4">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Nuestra misión
              </div>
              <h3 class="text-2xl font-bold text-impacto-blue mb-4">
                Democratizar la tecnología inmobiliaria
              </h3>
              <p class="text-gray-600 leading-relaxed">
                Creemos que <strong>cualquier agente</strong>, sin importar su tamaño o presupuesto, merece tener las mismas herramientas que las grandes franquicias. InmoAutomation es nuestra forma de nivelar el campo de juego.
              </p>
            </div>

            <!-- Lado Derecho: Beneficios Clave -->
            <div class="space-y-4">
              <div v-for="(benefit, index) in keyBenefits" :key="index" class="flex items-start gap-4 p-4 bg-gray-50 rounded-xl">
                <div class="w-10 h-10 bg-impacto-orange/10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <component :is="benefit.icon" class="w-5 h-5 text-impacto-orange" />
                </div>
                <div>
                  <h4 class="font-semibold text-impacto-blue mb-1">{{ benefit.title }}</h4>
                  <p class="text-sm text-gray-600">{{ benefit.description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, nextTick } from 'vue'
  import { gsap } from 'gsap'
  import { ScrollTrigger } from 'gsap/ScrollTrigger'
  import {
    UserIcon,
    BuildingOfficeIcon,
    StarIcon
  } from '@heroicons/vue/24/outline'

  gsap.registerPlugin(ScrollTrigger)



  // Beneficios clave de nuestra experiencia
  const keyBenefits = ref([
    {
      icon: UserIcon,
      title: 'Conocemos el sector',
      description: 'Entendemos los desafíos reales de captación y seguimiento de prospectos inmobiliarios'
    },
    {
      icon: BuildingOfficeIcon,
      title: 'Tecnología accesible',
      description: 'Convertimos herramientas complejas en soluciones simples que cualquiera puede usar'
    },
    {
      icon: StarIcon,
      title: 'Colaboraciones duraderas',
      description: 'Mantenemos proyectos a largo plazo con agentes que confían en nuestro trabajo'
    }
  ])

  // Refs para elementos del DOM
  const sectionEyebrowRef = ref<HTMLElement | null>(null)
  const sectionTitleRef = ref<HTMLElement | null>(null)
  
  onMounted(async () => {
    await nextTick();

  })
  </script>
  
  <style scoped>
  /* Asegurar visibilidad del contenido */
  #social-proof {
    opacity: 1 !important;
    visibility: visible !important;
  }

  #social-proof * {
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
  }

  /* Estilos específicos para SocialProofSection */
  img {
    transition: opacity 0.3s ease-in-out;
  }
  </style>