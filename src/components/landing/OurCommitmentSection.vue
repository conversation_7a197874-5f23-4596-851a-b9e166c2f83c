<template>
  <section id="our-pillars" class="py-20 sm:py-28 bg-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- C<PERSON><PERSON>a de la Sección -->
      <div class="text-center mb-16 md:mb-20">
        <h2
          ref="sectionTitleRef"
          class="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-impacto-blue tracking-tight mb-5"
        >
          Nuestros Pilares de Confianza
        </h2>
        <p
          ref="sectionSubtitleRef"
          class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto"
        >
          En Impacto Automation, cada herramienta y servicio se construye sobre principios sólidos, diseñados para potenciar tu agencia y asegurar tu tranquilidad.
        </p>
      </div>

      <!-- G<PERSON> de Pilar<PERSON> -->
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-x-8 gap-y-12">
        <!-- Pilar 1: IA Avanzada para Valoraciones Precisas -->
        <div
          ref="pillarCard1Ref"
          class="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 border border-gray-200 flex flex-col"
        >
          <div class="flex-shrink-0 flex items-center justify-center mb-6">
            <span class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-impacto-blue-100">
              <svg class="h-8 w-8 text-impacto-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke-width="1.5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" />
              </svg>
            </span>
          </div>
          <h3 class="text-xl font-semibold text-impacto-blue mb-3 text-center">
            Precisión Potenciada por IA
          </h3>
          <p class="text-gray-600 text-center leading-relaxed text-sm sm:text-base flex-grow">
            Aprovechamos la inteligencia artificial de última generación y un análisis exhaustivo de datos de mercado para ofrecerte valoraciones inmobiliarias de alta fiabilidad. Minimiza errores, fundamenta tus precios y asesora a tus clientes con la confianza que solo la precisión tecnológica puede brindar.
          </p>
        </div>

        <!-- Pilar 2: Eficiencia y Claridad en Tu Flujo de Trabajo -->
        <div
          ref="pillarCard2Ref"
          class="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 border border-gray-200 flex flex-col"
        >
          <div class="flex-shrink-0 flex items-center justify-center mb-6">
            <span class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-impacto-orange-100">
              <svg class="h-8 w-8 text-impacto-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke-width="1.5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.82m5.84-2.56a16.49 16.49 0 00-5.84-2.56m0 0A16.482 16.482 0 013.463 4.663L3.463 9.58m9.728-4.919a16.491 16.491 0 00-9.728 0m0 0V3.463c0-1.01.822-1.833 1.833-1.833h7.064a1.833 1.833 0 011.833 1.833v1.2m0 0a16.487 16.487 0 013.463 4.919m-3.463 0a16.487 16.487 0 003.463 4.919m0 0L21 9.58m-9.728 4.792a16.491 16.491 0 01-9.728 0m9.728 0V21m-9.728 0H3.463c-1.01 0-1.833-.823-1.833-1.833v-7.064a1.833 1.833 0 011.833-1.833h1.2" />
              </svg>
            </span>
          </div>
          <h3 class="text-xl font-semibold text-impacto-blue mb-3 text-center">
            Tu Tiempo Optimizado, Procesos Claros
          </h3>
          <p class="text-gray-600 text-center leading-relaxed text-sm sm:text-base flex-grow">
            Transforma horas de trabajo manual en minutos. Nuestra plataforma intuitiva te permite generar informes de valoración completos rápidamente, y con nuestra estructura de precios transparente de 49€/mes, siempre sabrás qué esperar. Simplifica tu día a día y enfócate en lo que realmente importa: tus clientes.
          </p>
        </div>

        <!-- Pilar 3: Soluciones Centradas en Tu Crecimiento -->
        <div
          ref="pillarCard3Ref"
          class="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 border border-gray-200 flex flex-col"
        >
          <div class="flex-shrink-0 flex items-center justify-center mb-6">
            <span class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-impacto-blue-100">
              <svg class="h-8 w-8 text-impacto-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke-width="1.5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" />
              </svg>
            </span>
          </div>
          <h3 class="text-xl font-semibold text-impacto-blue mb-3 text-center">
            Impulsando el Éxito de Tu Agencia
          </h3>
          <p class="text-gray-600 text-center leading-relaxed text-sm sm:text-base flex-grow">
            Más que un software, somos tu socio estratégico. Desde la personalización de marca hasta futuras herramientas de captación, cada funcionalidad está diseñada para ayudarte a conseguir más exclusivas, fortalecer la relación con tus clientes y destacar en un mercado competitivo. Tu crecimiento es el motor de nuestra innovación.
          </p>
        </div>

        <!-- Pilar 4: Seguridad de Datos y Soporte Dedicado -->
        <div
          ref="pillarCard4Ref"
          class="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 border border-gray-200 flex flex-col"
        >
          <div class="flex-shrink-0 flex items-center justify-center mb-6">
            <span class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-impacto-orange-100">
              <svg class="h-8 w-8 text-impacto-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke-width="1.5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.956 11.956 0 013.586 9H3.114c-1.042 0-1.903.843-1.903 1.886v1.043c0 1.043.861 1.886 1.903 1.886h.472v1.043c0 1.043.861 1.886 1.903 1.886h.472c1.042 0 1.903-.843 1.903-1.886V15.17c0-1.043-.861-1.886-1.903-1.886h-.472l.104-.054a11.956 11.956 0 015.432-3.62M15 12.75L11.25 15 9 9.75M21.036 9H19.586a11.956 11.956 0 01-5.432 3.62l.104.054h-.472c-1.042 0-1.903.843-1.903 1.886v1.043c0 1.043.861 1.886 1.903 1.886h.472v1.043c0 1.043.861 1.886 1.903 1.886h.472c1.042 0 1.903-.843 1.903-1.886V15.17c0-1.043-.861-1.886-1.903-1.886h-.472a11.956 11.956 0 013.586-9.036h.472c1.042 0 1.903.843 1.903 1.886v1.043c0 1.043-.861 1.886-1.903 1.886h-.472M12 18.75v2.25m0-16.5v2.25" />
              </svg>
            </span>
          </div>
          <h3 class="text-xl font-semibold text-impacto-blue mb-3 text-center">
            Tus Datos Seguros, Nuestro Equipo Contigo
          </h3>
          <p class="text-gray-600 text-center leading-relaxed text-sm sm:text-base flex-grow">
            La confidencialidad de tu información y la de tus clientes es nuestra máxima prioridad. Operamos con robustos protocolos de seguridad y, aunque nuestra plataforma es fácil de usar, nuestro equipo de soporte está listo para asistirte y asegurar que aproveches al máximo cada herramienta.
          </p>
        </div>

      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const sectionTitleRef = ref<HTMLElement | null>(null)
const sectionSubtitleRef = ref<HTMLElement | null>(null)
const pillarCard1Ref = ref<HTMLElement | null>(null)
const pillarCard2Ref = ref<HTMLElement | null>(null)
const pillarCard3Ref = ref<HTMLElement | null>(null)
const pillarCard4Ref = ref<HTMLElement | null>(null) // Added ref for the 4th card

onMounted(async () => {
  await nextTick() // Ensure DOM is ready

  const tl = gsap.timeline({
    scrollTrigger: {
      trigger: '#our-pillars',
      start: 'top 75%', // Adjusted start point for taller section
      toggleActions: 'play none none none',
    },
  })

  if (sectionTitleRef.value) {
    tl.from(sectionTitleRef.value, {
      opacity: 0,
      y: 40, // Slightly increased y for more noticeable animation
      duration: 0.7,
      ease: 'power2.out',
    })
  }
  if (sectionSubtitleRef.value) {
    tl.from(
      sectionSubtitleRef.value,
      {
        opacity: 0,
        y: 40,
        duration: 0.7,
        ease: 'power2.out',
      },
      '-=0.5' // Start slightly after title animation begins
    )
  }

  const cards = [
    pillarCard1Ref.value,
    pillarCard2Ref.value,
    pillarCard3Ref.value,
    pillarCard4Ref.value, // Added 4th card to animation array
  ].filter(Boolean) as HTMLElement[] // Filter out nulls and cast

  if (cards.length > 0) {
    tl.from(
      cards,
      {
        opacity: 0,
        y: 60, // Slightly increased y for card animation
        duration: 0.6,
        stagger: 0.25, // Adjusted stagger for 4 cards
        ease: 'power2.out',
      },
      '-=0.4' // Start slightly after subtitle animation
    )
  }
})
</script>

<style scoped>
/* Scoped styles if any specific fine-tuning is needed beyond Tailwind */
.bg-impacto-blue-100 {
  background-color: #E6F0FF; /* Example: A very light blue */
}
.text-impacto-blue-600 {
  color: #003B6D; /* Your main impacto-blue */
}
.bg-impacto-orange-100 {
  background-color: #FFF2E6; /* Example: A very light orange */
}
.text-impacto-orange-600 {
  color: #FF6600; /* Your main impacto-orange */
}
</style>
