<template>
    <section id="valorador-demo" class="py-16 sm:py-24 bg-gradient-to-br from-gray-50 to-blue-50 text-impacto-blue">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <!-- <PERSON><PERSON><PERSON><PERSON> de la Sección -->
        <div class="text-center mb-12 md:mb-16">
          <div ref="badgeRef" class="inline-flex items-center px-4 py-2 bg-impacto-orange/10 text-impacto-orange rounded-full text-sm font-semibold mb-6">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
            </svg>
            Pruébalo ahora mismo
          </div>
          <h2
            ref="sectionTitleRef"
            class="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight text-impacto-blue mb-6"
          >
            Así se verá <span class="text-impacto-orange">tu valorador</span>,<br>
            pero personalizado por y para ti
          </h2>
          <p ref="sectionSubtitleRef" class="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            No necesitas imaginar cómo funcionará. Interactúa directamente con nuestro valorador de demostración y descubre por qué las inmobiliarias más exitosas confían en nuestra tecnología para generar leads de calidad.
          </p>
        </div>

        <!-- Valorador Embebido Centrado y Ancho -->
        <div ref="valoradorContainerRef" class="max-w-6xl mx-auto mb-16">
          <div class="relative">
            <!-- Barra de navegador decorativa -->
            <div ref="browserBarRef" class="bg-gray-800 rounded-t-xl px-4 py-3 flex items-center space-x-2">
              <div class="flex space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <div class="flex-1 bg-gray-700 rounded-md px-3 py-1 ml-4">
                <span class="text-gray-300 text-sm">tuinmobiliaria.inmoautomation.com/valora</span>
              </div>
            </div>

            <!-- Contenedor del Valorador -->
            <div class="bg-white rounded-b-xl shadow-2xl border-4 border-gray-200 overflow-hidden">
              <div id="valorador-container" class="min-h-[600px] w-full"></div>
            </div>

            <!-- Indicador de interactividad -->
            <div ref="interactiveIndicatorRef" class="absolute -top-2 -right-2 bg-impacto-orange text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg animate-pulse">
              ¡Interactivo!
            </div>
          </div>
        </div>

        <!-- Sección de Beneficios y Características -->
        <div ref="benefitsContainerRef" class="max-w-6xl mx-auto">

          <!-- Título de beneficios -->
          <div class="text-center mb-12">
            <h3 class="text-2xl sm:text-3xl font-bold text-impacto-blue mb-4">
              Esto es lo que obtienes con tu valorador personalizado:
            </h3>
            <p class="text-gray-600 text-lg max-w-3xl mx-auto">
              Cada elemento que ves puede ser personalizado con tu marca, colores y datos de contacto.
            </p>
          </div>

          <!-- Grid de beneficios -->
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <div v-for="(benefit, index) in keyBenefits" :key="index"
                 ref="benefitItemsRef"
                 class="flex flex-col items-start p-6 bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300">
              <div class="flex-shrink-0 mb-4">
                <div class="w-12 h-12 bg-impacto-orange/10 rounded-xl flex items-center justify-center">
                  <component :is="benefit.icon" class="w-6 h-6 text-impacto-orange" />
                </div>
              </div>
              <div>
                <h4 class="text-lg font-semibold text-impacto-blue mb-2">{{ benefit.title }}</h4>
                <p class="text-gray-600 leading-relaxed">{{ benefit.description }}</p>
              </div>
            </div>
          </div>

          <!-- CTA secundario centrado -->
          <div ref="ctaSecondaryRef" class="bg-impacto-blue rounded-xl p-8 text-white text-center max-w-2xl mx-auto">
            <h4 class="text-2xl font-bold mb-3">¿Listo para tener el tuyo?</h4>
            <p class="text-blue-100 mb-6 text-lg">Configura tu valorador personalizado en menos de 5 minutos.</p>
            <RouterLink
              :to="{ name: 'ContratacionPage' }"
              class="inline-flex items-center px-8 py-4 bg-impacto-orange text-white font-semibold rounded-lg hover:bg-orange-600 transition-colors duration-300 shadow-lg text-lg"
            >
              Crear mi valorador ahora
              <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </RouterLink>
          </div>
        </div>
      </div>
    </section>
  </template>

  <script setup lang="ts">
  import { ref, onMounted, nextTick } from 'vue'
  import { gsap } from 'gsap'
  import { ScrollTrigger } from 'gsap/ScrollTrigger'
  import {
    SparklesIcon,
    DocumentTextIcon,
    PaintBrushIcon,
    ClockIcon,
    UserGroupIcon,
    ChartBarIcon
  } from '@heroicons/vue/24/outline'

  gsap.registerPlugin(ScrollTrigger)

  // Datos de beneficios clave
  const keyBenefits = ref([
    {
      icon: SparklesIcon,
      title: "Valoraciones instantáneas con IA",
      description: "Algoritmos avanzados que analizan miles de datos del mercado para ofrecer valoraciones precisas en segundos."
    },
    {
      icon: DocumentTextIcon,
      title: "Informes profesionales instantáneos",
      description: "Cada lead recibe un informe PDF detallado con tu marca, logo y datos de contacto incluidos."
    },
    {
      icon: PaintBrushIcon,
      title: "Personalización total de marca",
      description: "Colores, logo, textos y datos de contacto. Todo se adapta perfectamente a tu identidad corporativa."
    },
    {
      icon: ClockIcon,
      title: "Captación 24/7 sin descanso",
      description: "Tu valorador trabaja las 24 horas generando leads mientras tú duermes o atiendes otros clientes."
    },
    {
      icon: UserGroupIcon,
      title: "Leads de vendedores cualificados",
      description: "Solo propietarios interesados en vender contactan contigo, no compradores curiosos."
    },
    {
      icon: ChartBarIcon,
      title: "Estadísticas y seguimiento completo",
      description: "Dashboard con métricas detalladas de rendimiento, conversiones y análisis de leads."
    }
  ])



  // Refs para animaciones GSAP
  const badgeRef = ref<HTMLElement | null>(null)
  const sectionTitleRef = ref<HTMLElement | null>(null)
  const sectionSubtitleRef = ref<HTMLElement | null>(null)
  const valoradorContainerRef = ref<HTMLElement | null>(null)
  const browserBarRef = ref<HTMLElement | null>(null)
  const interactiveIndicatorRef = ref<HTMLElement | null>(null)
  const benefitsContainerRef = ref<HTMLElement | null>(null)
  const benefitItemsRef = ref<HTMLElement[]>([])
  const ctaSecondaryRef = ref<HTMLElement | null>(null)

  // Función para cargar el script del valorador
  const loadValoradorScript = () => {
    // Verificar si el script ya existe
    const existingScript = document.getElementById('valorador-js')
    if (existingScript) {
      existingScript.remove()
    }

    // Crear y cargar el script del valorador
    const script = document.createElement('script')
    script.id = 'valorador-js'
    script.src = 'https://tuinmobiliaria.inmoautomation.com/embed/valorador.js?id=tuinmobiliaria&apiKey=e4830c59-619c-11f0-971c-42010a800002'
    script.async = true

    script.onload = () => {
      setTimeout(() => {
        const container = document.getElementById('valorador-container')
        if (container) {
          // Aplicar estilos para liberar el ancho
          const allElements = container.querySelectorAll('*')
          allElements.forEach(el => {
            if (el instanceof HTMLElement) {
              el.style.width = '100%'
              el.style.maxWidth = 'none'
              el.style.minWidth = '100%'
            }
          })
        }
      }, 1000)
    }

    script.onerror = () => {
      console.error('Error loading valorador script')
    }

    document.head.appendChild(script)
  }

  // Función para forzar el redimensionamiento del valorador
  const forceValoradorResize = () => {
    const container = document.getElementById('valorador-container')
    if (container) {
      // Aplicar estilos de forma más agresiva
      const style = document.createElement('style')
      style.textContent = `
        #valorador-container * {
          width: 100% !important;
          max-width: none !important;
          min-width: 100% !important;
        }
        #valorador-container iframe {
          width: 100% !important;
          height: 650px !important;
        }
      `
      document.head.appendChild(style)

      // Forzar recálculo de layout
      container.style.display = 'none'
      container.offsetHeight // Trigger reflow
      container.style.display = 'block'
    }
  }

  onMounted(async () => {
    await nextTick()

    // Cargar el script del valorador
    loadValoradorScript()

    // Forzar redimensionamiento después de un tiempo
    setTimeout(forceValoradorResize, 2000)
    setTimeout(forceValoradorResize, 5000)

    // Observer para detectar cuando se añade contenido al valorador
    const container = document.getElementById('valorador-container')
    if (container) {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            setTimeout(forceValoradorResize, 500)
          }
        })
      })

      observer.observe(container, {
        childList: true,
        subtree: true
      })
    }

    // Animaciones GSAP

    // Animación para el badge
    if (badgeRef.value) {
      gsap.from(badgeRef.value, {
        opacity: 0,
        y: 20,
        duration: 0.6,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: badgeRef.value,
          start: 'top 90%',
          toggleActions: 'play none none none',
        },
      })
    }

    // Animación para el título y subtítulo
    if (sectionTitleRef.value && sectionSubtitleRef.value) {
      gsap.from([sectionTitleRef.value, sectionSubtitleRef.value], {
        opacity: 0,
        y: 50,
        duration: 0.8,
        ease: 'power3.out',
        stagger: 0.2,
        scrollTrigger: {
          trigger: sectionTitleRef.value,
          start: 'top 85%',
          toggleActions: 'play none none none',
        },
      })
    }

    // Animación para el contenedor del valorador
    if (valoradorContainerRef.value) {
      gsap.from(valoradorContainerRef.value, {
        opacity: 0,
        y: 50,
        scale: 0.95,
        duration: 1.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: valoradorContainerRef.value,
          start: 'top 80%',
          toggleActions: 'play none none none',
        },
      })
    }

    // Animación para la barra de navegador
    if (browserBarRef.value) {
      gsap.from(browserBarRef.value, {
        y: -20,
        opacity: 0,
        duration: 0.5,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: valoradorContainerRef.value,
          start: 'top 75%',
          toggleActions: 'play none none none',
        },
        delay: 0.3
      })
    }

    // Animación para el indicador interactivo
    if (interactiveIndicatorRef.value) {
      gsap.from(interactiveIndicatorRef.value, {
        scale: 0,
        rotation: 180,
        duration: 0.6,
        ease: 'back.out(1.7)',
        scrollTrigger: {
          trigger: valoradorContainerRef.value,
          start: 'top 75%',
          toggleActions: 'play none none none',
        },
        delay: 0.8
      })
    }

    // Animación para el contenedor de beneficios
    if (benefitsContainerRef.value) {
      gsap.from(benefitsContainerRef.value, {
        opacity: 0,
        y: 50,
        duration: 1,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: benefitsContainerRef.value,
          start: 'top 85%',
          toggleActions: 'play none none none',
        },
      })
    }

    // Animación para los elementos de beneficios
    if (benefitItemsRef.value.length > 0) {
      gsap.from(benefitItemsRef.value, {
        opacity: 0,
        y: 30,
        duration: 0.6,
        stagger: 0.15,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: benefitsContainerRef.value,
          start: 'top 80%',
          toggleActions: 'play none none none',
        },
        delay: 0.4
      })
    }

    // Animación para el CTA secundario
    if (ctaSecondaryRef.value) {
      gsap.from(ctaSecondaryRef.value, {
        opacity: 0,
        scale: 0.9,
        duration: 0.8,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: ctaSecondaryRef.value,
          start: 'top 85%',
          toggleActions: 'play none none none',
        },
      })
    }
  })
  </script>

  <style scoped>
  /* Estilos para el valorador embebido horizontal */
  #valorador-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    width: 100%;
    min-height: 600px;
    overflow: hidden;
  }

  /* Liberar completamente el ancho del valorador para que use todo el espacio */
  #valorador-container iframe,
  #valorador-container > div,
  #valorador-container * {
    width: 100% !important;
    max-width: none !important;
    min-height: 600px !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Asegurar que el contenido del valorador no tenga restricciones */
  #valorador-container .valorador-widget,
  #valorador-container .widget-container,
  #valorador-container form,
  #valorador-container .form-container {
    width: 100% !important;
    max-width: none !important;
    min-width: 100% !important;
  }

  /* Optimización para valorador horizontal en desktop */
  @media (min-width: 1024px) {
    #valorador-container {
      min-height: 650px;
    }

    #valorador-container iframe,
    #valorador-container > div,
    #valorador-container * {
      min-height: 650px !important;
    }
  }

  /* Animación personalizada para el indicador interactivo */
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(237, 135, 37, 0.7);
    }
    50% {
      box-shadow: 0 0 0 10px rgba(237, 135, 37, 0);
    }
  }

  .animate-pulse {
    animation: pulse-glow 2s infinite;
  }

  /* Mejoras de hover para los elementos interactivos */
  .hover\:shadow-lg:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  /* Grid de beneficios responsivo */
  @media (max-width: 768px) {
    .grid.md\:grid-cols-2.lg\:grid-cols-3 {
      grid-template-columns: 1fr;
    }
  }

  @media (min-width: 768px) and (max-width: 1024px) {
    .grid.md\:grid-cols-2.lg\:grid-cols-3 {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  /* Gradiente personalizado para el fondo */
  .bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  }

  /* Mejoras para el CTA */
  .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
  }
  </style>

  <!-- Estilos globales para el valorador (sin scoped) -->
  <style>
  /* Estilos globales para liberar completamente el valorador */
  #valorador-container .valorador-widget,
  #valorador-container .widget-container,
  #valorador-container .valorador-form,
  #valorador-container .form-wrapper,
  #valorador-container .step-container,
  #valorador-container .valorador-content,
  #valorador-container [class*="valorador"],
  #valorador-container [class*="widget"],
  #valorador-container [class*="form"] {
    width: 100% !important;
    max-width: none !important;
    min-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Asegurar que no hay restricciones de contenedor */
  #valorador-container .container,
  #valorador-container .container-fluid,
  #valorador-container .row,
  #valorador-container .col,
  #valorador-container [class*="col-"] {
    width: 100% !important;
    max-width: none !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  /* Liberar cualquier restricción de Bootstrap o frameworks CSS */
  #valorador-container .w-25,
  #valorador-container .w-50,
  #valorador-container .w-75,
  #valorador-container .w-100 {
    width: 100% !important;
  }

  /* Asegurar que los inputs y elementos del formulario usen el ancho completo */
  #valorador-container input,
  #valorador-container select,
  #valorador-container textarea,
  #valorador-container button {
    max-width: none !important;
  }
  </style>