<template>
  <section
    id="hero"
    class="relative bg-impacto-blue text-white overflow-hidden"
    :style="{ minHeight: `calc(100vh - ${headerHeight}px)` }"
  >
    <!-- Fondo con patrón tecnológico sutil (opcional, se puede añadir después) -->
    <!-- <div class="absolute inset-0 opacity-5">
      <img src="/img/patterns/tech-pattern.svg" alt="Tech Background" class="w-full h-full object-cover" />
    </div> -->

    <div
      class="relative container mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center z-10"
      :style="{ paddingTop: `${headerHeight}px`, paddingBottom: '4rem' }" 
    >
      <div class="grid md:grid-cols-2 gap-12 items-center w-full">
        <!-- Contenido de Texto (Izquierda) -->
        <div class="text-center md:text-left">
          <!-- Badge de confianza -->
          <div ref="heroBadgeRef" class="inline-flex items-center px-4 py-2 bg-white/10 text-white rounded-full text-sm font-semibold mb-6 backdrop-blur-sm">
            <svg class="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Inmobiliarias líderes confían en nosotros
          </div>

          <h1
            ref="heroTitleRef"
            class="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight text-white mb-6"
            style="line-height: 1.2;"
          >
            <span class="block xl:inline">Genera 3x más leads de vendedores</span> <span class="block text-impacto-orange xl:inline">en solo 5 minutos</span>
          </h1>
          <p ref="heroSubtitleRef" class="text-lg sm:text-xl text-gray-300 mb-8 max-w-2xl mx-auto md:mx-0">
            Tu valorador IA personalizado + landing page profesional + seguimiento automatizado. Todo listo para generar leads en piloto automático. <span class="text-white font-semibold">Sin conocimientos técnicos.</span>
          </p>

          <!-- Beneficios rápidos -->
          <div ref="heroBenefitsRef" class="flex flex-wrap gap-4 mb-10 justify-center md:justify-start">
            <div class="flex items-center text-sm text-gray-300">
              <svg class="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              Configuración en 5 minutos
            </div>
            <div class="flex items-center text-sm text-gray-300">
              <svg class="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              7 días gratis
            </div>
            <div class="flex items-center text-sm text-gray-300">
              <svg class="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              Landing page incluida
            </div>
          </div>

          <div ref="heroCtaRef" class="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
            <RouterLink
              :to="{ name: 'ContratacionPage' }"
              class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-white bg-impacto-orange rounded-lg shadow-xl hover:bg-orange-600 focus:outline-none focus:ring-4 focus:ring-orange-300 focus:ring-opacity-50 transition-all duration-300 ease-in-out transform hover:scale-105"
            >
              <span>Conseguir mi valorador gratis</span>
              <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </RouterLink>
            <a
              href="#valorador-demo"
              v-smooth-scroll
              class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg hover:bg-white hover:text-impacto-blue focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-impacto-blue transition-all duration-300 ease-in-out"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Ver demo en vivo
            </a>
          </div>

          <!-- Garantía -->
          <p ref="heroGuaranteeRef" class="mt-6 text-sm text-gray-400">
            Cancela cuando quieras, sin compromisos ni permanencias.
          </p>
        </div>

        <!-- Visual (Derecha) - Lottie Animation -->
        <div ref="heroVisualRef" class="hidden md:block relative w-full max-w-lg lg:max-w-xl aspect-square mx-auto ml-[-2rem]">
          <Vue3Lottie
            :animationData="GoingUpAnimation"
            :height="'100%'"
            :width="'100%'"
            :loop="true"
            :autoplay="true"
          />
        </div>
      </div>
    </div>
    <!-- Elementos decorativos animados sutilmente (opcional) -->
    <div class="absolute bottom-0 left-0 w-full h-24 overflow-hidden z-0">
        <svg viewBox="0 0 1440 100" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-auto absolute bottom-0" preserveAspectRatio="none">
            <path d="M0 56.8431L130.909 70C261.818 83.1569 523.636 109.468 785.455 97.9376C1047.27 86.4072 1309.09 37.0343 1440 12.3484V100H0V56.8431Z" :fill="colors.white"/>
            <!-- Este SVG crea una curva suave para transicionar a la siguiente sección si es de fondo blanco -->
        </svg>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { RouterLink } from 'vue-router'
import { gsap } from 'gsap'
// ScrollTrigger eliminado ya que no se utiliza
import { Vue3Lottie } from 'vue3-lottie'
import GoingUpAnimation from '@/assets/lottie/going-up.json'

// Directiva simple para smooth scroll (opcional si ya tienes Vue Router manejándolo bien para hashes)
// o si prefieres una librería más completa.
const vSmoothScroll = {
  mounted: (el: HTMLElement) => {
    el.addEventListener('click', (e) => {
      e.preventDefault()
      const href = el.getAttribute('href')
      if (href && href.startsWith('#')) {
        const targetElement = document.querySelector(href)
        if (targetElement) {
          targetElement.scrollIntoView({ behavior: 'smooth' })
        }
      }
    })
  },
}

const headerHeight = ref(80) // Altura estimada del header, podría ser dinámica

const colors = {
  blue: '#051f33',
  orange: '#ed8725',
  white: '#ffffff',
}

// Refs para animaciones GSAP
const heroBadgeRef = ref<HTMLElement | null>(null)
const heroTitleRef = ref<HTMLElement | null>(null)
const heroSubtitleRef = ref<HTMLElement | null>(null)
const heroBenefitsRef = ref<HTMLElement | null>(null)
const heroCtaRef = ref<HTMLElement | null>(null)
const heroGuaranteeRef = ref<HTMLElement | null>(null)
const heroVisualRef = ref<HTMLElement | null>(null)
// SVG specific refs removed

onMounted(async () => {
  // Esperar al siguiente tick para asegurar que los refs estén disponibles
  // y que el DOM esté completamente renderizado, especialmente para cálculos de altura.
  await nextTick(); 

  // (Opcional) Actualizar la altura del header si es dinámica y puede obtenerse
  // const headerElement = document.querySelector('header'); // Asumiendo que tu header tiene una etiqueta <header>
  // if (headerElement) {
  //   headerHeight.value = headerElement.clientHeight;
  // }


  // --- Animaciones de entrada para el texto y CTA ---
  const tl = gsap.timeline({ defaults: { ease: 'power3.out', duration: 0.8 } })

  if (heroBadgeRef.value) {
    tl.from(heroBadgeRef.value, {
      opacity: 0,
      y: 20,
      scale: 0.9,
      duration: 0.6,
    })
  }

  if (heroTitleRef.value) {
    tl.from(heroTitleRef.value, {
      opacity: 0,
      y: 50,
      duration: 1,
    }, '-=0.3')
  }

  if (heroSubtitleRef.value) {
    tl.from(heroSubtitleRef.value, {
      opacity: 0,
      y: 30,
      duration: 0.8,
    }, '-=0.5')
  }

  if (heroBenefitsRef.value && heroBenefitsRef.value.children.length > 0) {
    tl.from(heroBenefitsRef.value.children, {
      opacity: 0,
      x: -20,
      duration: 0.6,
      stagger: 0.1,
    }, '-=0.3')
  }

  if (heroCtaRef.value && heroCtaRef.value.children.length > 0) {
    gsap.set(heroCtaRef.value.children, { autoAlpha: 0, scale: 0.9 })
    tl.to(heroCtaRef.value.children, {
      autoAlpha: 1,
      scale: 1,
      stagger: 0.2,
      duration: 0.5,
      ease: 'back.out(1.7)'
    }, '-=0.4')
  }

  if (heroGuaranteeRef.value) {
    tl.from(heroGuaranteeRef.value, {
      opacity: 0,
      y: 20,
      duration: 0.6,
    }, '-=0.2')
  }

  // SVG Animation code removed as Lottie will handle visuals.
  // If heroVisualRef needs specific GSAP intro animation for the Lottie container:
  if (heroVisualRef.value) {
    gsap.from(heroVisualRef.value, {
      opacity: 0,
      scale: 0.8,
      duration: 1,
      ease: 'power3.out',
      delay: 0.1 // Appear much sooner, almost with the title
    });
  }

  // (Opcional) Animación de Parallax con ScrollTrigger
  // Necesita que ScrollTrigger esté registrado (gsap.registerPlugin(ScrollTrigger))
  // gsap.to(heroVisualRef.value, {
  //   yPercent: -20, // Mueve el visual hacia arriba un 20% de su altura al hacer scroll
  //   ease: "none",
  //   scrollTrigger: {
  //     trigger: "#hero", // El propio hero section
  //     start: "top top", // Cuando la parte superior del trigger llega a la parte superior del viewport
  //     end: "bottom top", // Cuando la parte inferior del trigger llega a la parte superior del viewport
  //     scrub: true, // Anima suavemente con el scroll
  //   },
  // });
  // gsap.to(heroTitleRef.value, {
  //   yPercent: 10,
  //   ease: "none",
  //   scrollTrigger: {
  //     trigger: "#hero",
  //     start: "top top",
  //     end: "bottom top",
  //     scrub: true,
  //   },
  // });

})
</script>

<style scoped>
/* Estilos específicos si son necesarios más allá de Tailwind */
#hero {
  /* Se puede usar para gradientes complejos de fondo o imágenes si se prefiere a un SVG o img tag */
  /* background: linear-gradient(135deg, var(--impacto-blue-darker) 0%, var(--impacto-blue) 100%); */
}

/* Animación de pulso para el icono (si se usa con un SVG simple en lugar de GSAP) */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.5;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: scale(1);
  }
}
.animate-pulse-slow {
  animation: pulse-slow 4s infinite ease-in-out;
}

/* Estilo para la forma SVG de la curva al final de la sección */
/* Asegura que el SVG llene el ancho y no deje espacios */
.wave-separator svg {
    display: block;
    width: 100%;
}
</style>