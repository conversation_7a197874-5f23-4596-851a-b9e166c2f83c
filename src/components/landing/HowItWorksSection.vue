<template>
    <section id="how-it-works" class="py-16 sm:py-24 bg-impacto-blue text-white">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Cabecera de la Sección -->
        <div class="text-center mb-12 md:mb-20">
          <h2
            ref="sectionTitleRef"
            class="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight mb-4"
          >
            De <PERSON>ero leads a <span class="text-impacto-orange">captación automática</span> en solo 3 pasos
          </h2>
          <p ref="sectionSubtitleRef" class="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto">
            El sistema más simple del mercado. Implementación en 5 minutos, resultados de por vida.
          </p>
        </div>
  
        <!-- Pasos del Proceso -->
        <div class="relative">
          <!-- <PERSON><PERSON><PERSON> de conexión (para desktop) -->
          <div
            ref="connectorLineRef"
            class="hidden lg:block absolute top-1/2 left-0 w-full h-1 bg-impacto-orange/30 rounded-full -translate-y-1/2"
            style="z-index: 0;"
          >
            <div ref="connectorLineProgressRef" class="h-full bg-impacto-orange rounded-full" style="width: 0%;"></div>
          </div>
  
          <div class="grid lg:grid-cols-3 gap-x-8 gap-y-16 lg:gap-y-0 relative z-10">
            <div
              v-for="(step, index) in steps"
              :key="index"
              :ref="el => setStepRef(el, index)"
              class="group relative flex flex-col items-center text-center p-6 bg-white/10 backdrop-blur-md rounded-xl shadow-xl border border-white/20 transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl hover:bg-white/20 hover:border-impacto-orange/60 overflow-hidden"
            >
              <!-- Contenido Principal de la Tarjeta (Icono, Título, Descripción) -->
              <div class="relative z-10 flex flex-col items-center w-full">
                <!-- Icono y Número del Paso (Número ahora va detrás) -->
                <div class="absolute -top-2 -left-6 sm:-top-4 sm:-left-8 md:-top-2 md:-left-12 z-0">
                  <span class="text-8xl sm:text-9xl md:text-[160px] lg:text-[180px] font-extrabold text-impacto-orange/20 leading-none tracking-tighter opacity-80 group-hover:text-impacto-orange/30 transition-colors duration-300 ease-in-out select-none">
                    0{{ index + 1 }}
                  </span>
                </div>
                
                <div class="relative p-3 sm:p-4 rounded-xl bg-white/10 backdrop-blur-md border border-white/25 shadow-lg transition-all duration-300 ease-in-out group-hover:bg-white/20 group-hover:border-impacto-orange/50 group-hover:shadow-xl mt-8 mb-5 sm:mb-6">
                  <component :is="step.icon" class="h-7 w-7 sm:h-8 lg:h-10 text-impacto-orange group-hover:text-white transition-colors duration-300 ease-in-out" aria-hidden="true" />
                </div>
    
                <h3 class="text-xl lg:text-2xl font-semibold text-white mb-3">
                  {{ step.title }}
                </h3>
                <p class="text-gray-300 text-sm lg:text-base leading-relaxed mb-4">
                  {{ step.description }}
                </p>

                <!-- Elementos de tiempo y resultado -->
                <div class="flex flex-col sm:flex-row gap-3 justify-center items-center mt-4">
                  <div class="flex items-center px-3 py-1.5 bg-impacto-orange/20 rounded-full border border-impacto-orange/30">
                    <svg class="w-4 h-4 text-impacto-orange mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-xs font-semibold text-white">{{ step.time }}</span>
                  </div>
                  <div class="flex items-center px-3 py-1.5 bg-green-500/20 rounded-full border border-green-400/30">
                    <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-xs font-semibold text-white">{{ step.result }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, shallowRef, nextTick } from 'vue'
  import { gsap } from 'gsap'
  import { ScrollTrigger } from 'gsap/ScrollTrigger'
  
  // Iconos de Heroicons (Nuevos iconos)
  import {
    RocketLaunchIcon, 
    PresentationChartLineIcon, 
    FunnelIcon 
  } from '@heroicons/vue/24/outline';
 // Old icons, might remove later if new ones are definitive:
 // import {
 //   SparklesIcon,
 //   DocumentCheckIcon,
 //   CpuChipIcon,
 // } from '@heroicons/vue/24/outline'
  
  gsap.registerPlugin(ScrollTrigger)
  
  const steps = shallowRef([
    {
      icon: RocketLaunchIcon,
      title: 'Configura en 5 minutos y empieza a captar',
      description: 'Sube tu logo, elige colores y copia tu enlace personalizado. Tu landing page + valorador están listos para generar leads desde el primer día. Sin instalaciones ni conocimientos técnicos.',
      time: '5 minutos',
      result: 'Sistema activo 24/7'
    },
    {
      icon: PresentationChartLineIcon,
      title: 'Convierte visitantes en leads verificados',
      description: 'Cada propietario que valora su vivienda recibe un informe profesional con tu marca y tú obtienes sus datos de contacto verificados. Email y teléfono obligatorios para ver la valoración.',
      time: 'Automático',
      result: 'Leads cualificados'
    },
    {
      icon: FunnelIcon,
      title: 'La IA mantiene calientes tus leads',
      description: 'Emails personalizados con contenido de valor se envían automáticamente. Tus leads reciben información útil del mercado mientras tú te posicionas como el experto de referencia.',
      time: 'Sin esfuerzo',
      result: 'Conversión maximizada'
    },
  ])
  
  // Refs para animaciones GSAP
  const sectionTitleRef = ref<HTMLElement | null>(null)
  const sectionSubtitleRef = ref<HTMLElement | null>(null)
  const stepRefs = ref<Array<HTMLElement | null>>([])
  const connectorLineRef = ref<HTMLElement | null>(null);
  const connectorLineProgressRef = ref<HTMLElement | null>(null);
  
  
  const setStepRef = (el: any, index: number) => {
    if (el) {
      stepRefs.value[index] = el as HTMLElement;
    }
  }
  
  onMounted(async () => {
    await nextTick();
  })
  </script>
  
  <style scoped>
  /* Estilos específicos para HowItWorksSection */
  /* Los estilos para números grandes y tarjetas ya están definidos en Tailwind utility classes */
  /* .shadow-orange-glow was removed as the icon container style changed */
  .text-pretty-wrap {
    text-wrap: pretty;
  }

  /* Asegurar visibilidad del contenido */
  #how-it-works {
    opacity: 1 !important;
    visibility: visible !important;
  }

  #how-it-works * {
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
  }

  /* Aumentar el gap vertical en móviles donde las tarjetas se apilan */
  @media (max-width: 1023px) {
    /* .grid.gap-y-16 is already applied by Tailwind, specific overrides would go here if needed */
    /* For example, to ensure cards don't get too wide on very small screens if scale makes them overflow */
    .group:hover {
      /* Might need to adjust scale effect on mobile if it causes layout issues */
    }
  }
  </style>