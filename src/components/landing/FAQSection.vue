<template
  ><section id="faq" class="py-16 sm:py-24 bg-gray-50 text-impacto-blue">
    <!-- Cambiado bg-gray-50/70 a bg-gray-50 para plena opacidad -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- C<PERSON><PERSON>a de la Sección -->
      <div class="text-center mb-12 md:mb-16">
        <h2
          ref="sectionTitleRef"
          class="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight text-impacto-blue mb-4"
        >
          Todo lo que necesitas saber para empezar
        </h2>
        <p
          ref="sectionSubtitleRef"
          class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto"
        >
          Respuestas claras a las dudas más importantes sobre automatización inmobiliaria con IA.
        </p>
      </div>

      <!-- Lista de FAQs (Acordeón) -->
      <div ref="faqListRef" class="max-w-3xl mx-auto space-y-5">
        <div
          v-for="(faq, index) in faqs"
          :key="index"
          :ref="(el) => setFaqItemRef(el, index)"
          class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200/80"
        >
          <button
            @click="toggleFaq(index)"
            class="flex items-center justify-between w-full p-5 sm:p-6 text-left focus:outline-none focus-visible:ring focus-visible:ring-impacto-orange focus-visible:ring-opacity-75"
            :aria-expanded="openFaqIndex === index"
            :aria-controls="`faq-answer-${index}`"
          >
            <span class="text-md sm:text-lg font-semibold text-impacto-blue">
              {{ faq.question }}
            </span>
            <span class="ml-4">
              <svg
                class="h-6 w-6 text-impacto-orange transform transition-transform duration-300 ease-in-out"
                :class="{ 'rotate-180': openFaqIndex === index }"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </span>
          </button>

          <transition
            name="faq-answer-transition" 
            @enter="onFaqEnter"
            @leave="onFaqLeave"
          >
            <div
              v-show="openFaqIndex === index"
              :id="`faq-answer-${index}`"
              class="overflow-hidden" 
              data-testid="faq-answer-content-wrapper" 
            >
              <div class="px-5 sm:px-6 py-5 sm:py-6 text-gray-700 text-sm sm:text-base leading-relaxed" data-testid="faq-answer-inner-content">
                <p v-html="faq.answer"></p>
              </div>
            </div>
          </transition>
        </div>
      </div>

      <!-- Contacto para más preguntas -->
      <div class="mt-12 text-center">
        <p class="text-gray-600">
          ¿Tienes alguna duda específica sobre tu caso?
          <a
            href="mailto:<EMAIL>?subject=Consulta%20sobre%20InmoAutomation"
            class="font-semibold text-impacto-orange hover:underline"
          >
            Escríbenos
          </a>
          y te respondemos en menos de 24 horas.
        </p>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef, nextTick } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const openFaqIndex = ref<number | null>(null)

const toggleFaq = (index: number) => {
  openFaqIndex.value = openFaqIndex.value === index ? null : index
}

const onFaqEnter = (el: Element, done: () => void) => {
  const htmlEl = el as HTMLElement;
  // El div interior es el que tiene el padding y el contenido real
  const innerContent = htmlEl.querySelector('[data-testid="faq-answer-inner-content"]') as HTMLElement | null;

  if (!innerContent) {
    // Como fallback, si no encontramos el div interno, animamos el propio elemento 'el'
    // pero esto podría no resolver el problema del padding perfectamente.
    gsap.fromTo(htmlEl,
      { height: 0, opacity: 0 },
      {
        height: 'auto',
        opacity: 1,
        duration: 0.3,
        ease: 'power2.out',
        onComplete: () => {
          htmlEl.style.height = 'auto'; // Asegurar altura auto al final
          done();
        }
      }
    );
    return;
  }
  
  // Medir la altura del contenido interno
  innerContent.style.display = 'block'; // Asegurar que sea visible para medición
  const targetHeight = innerContent.offsetHeight; // offsetHeight incluye padding, borde y scrollbar si está presente
  innerContent.style.display = ''; // Restaurar


  gsap.fromTo(htmlEl, // Animamos el wrapper exterior
    { height: 0, opacity: 0 },
    {
      height: targetHeight, // Animar el wrapper a la altura del contenido interno
      opacity: 1,
      duration: 0.3,
      ease: 'power2.out',
      onComplete: () => {
        htmlEl.style.height = 'auto'; // Importante para responsividad y contenido dinámico
        done();
      }
    }
  );
}

const onFaqLeave = (el: Element, done: () => void) => {
  const htmlEl = el as HTMLElement;
  gsap.to(htmlEl, // Animamos el wrapper exterior
    {
      height: 0,
      opacity: 0,
      // No necesitamos tocar el padding del wrapper si el interno lo tiene
      duration: 0.3,
      ease: 'power2.in',
      onComplete: done
    }
  );
}

const faqs = shallowRef([
  {
    question: '¿Realmente funciona la captación automática 24/7?',
    answer: 'Sí, y los resultados hablan por sí solos. Una vez configurado tu valorador IA, funciona las 24 horas generando leads cualificados mientras duermes, estás en reuniones o de vacaciones. Los propietarios pueden valorar sus inmuebles al instante y automáticamente entran en secuencias de nutrición personalizadas con IA. Es como tener un comercial experto trabajando sin descanso, pero que nunca se cansa ni comete errores.',
  },
  {
    question: '¿Los leads generados son de calidad o solo números?',
    answer: 'Los leads son de alta calidad porque llegan pre-cualificados. Solo contactan contigo propietarios que realmente están considerando vender (han valorado su inmueble) y han proporcionado datos detallados: ubicación exacta, características del inmueble, motivación de venta y datos de contacto. Además, las secuencias IA de seguimiento automático nutren estos leads hasta que están listos para una conversación seria contigo.',
  },
  {
    question: '¿Cuánto tiempo necesito invertir diariamente?',
    answer: 'Solo 15-30 minutos al día para revisar los nuevos leads y gestionar las conversaciones que ya están avanzadas. La IA se encarga del trabajo pesado: seguimiento inicial, envío de informes personalizados, secuencias de nutrición y cualificación automática. Tu tiempo se invierte en lo que realmente importa: cerrar ventas, no en perseguir prospectos.',
  },
  {
    question: '¿Qué incluye exactamente la prueba de 7 días?',
    answer: 'Acceso completo a todas las funcionalidades de tu plan elegido: valorador IA personalizado con tu marca, landing page automática, integración en tu web, secuencias de email con IA, exportación de leads y analíticas (según el plan). Es una prueba real, no una demo limitada. Puedes generar leads reales y ver resultados tangibles desde el primer día.',
  },
  {
    question: '¿Qué pasa si ya tengo un CRM o herramientas de marketing?',
    answer: 'InmoAutomation complementa perfectamente tus herramientas actuales. Los leads se exportan fácilmente a cualquier CRM (CSV) y desde el plan Pro tienes integraciones exclusivas. No reemplaza tu stack tecnológico, lo potencia añadiendo una fuente constante de leads cualificados que antes no tenías. Es la pieza que faltaba en tu estrategia de captación.',
  },
  {
    question: '¿Garantizan que voy a generar X cantidad de leads?',
    answer: 'InmoAutomation es una herramienta de conversión y automatización muy potente, pero no generamos tráfico por sí misma. Necesitas dirigir visitantes a tu valorador (mediante tu web, redes sociales, publicidad, etc.). Lo que sí garantizamos es que cada visitante que llegue tendrá una experiencia optimizada para convertir y, si se convierte, será nutrido automáticamente con IA. Por eso incluimos analíticas detalladas desde el plan Pro: para que puedas medir y optimizar tu estrategia de marketing según los datos reales de rendimiento.',
  },
  {
    question: '¿Puedo cancelar si no veo resultados?',
    answer: 'Por supuesto. No hay contratos de permanencia ni penalizaciones. Si durante tu prueba de 7 días o después no ves el valor, puedes cancelar cuando quieras. Estamos tan seguros de los resultados que preferimos que pruebes sin riesgo. La mayoría de nuestros usuarios ven sus primeros leads cualificados en las primeras 48 horas.',
  },
])

const sectionTitleRef = ref<HTMLElement | null>(null)
const sectionSubtitleRef = ref<HTMLElement | null>(null)
const faqListRef = ref<HTMLElement | null>(null)
const faqItemRefs = ref<Array<HTMLElement | null>>([])

const setFaqItemRef = (el: any, index: number) => {
  if (el) {
    faqItemRefs.value[index] = el as HTMLElement;
  }
}

onMounted(async () => {
  await nextTick();

  if (sectionTitleRef.value && sectionSubtitleRef.value) {
    gsap.from([sectionTitleRef.value, sectionSubtitleRef.value], {
      opacity: 0,
      y: 50,
      duration: 0.8,
      ease: 'power3.out',
      stagger: 0.2,
      scrollTrigger: {
        trigger: sectionTitleRef.value,
        start: 'top 85%',
        toggleActions: 'play none none none',
      },
    })
  }

  if (faqListRef.value) {
      faqItemRefs.value.forEach((item, index) => {
        if(item) {
            gsap.from(item, {
                opacity: 0,
                x: -50, 
                duration: 0.5,
                ease: 'power3.out',
                scrollTrigger: {
                    trigger: item,
                    start: 'top 90%',
                    toggleActions: 'play none none none',
                },
                delay: index * 0.1,
            });
        }
    });
  }
})
</script>

<style scoped>
/* Estilos específicos si son necesarios */
/* Las clases de animación CSS (.faq-answer-transition-enter-active, etc.)
   no son necesarias si GSAP maneja completamente la animación
   a través de los hooks @enter y @leave. */
</style>