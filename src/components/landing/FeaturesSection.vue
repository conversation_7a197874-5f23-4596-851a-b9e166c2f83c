<template>
  <section id="features" class="py-16 sm:py-24 bg-white text-impacto-blue">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Cabecera de la Sección -->
      <div class="text-center mb-12 md:mb-16">
        <h2
          ref="sectionTitleRef"
          class="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight mb-4"
        >
          La herramienta que <span class="text-impacto-orange">multiplica tus captaciones</span> automáticamente
        </h2>
        <p ref="sectionSubtitleRef" class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
          Sistema completo de captación inmobiliaria que combina valoraciones IA, automatización inteligente y seguimiento personalizado
        </p>
      </div>

      <!-- <PERSON>loques de Valor en Layout Alternating -->
      <div class="space-y-20 md:space-y-32">
        <div
          v-for="(block, index) in valueBlocks"
          :key="index"
          :ref="el => setFeatureCardRef(el, index)"
          class="flex flex-col md:flex-row items-stretch gap-8 lg:gap-12"
        >
          <!-- Bloque Visual Placeholder -->
          <div class="md:w-6/12 flex justify-center sticky top-24 self-start"
               :class="index % 2 === 0 ? 'md:order-1' : 'md:order-2'"
          >
            <img :src="block.imageSrc" :alt="block.title" class="w-full h-auto" />
          </div>
          <!-- Bloque de Contenido -->
          <div class="md:w-6/12 text-left"
               :class="index % 2 === 0 ? 'md:order-2' : 'md:order-1'"
          >
            <h3 class="text-2xl lg:text-3xl font-bold text-impacto-blue mb-3 capitalize-first">
              {{ block.title }}
            </h3>
            <p class="text-lg lg:text-xl text-impacto-orange font-semibold mb-5 capitalize-first">
              {{ block.subtitle }}
            </p>
            <div class="prose prose-lg text-gray-700 max-w-none space-y-4 mb-6" v-html="block.storytelling">
            </div>
            
            <div v-if="block.statistics && block.statistics.length > 0" class="statistics-container">
              <h4 class="capitalize-first statistics-title">{{ block.statisticsTitle }}</h4>
              <ul class="statistics-list">
                <li v-for="(stat, statIndex) in block.statistics" :key="`stat-${statIndex}`" class="statistic-item">
                  <svg class="h-5 w-5 mr-2.5 shrink-0 mt-0.5 text-impacto-orange" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                  <span v-html="stat"></span>
                </li>
              </ul>
            </div>

            <div v-if="block.miniBenefits && block.miniBenefits.length">
              <h4 class="text-md font-semibold text-impacto-blue mb-3 capitalize-first">{{ block.miniBenefitsTitle }}</h4>
              <ul class="space-y-3">
                <li v-for="(benefit, benefitIndex) in block.miniBenefits" :key="`benefit-${benefitIndex}`" class="flex items-start p-4 border border-slate-200 rounded-lg hover:shadow-md transition-shadow duration-200 ease-in-out">
                  <component :is="benefit.iconComponent" class="h-6 w-6 text-impacto-orange mr-3 shrink-0" aria-hidden="true" />
                  <span class="mini-benefit-text text-sm text-gray-600" v-html="benefit.text"></span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef, nextTick } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger' // Asegurar que se registró en main.ts

// Iconos de Heroicons (usamos shallowRef para componentes dinámicos)
import {
  ArrowPathRoundedSquareIcon,
  FunnelIcon,
  SwatchIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  RocketLaunchIcon,
  ComputerDesktopIcon,
  LinkIcon,
  DocumentTextIcon
} from '@heroicons/vue/24/outline';

gsap.registerPlugin(ScrollTrigger);

const valueBlocks = shallowRef([
  // BLOQUE 1: Landing Page + Valorador que Convierte Constantemente
  {
    title: 'Landing page profesional + valorador que convierte sin descanso',
    subtitle: 'No solo un valorador: una landing completa optimizada para conversión',
    statisticsTitle: 'Datos del mercado inmobiliario:',
    miniBenefitsTitle: 'Tu sistema completo incluye:',
    storytelling: `
      <p>Obtienes una landing page completa con estructura probada para maximizar conversiones + valorador integrado con tu marca. Todo funciona constantemente capturando leads mientras duermes.</p>
      <p><strong>Resultado:</strong> <span class="text-impacto-orange">3x más leads</span> que con formularios tradicionales.</p>
    `,
    statistics: [
      "<strong>85% de propietarios</strong> buscan valoraciones online antes de vender",
      "Las herramientas digitales generan <strong>3.2x más leads</strong> que métodos tradicionales",
      "Tiempo promedio en página: <strong>4.2 minutos</strong> vs 1.1 min en formularios básicos"
    ],
    miniBenefits: [
      { text: '<strong>Landing page optimizada:</strong> Estructura probada para maximizar conversiones', iconComponent: ArrowPathRoundedSquareIcon },
      { text: '<strong>Valorador integrado:</strong> Email y teléfono obligatorios para ver resultados', iconComponent: FunnelIcon },
      { text: '<strong>Branding completo:</strong> Tu marca, colores y dominio personalizado', iconComponent: SwatchIcon },
    ],
    imageSrc: '/assets/mockup_landing_valoracion.png'
  },
  // BLOQUE 2: Automatización Inteligente con IA
  {
    title: 'Seguimiento automatizado que convierte leads en clientes',
    subtitle: 'El 67% de propietarios prefiere respuestas inmediatas',
    statisticsTitle: 'Resultados comprobados:',
    miniBenefitsTitle: 'Automatización que funciona:',
    storytelling: `
      <p>Después de la valoración, nuestro sistema IA envía automáticamente emails personalizados con informes profesionales y contenido de valor específico para cada propietario.</p>
      <p><strong>El resultado:</strong> Tus leads se mantienen calientes y tú te posicionas como el experto de referencia sin esfuerzo manual.</p>
    `,
    statistics: [
      "Tasa de respuesta promedio: <strong>67% superior</strong> con seguimiento inmediato",
      "Seguimiento automático que <strong>nunca olvida</strong> un prospecto",
      "Integración perfecta con tu <strong>flujo de trabajo actual</strong>"
    ],
    miniBenefits: [
      { text: '<strong>Informes automáticos:</strong> PDF profesional con tu marca enviado al instante', iconComponent: DocumentTextIcon },
      { text: '<strong>Emails inteligentes:</strong> Contenido personalizado por IA para cada lead', iconComponent: ChatBubbleLeftRightIcon },
      { text: '<strong>Seguimiento perfecto:</strong> Nunca más se te escapa un lead potencial', iconComponent: ClockIcon },
    ],
    imageSrc: '/assets/mockup_informe_valoracion.png'
  },
  // BLOQUE 3: Implementación Inmediata
  {
    title: 'Listo en 5 minutos, captando leads toda la vida',
    subtitle: 'La implementación más rápida del mercado inmobiliario',
    statisticsTitle: 'Facilidad comprobada:',
    miniBenefitsTitle: 'Simplicidad extrema:',
    storytelling: `
      <p>Sin instalaciones complejas ni conocimientos técnicos. Configuras tu marca, copias un enlace y ya estás captando leads profesionalmente.</p>
      <p><strong>Resultado:</strong> <span class="text-impacto-orange">De idea a captación activa</span> en menos tiempo del que tardas en tomar un café.</p>
    `,
    statistics: [
      "Implementación promedio: <strong>menos de 5 minutos</strong>",
      "Mercado inmobiliario español: <strong>€890M anuales</strong> en oportunidades",
      "Compatible con <strong>cualquier web</strong> o plataforma existente"
    ],
    miniBenefits: [
      { text: '<strong>Setup instantáneo:</strong> Logo, colores y listo para compartir', iconComponent: RocketLaunchIcon },
      { text: '<strong>Panel intuitivo:</strong> Gestiona todo desde un dashboard simple', iconComponent: ComputerDesktopIcon },
      { text: '<strong>Integración universal:</strong> Web, redes sociales, WhatsApp, email', iconComponent: LinkIcon },
    ],
    imageSrc: '/assets/mockup_configuracion_valorador.png'
  }
]);

// Refs para animaciones GSAP
const sectionTitleRef = ref<HTMLElement | null>(null)
const sectionSubtitleRef = ref<HTMLElement | null>(null)
const valueBlockRefs = ref<Array<HTMLElement | null>>([])

const setFeatureCardRef = (el: any, index: number) => {
  if (el) {
    valueBlockRefs.value[index] = el as HTMLElement;
  }
}

onMounted(async () => {
  await nextTick();
  if (sectionTitleRef.value && sectionSubtitleRef.value) {
    gsap.from([sectionTitleRef.value, sectionSubtitleRef.value], {
      opacity: 0,
      y: 30,
      duration: 0.8,
      ease: 'power3.out',
      stagger: 0.2,
      delay: 0.2
    })
  }

  // Animación simple para los bloques sin ScrollTrigger
  setTimeout(() => {
    valueBlockRefs.value.forEach((item, index) => {
      if (item) {
        gsap.from(item, {
          opacity: 0,
          y: 30,
          duration: 0.6,
          ease: 'power3.out',
          delay: index * 0.2
        });
      }
    });
  }, 600);
});
</script>

<style scoped>
/* Estilos adicionales específicos para FeaturesSection, si son necesarios */
.aspect-square {
  aspect-ratio: 1 / 1;
}
/* En pantallas md y mayores, podrías querer que el alto del visual se ajuste más al contenido del texto */
@media (min-width: 768px) {
  .md\:aspect-auto {
    aspect-ratio: auto;
  }
}

.prose :where(p):not(:where([class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose :where(ul):not(:where([class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.capitalize-first::first-letter {
  text-transform: uppercase;
}

/* Statistics Box Styling - Inspired by Featured Pricing Plan */
.statistics-container {
  background-color: theme('colors.white');
  border: 2px solid theme('colors.impacto-orange.DEFAULT');
  border-radius: theme('borderRadius.xl'); /* 12px */
  padding: 1.5rem; /* p-6 */
  box-shadow: theme('boxShadow.lg');
  margin-top: 1.5rem; /* mt-6 */
  margin-bottom: 2rem; /* mb-8 */
}

.statistics-title {
  color: theme('colors.impacto-blue.DEFAULT');
  font-size: theme('fontSize.lg'); /* 1.125rem */
  font-weight: 600; /* semibold */
  margin-bottom: 1rem; /* mb-4 */
}

.statistics-list {
  list-style: none;
  padding-left: 0;
}

.statistics-list > li:not(:last-child) {
  margin-bottom: 0.5rem;
}

.statistic-item {
  display: flex;
  align-items: flex-start; /* Align icon with the start of the text line */
  font-size: theme('fontSize.sm'); /* 0.875rem */
  color: theme('colors.slate.700');
  line-height: 1.6;
}

/* Styling for the SVG icon is handled by Tailwind classes on the SVG element itself */
/* .statistic-item::before is no longer needed */

.statistic-item span strong {
  color: theme('colors.impacto-blue.DEFAULT'); /* Or slate.800 if blue is too much */
  font-weight: 700;
}

/* Mini Benefits Text Styling */
.mini-benefit-text strong {
  color: theme('colors.impacto-blue.DEFAULT');
  font-weight: 600; /* semibold */
}

/* Initial states for GSAP */
.value-block {
  opacity: 0;
  transform: translateY(30px);
}

.section-title,
.section-subtitle {
  opacity: 0;
  transform: translateY(20px);
}
</style>