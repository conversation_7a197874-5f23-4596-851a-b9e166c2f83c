<template>
  <section id="comparison-table" class="py-20 sm:py-28 bg-gray-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Cabecera de la Sección -->
      <div class="text-center mb-16 md:mb-20">
        <h2 class="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-impacto-blue tracking-tight mb-5">
          InmoAutomation vs Métodos tradicionales
        </h2>
        <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
          Descubre por qué automatizar tu captación con IA es la evolución natural frente a los métodos manuales que consumen tu tiempo y limitan tu crecimiento.
        </p>
      </div>

      <!-- Tabla Comparativa Mejorada -->
      <div class="bg-white shadow-xl rounded-xl border border-gray-200 overflow-hidden">
        <table class="min-w-full">
          <thead class="md:table-header-group hidden">
            <tr>
              <th scope="col" class="w-1/3 px-6 py-4 text-left text-sm font-semibold text-impacto-blue-700 uppercase tracking-wider bg-gray-200">
                Característica
              </th>
              <th scope="col" class="w-1/3 px-6 py-4 text-center text-sm font-semibold text-white uppercase tracking-wider bg-impacto-blue">
                InmoAutomation
              </th>
              <th scope="col" class="w-1/3 px-6 py-4 text-center text-sm font-semibold text-white uppercase tracking-wider bg-gray-600">
                Métodos Tradicionales
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="(item, index) in comparisonFeatures" :key="index" class="block md:table-row hover:bg-gray-50">
              
              <!-- Celda Característica (Visible en desktop, título en móvil) -->
              <td class="px-6 py-4 md:py-5 font-semibold md:font-medium text-impacto-blue-700 md:text-gray-800 text-base md:text-sm align-top md:text-left md:w-1/3 block md:table-cell border-b md:border-none">
                {{ item.feature }}
              </td>
              
              <!-- Celda Valorador IA Impacto -->
              <td class="px-6 py-3 md:py-5 text-sm text-gray-700 align-top md:text-left md:w-1/3 block md:table-cell md:bg-impacto-blue-50 relative">
                <span class="font-semibold text-impacto-blue-600 md:hidden absolute top-3 left-6">InmoAutomation:</span>
                <div class="flex items-center justify-start md:justify-start space-x-2 mt-8 md:mt-0">
                  <span v-if="item.impacto.icon === 'check'" class="flex-shrink-0 text-green-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                  </span>
                  <span v-else-if="item.impacto.icon === 'cross'" class="flex-shrink-0 text-red-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /></svg>
                  </span>
                  <span class="leading-snug">{{ item.impacto.text }}</span>
                </div>
              </td>
              
              <!-- Celda Otros Valoradores -->
              <td class="px-6 py-3 md:py-5 text-sm text-gray-700 align-top md:text-left md:w-1/3 block md:table-cell relative border-t md:border-t-0">
                <span class="font-semibold text-gray-600 md:hidden absolute top-3 left-6">Métodos Tradicionales:</span>
                <div class="flex items-center justify-start md:justify-start space-x-2 mt-8 md:mt-0">
                  <span v-if="item.others.icon === 'check'" class="flex-shrink-0 text-green-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                  </span>
                  <span v-else-if="item.others.icon === 'cross'" class="flex-shrink-0 text-red-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /></svg>
                  </span>
                  <span v-else-if="item.others.icon === 'warning'" class="flex-shrink-0 text-yellow-500">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5">
                      <path fill-rule="evenodd" d="M8.485 2.495c.646-1.118 2.379-1.118 3.025 0l6.09 10.52c.645 1.118-.206 2.505-1.512 2.505H3.908c-1.306 0-2.157-1.387-1.512-2.505L8.485 2.495zM10 6a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 6zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                    </svg>
                  </span>
                  <span class="leading-snug">{{ item.others.text }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
       <p class="mt-8 text-center text-sm text-gray-500">
        * "Métodos Tradicionales" incluye captación manual, publicidad convencional, llamadas en frío y seguimiento no automatizado.
      </p>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue';

const comparisonFeatures = ref([
  {
    feature: 'Captación de leads 24/7',
    impacto: { text: 'Sistema automatizado que genera leads cualificados las 24 horas, incluso mientras duermes. Valoraciones IA + secuencias de nutrición automáticas que convierten prospectos en clientes sin tu intervención.', icon: 'check' },
    others: { text: 'Captación limitada a tu horario laboral. Dependes de llamadas en frío, visitas puerta a puerta o publicidad costosa que requiere gestión manual constante.', icon: 'cross' }
  },
  {
    feature: 'Costo por lead cualificado',
    impacto: { text: 'Entre €2-5 por lead altamente cualificado con datos completos y interés real en vender/comprar. ROI comprobado con conversión optimizada por IA.', icon: 'check' },
    others: { text: 'Entre €15-50 por lead (publicidad + tiempo invertido). Muchos leads de baja calidad que no convierten, desperdiciando presupuesto y esfuerzo.', icon: 'cross' }
  },
  {
    feature: 'Tiempo de seguimiento diario',
    impacto: { text: 'Solo 15-30 minutos para revisar leads nuevos y gestionar conversaciones avanzadas. La IA se encarga del seguimiento inicial y nutrición automática.', icon: 'check' },
    others: { text: '2-4 horas diarias en llamadas de seguimiento, emails manuales y gestión de prospectos. Tiempo que podrías invertir en cerrar ventas o crecer tu negocio.', icon: 'cross' }
  },
  {
    feature: 'Escalabilidad del negocio',
    impacto: { text: 'Crece ilimitadamente sin contratar más personal. El sistema maneja 10 o 1000 leads con la misma eficiencia, permitiendo expansión sin incrementar costos operativos.', icon: 'check' },
    others: { text: 'Crecimiento limitado por tu capacidad personal o necesidad de contratar más agentes. Cada nuevo lead requiere más tiempo y recursos humanos.', icon: 'cross' }
  },
  {
    feature: 'Consistencia en el mensaje',
    impacto: { text: 'Comunicación perfecta y personalizada en cada interacción. La IA adapta el mensaje según el perfil del lead pero mantiene tu propuesta de valor siempre optimizada.', icon: 'check' },
    others: { text: 'Calidad variable según tu estado de ánimo, cansancio o experiencia. Mensajes inconsistentes que pueden alejar prospectos o transmitir falta de profesionalismo.', icon: 'warning' }
  },
  {
    feature: 'Conversión de prospectos',
    impacto: { text: 'Tasas de conversión 3-5x superiores gracias a seguimiento inmediato, personalización IA y secuencias optimizadas basadas en comportamiento del usuario.', icon: 'check' },
    others: { text: 'Conversión baja por seguimiento tardío, mensajes genéricos y falta de personalización. Muchos leads se pierden por no contactar en el momento óptimo.', icon: 'cross' }
  },
  {
    feature: 'Disponibilidad para el cliente',
    impacto: { text: 'Respuesta inmediata 24/7/365. Los clientes reciben su valoración al instante y comienzan el proceso de nutrición automáticamente, sin esperas.', icon: 'check' },
    others: { text: 'Limitado a horario comercial. Clientes que buscan valoración fuera de horario se van a la competencia. Pérdida de oportunidades por no estar disponible.', icon: 'cross' }
  },
  {
    feature: 'Análisis y optimización',
    impacto: { text: 'Métricas detalladas de rendimiento: tráfico, conversiones, CTR, comportamiento del usuario. Optimización continua basada en datos reales para mejorar resultados.', icon: 'check' },
    others: { text: 'Sin datos precisos sobre efectividad. Decisiones basadas en intuición o estimaciones. Imposible optimizar lo que no se puede medir con precisión.', icon: 'cross' }
  }
]);

// Animaciones con GSAP (opcional, se puede añadir después)
// import { onMounted } from 'vue';
// import gsap from 'gsap';
// import { ScrollTrigger } from 'gsap/ScrollTrigger';
// gsap.registerPlugin(ScrollTrigger);

// const sectionTitleRef = ref(null);
// const sectionSubtitleRef = ref(null);

// onMounted(() => {
//   gsap.from(sectionTitleRef.value, {
//     scrollTrigger: { trigger: sectionTitleRef.value, start: "top 80%" },
//     opacity: 0, y: 50, duration: 0.8, ease: "power3.out"
//   });
//   gsap.from(sectionSubtitleRef.value, {
//     scrollTrigger: { trigger: sectionSubtitleRef.value, start: "top 80%" },
//     opacity: 0, y: 50, duration: 0.8, delay: 0.2, ease: "power3.out"
//   });
  // TODO: Animar la tabla si se desea
// });

</script>

<style scoped>
.text-impacto-blue-700 {
  color: #00305C; /* Ajusta si tienes un color específico para texto oscuro de impacto blue */
}
.bg-impacto-blue-50 {
  background-color: #E6EEF3; /* Un azul muy claro para el encabezado de la tabla */
}
/* Estilos adicionales si son necesarios */
.text-impacto-blue-700 {
  color: #00305C; /* Ajusta si tienes un color específico para texto oscuro de impacto blue */
}
.bg-impacto-blue-50 {
  background-color: #E6EEF3; /* Un azul muy claro para el encabezado de la tabla */
}
</style>
