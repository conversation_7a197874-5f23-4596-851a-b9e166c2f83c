<template>
  <section id="problem-agitation" class="py-16 sm:py-24 bg-gradient-to-br from-orange-50 to-yellow-50 text-impacto-blue">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- <PERSON><PERSON><PERSON><PERSON> de la Sección -->
      <div class="text-center mb-12 md:mb-16">
        <div ref="badgeRef" class="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-700 rounded-full text-sm font-semibold mb-6">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
          </svg>
          Oportunidades perdidas
        </div>

        <h2
          ref="sectionTitleRef"
          class="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight text-impacto-blue mb-6"
        >
          ¿Cuántas oportunidades de <span class="text-impacto-orange">captación</span> estás perdiendo?
        </h2>
        <p ref="sectionSubtitleRef" class="text-lg sm:text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
          El sector inmobiliario está lleno de ineficiencias que te impiden alcanzar tu verdadero potencial. Es hora de identificarlas y superarlas.
        </p>
      </div>

      <!-- Grid de Problemas con nuevo diseño -->
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div v-for="(problem, index) in problems" :key="index"
             :ref="el => setProblemRef(el, index)"
             class="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-orange-100 hover:border-orange-200 group">
          <!-- Indicador visual superior -->
          <div class="absolute top-0 left-6 w-12 h-1 bg-gradient-to-r from-orange-400 to-yellow-400 rounded-b-full"></div>

          <!-- Icono flotante -->
          <div class="relative -mt-4 mb-6">
            <div class="w-16 h-16 bg-gradient-to-br from-orange-100 to-yellow-100 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
              <component :is="problem.icon" class="w-8 h-8 text-orange-600" />
            </div>
          </div>

          <div>
            <h3 class="text-xl font-bold text-impacto-blue mb-3 leading-tight">{{ problem.title }}</h3>
            <p class="text-gray-600 leading-relaxed text-sm">{{ problem.description }}</p>
          </div>

          <!-- Elemento decorativo inferior -->
          <div class="absolute bottom-4 right-4 w-8 h-8 bg-orange-50 rounded-full opacity-50 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
      </div>

      <!-- Estadísticas Impactantes -->
      <div ref="statsContainerRef" class="bg-white rounded-2xl shadow-xl p-8 md:p-12 border border-gray-200">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-impacto-blue mb-3">El potencial del mercado inmobiliario</h3>
          <p class="text-gray-600">Oportunidades que puedes aprovechar con las herramientas adecuadas</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div v-for="(stat, index) in impactStats" :key="index"
               :ref="el => setStatRef(el, index)"
               class="text-center p-4">
            <div class="text-3xl font-bold text-impacto-orange mb-2">{{ stat.value }}</div>
            <div class="text-sm text-gray-600 font-medium">{{ stat.label }}</div>
            <div class="text-xs text-gray-500 mt-1">{{ stat.context }}</div>
          </div>
        </div>
      </div>

      <!-- Llamada a la Oportunidad -->
      <div ref="agitationRef" class="mt-16 text-center bg-impacto-blue rounded-2xl p-8 md:p-12 text-white">
        <h3 class="text-2xl md:text-3xl font-bold mb-4">
          ¿Estás listo para transformar estas ineficiencias en <span class="text-impacto-orange">oportunidades</span>?
        </h3>
        <p class="text-lg text-blue-100 mb-6 max-w-3xl mx-auto">
          Cada problema que identificas es una oportunidad de mejora. Los propietarios buscan profesionales que les ofrezcan
          valor inmediato y experiencias modernas. Es tu momento de destacar.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <RouterLink
            :to="{ name: 'ContratacionPage' }"
            class="inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-white bg-impacto-orange rounded-lg shadow-xl hover:bg-orange-600 transition-all duration-300 transform hover:scale-105"
          >
            <span>Aprovechar estas oportunidades</span>
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </RouterLink>
          <a
            href="#valorador-demo"
            v-smooth-scroll
            class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg hover:bg-white hover:text-impacto-blue transition-all duration-300"
          >
            Ver cómo funciona
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { RouterLink } from 'vue-router'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import {
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowTrendingDownIcon,
  UserGroupIcon,
  CurrencyEuroIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'

gsap.registerPlugin(ScrollTrigger)

// Datos de problemas (enfoque en oportunidades)
const problems = ref([
  {
    icon: ClockIcon,
    title: "Tiempo valioso mal invertido",
    description: "Horas dedicadas a métodos de captación tradicionales que podrían optimizarse para generar mejores resultados."
  },
  {
    icon: ExclamationTriangleIcon,
    title: "Oportunidades de leads desaprovechadas",
    description: "Visitantes de tu web que se van sin dejar datos porque no encuentran una razón convincente para hacerlo."
  },
  {
    icon: ArrowTrendingDownIcon,
    title: "Potencial de crecimiento sin explotar",
    description: "El mercado inmobiliario ofrece oportunidades que requieren herramientas modernas para ser aprovechadas al máximo."
  },
  {
    icon: UserGroupIcon,
    title: "Propietarios que buscan profesionalismo",
    description: "Los vendedores valoran cada vez más la tecnología y el servicio inmediato en su experiencia inmobiliaria."
  },
  {
    icon: CurrencyEuroIcon,
    title: "ROI incierto en marketing tradicional",
    description: "Dificultad para medir el retorno real de inversiones en publicidad y herramientas convencionales."
  },
  {
    icon: ChartBarIcon,
    title: "Seguimiento manual ineficiente",
    description: "Pérdida de oportunidades por falta de un sistema automatizado que mantenga el contacto con leads potenciales."
  }
])

// Estadísticas de oportunidad
const impactStats = ref([
  { value: "85%", label: "de propietarios", context: "buscan valoraciones online" },
  { value: "3.2x", label: "más leads", context: "con herramientas digitales" },
  { value: "67%", label: "prefiere", context: "respuestas inmediatas" },
  { value: "€890M", label: "mercado", context: "inmobiliario español anual" }
])

// Refs para animaciones GSAP
const badgeRef = ref<HTMLElement | null>(null)
const sectionTitleRef = ref<HTMLElement | null>(null)
const sectionSubtitleRef = ref<HTMLElement | null>(null)
const problemItemsRef = ref<HTMLElement[]>([])
const statsContainerRef = ref<HTMLElement | null>(null)
const statItemsRef = ref<HTMLElement[]>([])
const agitationRef = ref<HTMLElement | null>(null)

// Funciones para capturar referencias de arrays
const setProblemRef = (el: any, index: number) => {
  if (el) {
    problemItemsRef.value[index] = el as HTMLElement
  }
}

const setStatRef = (el: any, index: number) => {
  if (el) {
    statItemsRef.value[index] = el as HTMLElement
  }
}

onMounted(async () => {
  await nextTick()
})
</script>

<style scoped>
/* Gradiente personalizado para el fondo */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* Mejoras de hover para los elementos interactivos */
.hover\:shadow-xl:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Asegurar visibilidad del contenido */
#problem-agitation {
  opacity: 1 !important;
  visibility: visible !important;
}

#problem-agitation * {
  opacity: 1 !important;
  visibility: visible !important;
  transform: none !important;
}
</style>
