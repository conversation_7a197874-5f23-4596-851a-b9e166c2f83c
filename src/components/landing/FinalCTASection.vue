<template>
  <section id="final-cta" class="bg-impacto-blue text-white overflow-hidden">
    <div
      class="container mx-auto px-4 sm:px-6 lg:px-8 py-20 sm:py-28 text-center"
    >
      <!-- Contenido del CTA -->
      <div ref="ctaContentRef" class="max-w-3xl mx-auto">
        <h2
          class="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight mb-6"
        >
          Deja de perseguir leads y que <span class="text-impacto-orange">ellos te encuentren</span>
        </h2>
        <p class="text-lg sm:text-xl text-gray-200 mb-10 max-w-2xl mx-auto">
          Únete a los agentes que ya trabajan menos y venden más. Mientras la competencia sigue con métodos del siglo pasado, tú dominas el futuro de la captación inmobiliaria.
        </p>

        <!-- Mini-Beneficios -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12">
          <div class="flex flex-col items-center p-6 rounded-lg bg-white/5 ">
            <MagnifyingGlassIcon class="h-10 w-10 text-impacto-orange mb-3" />
            <h3 class="font-semibold text-lg mb-1">Leads que te buscan</h3>
            <p class="text-sm text-gray-300">Propietarios motivados llegan a ti, no al revés.</p>
          </div>
          <div class="flex flex-col items-center p-6 rounded-lg bg-white/5 ">
            <StarIcon class="h-10 w-10 text-impacto-orange mb-3" />
            <h3 class="font-semibold text-lg mb-1">Autoridad instantánea</h3>
            <p class="text-sm text-gray-300">Posiciónate como el experto local.</p>
          </div>
          <div class="flex flex-col items-center p-6 rounded-lg bg-white/5 ">
            <TrophyIcon class="h-10 w-10 text-impacto-orange mb-3" />
            <h3 class="font-semibold text-lg mb-1">Ventaja competitiva</h3>
            <p class="text-sm text-gray-300">Mientras otros llaman en frío, tú ya tienes la cita.</p>
          </div>
        </div>

        <RouterLink
          :to="{ name: 'ContratacionPage' }"
          class="inline-flex items-center justify-center px-10 py-5 text-xl sm:text-2xl font-bold text-white bg-impacto-orange rounded-lg shadow-xl hover:bg-orange-600 focus:outline-none focus:ring-4 focus:ring-orange-300 focus:ring-opacity-50 transition-all duration-300 ease-in-out transform hover:scale-105 ring-offset-2 ring-offset-impacto-blue"
        >
          <span>Comenzar ahora</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-7 h-7 ml-3">
            <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm4.28 10.28a.75.75 0 0 0 0-1.06l-3-3a.75.75 0 1 0-1.06 1.06l1.72 1.72H8.25a.75.75 0 0 0 0 1.5h5.69l-1.72 1.72a.75.75 0 1 0 1.06 1.06l3-3Z" clip-rule="evenodd" />
          </svg>
        </RouterLink>
        <p class="mt-8 text-sm text-gray-300">
          Tu primer valorador listo en 5 minutos.
        </p>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { RouterLink } from 'vue-router'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { MagnifyingGlassIcon, StarIcon, TrophyIcon } from '@heroicons/vue/24/outline'

gsap.registerPlugin(ScrollTrigger)

const ctaContentRef = ref<HTMLElement | null>(null)

onMounted(async () => {
  await nextTick();

  if (ctaContentRef.value) {
    const elementsToAnimate = Array.from(ctaContentRef.value.children);
    const miniBenefitsContainer = ctaContentRef.value.querySelector('.grid');
    if (miniBenefitsContainer) {
      // Add individual benefit cards to the animation stagger for a nicer effect
      elementsToAnimate.splice(elementsToAnimate.indexOf(miniBenefitsContainer), 1, ...Array.from(miniBenefitsContainer.children));
    }

    if (elementsToAnimate.length === 0) return;

    gsap.set(elementsToAnimate, {
      autoAlpha: 0,
      y: 25, 
      scale: 0.97 
    });

    gsap.to(elementsToAnimate, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      duration: 0.4,
      ease: 'power1.out',
      stagger: 0.08,
      scrollTrigger: {
        trigger: ctaContentRef.value,
        start: 'top 80%',
        end: 'bottom 60%',
        toggleActions: 'play none none none',
        // markers: true,
      },
    });
  }
})
</script>

<style scoped>
/* Mini-beneficios con un fondo sutil para destacar ligeramente */
.bg-white\/5 {
  background-color: rgba(255, 255, 255, 0.05);
}
</style>