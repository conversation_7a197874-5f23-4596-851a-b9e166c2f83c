<template>
  <section id="pricing" class="py-16 sm:py-24 bg-impacto-blue text-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Cabecera de la Sección -->
      <div class="text-center mb-12 md:mb-16">
        <h2
          ref="sectionTitleRef"
          class="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight mb-4"
        >
          Elige el plan perfecto para tu crecimiento
        </h2>
        <p ref="sectionSubtitleRef" class="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto">
          Automatiza tu captación de leads con IA desde el primer día. Todos los planes incluyen 7 días de prueba gratuita.
        </p>
      </div>

      <!-- Selector Mensual/Anual Moderno -->
      <div ref="billingCycleSwitchRef" class="flex justify-center items-center mb-10 md:mb-12 space-x-4">
        <!-- El switch -->
        <div
          @click="selectedBillingCycle = selectedBillingCycle === 'monthly' ? 'annual' : 'monthly'"
          class="relative flex items-center w-44 sm:w-48 h-10 bg-impacto-blue/25 rounded-full cursor-pointer p-1 shadow-inner"
        >
          <!-- El "thumb" deslizante blanco -->
          <div
            class="absolute top-1/2 left-1 transform -translate-y-1/2 w-[calc(50%-0.25rem)] h-[calc(100%-0.25rem)] bg-white rounded-full shadow-md transition-transform duration-300 ease-in-out"
            :class="{ 'translate-x-[calc(100%+0.25rem)]': selectedBillingCycle === 'annual' }"
          ></div>
          <!-- Texto Mensual -->
          <span
            class="flex-1 text-center text-xs sm:text-sm font-semibold transition-colors duration-300 ease-in-out z-10 select-none"
            :class="selectedBillingCycle === 'monthly' ? 'text-impacto-blue' : 'text-gray-300 hover:text-gray-100'"
          >
            Mensual
          </span>
          <!-- Texto Anual -->
          <span
            class="flex-1 text-center text-xs sm:text-sm font-semibold transition-colors duration-300 ease-in-out z-10 select-none"
            :class="selectedBillingCycle === 'annual' ? 'text-impacto-blue' : 'text-gray-300 hover:text-gray-100'"
          >
            Anual
          </span>
        </div>
        <!-- Etiqueta de Descuento -->
        <div
          :class="[
            'text-xs font-bold px-2.5 py-1 rounded-md transition-all duration-300 ease-in-out',
            selectedBillingCycle === 'annual' ? 'bg-impacto-orange text-white scale-100 opacity-100' : 'bg-gray-500/50 text-gray-200 scale-95 opacity-70'
          ]"
        >
          ¡AHORRO ANUAL!
        </div>
      </div>

      <!-- Grid de Planes de Precios -->
      <div class="grid lg:grid-cols-3 gap-8 md:gap-10 items-stretch max-w-md mx-auto lg:max-w-7xl">
        <div
          v-for="(plan, index) in pricingPlans"
          :key="plan.id"
          :ref="el => setPlanCardRef(el, index)"
          class="flex flex-col rounded-xl shadow-2xl transform transition-all duration-300 hover:shadow-3xl w-full"
          :class="[
            plan.featured ? 'border-2 md:border-4 border-impacto-orange bg-white text-gray-800 relative' : 'bg-white/10 backdrop-blur-md border border-white/20',
          ]"
        >
          <div
            v-if="plan.badgeText && plan.featured"
            class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 px-3 py-1 bg-impacto-orange text-white text-xs sm:text-sm font-semibold uppercase tracking-wider rounded-full shadow-md z-10"
          >
            {{ plan.badgeText }}
          </div>

          <div class="p-6 sm:p-8 flex-grow flex flex-col">
            <h3
              class="text-2xl sm:text-3xl font-bold mb-1 text-center"
              :class="plan.featured ? 'text-impacto-blue' : 'text-white'"
            >
              {{ plan.name }}
            </h3>
            <p v-if="plan.description" 
               class="text-xs sm:text-sm text-center mb-3 min-h-[3em]"
               :class="plan.featured ? 'text-gray-600' : 'text-gray-300'">
              {{ plan.description }}
            </p>
            
            <div class="my-4 text-center">
              <span class="text-4xl sm:text-5xl font-extrabold tracking-tight" :class="plan.featured ? 'text-impacto-blue' : 'text-white'">
                {{ getPlanPrice(plan) }}
              </span>
              <span class="text-base font-medium ml-1" :class="plan.featured ? 'text-gray-600' : 'text-gray-400'">
                {{ periodicityText }}
              </span>
              <p v-if="selectedBillingCycle === 'annual'" 
                 class="text-xs sm:text-sm mt-1 font-medium" 
                 :class="plan.featured ? 'text-gray-500' : 'text-gray-300'">
                {{ plan.price_annual }}{{ plan.currencySymbol || '€' }} pagado anualmente
              </p>
              <p v-if="selectedBillingCycle === 'annual' && plan.savingsText && !plan.featured" class="text-sm mt-1 font-medium text-impacto-orange-light">
                {{ plan.savingsText }}
              </p>
               <p v-if="selectedBillingCycle === 'annual' && plan.savingsText && plan.featured" class="text-sm mt-1 font-medium text-impacto-orange">
                {{ plan.savingsText }}
              </p>
            </div>
            
            <ul class="space-y-3 text-sm sm:text-base mb-8 flex-grow pt-4" :class="plan.featured ? 'text-gray-700' : 'text-gray-200'">
              <li
                v-for="(feature, fIndex) in plan.features"
                :key="fIndex"
                class="flex items-start"
              >
                <svg class="h-5 w-5 mr-2.5 shrink-0 mt-0.5 text-impacto-orange" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span v-html="feature.text"></span>
              </li>
            </ul>
          </div>

          <div class="p-6 sm:p-8 mt-auto" :class="plan.featured ? 'bg-gray-50/80' : 'bg-transparent'">
            <RouterLink
              :to="{ name: 'ContratacionPage', query: { plan: plan.id, cycle: selectedBillingCycle } }"
              class="block w-full text-center px-6 py-3.5 text-base font-semibold rounded-lg transition-all duration-300 ease-in-out shadow-md hover:shadow-lg"
              :class="plan.featured
                ? 'bg-impacto-orange text-white hover:bg-orange-500 transform hover:scale-105'
                : 'bg-impacto-orange/90 text-white hover:bg-impacto-orange border border-transparent hover:border-impacto-orange/50'"
            >
              {{ plan.ctaText }}
            </RouterLink>
          </div>
        </div>
      </div>

      <div ref="customPlanNoteRef" class="mt-16 text-center">
        <p class="text-gray-300">
          ¿Eres una gran agencia con necesidades específicas o manejas alto volumen de valoraciones?
          <a href="mailto:<EMAIL>?subject=Consulta%20Plan%20Enterprise%20-%20InmoAutomation" class="font-semibold text-impacto-orange hover:underline">
            Hablemos de un plan Enterprise
          </a>
        </p>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef, nextTick, computed } from 'vue'
import { RouterLink } from 'vue-router'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

interface PlanFeature {
  text: string;
  icon?: string; // Icono es opcional ahora, ya que el SVG está hardcodeado en el template
}

interface PricingPlan {
  id: string;
  name: string;
  description?: string;
  price_monthly: number;
  price_annual: number;
  currencySymbol?: string;
  features: PlanFeature[];
  ctaText: string;
  featured: boolean;
  badgeText: string | null;
  savingsText?: string | null;
}

const selectedBillingCycle = ref<'monthly' | 'annual'>('annual');

const pricingPlans = shallowRef<PricingPlan[]>([
  {
    id: '1',
    name: 'IA Starter',
    description: 'Para agentes individuales o pequeños equipos que inician con automatización IA.',
    price_monthly: 49,
    price_annual: 420,
    currencySymbol: '€',
    features: [
      { text: 'Acceso para <strong>1 usuario</strong>' },
      { text: 'Valorador IA 100% personalizado' },
      { text: 'Landing page automática de valoración' },
      { text: 'Integración nativa para tu web' },
      { text: 'Valoraciones ilimitadas' },
      { text: 'Captación de leads ilimitada' },
      { text: 'Secuencia IA básica de nutrición automática' },
      { text: 'Exportación de leads y valoraciones' },
    ],
    ctaText: 'Prueba 7 días gratis',
    featured: false,
    badgeText: null,
    savingsText: `¡AHORRA ${ (49 * 12) - 420 }€!`
  },
  {
    id: '2',
    name: 'IA Pro',
    description: 'Para agencias que buscan mayor control y profesionalización de su marketing.',
    price_monthly: 99,
    price_annual: 780,
    currencySymbol: '€',
    features: [
      { text: '<strong>Todo en IA Starter, más:</strong>' },
      { text: 'Acceso hasta <strong>4 usuarios</strong>' },
      { text: 'Analíticas detalladas (tráfico, conversiones, CTR, aperturas)' },
      { text: 'Secuencias IA múltiples según comportamiento' },
      { text: 'Integraciones exclusivas <span class="inline-block bg-gray-400 text-white text-xs px-2 py-0.5 rounded-full ml-1">Próximamente</span>' },
    ],
    ctaText: 'Prueba 7 días gratis',
    featured: true,
    badgeText: 'MÁS POPULAR',
    savingsText: `¡AHORRA ${ (99 * 12) - 780 }€!`
  },
  {
    id: '3',
    name: 'IA Élite',
    description: 'Perfecto para agencias con múltiples oficinas que buscan el máximo nivel.',
    price_monthly: 199,
    price_annual: 1500,
    currencySymbol: '€',
    features: [
      { text: '<strong>Todo en IA Pro, más:</strong>' },
      { text: 'Acceso hasta <strong>7 usuarios</strong>' },
      { text: 'Hasta <strong>5 valoradores IA</strong> diferentes' },
      { text: 'Secuencias IA avanzadas de largo plazo' },
      { text: 'Soporte prioritario' },
      { text: 'Configuración personalizada' },
    ],
    ctaText: 'Prueba 7 días gratis',
    featured: false,
    badgeText: null,
    savingsText: `¡AHORRA ${ (199 * 12) - 1500 }€!`
  },
]);

const periodicityText = computed(() => {
  return selectedBillingCycle.value === 'monthly' ? '/mes' : '/mes';
});

const getPlanPrice = (plan: PricingPlan) => {
  const price = selectedBillingCycle.value === 'monthly'
    ? plan.price_monthly
    : (plan.price_annual / 12);
  const formattedPrice = Number.isInteger(price) ? price.toFixed(0) : price.toFixed(2);
  return `${formattedPrice}${plan.currencySymbol || '€'}`;
};

const sectionTitleRef = ref<HTMLElement | null>(null)
const sectionSubtitleRef = ref<HTMLElement | null>(null)
const billingCycleSwitchRef = ref<HTMLElement | null>(null)
const planCardRefs = ref<Array<HTMLElement | null>>([])
const customPlanNoteRef = ref<HTMLElement | null>(null)

const setPlanCardRef = (el: any, index: number) => {
  if (el) {
    planCardRefs.value[index] = el as HTMLElement;
  }
}

onMounted(async () => {
  await nextTick(); 

  // if (sectionTitleRef.value && sectionSubtitleRef.value) {
  //   gsap.from([sectionTitleRef.value, sectionSubtitleRef.value], {
  //     opacity: 0,
  //     y: 30,
  //     duration: 0.7,
  //     ease: 'power2.out',
  //     stagger: 0.15,
  //     scrollTrigger: {
  //       trigger: sectionTitleRef.value,
  //       start: 'top 88%',
  //       toggleActions: 'play none none none',
  //     },
  //   });
  // }

  // if (billingCycleSwitchRef.value) {
  //   gsap.from(billingCycleSwitchRef.value, {
  //     opacity: 0,
  //     y: 20,
  //     duration: 0.5,
  //     ease: 'power2.out',
  //     scrollTrigger: {
  //       trigger: billingCycleSwitchRef.value,
  //       start: 'top 90%',
  //       toggleActions: 'play none none none',
  //     }
  //   });
  // }
  
  // planCardRefs.value.forEach((item, index) => {
  //   if (item) {
  //     gsap.from(item, { 
  //       opacity: 0,
  //       y: 40,
  //       duration: 0.6,
  //       ease: 'power2.out',
  //       stagger: 0.2, 
  //       scrollTrigger: {
  //         trigger: item, 
  //         start: 'top 85%', 
  //         toggleActions: 'play none none none',
  //       },
  //     });
  //   }
  // });

  // if (customPlanNoteRef.value) {
  //   gsap.from(customPlanNoteRef.value, {
  //     opacity: 0,
  //     y: 25,
  //     duration: 0.6,
  //     ease: 'power2.out',
  //     scrollTrigger: {
  //       trigger: customPlanNoteRef.value,
  //       start: 'top 92%',
  //       toggleActions: 'play none none none',
  //     },
  //   });
  // }
});

</script>

<style scoped>
.min-h-\[3em\] {
  min-height: 3em;
}
.hover\:shadow-3xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

</style>