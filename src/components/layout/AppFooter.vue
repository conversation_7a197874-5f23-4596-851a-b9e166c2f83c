<template>
  <footer class="bg-impacto-blue text-gray-300 border-t border-gray-700/50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
        <!-- Columna 1: Logo y Copyright -->
        <div class="flex flex-col items-center md:items-start text-center md:text-left">
          <RouterLink :to="{ name: 'Landing' }" class="mb-4">
            <img
              src="/img/logo-impacto-automation-valorador-white.svg"
              alt="Impacto Automation"
              class="h-8 w-auto"
            />
            <!-- Alternativa: solo texto si el logo es muy grande aquí -->
            <!-- <span class="text-lg font-semibold text-white">Impacto Automation</span> -->
          </RouterLink>
          <p class="text-sm text-gray-400">
            © {{ new Date().getFullYear() }} Impacto Automation.
            <br class="sm:hidden" />Todos los derechos reservados.
          </p>
        </div>

        <!-- Columna 2: Enlaces Rápidos -->
        <nav class="flex flex-col items-center space-y-3 md:mx-auto">
          <h3 class="text-sm font-semibold text-gray-100 tracking-wider uppercase mb-2">Navegación</h3>
          <a href="#how-it-works" class="text-sm hover:text-white transition-colors duration-150">Cómo funciona</a>
          <a href="#features" class="text-sm hover:text-white transition-colors duration-150">Características</a>
          <a href="#pricing" class="text-sm hover:text-white transition-colors duration-150">Precios</a>
          <a href="#faq" class="text-sm hover:text-white transition-colors duration-150">Preguntas frecuentes</a>
        </nav>

        <!-- Columna 3: Contacto y Enlaces Legales -->
        <div class="flex flex-col items-center md:items-end text-center md:text-right space-y-3">
          <div class="mb-2">
            <h3 class="text-sm font-semibold text-gray-100 tracking-wider uppercase mb-2">Contacto</h3>
            <a
              href="mailto:<EMAIL>"
              class="text-sm text-gray-400 hover:text-white transition-colors duration-150"
            >
              <EMAIL>
            </a>
          </div>
          <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
            <RouterLink
              :to="{ name: 'PrivacyPolicy' }"
              class="text-xs text-gray-400 hover:text-white hover:underline transition-colors duration-150"
            >
              Política de Privacidad
            </RouterLink>
            <RouterLink
              :to="{ name: 'TermsOfService' }"
              class="text-xs text-gray-400 hover:text-white hover:underline transition-colors duration-150"
            >
              Términos de Servicio
            </RouterLink>
          </div>
        </div>
      </div>

      <!-- Divisor y mensaje final -->
      <div class="mt-10 pt-8 border-t border-gray-700/60 text-center text-sm text-gray-500">
        <p>Automatizando la captación inmobiliaria con Inteligencia Artificial.</p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'

// No se necesita lógica JavaScript específica para este footer básico por ahora.
// new Date().getFullYear() se usa directamente en la plantilla.
</script>

<style scoped>
/* Estilos específicos para el AppFooter si fueran necesarios más allá de Tailwind */
footer a {
  /* Si queremos un estilo base para todos los enlaces en el footer.
     Aunque ya se ha aplicado text-gray-400 hover:text-white a cada uno. */
}
</style>