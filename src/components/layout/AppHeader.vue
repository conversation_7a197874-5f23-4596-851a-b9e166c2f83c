<template>
  <header
    class="sticky top-0 z-40 w-full bg-impacto-blue"
    :class="{ 'shadow-lg': isScrolled }"
  >
    <div
      class="container mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between h-20 transition-all duration-300"
      :class="{ 'h-16': isScrolled }"
    >
      <!-- Logo -->
      <RouterLink :to="{ name: 'Landing' }" class="flex items-center shrink-0">
        <img
          src="/img/logo-impacto-automation-valorador-white.svg"
          alt="Impacto Automation - Valorador Inmobiliario IA"
          class="h-12 md:h-14 w-auto transition-all duration-300"
          :class="{ 'h-10 md:h-12': isScrolled }"
        />
      </RouterLink>

      <nav class="hidden md:flex items-center space-x-6 lg:space-x-8">
        <a href="#how-it-works" @click.prevent="scrollToSection('#how-it-works')" class="text-gray-300 hover:text-white transition-colors duration-150 text-base font-medium py-1" :class="{ 'text-white font-bold border-b-2 border-impacto-orange': activeLink === '#how-it-works' }">Cómo funciona</a>
        <a href="#features" @click.prevent="scrollToSection('#features')" class="text-gray-300 hover:text-white transition-colors duration-150 text-base font-medium py-1" :class="{ 'text-white font-bold border-b-2 border-impacto-orange': activeLink === '#features' }">Características</a>
        <a href="#pricing" @click.prevent="scrollToSection('#pricing')" class="text-gray-300 hover:text-white transition-colors duration-150 text-base font-medium py-1" :class="{ 'text-white font-bold border-b-2 border-impacto-orange': activeLink === '#pricing' }">Precios</a>
        <a href="#faq" @click.prevent="scrollToSection('#faq')" class="text-gray-300 hover:text-white transition-colors duration-150 text-base font-medium py-1" :class="{ 'text-white font-bold border-b-2 border-impacto-orange': activeLink === '#faq' }">FAQ</a>
      </nav>

      <!-- CTA Principal -->
      <div class="flex items-center space-x-3">
        <a
          :href="appBaseUrl"
          class="hidden md:inline-flex px-4 py-2 text-sm font-medium text-gray-300 hover:text-white border border-gray-300 hover:border-white rounded-lg transition-all duration-200 ease-in-out"
        >
          Entrar
        </a>
        <RouterLink
          :to="{ name: 'ContratacionPage' }"
          class="px-5 py-2.5 text-sm font-semibold text-white bg-impacto-orange rounded-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 focus:ring-offset-impacto-blue transition-all duration-200 ease-in-out transform hover:scale-105"
        >
          Empezar gratis
        </RouterLink>
        <!-- Botón para menú móvil (placeholder) -->
        <button
          @click="isMobileMenuOpen = !isMobileMenuOpen"
          class="ml-4 md:hidden p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
          aria-label="Abrir menú principal"
        >
          <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
            <path v-if="!isMobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Menú Móvil (se expande abajo del header) -->
    <Transition
      enter-active-class="transition ease-out duration-200 transform"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition ease-in duration-150 transform"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div v-if="isMobileMenuOpen" class="md:hidden bg-impacto-blue shadow-lg pb-4">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          <a href="#how-it-works" @click.prevent="scrollToSection('#how-it-works')" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-700">Cómo funciona</a>
          <a href="#features" @click.prevent="scrollToSection('#features')" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-700">Características</a>
          <a href="#pricing" @click.prevent="scrollToSection('#pricing')" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-700">Precios</a>
          <a href="#faq" @click.prevent="scrollToSection('#faq')" class="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-gray-700">FAQ</a>
        </div>
        <!-- CTAs en el menú móvil -->
         <div class="px-5 pt-2 space-y-3">
            <a
              :href="appBaseUrl"
              @click="closeMobileMenu"
              class="block w-full text-center px-5 py-3 text-base font-medium text-gray-300 border border-gray-300 rounded-lg hover:text-white hover:border-white transition-all duration-200 ease-in-out"
            >
              Entrar
            </a>
            <RouterLink
              :to="{ name: 'ContratacionPage' }"
              @click="closeMobileMenu"
              class="block w-full text-center px-5 py-3 text-base font-semibold text-white bg-impacto-orange rounded-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 focus:ring-offset-impacto-blue transition-all duration-200 ease-in-out"
            >
              Empezar gratis
            </RouterLink>
        </div>
      </div>
    </Transition>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { RouterLink } from 'vue-router'

const appBaseUrl = computed(() => import.meta.env.VITE_APP_BASE_URL);

const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)
const activeLink = ref('') // Holds the href of the active link, e.g., '#features'

const handleScroll = () => {
  isScrolled.value = window.scrollY > 20; // Activar efecto cuando se ha hecho scroll más de 20px
  // Initial check for active link when not scrolled to top, observer will handle the rest
  // This part might be better handled purely by the observer or a specific top-of-page check
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false;
}

const scrollToSection = (selector: string) => {
  // Da feedback inmediato al usuario cambiando el estilo del enlace activo.
  activeLink.value = selector;

  const element = document.getElementById(selector.substring(1));
  if (element) {
    // La altura del header es de 80px (h-20) o 64px (h-16) cuando se hace scroll.
    const headerHeight = isScrolled.value ? 64 : 80;
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - headerHeight - 16; // Margen extra de 16px

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }

  // Cierra el menú móvil si está abierto.
  if (isMobileMenuOpen.value) {
    closeMobileMenu();
  }
};

let observer: IntersectionObserver | null = null;
const sectionIds = ['how-it-works', 'features', 'pricing', 'faq'];

onMounted(() => {
  window.addEventListener('scroll', handleScroll);

  // Set initial activeLink if page is loaded scrolled to a section or very top
  if (window.scrollY < 50) { // Heuristic for being at the top
    activeLink.value = '';
  } else {
    // Attempt to set initial active link based on current scroll, can be refined
    for (const id of sectionIds.slice().reverse()) { // Check from bottom up
      const section = document.getElementById(id);
      if (section && section.getBoundingClientRect().top < window.innerHeight * 0.5) {
        activeLink.value = '#' + id;
        break;
      }
    }
  }

  const headerHeight = 80; // Altura máxima del header para el cálculo del margen.
  const extraMargin = 16;
  const observerOptions = {
    root: null, // relative to document viewport
    rootMargin: `-${headerHeight + extraMargin}px 0px -55% 0px`, // Ajustado para el header y la activación a mitad de pantalla
    threshold: 0, // Trigger as soon as any part enters/leaves this zone
  };

  observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        activeLink.value = `#${entry.target.id}`;
      }
    });
    // If nothing is intersecting according to the latest batch of entries, and we've scrolled up high
    // (e.g. above all observed sections), clear the activeLink.
    // This requires checking if ALL observed sections are NOT intersecting.
    const anyManuallyScrolledToTop = window.scrollY < (document.getElementById(sectionIds[0])?.offsetTop || 200) - 100; // 100px buffer
    if (anyManuallyScrolledToTop && !entries.some(e => e.isIntersecting)) {
        activeLink.value = '';
    }

  }, observerOptions);

  sectionIds.forEach(id => {
    const section = document.getElementById(id);
    if (section) observer?.observe(section);
  });
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
  if (observer) {
    observer.disconnect();
  }
})

// Podríamos añadir lógica aquí para cerrar el menú móvil si cambia la ruta
// import { watch } from 'vue'
// import { useRoute } from 'vue-router'
// const route = useRoute()
// watch(() => route.path, () => {
//   isMobileMenuOpen.value = false
// })
</script>

<style scoped>
/*
  Estilos adicionales o más complejos pueden ir aquí.
  El header es 'sticky' y usa 'backdrop-blur-md' con un fondo semitransparente
  (`bg-impacto-blue/80`) para un efecto moderno cuando hay contenido detrás.
  Al hacer scroll (`isScrolled` es true):
  - La altura del header y el logo se reducen ligeramente.
  - Se añade una sombra más pronunciada (`shadow-lg`).
*/

header > div {
  position: relative;
  z-index: 1;
}
</style>