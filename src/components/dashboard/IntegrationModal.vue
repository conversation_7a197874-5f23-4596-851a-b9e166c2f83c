<template>
  <div v-if="isOpen" class="fixed inset-0 z-[9998] overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Fondo oscuro del overlay -->
      <div class="fixed inset-0 bg-gray-700 bg-opacity-75 transition-opacity" aria-hidden="true" @click="closeModal"></div>

      <!-- Contenedor para centrar el modal -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <!-- Panel del Modal -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
        <div class="bg-gradient-to-r from-impacto-blue to-impacto-blue/90 px-6 py-5 sm:px-8 sm:py-6">
          <div class="flex items-start justify-between">
            <div class="flex items-center">
              <div class="bg-impacto-orange/20 p-2 rounded-lg mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
              </div>
              <div>
                <h3 class="text-xl leading-6 font-bold text-white" id="modal-title">
                  Integra el valorador en tu sitio web
                </h3>
                <p class="text-sm text-blue-100 mt-1">Copia y pega este script en tu página web para mostrar el valorador.</p>
              </div>
            </div>
            <button @click="closeModal" class="text-blue-200 hover:text-white transition-colors">
              <span class="sr-only">Cerrar</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div class="px-6 py-10 sm:px-8 sm:py-12">
          <!-- Estado: Configuración Requerida (si falta clientIdentifier o apiKey) -->
          <div v-if="!clientIdentifier || clientIdentifier.trim() === '' || !apiKey || apiKey.trim() === ''" class="text-center">
            <div class="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-gradient-to-br from-impacto-orange/10 to-impacto-blue/10 mb-6">
              <svg class="h-12 w-12 text-impacto-orange" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-impacto-blue mb-3">Configuración requerida</h3>
            <p class="text-md text-gray-600 mb-6 max-w-md mx-auto">
              El script de integración estará disponible una vez que configures tu valorador por primera vez.
            </p>
            <router-link
              to="/configuracion"
              @click="closeModal"
              class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-impacto-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange transition-all duration-150 ease-in-out"
            >
              Ir a configuración
            </router-link>
          </div>

          <!-- Estado: Script Disponible (clientIdentifier Y apiKey presentes) -->
          <div v-else>
            <h4 class="text-md font-semibold text-impacto-blue mb-2">Paso 1: Copia el código</h4>
            <p class="text-sm text-gray-600 mb-3">
              Haz clic en el botón "Copiar código" o selecciónalo manualmente y cópialo.
            </p>

            <div class="bg-gray-900 rounded-lg p-4 my-4 overflow-x-auto">
              <pre class="text-sm"><code class="language-javascript text-gray-200">{{ scriptToDisplay }}</code></pre>
            </div>

            <button
              @click="copyScriptToClipboard"
              :disabled="isCopying || !clientIdentifier || clientIdentifier.trim() === '' || !apiKey || apiKey.trim() === ''"
              class="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-impacto-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange transition-all duration-150 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed group"
            >
              <svg v-if="!isCopied && !isCopying" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              <svg v-if="isCopying" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-if="isCopied" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
              </svg>
              {{ copyButtonText }}
            </button>
            <p v-if="copyError" class="text-xs text-red-600 mt-2 text-center">{{ copyError }}</p>

            <div class="mt-8">
              <h4 class="text-md font-semibold text-impacto-blue mb-2">Paso 2: Pega el código en tu HTML</h4>
              <p class="text-sm text-gray-600 mb-1">
                Pega el código que copiaste justo antes de la etiqueta de cierre <code class="text-xs bg-gray-200 p-1 rounded">&lt;/body&gt;</code> en la(s) página(s) donde deseas que aparezca el valorador.
              </p>
              <p class="text-sm text-gray-600">
                Una vez pegado, el valorador debería cargarse automáticamente. Te recomendamos probar la integración en diferentes navegadores y dispositivos.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 px-6 py-4 sm:px-8 sm:flex sm:flex-row-reverse border-t border-gray-200">
          <button
            type="button"
            class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition-colors"
            @click="closeModal"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useToast } from '@/composables/useToast';

const props = defineProps<{
  isOpen: boolean;
  clientIdentifier: string | null;
  apiKey: string | null;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const { showToast } = useToast();

const scriptTagOpen = '<' + 'script' + '>';
const scriptTagClose = '<' + '/script' + '>';

// Construye la URL del script de inserción dinámicamente usando el subdominio del cliente.
const embedScriptUrl = computed(() => {
  if (!props.clientIdentifier) return '';
  // En desarrollo, podríamos apuntar a una URL local si fuera necesario, pero para el subdominio es mejor usar la URL de producción.
  return `https://${props.clientIdentifier}.inmoautomation.com/embed/valorador.js`;
});

const baseScriptTemplate = computed(() => `<div id="valorador-container"></div>

${scriptTagOpen}
  (function(w, d, s, o, f, js, fjs) {
      js = d.createElement(s), fjs = d.getElementsByTagName(s)[0];
      js.id = 'valorador-js';
      js.src = f + '?id=' + o.clientId + '&apiKey=' + o.apiKey;
      js.async = 1;
      fjs.parentNode.insertBefore(js, fjs);
  })(window, document, 'script', {
      clientId: '{{CLIENT_ID}}',
      apiKey: '{{API_KEY}}'
  }, '{{EMBED_URL}}');
${scriptTagClose}`);

const scriptContent = computed(() => {
  if (props.clientIdentifier && props.clientIdentifier.trim() !== '' && props.apiKey && props.apiKey.trim() !== '') {
    let script = baseScriptTemplate.value.replace('{{CLIENT_ID}}', props.clientIdentifier);
    script = script.replace('{{API_KEY}}', props.apiKey);
    script = script.replace('{{EMBED_URL}}', embedScriptUrl.value);
    return script;
  }
  return "// Script no disponible. Faltan datos de configuración.";
});

const scriptToDisplay = computed(() => {
  if (props.clientIdentifier && props.clientIdentifier.trim() !== '' && props.apiKey && props.apiKey.trim() !== '') {
    return scriptContent.value;
  }
  return "// Configura tu valorador para obtener el script.";
});

const isCopying = ref(false);
const isCopied = ref(false);
const copyError = ref<string | null>(null);

const copyButtonText = computed(() => {
  if (isCopied.value) return '¡Copiado!';
  if (isCopying.value) return 'Copiando...';
  return 'Copiar código';
});

const closeModal = () => {
  emit('close');
};

const copyScriptToClipboard = async () => {
  const scriptToCopy = scriptContent.value;

  if (!props.clientIdentifier || props.clientIdentifier.trim() === '' ||
      !props.apiKey || props.apiKey.trim() === '' ||
      scriptToCopy.includes("// Script no disponible")) {
    copyError.value = 'El script de integración aún no está disponible.';
    showToast('Configura tu valorador para generar el script.', 'warning');
    return;
  }

  if (!navigator.clipboard) {
    copyError.value = 'Tu navegador no soporta la copia al portapapeles. Inténtalo manualmente.';
    showToast(copyError.value, 'error');
    return;
  }

  isCopying.value = true;
  isCopied.value = false;
  copyError.value = null;

  try {
    await navigator.clipboard.writeText(scriptToCopy);
    isCopied.value = true;
    showToast('Script copiado al portapapeles.', 'success');
    setTimeout(() => {
      isCopied.value = false;
    }, 2500);
  } catch (err) {
    console.error('Error al copiar el script: ', err);
    copyError.value = 'No se pudo copiar el script. Inténtalo manualmente.';
    showToast(copyError.value, 'error');
  } finally {
    isCopying.value = false;
  }
};

watch(() => props.isOpen, (newVal) => {
  if (newVal) {
    isCopied.value = false;
    isCopying.value = false;
    copyError.value = null;
  }
});

</script>

<style scoped>
pre {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

code.language-javascript {
  display: block;
  overflow-x: auto;
  padding: 1em;
  background: #2d2d2d;
  color: #ccc;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #999;
}
.token.punctuation {
  color: #ccc;
}
.token.tag,
.token.attr-name,
.token.namespace,
.token.deleted {
  color: #e2777a;
}
.token.function-name {
  color: #6196cc;
}
.token.boolean,
.token.number,
.token.function {
  color: #f08d49;
}
.token.property,
.token.class-name,
.token.constant,
.token.symbol {
  color: #f8c555;
}
.token.selector,
.token.important,
.token.atrule,
.token.keyword,
.token.builtin {
  color: #cc99cd;
}
.token.string,
.token.char,
.token.attr-value,
.token.regex,
.token.variable {
  color: #7ec699;
}
.token.operator,
.token.entity,
.token.url {
  color: #67cdcc;
}
</style> 