<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen p-4 text-center sm:p-0">
      <!-- Overlay de fondo -->
      <div @click="closeAndSkip" class="fixed inset-0 bg-gray-900 bg-opacity-80 transition-opacity" aria-hidden="true"></div>

      <!-- Contenido del Modal -->
      <div 
        ref="modalRef"
        class="relative inline-block bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:max-w-2xl w-full"
      >
        <div class="p-6 sm:p-10">
          <!-- Encabezado -->
          <div class="text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.898 20.528L16.5 21.75l-.398-1.222a3.375 3.375 0 00-2.456-2.456L12.75 18l1.222-.398a3.375 3.375 0 002.456-2.456L16.5 14.25l.398 1.222a3.375 3.375 0 002.456 2.456L20.25 18l-1.222.398a3.375 3.375 0 00-2.456 2.456z" />
            </svg>
            <h2 class="mt-4 text-2xl sm:text-3xl font-extrabold text-gray-900" id="modal-title">
              ¡Bienvenido a InmoAutomation!
            </h2>
            <p class="mt-3 text-base sm:text-lg text-gray-600 max-w-xl mx-auto">
              Has activado tu motor de captación inmobiliaria. Esto es lo que ahora puedes hacer:
            </p>
          </div>

          <!-- Contenido Principal: Features -->
          <div class="mt-10 space-y-6">
            <!-- Feature 1: Captura -->
            <div class="flex items-start space-x-4 sm:space-x-6 bg-gray-50 p-4 sm:p-6 rounded-xl border border-gray-200">
              <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-lg bg-impacto-blue text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-gray-900">Captura leads en piloto automático</h3>
                <p class="mt-1 text-sm sm:text-base text-gray-600">
                  Tu valorador es un imán de propietarios. Comparte tu enlace único o intégralo en tu web y comenzará a generar leads cualificados 24/7.
                </p>
              </div>
            </div>

            <!-- Feature 2: Nutre -->
            <div class="flex items-start space-x-4 sm:space-x-6 bg-gray-50 p-4 sm:p-6 rounded-xl border border-gray-200">
              <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-lg bg-impacto-orange text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-gray-900">Nutre a tus leads con IA</h3>
                <p class="mt-1 text-sm sm:text-base text-gray-600">
                  Nuestro sistema envía secuencias de emails inteligentes y personalizadas para construir confianza, educar al lead y posicionarte como experto.
                </p>
              </div>
            </div>
          </div>

          <!-- Llamada a la Acción (CTA) -->
          <div class="mt-10 bg-gray-100 p-6 sm:p-8 rounded-2xl text-center">
            <h3 class="text-xl sm:text-2xl font-bold text-gray-900">Tu primer paso hacia el éxito</h3>
            <p class="mt-2 text-base text-gray-600 max-w-xl mx-auto">
              Para poner todo en marcha, solo necesitas personalizar el valorador con la identidad de tu agencia. ¡Es rápido y fácil!
            </p>
            <button
              @click="goToConfiguration"
              type="button"
              class="mt-6 w-full sm:w-auto inline-flex items-center justify-center rounded-xl border border-transparent bg-impacto-orange px-8 py-3 text-base sm:text-lg font-bold text-white shadow-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 transition-all transform hover:scale-105"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Configurar mi valorador
            </button>
            <div class="mt-5">
                <button @click="closeAndSkip" class="text-sm text-gray-500 hover:text-gray-800 transition">Lo haré más tarde</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import gsap from 'gsap';

const props = defineProps<{
  isOpen: boolean;
}>();

const emit = defineEmits(['close']);

const router = useRouter();
const modalRef = ref<HTMLElement | null>(null);

// Animación de entrada
watch(() => props.isOpen, (newVal) => {
  if (newVal && modalRef.value) {
    gsap.fromTo(
      modalRef.value,
      { y: 60, opacity: 0, scale: 0.98 },
      { y: 0, opacity: 1, scale: 1, duration: 0.5, ease: 'power3.out' }
    );
  }
});

// Acción principal: Ir a configuración
const goToConfiguration = () => {
  localStorage.setItem('hasSeenOnboarding', 'true');
  emit('close');
  router.push('/configuracion');
};

// Acción secundaria: Omitir y no volver a mostrar
const closeAndSkip = () => {
  localStorage.setItem('hasSeenOnboarding', 'true');
  emit('close');
};
</script>