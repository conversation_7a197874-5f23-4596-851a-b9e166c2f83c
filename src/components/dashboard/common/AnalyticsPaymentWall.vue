<template>
  <div class="bg-white rounded-xl shadow-lg border border-gray-200/70 overflow-hidden">
    <!-- Header -->
    <div class="bg-gray-50 border-b border-gray-200 p-6">
      <div class="flex items-center justify-center mb-4">
        <div class="p-3 bg-impacto-blue/10 rounded-xl">
          <BarChart3 class="h-8 w-8 text-impacto-blue" />
        </div>
      </div>
      <h3 class="text-xl font-bold text-center text-[#051f33] mb-2">Analíticas detalladas</h3>
      <p class="text-center text-gray-600 text-sm">
        Desbloquea métricas avanzadas para optimizar tu captación de leads
      </p>
    </div>

    <!-- Contenido principal -->
    <div class="p-8">
      <div class="max-w-2xl mx-auto text-center">
        <!-- Mensaje principal -->
        <div class="mb-8">
          <h4 class="text-lg font-semibold text-gray-900 mb-3">
            Tu plan actual no incluye analíticas detalladas
          </h4>
          <p class="text-gray-600 leading-relaxed">
            Actualiza a un plan superior para acceder a métricas completas que te ayudarán a
            optimizar tu estrategia de captación y maximizar el rendimiento de tu valorador.
          </p>
        </div>

        <!-- Características que se desbloquean -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200/50">
            <TrendingUp class="h-5 w-5 text-impacto-orange flex-shrink-0 mt-1" />
            <div class="text-left">
              <h5 class="font-medium text-gray-900 text-sm">Estadísticas de captación</h5>
              <p class="text-xs text-gray-600 mt-1">Visitas, conversiones y ratio de efectividad de tu valorador</p>
            </div>
          </div>

          <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200/50">
            <BarChart2 class="h-5 w-5 text-impacto-orange flex-shrink-0 mt-1" />
            <div class="text-left">
              <h5 class="font-medium text-gray-900 text-sm">Rendimiento de emails</h5>
              <p class="text-xs text-gray-600 mt-1">Métricas detalladas de apertura, clics y engagement</p>
            </div>
          </div>

          <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200/50">
            <Target class="h-5 w-5 text-impacto-orange flex-shrink-0 mt-1" />
            <div class="text-left">
              <h5 class="font-medium text-gray-900 text-sm">Análisis geográfico</h5>
              <p class="text-xs text-gray-600 mt-1">Zonas de mayor actividad y oportunidades de mercado</p>
            </div>
          </div>

          <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200/50">
            <Lightbulb class="h-5 w-5 text-impacto-orange flex-shrink-0 mt-1" />
            <div class="text-left">
              <h5 class="font-medium text-gray-900 text-sm">Insights de optimización</h5>
              <p class="text-xs text-gray-600 mt-1">Recomendaciones para mejorar tu estrategia de captación</p>
            </div>
          </div>
        </div>

        <!-- Botón de acción -->
        <div class="space-y-4">
          <button
            @click="$emit('upgrade-plan')"
            class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-impacto-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange transition-all duration-200 shadow-md hover:shadow-lg"
          >
            <Crown class="h-4 w-4 mr-2" />
            Actualizar plan
          </button>

          <p class="text-xs text-gray-500">
            Accede inmediatamente a todas las analíticas detalladas
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  BarChart3,
  TrendingUp,
  BarChart2,
  Target,
  Lightbulb,
  Crown
} from 'lucide-vue-next';

// Definir los eventos que puede emitir el componente
defineEmits<{
  (e: 'upgrade-plan'): void;
}>();
</script>

<style scoped>
/* Estilos adicionales si son necesarios */
</style>
