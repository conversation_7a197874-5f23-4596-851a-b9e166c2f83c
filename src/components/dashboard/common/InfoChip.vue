<template>
  <div class="p-2 sm:p-3 bg-slate-50 rounded-md border border-slate-200">
    <div class="text-xs text-gray-500 mb-0.5">{{ label }}</div>
    <div class="font-medium text-gray-800">{{ value }}</div>
    <div v-if="subValue" class="text-xs text-gray-500 mt-0.5">{{ subValue }}</div>
  </div>
</template>
<script setup lang="ts">
defineProps<{
  label: string;
  value: string;
  subValue?: string;
}>();
</script> 