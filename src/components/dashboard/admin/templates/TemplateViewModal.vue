<template>
  <TransitionRoot as="template" :show="show">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-6xl sm:p-6">
              <div v-if="templateData">
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center">
                    <span class="text-3xl mr-3">{{ templateData.type_info?.icon || '📄' }}</span>
                    <div>
                      <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                        {{ templateData.nombre_interno_plantilla }}
                      </DialogTitle>
                      <p class="mt-1 text-sm text-gray-500">
                        {{ templateData.descripcion }}
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-3">
                    <button
                      @click="$emit('edit')"
                      class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-impacto-blue hover:bg-impacto-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
                    >
                      <PencilIcon class="h-4 w-4 mr-2" />
                      Editar
                    </button>
                    <button
                      @click="$emit('close')"
                      class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-impacto-blue focus:ring-offset-2"
                    >
                      <XMarkIcon class="h-6 w-6" />
                    </button>
                  </div>
                </div>

                <!-- Tabs -->
                <div class="border-b border-gray-200 mb-6">
                  <nav class="-mb-px flex space-x-8">
                    <button
                      v-for="tab in tabs"
                      :key="tab.id"
                      @click="activeTab = tab.id"
                      :class="[
                        'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
                        activeTab === tab.id
                          ? 'border-impacto-blue text-impacto-blue'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      ]"
                    >
                      <span class="mr-2">{{ tab.icon }}</span>
                      {{ tab.name }}
                    </button>
                  </nav>
                </div>

                <!-- Tab Content -->
                <div class="min-h-96">
                  <!-- Tab: Información -->
                  <div v-show="activeTab === 'info'" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <!-- Información básica -->
                      <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-4">Información Básica</h4>
                        <dl class="space-y-3">
                          <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Tipo</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ templateData.type_info?.display_name }}</dd>
                          </div>
                          <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Estado</dt>
                            <dd class="mt-1">
                              <span
                                :class="[
                                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                  templateData.status?.color === 'green' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                                ]"
                              >
                                {{ templateData.status?.text }}
                              </span>
                            </dd>
                          </div>
                          <div v-if="templateData.asunto_predeterminado">
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Asunto Predeterminado</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ templateData.asunto_predeterminado }}</dd>
                          </div>
                          <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Tamaño HTML</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ formatBytes(templateData.html_size) }}</dd>
                          </div>
                        </dl>
                      </div>

                      <!-- Estadísticas de uso -->
                      <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-4">Estadísticas de Uso</h4>
                        <dl class="space-y-3">
                          <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Usos Totales</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ templateData.usage_stats?.total_usage || 0 }}</dd>
                          </div>
                          <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Secuencias que la Usan</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ templateData.usage_stats?.sequences_using || 0 }}</dd>
                          </div>
                          <div v-if="templateData.usage_stats?.sequence_names?.length > 0">
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Secuencias</dt>
                            <dd class="mt-1">
                              <div class="flex flex-wrap gap-1">
                                <span
                                  v-for="name in templateData.usage_stats.sequence_names"
                                  :key="name"
                                  class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                                >
                                  {{ name }}
                                </span>
                              </div>
                            </dd>
                          </div>
                          <div>
                            <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Puede Eliminarse</dt>
                            <dd class="mt-1">
                              <span
                                :class="[
                                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                  templateData.usage_stats?.can_delete ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                ]"
                              >
                                {{ templateData.usage_stats?.can_delete ? 'Sí' : 'No (en uso)' }}
                              </span>
                            </dd>
                          </div>
                        </dl>
                      </div>
                    </div>

                    <!-- Fechas -->
                    <div class="bg-gray-50 rounded-lg p-4">
                      <h4 class="text-sm font-medium text-gray-900 mb-4">Fechas</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Creada</dt>
                          <dd class="mt-1 text-sm text-gray-900">{{ formatDate(templateData.fecha_creacion) }}</dd>
                        </div>
                        <div>
                          <dt class="text-xs font-medium text-gray-500 uppercase tracking-wide">Última Modificación</dt>
                          <dd class="mt-1 text-sm text-gray-900">{{ formatDate(templateData.fecha_modificacion) }}</dd>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Tab: Vista Previa -->
                  <div v-show="activeTab === 'preview'">
                    <div class="border border-gray-300 rounded-lg overflow-hidden">
                      <div class="bg-gray-50 px-4 py-2 border-b border-gray-300 flex items-center justify-between">
                        <h4 class="text-sm font-medium text-gray-700">Vista Previa HTML</h4>
                        <div class="flex items-center space-x-2">
                          <button
                            @click="copyHtml"
                            class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
                          >
                            <DocumentDuplicateIcon class="h-3 w-3 mr-1" />
                            Copiar HTML
                          </button>
                          <button
                            @click="downloadHtml"
                            class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
                          >
                            <ArrowDownTrayIcon class="h-3 w-3 mr-1" />
                            Descargar
                          </button>
                        </div>
                      </div>
                      <div class="p-4">
                        <iframe
                          :srcdoc="templateData.html_content"
                          class="w-full h-96 border-0"
                          sandbox="allow-same-origin"
                        ></iframe>
                      </div>
                    </div>
                  </div>

                  <!-- Tab: Código HTML -->
                  <div v-show="activeTab === 'code'">
                    <div class="border border-gray-300 rounded-lg overflow-hidden">
                      <div class="bg-gray-50 px-4 py-2 border-b border-gray-300 flex items-center justify-between">
                        <h4 class="text-sm font-medium text-gray-700">Código HTML</h4>
                        <button
                          @click="copyHtml"
                          class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
                        >
                          <DocumentDuplicateIcon class="h-3 w-3 mr-1" />
                          Copiar
                        </button>
                      </div>
                      <div class="p-4 bg-gray-900 text-gray-100 overflow-x-auto">
                        <pre class="text-sm font-mono whitespace-pre-wrap">{{ templateData.html_content }}</pre>
                      </div>
                    </div>
                  </div>

                  <!-- Tab: Variables -->
                  <div v-show="activeTab === 'variables'" class="space-y-6">
                    <div v-if="templateData.variables_found?.length > 0">
                      <h4 class="text-sm font-medium text-gray-900 mb-4">Variables Detectadas en la Plantilla</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        <div
                          v-for="variable in templateData.variables_found"
                          :key="variable"
                          class="border border-gray-200 rounded-lg p-3 bg-blue-50"
                        >
                          <code class="text-sm font-mono text-blue-600">{{ variable }}</code>
                        </div>
                      </div>
                    </div>

                    <div v-if="templateData.type_info?.recommended_variables?.length > 0">
                      <h4 class="text-sm font-medium text-gray-900 mb-4">Variables Recomendadas para este Tipo</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        <div
                          v-for="variable in templateData.type_info.recommended_variables"
                          :key="variable"
                          :class="[
                            'border rounded-lg p-3',
                            templateData.variables_found?.includes(`{{${variable}}}`)
                              ? 'border-green-200 bg-green-50'
                              : 'border-yellow-200 bg-yellow-50'
                          ]"
                        >
                          <code class="text-sm font-mono" :class="isVariableUsed(variable) ? 'text-green-600' : 'text-yellow-600'">
                            {{ formatVariable(variable) }}
                          </code>
                          <div class="mt-1">
                            <span
                              :class="[
                                'inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium',
                                isVariableUsed(variable)
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              ]"
                            >
                              {{ isVariableUsed(variable) ? 'Usada' : 'Recomendada' }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div v-if="!templateData.variables_found?.length && !templateData.type_info?.recommended_variables?.length">
                      <div class="text-center py-8">
                        <TagIcon class="mx-auto h-12 w-12 text-gray-400" />
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No hay variables</h3>
                        <p class="mt-1 text-sm text-gray-500">Esta plantilla no utiliza variables dinámicas.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import {
  XMarkIcon,
  PencilIcon,
  DocumentDuplicateIcon,
  ArrowDownTrayIcon,
  TagIcon
} from '@heroicons/vue/24/outline'
import { useToast } from '@/composables/useToast'
import { apiFetch } from '@/utils/apiFetch'

const props = defineProps({
  show: Boolean,
  template: Object
})

const emit = defineEmits(['close', 'edit'])

const { showToast } = useToast()

// Estado reactivo
const templateData = ref(null)
const loading = ref(false)
const activeTab = ref('info')

// Helper functions
const formatVariable = (variable) => {
  return `{{${variable}}}`
}

const isVariableUsed = (variable) => {
  return templateData.value?.variables_found?.includes(`{{${variable}}}`) || false
}

// Tabs
const tabs = [
  { id: 'info', name: 'Información', icon: 'ℹ️' },
  { id: 'preview', name: 'Vista Previa', icon: '👁️' },
  { id: 'code', name: 'Código HTML', icon: '💻' },
  { id: 'variables', name: 'Variables', icon: '🏷️' }
]

// Watchers
watch(() => props.show, (newVal) => {
  if (newVal && props.template) {
    loadTemplateData()
    activeTab.value = 'info'
  }
})

// Métodos
const loadTemplateData = async () => {
  if (!props.template?.id) return

  loading.value = true
  try {
    const data = await apiFetch(`admin_get_html_template.php?id=${props.template.id}`)

    if (data.success) {
      templateData.value = data.data
    } else {
      showToast('Error cargando plantilla: ' + data.message, 'error')
    }
  } catch (error) {
    showToast('Error de conexión al cargar plantilla', 'error')
    console.error('Error loading template:', error)
  } finally {
    loading.value = false
  }
}

const copyHtml = async () => {
  if (!templateData.value?.html_content) return

  try {
    await navigator.clipboard.writeText(templateData.value.html_content)
    showToast('HTML copiado al portapapeles', 'success')
  } catch (error) {
    showToast('Error copiando HTML', 'error')
  }
}

const downloadHtml = () => {
  if (!templateData.value?.html_content) return

  const blob = new Blob([templateData.value.html_content], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${templateData.value.nombre_interno_plantilla}.html`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  showToast('HTML descargado', 'success')
}

// Utilidades
const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
