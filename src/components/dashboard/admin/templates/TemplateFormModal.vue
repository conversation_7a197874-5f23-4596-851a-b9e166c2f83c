<template>
  <TransitionRoot as="template" :show="show">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl sm:p-6">
              <div>
                <div class="flex items-center justify-between mb-6">
                  <div>
                    <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                      {{ isEditing ? 'Editar Plantilla HTML' : 'Nueva Plantilla HTML' }}
                    </DialogTitle>
                    <p class="mt-1 text-sm text-gray-500">
                      {{ isEditing ? 'Modifica los datos de la plantilla' : 'Crea una nueva plantilla HTML para emails o reportes' }}
                    </p>
                  </div>
                  <button
                    @click="$emit('close')"
                    class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-impacto-blue focus:ring-offset-2"
                  >
                    <XMarkIcon class="h-6 w-6" />
                  </button>
                </div>

                <!-- Tabs -->
                <div class="border-b border-gray-200 mb-6">
                  <nav class="-mb-px flex space-x-8">
                    <button
                      v-for="tab in tabs"
                      :key="tab.id"
                      @click="activeTab = tab.id"
                      :class="[
                        'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
                        activeTab === tab.id
                          ? 'border-impacto-blue text-impacto-blue'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      ]"
                    >
                      <span class="mr-2">{{ tab.icon }}</span>
                      {{ tab.name }}
                    </button>
                  </nav>
                </div>

                <!-- Tab Content -->
                <form @submit.prevent="handleSubmit">
                  <!-- Tab: Información Básica -->
                  <div v-show="activeTab === 'basic'" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <!-- Nombre interno -->
                      <div>
                        <label for="nombre_interno" class="block text-sm font-medium text-gray-700">
                          Nombre Interno *
                        </label>
                        <input
                          id="nombre_interno"
                          v-model="form.nombre_interno_plantilla"
                          type="text"
                          required
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                          placeholder="ej: secuencia_bienvenida"
                        >
                        <p class="mt-1 text-xs text-gray-500">
                          Identificador único para la plantilla (sin espacios)
                        </p>
                      </div>

                      <!-- Tipo de plantilla -->
                      <div>
                        <label for="tipo_plantilla" class="block text-sm font-medium text-gray-700">
                          Tipo de Plantilla *
                        </label>
                        <select
                          id="tipo_plantilla"
                          v-model="form.tipo_plantilla"
                          required
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                        >
                          <option value="">Seleccionar tipo</option>
                          <option value="secuencia_email">📧 Secuencia de Email</option>
                          <option value="informe_valoracion">📊 Informe de Valoración</option>
                          <option value="general">📄 General</option>
                          <option value="personalizada">📝 Personalizada</option>
                        </select>
                      </div>
                    </div>

                    <!-- Descripción -->
                    <div>
                      <label for="descripcion" class="block text-sm font-medium text-gray-700">
                        Descripción *
                      </label>
                      <textarea
                        id="descripcion"
                        v-model="form.descripcion"
                        rows="3"
                        required
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                        placeholder="Describe el propósito y uso de esta plantilla..."
                      ></textarea>
                    </div>

                    <!-- Asunto predeterminado -->
                    <div>
                      <label for="asunto_predeterminado" class="block text-sm font-medium text-gray-700">
                        Asunto Predeterminado
                      </label>
                      <input
                        id="asunto_predeterminado"
                        v-model="form.asunto_predeterminado"
                        type="text"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                        placeholder="Asunto por defecto para emails (opcional)"
                      >
                      <p class="mt-1 text-xs text-gray-500">
                        Solo para plantillas de email. Puede usar variables como {{lead_name}}
                      </p>
                    </div>

                    <!-- Estado activo -->
                    <div class="flex items-center">
                      <input
                        id="activa"
                        v-model="form.activa"
                        type="checkbox"
                        class="h-4 w-4 text-impacto-blue focus:ring-impacto-blue border-gray-300 rounded"
                      >
                      <label for="activa" class="ml-2 block text-sm text-gray-900">
                        Plantilla activa
                      </label>
                      <p class="ml-4 text-xs text-gray-500">
                        Solo las plantillas activas aparecerán en los selectores
                      </p>
                    </div>
                  </div>

                  <!-- Tab: Editor HTML -->
                  <div v-show="activeTab === 'html'" class="space-y-6">
                    <div>
                      <div class="flex items-center justify-between mb-3">
                        <label for="html_content" class="block text-sm font-medium text-gray-700">
                          Contenido HTML *
                        </label>
                        <div class="flex items-center space-x-2">
                          <button
                            type="button"
                            @click="showPreview = !showPreview"
                            class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
                          >
                            <EyeIcon class="h-4 w-4 mr-1" />
                            {{ showPreview ? 'Ocultar' : 'Vista Previa' }}
                          </button>
                          <button
                            type="button"
                            @click="insertVariable"
                            class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
                          >
                            <PlusIcon class="h-4 w-4 mr-1" />
                            Insertar Variable
                          </button>
                        </div>
                      </div>

                      <div class="grid grid-cols-1 gap-4" :class="showPreview ? 'lg:grid-cols-2' : ''">
                        <!-- Editor -->
                        <div>
                          <textarea
                            id="html_content"
                            v-model="form.html_content"
                            rows="20"
                            required
                            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm font-mono text-sm"
                            placeholder="Escribe aquí el contenido HTML de la plantilla..."
                          ></textarea>
                          <p class="mt-1 text-xs text-gray-500">
                            Usa variables como {{lead_name}}, {{property_address}}, etc.
                          </p>
                        </div>

                        <!-- Vista previa -->
                        <div v-if="showPreview" class="border border-gray-300 rounded-md">
                          <div class="bg-gray-50 px-3 py-2 border-b border-gray-300">
                            <h4 class="text-sm font-medium text-gray-700">Vista Previa</h4>
                          </div>
                          <div class="p-4 max-h-96 overflow-y-auto">
                            <iframe
                              :srcdoc="form.html_content"
                              class="w-full h-80 border-0"
                              sandbox="allow-same-origin"
                            ></iframe>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Tab: Variables -->
                  <div v-show="activeTab === 'variables'" class="space-y-6">
                    <div>
                      <h4 class="text-sm font-medium text-gray-900 mb-4">Variables Disponibles</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div
                          v-for="variable in availableVariables"
                          :key="variable.name"
                          class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 cursor-pointer"
                          @click="insertVariableIntoEditor(variable.name)"
                        >
                          <div class="flex items-center justify-between">
                            <code class="text-sm font-mono text-blue-600">{{ variable.name }}</code>
                            <button
                              type="button"
                              class="text-gray-400 hover:text-gray-600"
                              title="Insertar en editor"
                            >
                              <PlusIcon class="h-4 w-4" />
                            </button>
                          </div>
                          <p class="text-xs text-gray-500 mt-1">{{ variable.description }}</p>
                        </div>
                      </div>
                    </div>

                    <!-- Variables encontradas en el HTML -->
                    <div v-if="foundVariables.length > 0">
                      <h4 class="text-sm font-medium text-gray-900 mb-4">Variables Detectadas en el HTML</h4>
                      <div class="flex flex-wrap gap-2">
                        <span
                          v-for="variable in foundVariables"
                          :key="variable"
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {{ variable }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- Botones de acción -->
                  <div class="mt-8 flex justify-end space-x-3">
                    <button
                      type="button"
                      @click="$emit('close')"
                      class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
                    >
                      Cancelar
                    </button>
                    <button
                      type="submit"
                      :disabled="saving"
                      class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-impacto-blue hover:bg-impacto-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg v-if="saving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {{ saving ? 'Guardando...' : (isEditing ? 'Actualizar' : 'Crear') }} Plantilla
                    </button>
                  </div>
                </form>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon, EyeIcon, PlusIcon } from '@heroicons/vue/24/outline'
import { useToast } from '@/composables/useToast'
import { apiFetch } from '@/utils/apiFetch'

const props = defineProps({
  show: Boolean,
  template: Object,
  isEditing: Boolean
})

const emit = defineEmits(['close', 'saved'])

const { showToast } = useToast()

// Estado reactivo
const saving = ref(false)
const showPreview = ref(false)
const activeTab = ref('basic')

// Tabs
const tabs = [
  { id: 'basic', name: 'Información Básica', icon: '📝' },
  { id: 'html', name: 'Editor HTML', icon: '🔧' },
  { id: 'variables', name: 'Variables', icon: '🏷️' }
]

// Formulario
const form = reactive({
  nombre_interno_plantilla: '',
  descripcion: '',
  asunto_predeterminado: '',
  html_content: '',
  tipo_plantilla: '',
  activa: true
})

// Variables disponibles
const availableVariables = [
  { name: '{{lead_name}}', description: 'Nombre del lead' },
  { name: '{{lead_email}}', description: 'Email del lead' },
  { name: '{{lead_phone}}', description: 'Teléfono del lead' },
  { name: '{{property_address}}', description: 'Dirección de la propiedad' },
  { name: '{{property_type}}', description: 'Tipo de propiedad' },
  { name: '{{property_value_min}}', description: 'Valor mínimo estimado' },
  { name: '{{property_value_max}}', description: 'Valor máximo estimado' },
  { name: '{{property_value_range}}', description: 'Rango de valor completo' },
  { name: '{{agency_name}}', description: 'Nombre de la agencia' },
  { name: '{{current_date}}', description: 'Fecha actual' },
  { name: '{{current_month}}', description: 'Mes actual' },
  { name: '{{current_year}}', description: 'Año actual' },
  { name: '{{email_content}}', description: 'Contenido generado por IA (solo secuencias)' }
]

// Computed
const foundVariables = computed(() => {
  const matches = form.html_content.match(/\{\{([^}]+)\}\}/g)
  return matches ? [...new Set(matches)] : []
})

// Watchers
watch(() => props.show, (newVal) => {
  if (newVal) {
    resetForm()
    if (props.isEditing && props.template) {
      loadTemplateData()
    }
    activeTab.value = 'basic'
    showPreview.value = false
  }
})

// Métodos
const resetForm = () => {
  Object.assign(form, {
    nombre_interno_plantilla: '',
    descripcion: '',
    asunto_predeterminado: '',
    html_content: '',
    tipo_plantilla: '',
    activa: true
  })
}

const loadTemplateData = async () => {
  if (!props.template?.id) return

  try {
    const data = await apiFetch(`admin_get_html_template.php?id=${props.template.id}`)

    if (data.success) {
      const template = data.data
      Object.assign(form, {
        nombre_interno_plantilla: template.nombre_interno_plantilla,
        descripcion: template.descripcion,
        asunto_predeterminado: template.asunto_predeterminado,
        html_content: template.html_content,
        tipo_plantilla: template.tipo_plantilla,
        activa: template.activa
      })
    } else {
      showToast('Error cargando plantilla: ' + data.message, 'error')
    }
  } catch (error) {
    showToast('Error de conexión al cargar plantilla', 'error')
    console.error('Error loading template:', error)
  }
}

const handleSubmit = async () => {
  saving.value = true

  try {
    const endpoint = props.isEditing ? 'admin_update_html_template.php' : 'admin_create_html_template.php'
    const method = props.isEditing ? 'PUT' : 'POST'

    const payload = { ...form }
    if (props.isEditing) {
      payload.id = props.template.id
    }

    const data = await apiFetch(endpoint, {
      method,
      body: JSON.stringify(payload)
    })

    if (data.success) {
      showToast(
        props.isEditing ? 'Plantilla actualizada exitosamente' : 'Plantilla creada exitosamente',
        'success'
      )
      emit('saved')
    } else {
      showToast('Error guardando plantilla: ' + data.message, 'error')
    }
  } catch (error) {
    showToast('Error de conexión al guardar plantilla', 'error')
    console.error('Error saving template:', error)
  } finally {
    saving.value = false
  }
}

const insertVariable = () => {
  // Mostrar tab de variables
  activeTab.value = 'variables'
}

const insertVariableIntoEditor = (variableName) => {
  const textarea = document.getElementById('html_content')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = form.html_content

    form.html_content = text.substring(0, start) + variableName + text.substring(end)

    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + variableName.length, start + variableName.length)
    })
  }

  // Volver al tab del editor
  activeTab.value = 'html'
}
</script>
