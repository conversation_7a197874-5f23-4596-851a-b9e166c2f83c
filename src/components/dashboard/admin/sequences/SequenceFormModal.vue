<template>
  <div v-if="show" class="fixed z-20 inset-0 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true" @click="close">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="handleSubmit">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-impacto-blue sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  {{ isEditing ? 'Editar Secuencia' : 'Crear Nueva Secuencia' }}
                </h3>
                <div class="mt-4 space-y-4">
                  <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Nombre</label>
                    <input v-model="form.name" type="text" id="name" class="mt-1 shadow-sm focus:ring-impacto-orange focus:border-impacto-orange block w-full sm:text-sm border-gray-300 rounded-md" required>
                  </div>
                  <div>
                    <label for="description" class="block text-sm font-medium text-gray-700">Descripción</label>
                    <textarea v-model="form.description" id="description" rows="3" class="mt-1 shadow-sm focus:ring-impacto-orange focus:border-impacto-orange block w-full sm:text-sm border-gray-300 rounded-md"></textarea>
                  </div>
                  <div>
                    <label for="trigger_event" class="block text-sm font-medium text-gray-700">Evento de Disparo</label>
                    <input v-model="form.trigger_event" type="text" id="trigger_event" class="mt-1 shadow-sm focus:ring-impacto-orange focus:border-impacto-orange block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Ej: email_confirmed" required>
                    <p class="mt-1 text-xs text-gray-500">Este es el evento que disparará la secuencia. Debe coincidir con el evento registrado en el sistema.</p>
                  </div>
                  <div class="col-span-1">
                    <label for="is_active" class="block text-sm font-medium text-gray-700">Activa</label>
                    <input type="checkbox" id="is_active" v-model="form.is_active" class="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                  </div>
                  <div>
                    <label for="plans" class="block text-sm font-medium text-gray-700">Asignar a Planes</label>
                    <select id="plans" v-model="form.assigned_plan_ids" multiple class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                      <option v-for="plan in plans" :key="plan.id" :value="plan.id">{{ plan.name }}</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Mantén presionado Ctrl (o Cmd en Mac) para seleccionar múltiples planes.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit" :disabled="isSubmitting" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-impacto-blue text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
              {{ isSubmitting ? 'Guardando...' : 'Guardar' }}
            </button>
            <button @click="close" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange sm:mt-0 sm:w-auto sm:text-sm">
              Cancelar
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, type PropType } from 'vue';

interface Sequence {
  id?: number | null;
  name: string;
  description: string;
  trigger_event: string;
  is_active: boolean | number;
  assigned_plan_ids?: number[];
}

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  sequence: {
    type: Object as PropType<Sequence | null>,
    default: null,
  },
  isSubmitting: {
    type: Boolean,
    default: false,
  },
  plans: {
    type: Array as () => any[],
    required: true,
  }
});

const emit = defineEmits(['close', 'save']);

const getInitialFormState = (): Sequence => ({
  name: '',
  description: '',
  trigger_event: '',
  is_active: true,
  assigned_plan_ids: []
});

const form = ref<Sequence>(getInitialFormState());

const isEditing = ref(false);

watch(() => props.sequence, (newVal) => {
  isEditing.value = !!newVal;
  if (newVal) {
    form.value = {
      ...newVal,
      is_active: !!newVal.is_active, // Asegurar que sea booleano
      assigned_plan_ids: newVal.assigned_plan_ids || []
    };
  } else {
    form.value = getInitialFormState();
  }
}, { deep: true, immediate: true });

const close = () => {
  emit('close');
};

const handleSubmit = () => {
  // Aquí podrías añadir validación más compleja si es necesario
  emit('save', { ...form.value });
};

</script>
