<template>
  <div v-if="show" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click.self="close">
    <div class="relative top-5 mx-auto p-6 border w-full max-w-7xl shadow-xl rounded-lg bg-white">
      <div class="mt-3">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-2xl leading-6 font-bold text-gray-900">{{ formTitle }}</h3>
          <button type="button" @click="close" class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Tabs Navigation -->
        <div class="border-b border-gray-200 mb-6">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              type="button"
              @click="activeTab = tab.id"
              :class="[
                'py-3 px-4 border-b-2 font-medium text-sm transition-colors',
                activeTab === tab.id 
                  ? 'border-impacto-blue text-impacto-blue' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              {{ tab.icon }} {{ tab.name }}
            </button>
          </nav>
        </div>

        <form @submit.prevent="submitForm" class="space-y-6">
          <!-- TAB 1: Configuración Básica -->
          <div v-show="activeTab === 'basic'" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Nombre del Paso</label>
                <input 
                  id="name" 
                  v-model="form.name" 
                  type="text" 
                  required 
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                  placeholder="Ej: Seguimiento inicial"
                >
                <p class="text-xs text-gray-500 mt-1">Nombre interno para identificar este paso</p>
              </div>

              <div>
                <label for="email_subject" class="block text-sm font-medium text-gray-700">Asunto Base del Email</label>
                <input 
                  id="email_subject" 
                  v-model="form.email_subject" 
                  type="text" 
                  required 
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                  placeholder="Ej: Información sobre tu valoración"
                >
                <p class="text-xs text-gray-500 mt-1">La IA generará un asunto personalizado basado en este tema</p>
              </div>

              <div>
                <label for="delay_days" class="block text-sm font-medium text-gray-700">Días de Retraso</label>
                <input 
                  id="delay_days" 
                  v-model.number="form.delay_days" 
                  type="number" 
                  min="0" 
                  max="365" 
                  required 
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                >
                <p class="text-xs text-gray-500 mt-1">Días después del paso anterior</p>
              </div>

              <div>
                <label for="step_order" class="block text-sm font-medium text-gray-700">Orden del Paso</label>
                <input 
                  id="step_order" 
                  v-model.number="form.step_order" 
                  type="number" 
                  min="1" 
                  required 
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                >
                <p class="text-xs text-gray-500 mt-1">Posición en la secuencia</p>
              </div>
            </div>

            <!-- Plantilla HTML -->
            <div>
              <label for="email_template_id" class="block text-sm font-medium text-gray-700">Plantilla HTML</label>
              <div class="mt-1 flex rounded-md shadow-sm">
                <select 
                  id="email_template_id" 
                  v-model="form.email_template_id" 
                  class="flex-1 block w-full px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                >
                  <option value="">Seleccionar plantilla...</option>
                  <option 
                    v-for="template in emailTemplates" 
                    :key="template.id" 
                    :value="template.id"
                    :disabled="!template.activa"
                  >
                    {{ template.icon }} {{ template.display_type }} - {{ template.descripcion }}
                  </option>
                </select>
                <button 
                  type="button" 
                  @click="previewTemplate" 
                  :disabled="!form.email_template_id"
                  class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100 disabled:opacity-50 transition-colors"
                >
                  👁️ Vista Previa
                </button>
              </div>
              <p class="text-xs text-gray-500 mt-1">Selecciona la plantilla HTML que se usará como "cascarón" para este paso</p>
            </div>

            <!-- Configuración de IA -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 class="text-sm font-medium text-blue-900 mb-3">🤖 Configuración de IA</h4>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Modelo de IA -->
                <div>
                  <label for="ai_model" class="block text-sm font-medium text-gray-700">Modelo de IA</label>
                  <select
                    id="ai_model"
                    v-model="form.ai_model"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                  >
                    <option value="openai">🔵 OpenAI GPT-4o-mini</option>
                    <option value="gemini">🟢 Google Gemini Pro</option>
                  </select>
                  <p class="text-xs text-gray-500 mt-1">Modelo que generará el contenido del email</p>
                </div>

                <!-- Búsqueda web inteligente -->
                <div>
                  <div class="flex items-center mb-2">
                    <input
                      id="use_web_search"
                      v-model="form.use_web_search"
                      type="checkbox"
                      class="h-4 w-4 text-impacto-blue focus:ring-impacto-blue border-gray-300 rounded"
                    >
                    <label for="use_web_search" class="ml-2 block text-sm font-medium text-gray-700">
                      🌐 Búsqueda web inteligente
                    </label>
                  </div>
                  <div v-if="form.use_web_search" class="bg-green-50 border border-green-200 rounded-lg p-3 mt-2">
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-3">
                        <h4 class="text-sm font-medium text-green-800">Búsqueda automática activada</h4>
                        <div class="mt-1 text-sm text-green-700">
                          <p>El sistema analizará automáticamente:</p>
                          <ul class="list-disc list-inside mt-1 space-y-1">
                            <li>La zona de la propiedad ({{ form.ai_model === 'gemini' ? 'Google Search' : 'Web Search' }})</li>
                            <li>Tipo de propiedad y mercado local</li>
                            <li>Tendencias y precios actualizados</li>
                            <li>Información relevante según el prompt</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">
                    {{ form.use_web_search ? 'La IA generará queries específicos basados en la propiedad y el contexto' : 'Solo usará el conocimiento base del modelo' }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Estado activo -->
            <div class="flex items-center">
              <input
                id="is_active"
                v-model="form.is_active"
                type="checkbox"
                class="h-4 w-4 text-impacto-blue focus:ring-impacto-blue border-gray-300 rounded"
              >
              <label for="is_active" class="ml-2 block text-sm text-gray-900">
                Paso activo
              </label>
              <p class="text-xs text-gray-500 ml-4">Solo los pasos activos se ejecutarán en la secuencia</p>
            </div>
          </div>

          <!-- TAB 2: Prompt de IA -->
          <div v-show="activeTab === 'prompt'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Editor de Prompt -->
              <div class="space-y-4">
                <div>
                  <label for="prompt_template" class="block text-sm font-medium text-gray-700">Plantilla de Prompt para IA</label>
                  <div class="mt-1 relative">
                    <textarea 
                      id="prompt_template" 
                      v-model="form.prompt_template" 
                      rows="15" 
                      required 
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm font-mono text-sm"
                      placeholder="Escribe las instrucciones para la IA..."
                    ></textarea>
                    <div class="absolute bottom-2 right-2 text-xs text-gray-400">
                      {{ form.prompt_template?.length || 0 }} caracteres
                    </div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">
                    Escribe las instrucciones para la IA. Usa variables como <code class="bg-gray-100 px-1 rounded">{{lead_name}}</code>, 
                    <code class="bg-gray-100 px-1 rounded">{{property_address}}</code>, etc.
                  </p>
                </div>

                <!-- Botones de ayuda -->
                <div class="flex flex-wrap gap-2">
                  <button 
                    type="button" 
                    @click="insertPromptTemplate('basic')"
                    class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    📝 Plantilla Básica
                  </button>
                  <button 
                    type="button" 
                    @click="insertPromptTemplate('educational')"
                    class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    🎓 Educativo
                  </button>
                  <button 
                    type="button" 
                    @click="insertPromptTemplate('cta')"
                    class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    🎯 Llamada a la Acción
                  </button>
                </div>
              </div>

              <!-- Panel de Variables -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Variables Disponibles</h4>



                <div class="space-y-3 max-h-96 overflow-y-auto">
                  <template v-for="(category, categoryKey) in variableCategories" :key="categoryKey">
                    <div v-if="category && category.title">
                    <button
                      type="button"
                      @click="toggleCategory(categoryKey)"
                      class="flex items-center justify-between w-full text-left text-xs font-medium text-gray-700 hover:text-gray-900"
                    >
                      <span>{{ category.title }}</span>
                      <svg 
                        :class="['w-4 h-4 transition-transform', expandedCategories[categoryKey] ? 'rotate-180' : '']"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </button>
                    <div v-show="expandedCategories[categoryKey]" class="mt-2 space-y-1">


                      <button
                        v-for="(variable, index) in (category.variables || [])"
                        :key="`${categoryKey}-${index}`"
                        type="button"
                        @click="copyVariableToClipboard(variable.key)"
                        class="block w-full text-left px-2 py-1 text-xs bg-white rounded border hover:bg-gray-50 transition-colors group"
                        :title="`${variable.description || 'Variable'} - Clic para copiar al portapapeles`"
                      >
                        <div class="flex items-center justify-between">
                          <code class="text-impacto-blue">{{ variable.key }}</code>
                          <svg class="h-3 w-3 text-gray-400 group-hover:text-impacto-blue transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div class="text-gray-500 text-xs mt-1">{{ variable.description || 'Variable disponible' }}</div>
                      </button>
                    </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>

          <!-- TAB 3: Testing -->
          <div v-show="activeTab === 'testing'" class="space-y-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 class="text-lg font-medium text-blue-900 mb-2">🧪 Testing Completo del Paso</h4>
              <p class="text-sm text-blue-700">
                Prueba cómo se verá el email final con datos reales de un lead, incluyendo la plantilla HTML y el contenido generado por IA.
              </p>
            </div>

            <!-- Selector de Lead -->
            <div>
              <label for="test_lead_id" class="block text-sm font-medium text-gray-700">Lead de Prueba</label>
              <select 
                id="test_lead_id" 
                v-model="testLeadId" 
                class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
              >
                <option value="">Seleccionar lead para testing...</option>
                <option 
                  v-for="lead in testLeads" 
                  :key="lead.id" 
                  :value="lead.id"
                >
                  {{ lead.nombre }} - {{ lead.property_address }}
                </option>
              </select>
              <p class="text-xs text-gray-500 mt-1">Selecciona un lead con valoración completa para obtener resultados realistas</p>
            </div>

            <!-- Botón de Testing -->
            <div class="flex items-center space-x-4">
              <button 
                type="button" 
                @click="runCompleteTest" 
                :disabled="!testLeadId || !form.email_template_id || !form.prompt_template || testingInProgress"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-impacto-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="testingInProgress" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ testingInProgress ? 'Generando...' : '🚀 Ejecutar Test Completo' }}
              </button>
              
              <div v-if="lastTestResult" class="text-sm text-gray-600">
                Último test: {{ formatDate(lastTestResult.timestamp) }}
              </div>
            </div>

            <!-- Resultados del Testing -->
            <div v-if="testResult" class="space-y-6">
              <!-- Estadísticas -->
              <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div class="bg-white p-4 rounded-lg border border-gray-200">
                  <div class="text-2xl font-bold text-impacto-blue">{{ testResult.statistics.subject_length }}</div>
                  <div class="text-xs text-gray-500">Caracteres en asunto</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-gray-200">
                  <div class="text-2xl font-bold text-green-600">{{ testResult.statistics.content_length }}</div>
                  <div class="text-xs text-gray-500">Caracteres de contenido IA</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-gray-200">
                  <div class="text-2xl font-bold text-purple-600">{{ testResult.statistics.html_length }}</div>
                  <div class="text-xs text-gray-500">Caracteres HTML final</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-gray-200">
                  <div class="text-2xl font-bold text-orange-600">{{ testResult.statistics.variables_used }}</div>
                  <div class="text-xs text-gray-500">Variables reemplazadas</div>
                </div>
                <div class="bg-white p-4 rounded-lg border border-blue-200">
                  <div class="text-lg font-bold text-blue-600">
                    {{ testResult.statistics.ai_model_used?.includes('Gemini') ? '🟢' : '🔵' }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ testResult.statistics.ai_model_used || 'Modelo IA' }}
                  </div>
                  <div v-if="testResult.statistics.web_search_used" class="text-xs text-green-600 mt-1">
                    🌐 Con búsqueda web
                  </div>
                </div>
              </div>

              <!-- Tabs de Resultados -->
              <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                  <button
                    v-for="resultTab in resultTabs"
                    :key="resultTab.id"
                    type="button"
                    @click="activeResultTab = resultTab.id"
                    :class="[
                      'py-2 px-1 border-b-2 font-medium text-sm',
                      activeResultTab === resultTab.id 
                        ? 'border-impacto-blue text-impacto-blue' 
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    ]"
                  >
                    {{ resultTab.icon }} {{ resultTab.name }}
                  </button>
                </nav>
              </div>

              <!-- Contenido de Resultados -->
              <div class="bg-white rounded-lg border border-gray-200 p-4">
                <!-- Asunto Generado -->
                <div v-show="activeResultTab === 'subject'">
                  <h5 class="text-sm font-medium text-gray-900 mb-2">Asunto Generado por IA</h5>
                  <div class="bg-gray-50 p-3 rounded border">
                    <p class="text-sm font-medium">{{ testResult?.ai_generated?.subject || 'No disponible' }}</p>
                  </div>
                  <p class="text-xs text-gray-500 mt-2">
                    Basado en: "{{ form.email_subject }}"
                  </p>
                </div>

                <!-- Contenido IA -->
                <div v-show="activeResultTab === 'content'">
                  <h5 class="text-sm font-medium text-gray-900 mb-2">Contenido Generado por IA</h5>
                  <div class="bg-gray-50 p-3 rounded border max-h-96 overflow-y-auto">
                    <pre class="text-sm whitespace-pre-wrap">{{ testResult?.ai_generated?.content || 'No disponible' }}</pre>
                  </div>
                </div>

                <!-- Vista Previa HTML -->
                <div v-show="activeResultTab === 'preview'">
                  <h5 class="text-sm font-medium text-gray-900 mb-2">Vista Previa del Email Final</h5>
                  <div class="border rounded-lg overflow-hidden">
                    <iframe
                      :srcdoc="testResult?.final_email?.html || '<p>No hay vista previa disponible</p>'"
                      class="w-full h-96"
                      sandbox="allow-same-origin"
                    ></iframe>
                  </div>
                  <div class="mt-2 flex space-x-4 text-xs text-gray-500">
                    <span>Plantilla: {{ testResult?.statistics?.template_name || 'N/A' }}</span>
                    <span>Variables: {{ testResult?.statistics?.variables_used || 0 }}</span>
                  </div>
                </div>

                <!-- Contexto Usado -->
                <div v-show="activeResultTab === 'context'">
                  <h5 class="text-sm font-medium text-gray-900 mb-2">Contexto del Lead y Configuración IA</h5>
                  <div class="space-y-3">
                    <div class="bg-gray-50 p-3 rounded">
                      <div class="text-xs font-medium text-gray-700">Lead</div>
                      <div class="text-sm">{{ testResult?.context_used?.lead_name || 'N/A' }}</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded">
                      <div class="text-xs font-medium text-gray-700">Propiedad</div>
                      <div class="text-sm">{{ testResult?.context_used?.property_address || 'N/A' }}</div>
                    </div>
                    <div class="bg-blue-50 p-3 rounded border border-blue-200">
                      <div class="text-xs font-medium text-blue-700">🤖 Modelo de IA</div>
                      <div class="text-sm font-medium">{{ testResult?.context_used?.ai_model === 'gemini' ? '🟢 Google Gemini Pro' : '🔵 OpenAI GPT-4o-mini' }}</div>
                    </div>
                    <div v-if="testResult?.context_used?.web_search_used" class="bg-green-50 p-3 rounded border border-green-200">
                      <div class="text-xs font-medium text-green-700">🌐 Búsqueda Web Activada</div>
                      <div class="text-sm">{{ testResult?.context_used?.search_query || 'Búsqueda automática' }}</div>
                      <div class="text-xs text-green-600 mt-1">
                        {{ testResult?.context_used?.ai_model === 'gemini' ? 'Usando Google Search API' : 'Usando OpenAI Web Search' }}
                      </div>
                    </div>
                    <div v-else class="bg-gray-50 p-3 rounded">
                      <div class="text-xs font-medium text-gray-700">🌐 Búsqueda Web</div>
                      <div class="text-sm text-gray-500">Desactivada - Solo conocimiento del modelo</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Botones de Acción -->
          <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <button 
              type="button" 
              @click="close" 
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
            >
              Cancelar
            </button>
            
            <div class="flex space-x-3">
              <button 
                v-if="!isEditing"
                type="button" 
                @click="saveAsDraft" 
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue"
              >
                💾 Guardar como Borrador
              </button>
              
              <button 
                type="submit" 
                :disabled="!isFormValid || submitting"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-impacto-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg v-if="submitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ submitting ? 'Guardando...' : (isEditing ? '✅ Actualizar Paso' : '➕ Crear Paso') }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Modal de Vista Previa de Plantilla -->
    <div v-if="showTemplatePreview" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-60" @click.self="closeTemplatePreview">
      <div class="relative top-10 mx-auto p-6 border w-full max-w-4xl shadow-xl rounded-lg bg-white">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-lg font-medium text-gray-900">Vista Previa de Plantilla</h4>
          <button type="button" @click="closeTemplatePreview" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div class="border rounded-lg overflow-hidden">
          <iframe 
            v-if="templatePreviewHtml" 
            :srcdoc="templatePreviewHtml" 
            class="w-full h-96"
            sandbox="allow-same-origin"
          ></iframe>
        </div>
        <p class="text-xs text-gray-500 mt-2">
          Esta es una vista previa con datos de ejemplo. Los datos reales variarán según el lead.
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useToast } from '@/composables/useToast'
import { apiFetch } from '@/utils/apiFetch'

export default {
  name: 'StepFormModalRobust',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    step: {
      type: Object,
      default: null
    },
    sequenceId: {
      type: [String, Number],
      required: true
    }
  },
  emits: ['close', 'step-saved'],
  setup(props, { emit }) {
    const { showToast } = useToast()

    // Estado reactivo
    const activeTab = ref('basic')
    const activeResultTab = ref('subject')
    const submitting = ref(false)
    const testingInProgress = ref(false)
    const showTemplatePreview = ref(false)
    const templatePreviewHtml = ref('')
    const testLeadId = ref('')
    const testResult = ref(null)
    const lastTestResult = ref(null)

    // Datos
    const emailTemplates = ref([])
    const testLeads = ref([])
    const variableCategories = ref({})
    const expandedCategories = reactive({
      lead: true,
      property: false,
      zone: false,
      agency: false,
      date: false,
      calculated: false
    })

    // Formulario
    const form = reactive({
      name: '',
      email_subject: '',
      prompt_template: '',
      delay_days: 0,
      step_order: 1,
      email_template_id: '',
      is_active: true,
      ai_model: 'openai',
      use_web_search: false
    })

    // Configuración de tabs
    const tabs = [
      { id: 'basic', name: 'Configuración', icon: '⚙️' },
      { id: 'prompt', name: 'Prompt IA', icon: '🤖' },
      { id: 'testing', name: 'Testing', icon: '🧪' }
    ]

    const resultTabs = [
      { id: 'subject', name: 'Asunto', icon: '📧' },
      { id: 'content', name: 'Contenido IA', icon: '📝' },
      { id: 'preview', name: 'Vista Previa', icon: '👁️' },
      { id: 'context', name: 'Contexto', icon: '📊' }
    ]

    // Computed
    const formTitle = computed(() => {
      return props.step ? 'Editar Paso de Secuencia' : 'Crear Nuevo Paso'
    })

    const isEditing = computed(() => {
      return !!props.step
    })

    const isFormValid = computed(() => {
      return form.name &&
             form.email_subject &&
             form.prompt_template &&
             form.email_template_id &&
             form.delay_days >= 0 &&
             form.step_order >= 1
    })

    // Métodos
    const close = () => {
      emit('close')
    }

    const loadEmailTemplates = async () => {
      try {
        const data = await apiFetch('admin_get_email_templates.php')
        if (data.success) {
          emailTemplates.value = data.data.templates
        }
      } catch (error) {
        console.error('Error loading templates:', error)
        showToast('Error al cargar plantillas', 'error')
      }
    }

    const loadVariables = async () => {
      try {
        const data = await apiFetch('admin_get_sequence_variables.php')

        if (data.success && data.data && data.data.categories) {
          console.log('[DEBUG] Variables cargadas exitosamente:', Object.keys(data.data.categories))
          console.log('[DEBUG] Total variables por categoría:',
            Object.entries(data.data.categories).map(([key, cat]) =>
              `${key}: ${cat.variables?.length || 0}`
            ).join(', ')
          )

          // Asignar las variables
          variableCategories.value = data.data.categories

          console.log('[DEBUG] Variables cargadas exitosamente:', Object.keys(data.data.categories))
          console.log('[DEBUG] Total variables por categoría:',
            Object.entries(data.data.categories).map(([key, cat]) =>
              `${key}: ${cat.variables?.length || 0}`
            ).join(', ')
          )

          // Verificar reactividad
          setTimeout(() => {
          }, 100)
        } else {
          console.error('[DEBUG] Datos de variables inválidos:', data)
          showToast('Error al cargar variables: datos inválidos', 'error')
        }
      } catch (error) {
        console.error('[DEBUG] Error cargando variables:', error)
        showToast('Error al cargar variables', 'error')
        // Proporcionar variables básicas como fallback
        variableCategories.value = {
          lead: {
            title: '👤 Variables del Lead',
            variables: [
              { key: '{{lead_name}}', description: 'Nombre del lead' },
              { key: '{{lead_email}}', description: 'Email del lead' }
            ]
          }
        }
      }
    }

    const loadTestLeads = async () => {
      try {
        const data = await apiFetch('admin_get_leads_for_testing.php')
        if (data.success) {
          testLeads.value = data.data.leads
        }
      } catch (error) {
        console.error('Error loading test leads:', error)
      }
    }

    const previewTemplate = async () => {
      if (!form.email_template_id) return

      try {
        const data = await apiFetch(`admin_preview_template.php?template_id=${form.email_template_id}`)
        if (data.success) {
          templatePreviewHtml.value = data.data.html
          showTemplatePreview.value = true
        } else {
          showToast('Error al generar vista previa', 'error')
        }
      } catch (error) {
        console.error('Error previewing template:', error)
        showToast('Error al cargar vista previa', 'error')
      }
    }

    const closeTemplatePreview = () => {
      showTemplatePreview.value = false
      templatePreviewHtml.value = ''
    }

    const toggleCategory = (categoryKey) => {
      try {
        expandedCategories[categoryKey] = !expandedCategories[categoryKey]
      } catch (error) {
        console.error('Error toggling category:', error)
      }
    }

    const copyVariableToClipboard = async (variable) => {
      try {
        await navigator.clipboard.writeText(variable)
        showToast(`Variable ${variable} copiada al portapapeles`, 'success')
      } catch (error) {
        console.error('Error copying to clipboard:', error)
        // Fallback para navegadores que no soportan clipboard API
        try {
          const textArea = document.createElement('textarea')
          textArea.value = variable
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          showToast(`Variable ${variable} copiada al portapapeles`, 'success')
        } catch (fallbackError) {
          showToast('Error copiando variable. Cópiala manualmente: ' + variable, 'error')
        }
      }
    }

    // Mantener función legacy para compatibilidad
    const insertVariable = copyVariableToClipboard

    const insertPromptTemplate = (type) => {
      const templates = {
        basic: `Eres un experto agente inmobiliario escribiendo a {{lead_name}} sobre su propiedad en {{property_address}}.

CONTEXTO:
- Propiedad valorada: {{property_value_min}} - {{property_value_max}}
- Inmobiliaria: {{nombre_agencia}}

INSTRUCCIONES:
1. INICIA con saludo: "Estimado/a {{lead_name}}," o "Hola {{lead_name}},"
2. [Describe el objetivo específico de este email]
3. [Incluye información relevante y útil]
4. Mantén un tono profesional pero cercano
5. Máximo 200 palabras
6. NO incluyas despedida formal (se añade automáticamente)

Objetivo: [Define el objetivo específico de este paso]`,

        educational: `Eres un experto agente inmobiliario escribiendo a {{lead_name}} con información educativa sobre el mercado inmobiliario.

CONTEXTO:
- Propiedad en {{property_address}}
- Valoración: {{property_value_range}}
- Zona: {{zone_name}}
- Precio promedio zona: {{zone_avg_price_m2}}

INSTRUCCIONES:
1. INICIA con saludo: "Estimado/a {{lead_name}}," o "Hola {{lead_name}},"
2. Comparte insights valiosos sobre el mercado local
3. Incluye datos específicos de la zona
4. Explica tendencias actuales del mercado
5. Ofrece consejos prácticos y accionables
6. Tono: experto pero accesible
7. Máximo 250 palabras
8. NO incluyas despedida formal

Objetivo: Educar y posicionarte como experto del mercado local.`,

        cta: `Eres un experto agente inmobiliario escribiendo a {{lead_name}} para generar una acción específica.

CONTEXTO:
- Ha recibido información previa sobre su propiedad
- Valoración: {{property_value_range}}
- Inmobiliaria: {{nombre_agencia}}

INSTRUCCIONES:
1. INICIA con saludo: "Estimado/a {{lead_name}}," o "Hola {{lead_name}},"
2. Recapitula brevemente el valor proporcionado anteriormente
3. Presenta una oferta o propuesta específica
4. Incluye beneficios claros y urgencia sutil
5. Menciona que no hay compromiso
6. Tono: profesional, servicial, sin presión
7. Máximo 200 palabras
8. NO incluyas despedida formal

Objetivo: Convertir el lead nutrido en una acción concreta.`
      }

      form.prompt_template = templates[type] || ''
    }

    const runCompleteTest = async () => {
      if (!testLeadId.value || !form.email_template_id || !form.prompt_template) {
        showToast('Completa todos los campos requeridos para el testing', 'warning')
        return
      }

      testingInProgress.value = true
      testResult.value = null

      try {
        const data = await apiFetch('admin_test_sequence_step.php', {
          method: 'POST',
          body: JSON.stringify({
            prompt_template: form.prompt_template,
            lead_id: testLeadId.value,
            email_template_id: form.email_template_id,
            base_subject: form.email_subject,
            ai_model: form.ai_model,
            use_web_search: form.use_web_search
          })
        })

        if (data.success) {
          // Asegurar que todas las propiedades existen
          testResult.value = {
            ai_generated: data.data.ai_generated || { subject: '', content: '' },
            final_email: data.data.final_email || { subject: '', html: '' },
            context_used: data.data.context_used || {},
            statistics: data.data.statistics || {},
            variables_replaced: data.data.variables_replaced || []
          }
          lastTestResult.value = {
            timestamp: new Date(),
            success: true
          }
          activeResultTab.value = 'subject'
          showToast('Test completado exitosamente', 'success')
        } else {
          showToast('Error en el testing: ' + data.message, 'error')
        }
      } catch (error) {
        console.error('Error running test:', error)
        showToast('Error al ejecutar el test', 'error')
      } finally {
        testingInProgress.value = false
      }
    }

    const formatDate = (date) => {
      return new Intl.DateTimeFormat('es-ES', {
        hour: '2-digit',
        minute: '2-digit',
        day: '2-digit',
        month: '2-digit'
      }).format(date)
    }

    const saveAsDraft = async () => {
      // Implementar guardado como borrador
      form.is_active = false
      await submitForm()
    }

    const submitForm = async () => {
      if (!isFormValid.value) {
        showToast('Por favor completa todos los campos requeridos', 'warning')
        return
      }

      submitting.value = true

      try {
        const url = isEditing.value
          ? 'admin_update_sequence_step.php'
          : 'admin_create_sequence_step.php'

        const payload = {
          ...form,
          sequence_id: props.sequenceId
        }

        if (isEditing.value) {
          payload.uuid = props.step.uuid
        }

        const data = await apiFetch(url, {
          method: isEditing.value ? 'PUT' : 'POST',
          body: JSON.stringify(payload)
        })

        if (data.success) {
          showToast(isEditing.value ? 'Paso actualizado exitosamente' : 'Paso creado exitosamente', 'success')
          emit('step-saved', data.data)
          close()
        } else {
          showToast('Error: ' + data.message, 'error')
        }
      } catch (error) {
        console.error('Error saving step:', error)
        showToast('Error al guardar el paso', 'error')
      } finally {
        submitting.value = false
      }
    }

    // Watchers
    watch(() => props.show, (newValue, oldValue) => {
      if (newValue && !oldValue) { // Solo cuando se abre por primera vez
        loadEmailTemplates()
        loadVariables()
        loadTestLeads()

        if (props.step) {
          // Cargar datos del paso para edición
          Object.assign(form, {
            name: props.step.name || '',
            email_subject: props.step.email_subject || '',
            prompt_template: props.step.prompt_template || '',
            delay_days: props.step.delay_days || 0,
            step_order: props.step.step_order || 1,
            email_template_id: props.step.email_template_id || '',
            is_active: props.step.is_active !== false,
            ai_model: props.step.ai_model || 'openai',
            use_web_search: props.step.use_web_search || false
          })
        } else {
          // Resetear formulario para nuevo paso
          Object.assign(form, {
            name: '',
            email_subject: '',
            prompt_template: '',
            delay_days: 0,
            step_order: 1,
            email_template_id: '',
            is_active: true,
            ai_model: 'openai',
            use_web_search: false
          })
        }

        // Resetear estado solo al abrir por primera vez
        activeTab.value = 'basic'
        testResult.value = null
        testLeadId.value = ''
      }
    })

    return {
      // Estado
      activeTab,
      activeResultTab,
      submitting,
      testingInProgress,
      showTemplatePreview,
      templatePreviewHtml,
      testLeadId,
      testResult,
      lastTestResult,

      // Datos
      emailTemplates,
      testLeads,
      variableCategories,
      expandedCategories,
      form,
      tabs,
      resultTabs,

      // Computed
      formTitle,
      isEditing,
      isFormValid,

      // Métodos
      close,
      previewTemplate,
      closeTemplatePreview,
      toggleCategory,
      copyVariableToClipboard,
      insertVariable,
      insertPromptTemplate,
      runCompleteTest,
      formatDate,
      saveAsDraft,
      submitForm
    }
  }
}
</script>

<style scoped>
.z-60 {
  z-index: 60;
}

/* Estilos para el editor de código */
textarea[id="prompt_template"] {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  tab-size: 2;
}

/* Animaciones suaves */
.transition-colors {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

/* Scrollbar personalizado */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
