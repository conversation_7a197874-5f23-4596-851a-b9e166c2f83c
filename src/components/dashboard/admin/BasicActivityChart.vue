<template>
  <div class="w-full h-full p-2">
    <svg v-if="data && data.length > 1" :viewBox="`0 0 ${svgWidth} ${svgHeight}`" class="w-full h-full">
      <!-- <PERSON><PERSON>s (simplificado) -->
      <line :x1="padding" :y1="svgHeight - padding" :x2="svgWidth - padding" :y2="svgHeight - padding" stroke="#cbd5e1" stroke-width="1" />
      <line :x1="padding" :y1="padding" :x2="padding" :y2="svgHeight - padding" stroke="#cbd5e1" stroke-width="1" />

      <!-- Leyenda -->
      <g transform="translate(20, 10)">
        <rect x="0" y="0" width="10" height="10" fill="#3b82f6" />
        <text x="15" y="9" font-size="10" fill="#4b5563">Usuarios</text>
        <rect x="70" y="0" width="10" height="10" fill="#10b981" />
        <text x="85" y="9" font-size="10" fill="#4b5563">Valoraciones</text>
        <rect x="160" y="0" width="10" height="10" fill="#f59e0b" />
        <text x="175" y="9" font-size="10" fill="#4b5563">Leads</text>
      </g>

      <!-- Usuarios Path -->
      <path :d="generatePath(data, 'newUsers')" stroke="#3b82f6" stroke-width="2" fill="none" />
      <!-- Valoraciones Path -->
      <path :d="generatePath(data, 'newValoraciones')" stroke="#10b981" stroke-width="2" fill="none" />
      <!-- Leads Path -->
      <path :d="generatePath(data, 'newLeads')" stroke="#f59e0b" stroke-width="2" fill="none" />

       <!-- Puntos y Tooltips (simplificado) -->
      <g v-for="(type) in ['newUsers', 'newValoraciones', 'newLeads']" :key="type">
        <circle 
          v-for="(point, index) in data" 
          :key="`${type}-${index}`"
          :cx="getX(index)"
          :cy="getY(point[type as keyof ActivityChartDataPoint] as number)"
          r="3"
          :fill="getColor(type)"
          class="cursor-pointer"
          @mouseenter="showTooltip($event, point, type)"
          @mouseleave="hideTooltip"
        />
      </g>

       <!-- Tooltip -->
      <g v-if="tooltip.visible" :transform="`translate(${tooltip.x}, ${tooltip.y - 10})`">
        <rect x="-35" y="-20" width="70" height="22" rx="3" fill="rgba(0,0,0,0.7)" />
        <text x="0" y="-5" font-size="10" fill="white" text-anchor="middle">
          {{ tooltip.text }}
        </text>
      </g>

    </svg>
    <div v-else-if="data && data.length <= 1" class="flex items-center justify-center h-full text-gray-500">
      No hay suficientes datos para dibujar el gráfico.
    </div>
    <div v-else class="flex items-center justify-center h-full text-gray-500">
      Cargando datos del gráfico...
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';

interface ActivityChartDataPoint {
  date: string;
  newUsers: number;
  newValoraciones: number;
  newLeads: number;
}

const props = defineProps<{
  chartData: ActivityChartDataPoint[];
}>();

const svgWidth = 500;
const svgHeight = 250;
const padding = 30; // Espacio para ejes y etiquetas

const data = computed(() => props.chartData);

const maxValue = computed(() => {
  if (!data.value || data.value.length === 0) return 0;
  let max = 0;
  data.value.forEach(point => {
    max = Math.max(max, point.newUsers, point.newValoraciones, point.newLeads);
  });
  return max === 0 ? 10 : max; // Evitar división por cero, y dar espacio si todo es 0
});

const getX = (index: number): number => {
  if (data.value.length <= 1) return padding;
  return padding + index * (svgWidth - 2 * padding) / (data.value.length - 1);
};

const getY = (value: number): number => {
  return svgHeight - padding - (value / maxValue.value) * (svgHeight - 2 * padding);
};

const getColor = (type: string): string => {
  if (type === 'newUsers') return '#3b82f6';
  if (type === 'newValoraciones') return '#10b981';
  if (type === 'newLeads') return '#f59e0b';
  return '#000000';
};

const generatePath = (dataset: ActivityChartDataPoint[], key: keyof ActivityChartDataPoint): string => {
  if (!dataset || dataset.length === 0) return '';
  let path = `M ${getX(0)} ${getY(dataset[0][key] as number)}`;
  dataset.forEach((point, index) => {
    if (index > 0) {
      path += ` L ${getX(index)} ${getY(point[key] as number)}`;
    }
  });
  return path;
};

// Tooltip state
const tooltip = ref({
  visible: false,
  text: '',
  x: 0,
  y: 0
});

const showTooltip = (event: MouseEvent, point: ActivityChartDataPoint, type: string) => {
  const typeTextMap: Record<string, string> = {
      newUsers: 'Usuarios',
      newValoraciones: 'Valoraciones',
      newLeads: 'Leads'
  };
  tooltip.value = {
    visible: true,
    text: `${typeTextMap[type]}: ${point[type as keyof ActivityChartDataPoint]} (${point.date})`,
    x: event.offsetX - (event.target as SVGCircleElement).closest('svg')!.getBoundingClientRect().left + (event.target as SVGCircleElement).cx.baseVal.value,
    y: event.offsetY - (event.target as SVGCircleElement).closest('svg')!.getBoundingClientRect().top + (event.target as SVGCircleElement).cy.baseVal.value - 15,
  };
};

const hideTooltip = () => {
  tooltip.value.visible = false;
};

</script>

<style scoped>
/* Puedes añadir estilos aquí si es necesario */
</style> 