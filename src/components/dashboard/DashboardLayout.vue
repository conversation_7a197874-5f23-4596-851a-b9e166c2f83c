<template>
  <div class="min-h-screen flex bg-gray-50">
    <!-- Sidebar -->
    <aside class="w-64 bg-gradient-to-b from-impacto-blue to-impacto-blue/95 text-white flex flex-col py-6 px-4 shadow-lg backdrop-blur-md relative overflow-hidden">
      <!-- Patrón de fondo decorativo -->
      <div class="absolute inset-0 opacity-5">
        <div class="absolute top-0 left-0 w-full h-full bg-pattern-dots opacity-10"></div>
      </div>
      
      <div class="relative z-10 flex items-center gap-2 mb-10">
        <img src="/img/logo-impacto-automation-valorador-white.svg" alt="Logo Impacto" class="h-10" />
        <span class="font-bold text-xl tracking-tight">Valorador IA</span>
      </div>
      <nav class="relative z-10 flex-1 flex flex-col gap-3">
        <RouterLink to="/dashboard" class="dashboard-link group" active-class="active" exact>
          <div class="relative p-3 rounded-lg transition-all duration-300 ease-in-out">
            <div class="flex items-center gap-3">
              <div class="p-2 rounded-lg bg-white/10 backdrop-blur-md border border-white/10 shadow-lg transition-all duration-300 ease-in-out group-hover:bg-white/20 group-hover:border-impacto-orange/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white group-hover:text-impacto-orange transition-colors duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>
              <span class="font-medium tracking-wide group-hover:text-impacto-orange transition-colors duration-300 ease-in-out">Inicio</span>
            </div>
          </div>
        </RouterLink>
        
        <RouterLink to="/configuracion" class="dashboard-link group" active-class="active">
          <div class="relative p-3 rounded-lg transition-all duration-300 ease-in-out">
            <div class="flex items-center gap-3">
              <div class="p-2 rounded-lg bg-white/10 backdrop-blur-md border border-white/10 shadow-lg transition-all duration-300 ease-in-out group-hover:bg-white/20 group-hover:border-impacto-orange/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white group-hover:text-impacto-orange transition-colors duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <span class="font-medium tracking-wide group-hover:text-impacto-orange transition-colors duration-300 ease-in-out">Configuración valorador</span>
            </div>
          </div>
        </RouterLink>
        
        <RouterLink to="/suscripcion" class="dashboard-link group" active-class="active">
          <div class="relative p-3 rounded-lg transition-all duration-300 ease-in-out">
            <div class="flex items-center gap-3">
              <div class="p-2 rounded-lg bg-white/10 backdrop-blur-md border border-white/10 shadow-lg transition-all duration-300 ease-in-out group-hover:bg-white/20 group-hover:border-impacto-orange/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white group-hover:text-impacto-orange transition-colors duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <span class="font-medium tracking-wide group-hover:text-impacto-orange transition-colors duration-300 ease-in-out">Suscripción</span>
            </div>
          </div>
        </RouterLink>
        
        <RouterLink to="/valoraciones" class="dashboard-link group" active-class="active">
          <div class="relative p-3 rounded-lg transition-all duration-300 ease-in-out">
            <div class="flex items-center gap-3">
              <div class="p-2 rounded-lg bg-white/10 backdrop-blur-md border border-white/10 shadow-lg transition-all duration-300 ease-in-out group-hover:bg-white/20 group-hover:border-impacto-orange/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white group-hover:text-impacto-orange transition-colors duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <span class="font-medium tracking-wide group-hover:text-impacto-orange transition-colors duration-300 ease-in-out">Valoraciones</span>
            </div>
          </div>
        </RouterLink>
        
        <RouterLink to="/leads" class="dashboard-link group" active-class="active">
          <div class="relative p-3 rounded-lg transition-all duration-300 ease-in-out">
            <div class="flex items-center gap-3">
              <div class="p-2 rounded-lg bg-white/10 backdrop-blur-md border border-white/10 shadow-lg transition-all duration-300 ease-in-out group-hover:bg-white/20 group-hover:border-impacto-orange/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white group-hover:text-impacto-orange transition-colors duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <span class="font-medium tracking-wide group-hover:text-impacto-orange transition-colors duration-300 ease-in-out">Leads</span>
            </div>
          </div>
        </RouterLink>
        
        <RouterLink to="/emails" class="dashboard-link group" active-class="active">
          <div class="relative p-3 rounded-lg transition-all duration-300 ease-in-out">
            <div class="flex items-center gap-3">
              <div class="p-2 rounded-lg bg-white/10 backdrop-blur-md border border-white/10 shadow-lg transition-all duration-300 ease-in-out group-hover:bg-white/20 group-hover:border-impacto-orange/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white group-hover:text-impacto-orange transition-colors duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <span class="font-medium tracking-wide group-hover:text-impacto-orange transition-colors duration-300 ease-in-out">Emails enviados</span>
            </div>
          </div>
        </RouterLink>
      </nav>
      <button @click="logout" class="mt-8 bg-impacto-orange text-white px-6 py-3 rounded-lg shadow-lg font-medium hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-impacto-orange focus:ring-offset-2 focus:ring-offset-impacto-blue transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
        </svg>
        Cerrar sesión
      </button>
    </aside>
    <!-- Main Content -->
    <section class="flex-1 flex flex-col">
      <header class="bg-white/80 backdrop-blur-md shadow-md px-8 py-4 flex items-center justify-between sticky top-0 z-10">
        <h1 class="text-xl font-bold text-impacto-blue">{{ pageTitle }}</h1>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">{{ userName }}</span>
          <button @click="logout" class="text-gray-600 hover:text-impacto-blue transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </header>
      <main class="flex-1 p-8 bg-gray-50">
        <router-view />
      </main>
    </section>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth';
import { useRouter, useRoute } from 'vue-router';
import { computed } from 'vue';

const auth = useAuthStore();
const router = useRouter();
const route = useRoute();

function logout() {
  auth.logout();
  router.push('/login');
}

// Obtener el nombre de usuario del store de autenticación
const userName = computed(() => {
  return auth.user?.name || auth.user?.email || 'Usuario';
});

const pageTitle = computed(() => {
  switch (route.name) {
    case 'DashboardHome':
      return 'Panel de control';
    case 'ConfiguracionValorador':
      return 'Configuración del valorador';
    case 'Suscripcion':
      return 'Suscripción';
    case 'Valoraciones':
      return 'Valoraciones realizadas';
    case 'Leads':
      return 'Leads';
    case 'Emails':
      return 'Emails enviados';
    default:
      return 'Panel de control';
  }
});
</script>

<style scoped>
.dashboard-link {
  display: block;
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}

.dashboard-link.active .p-3,
.dashboard-link.router-link-exact-active .p-3 {
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 3px solid #FF6B00; /* impacto-orange */
}

.dashboard-link.active span,
.dashboard-link.router-link-exact-active span {
  color: #FF6B00; /* impacto-orange */
  font-weight: 600;
}

.dashboard-link.active svg,
.dashboard-link.router-link-exact-active svg {
  color: #FF6B00 !important; /* impacto-orange */
}

.dashboard-link.active .bg-white\/10,
.dashboard-link.router-link-exact-active .bg-white\/10 {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 107, 0, 0.5); /* impacto-orange */
}

/* Patrón de puntos para el fondo */
.bg-pattern-dots {
  background-image: radial-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
