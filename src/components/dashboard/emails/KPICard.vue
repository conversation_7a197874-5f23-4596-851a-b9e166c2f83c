<template>
  <div class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-2xl transition-all duration-300 ease-in-out transform hover:-translate-y-1">
    <div class="flex items-start justify-between mb-1">
      <p class="text-sm font-medium text-gray-500 truncate" :title="title">{{ title }}</p>
      <div v-if="trend" class="flex items-center text-xs" :class="trend === 'up' ? 'text-green-600' : 'text-red-600'">
        <svg v-if="trend === 'up'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2.5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2.5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
        <span class="font-semibold">{{ trend_value }}</span>
      </div>
    </div>
    <h3 class="text-3xl font-bold text-[#051f33] mb-2">{{ value }}</h3>
    <div v-if="insight || insight_highlight" class="text-xs text-gray-500">
      <p v-if="insight">{{ insight }}</p>
      <p 
        v-if="insight_highlight" 
        :class="{
          'text-green-700 bg-green-50 p-1.5 rounded-md': insight_type === 'positive',
          'text-red-700 bg-red-50 p-1.5 rounded-md': insight_type === 'negative',
          'text-sky-700 bg-sky-50 p-1.5 rounded-md': insight_type === 'info',
          'text-yellow-700 bg-yellow-50 p-1.5 rounded-md': insight_type === 'neutral' || !insight_type,
        }"
        class="mt-1"
      >
        {{ insight_highlight }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { computed } from 'vue'; // Eliminado

interface Props {
  title: string;
  value: string | number;
  period?: string;
  trend?: 'up' | 'down';
  trend_value?: string;
  insight?: string;
  insight_type?: 'positive' | 'negative' | 'info' | 'neutral';
  insight_highlight?: string;
  is_percentage?: boolean;
}

const props = defineProps<Props>();

// Acceder a una propiedad para marcar 'props' como utilizado
if (props.title) {
  // No es necesario hacer nada aquí, solo acceder a la propiedad
}

// const percentageChange = computed(() => { // Eliminado
//   // ... existing code ...
// });
</script>

<style scoped>
/* Puedes añadir estilos específicos para las tarjetas de KPI aquí */
.hover\:-translate-y-1:hover {
  transform: translateY(-0.25rem);
}
</style> 