<template>
  <div>
    <div v-if="isLoading" class="text-center py-20 bg-white rounded-xl shadow-md">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto" :style="{ borderColor: '#ed8725' }"></div>
      <p class="mt-4 text-lg font-semibold text-gray-700">Cargando actividad...</p>
      <p class="text-sm text-gray-500">Por favor, espera un momento.</p>
    </div>

    <div v-else-if="errorLoading && !isEmailModalVisible" class="bg-red-50 border-l-4 border-red-600 text-red-800 p-6 rounded-xl shadow-md">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <AlertTriangle class="h-8 w-8 text-red-600" />
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-bold">Error al cargar la actividad</h3>
          <p class="text-sm mt-1">{{ errorLoading }}</p>
          <button @click="fetchEmailsLog()" class="mt-3 px-3 py-1.5 text-sm bg-red-700 text-white rounded-md hover:bg-red-800 transition-colors font-medium">Reintentar</button>
        </div>
      </div>
    </div>

    <div v-else-if="totalAgencyEmailCount === 0" class="p-8 rounded-xl shadow-md text-center" style="background-color: #fffaf0; border: 1px solid #f0e4d0;">
       <MailX class="h-16 w-16 mx-auto mb-4 opacity-80" style="color: #051f33;" />
      <h3 class="text-xl font-semibold mb-2" :style="{ color: '#051f33' }">No hay actividad de emails</h3>
      <p class="text-gray-600 max-w-md mx-auto">
        Cuando se envíen o programen emails para tus leads, aparecerán listados aquí.
      </p>
    </div>

    <div v-else class="bg-white shadow-xl rounded-lg overflow-hidden">
      <!-- Sección de Estadísticas Básicas -->
      <div v-if="basicStats && !isLoading" class="px-6 py-5 border-b border-gray-200 bg-gray-50/50">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
          
          <!-- Tarjeta: Emails Enviados (Mes) -->
          <div class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 flex flex-col justify-between">
            <div>
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-impacto-blue-light p-2.5 rounded-lg">
                  <Send class="w-6 h-6 text-impacto-blue" />
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-medium text-gray-500 truncate">Emails enviados (mes)</h4>
                  <p class="mt-1 text-3xl font-semibold text-impacto-blue">{{ basicStats.emailsSentThisMonth.value ?? '-' }}</p>
                </div>
              </div>
            </div>
            <div v-if="basicStats.emailsSentThisMonth.changePercent !== null" class="mt-2 text-xs flex items-center" :class="getChangeIndicator(basicStats.emailsSentThisMonth.changePercent).class">
              <span class="mr-1">{{ getChangeIndicator(basicStats.emailsSentThisMonth.changePercent).icon }}</span>
              <span>{{ getChangeIndicator(basicStats.emailsSentThisMonth.changePercent).text }} vs mes anterior</span>
            </div>
            <div v-else class="mt-2 text-xs text-gray-400"><span>&nbsp;</span></div> <!-- Placeholder for height consistency -->
          </div>

          <!-- Tarjeta: Leads en Nutrición Activa -->
          <div class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 flex flex-col justify-between">
            <div>
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 p-2.5 rounded-lg">
                   <Users class="w-6 h-6 text-green-600" />
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-medium text-gray-500 truncate">Leads en nutrición activa</h4>
                  <p class="mt-1 text-3xl font-semibold text-green-600">{{ basicStats.activeNurturingLeads.value ?? '-' }}</p>
                </div>
              </div>
            </div>
            <div v-if="basicStats.activeNurturingLeads.changePercent !== null" class="mt-2 text-xs flex items-center" :class="getChangeIndicator(basicStats.activeNurturingLeads.changePercent).class">
              <span class="mr-1">{{ getChangeIndicator(basicStats.activeNurturingLeads.changePercent).icon }}</span>
              <span>{{ getChangeIndicator(basicStats.activeNurturingLeads.changePercent).text }} vs mes anterior</span>
            </div>
            <div v-else class="mt-2 text-xs text-gray-400"><span>&nbsp;</span></div>
          </div>

          <!-- Tarjeta: Emails Programados -->
          <div class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 flex flex-col justify-between">
            <div>
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-100 p-2.5 rounded-lg">
                  <Clock class="w-6 h-6 text-yellow-600" />
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-medium text-gray-500 truncate">Emails programados</h4>
                  <p class="mt-1 text-3xl font-semibold text-yellow-600">{{ basicStats.totalScheduledEmails.value ?? '-' }}</p>
                </div>
              </div>
            </div>
            <div v-if="basicStats.totalScheduledEmails.changePercent !== null" class="mt-2 text-xs flex items-center" :class="getChangeIndicator(basicStats.totalScheduledEmails.changePercent).class">
              <span class="mr-1">{{ getChangeIndicator(basicStats.totalScheduledEmails.changePercent).icon }}</span>
              <span>{{ getChangeIndicator(basicStats.totalScheduledEmails.changePercent).text }} vs mes anterior</span>
            </div>
            <div v-else class="mt-2 text-xs text-gray-400"><span>&nbsp;</span></div>
          </div>

          <!-- Tarjeta: Leads Contactados (Mes) -->
          <div class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 flex flex-col justify-between">
            <div>
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-purple-100 p-2.5 rounded-lg">
                  <Contact class="w-6 h-6 text-purple-600" />
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-medium text-gray-500 truncate">Leads contactados (mes)</h4>
                  <p class="mt-1 text-3xl font-semibold text-purple-600">{{ basicStats.uniqueLeadsContactedThisMonth.value ?? '-' }}</p>
                </div>
              </div>
            </div>
             <div v-if="basicStats.uniqueLeadsContactedThisMonth.changePercent !== null" class="mt-2 text-xs flex items-center" :class="getChangeIndicator(basicStats.uniqueLeadsContactedThisMonth.changePercent).class">
              <span class="mr-1">{{ getChangeIndicator(basicStats.uniqueLeadsContactedThisMonth.changePercent).icon }}</span>
              <span>{{ getChangeIndicator(basicStats.uniqueLeadsContactedThisMonth.changePercent).text }} vs mes anterior</span>
            </div>
            <div v-else class="mt-2 text-xs text-gray-400"><span>&nbsp;</span></div>
          </div>

        </div>
      </div>

      <!-- Filtros de Estado -->
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50/50">
        <div class="flex items-center space-x-4">
          <span class="text-sm font-medium text-gray-700">Filtrar por estado:</span>
          <div class="flex flex-wrap gap-2 rounded-lg p-1">
            <button
              v-for="filter in statusFilters"
              :key="filter.value"
              @click="changeStatusFilter(filter.value)"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue',
                currentStatusFilter === filter.value
                  ? 'bg-impacto-blue text-white shadow'
                  : 'text-gray-600 bg-gray-100 hover:bg-gray-200'
              ]"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table v-if="emailsLog.length > 0" class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lead</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email del lead</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asunto</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fecha de envío</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="logItem in emailsLog" :key="logItem.id" class="hover:bg-gray-50 transition-colors duration-150">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                <router-link :to="{ name: 'DashboardLeadDetail', params: { id: logItem.lead_id } }" class="text-impacto-blue hover:underline">
                  {{ logItem.lead_nombre }}
                </router-link>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ logItem.lead_email }}</td>
              <td class="px-6 py-4 text-sm text-gray-700 max-w-xs truncate">
                <button @click="openEmailContentModal(logItem)" class="text-impacto-blue hover:underline focus:outline-none w-full text-left" :title="logItem.asunto_final || 'Ver contenido del email'">
                  {{ logItem.asunto_final || '---' }}
                </button>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span v-if="logItem.fecha_envio_real">{{ formatSimpleDateTime(logItem.fecha_envio_real) }}</span>
                <span v-else-if="logItem.fecha_programada_envio">{{ formatSimpleDateTime(logItem.fecha_programada_envio) }}</span>
                <span v-else>N/A</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                 <span :class="getEmailEstadoClass(logItem.estado_envio)" class="px-2 py-0.5 rounded-full text-xs font-medium inline-block">
                  {{ formatEmailEstado(logItem.estado_envio) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button @click="openEmailContentModal(logItem)" class="text-impacto-blue hover:text-impacto-orange p-1 rounded-md hover:bg-blue-50 transition-colors" title="Ver email y acciones">
                  <Eye class="h-5 w-5" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div v-else class="text-center p-12 bg-gray-50/50">
          <SearchX class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-lg font-medium text-gray-800">No se encontraron resultados</h3>
          <p class="mt-1 text-sm text-gray-500">No hay emails que coincidan con el filtro seleccionado.</p>
        </div>
      </div>
      <div v-if="emailsLog.length > 0 && totalPages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button @click="prevPage()" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
            Anterior
          </button>
          <button @click="nextPage()" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
            Siguiente
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Mostrando <span class="font-medium">{{ (currentPage - 1) * itemsPerPage + 1 }}</span>
              a <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, totalItems) }}</span>
              de <span class="font-medium">{{ totalItems }}</span> resultados
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button @click="prevPage()" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                <span class="sr-only">Anterior</span>
                <ChevronLeft class="h-5 w-5" />
              </button>
              <button @click="nextPage()" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50">
                <span class="sr-only">Siguiente</span>
                <ChevronRight class="h-5 w-5" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <div v-if="isEmailModalVisible && currentOpenEmailLog" 
         class="fixed inset-0 z-50 overflow-y-auto bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 transition-opacity duration-300 ease-in-out" 
         @click.self="closeEmailContentModal">
      <div class="bg-slate-50 rounded-xl shadow-2xl overflow-hidden w-full max-w-5xl transform transition-all duration-300 ease-in-out scale-100" 
           :class="{ 'scale-95 opacity-0': !isEmailModalVisible, 'scale-100 opacity-100': isEmailModalVisible }">
        
        <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-white">
          <h3 class="text-xl font-semibold text-gray-800 truncate" :title="currentOpenEmailLog.asunto_final || 'Detalle del Email'">
            <span v-if="currentOpenEmailLog.asunto_final">{{ currentOpenEmailLog.asunto_final }}</span>
            <span v-else-if="isLoadingEmailContent">Cargando asunto...</span>
            <span v-else>Detalle del email</span>
          </h3>
          <button @click="closeEmailContentModal" class="text-gray-400 hover:text-red-500 focus:outline-none p-1 rounded-full hover:bg-red-100 transition-colors">
            <X class="h-6 w-6" />
          </button>
        </div>

        <div class="p-6 grid grid-cols-1 md:grid-cols-3 gap-6 max-h-[calc(90vh-150px)] overflow-y-auto">
          <!-- Columna Izquierda: Paneles Informativos -->
          <div class="md:col-span-1 flex flex-col gap-y-5">
            <!-- Panel Destinatario -->
            <div class="bg-white p-4 rounded-lg shadow-md flex flex-col">
              <h4 class="text-base font-semibold text-gray-700 mb-3 flex items-center">
                <User class="h-5 w-5 mr-2 text-impacto-blue" />
                Destinatario
              </h4>
              <div class="text-sm space-y-1.5 text-gray-600 flex-grow">
                <p><strong>Lead:</strong> 
                  <router-link :to="{ name: 'DashboardLeadDetail', params: { id: currentOpenEmailLog.lead_id } }" class="text-impacto-blue hover:underline font-medium">
                    {{ currentOpenEmailLog.lead_nombre }}
                  </router-link>
                </p>
                <p><strong>Email:</strong> {{ currentOpenEmailLog.lead_email }}</p>
                <div>
                  <p class="mb-0.5"><strong>Estado secuencia:</strong></p> 
                  <span class="font-medium px-2 py-0.5 text-xs rounded-full" :class="getLeadSequenceStatusClass(currentOpenEmailLog.lead_estado_secuencia)">
                    {{ formatLeadSequenceStatus(currentOpenEmailLog.lead_estado_secuencia) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Panel Acciones Disponibles -->
            <div class="bg-white p-4 rounded-lg shadow-md flex flex-col">
              <h4 class="text-base font-semibold text-gray-700 mb-3 flex items-center">
                <PlaySquare class="h-5 w-5 mr-2 text-impacto-blue" />
                Acciones disponibles
              </h4>
              <div class="space-y-3 flex-grow flex flex-col justify-center">
                <button 
                  class="w-full flex items-center justify-center px-3 py-2.5 text-xs sm:text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-150 ease-in-out transform hover:scale-[1.02] disabled:opacity-70 disabled:cursor-not-allowed"
                  @click="mejorarContenidoEmail"
                  :disabled="true" title="Próximamente">
                  <Sparkles class="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                  Mejorar contenido
                </button>

                <button 
                  v-if="currentOpenEmailLog.estado_envio === 'scheduled'"
                  @click="confirmCancelarEnvioProgramado"
                  class="w-full flex items-center justify-center px-3 py-2.5 text-xs sm:text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors transform hover:scale-[1.02]">
                  <XCircle class="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                  Cancelar envío
                </button>
                
                <button 
                  v-if="currentOpenEmailLog.lead_id_secuencia_asignada && (currentOpenEmailLog.lead_estado_secuencia === 'active' || currentOpenEmailLog.lead_estado_secuencia === 'paused_by_user')"
                  class="w-full flex items-center justify-center px-3 py-2.5 text-xs sm:text-sm font-medium rounded-md shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-150 ease-in-out transform hover:scale-[1.02] disabled:opacity-70 disabled:cursor-not-allowed"
                  :class="currentOpenEmailLog.lead_estado_secuencia === 'active' ? 'text-white bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 focus:ring-orange-500' : 'text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 focus:ring-emerald-500'"
                  @click="updateLeadSequenceState(currentOpenEmailLog.lead_estado_secuencia === 'active' ? 'pause' : 'resume')">
                  <component :is="currentOpenEmailLog.lead_estado_secuencia === 'active' ? PauseCircle : PlayCircle" class="h-4 w-4 mr-2" />
                  {{ currentOpenEmailLog.lead_estado_secuencia === 'active' ? 'Pausar secuencia' : 'Reanudar secuencia' }}
                </button>
              </div>
            </div>

            <!-- Panel Detalles del Email y Progreso -->
            <div class="bg-white p-4 rounded-lg shadow-md flex flex-col">
              <h4 class="text-base font-semibold text-gray-700 mb-3 flex items-center">
                <Mail class="h-5 w-5 mr-2 text-impacto-blue" />
                Detalles del envío
              </h4>
              <div class="text-sm space-y-1.5 text-gray-600 flex-grow">
                <p><strong>Estado:</strong> <span :class="getEmailEstadoClass(currentOpenEmailLog.estado_envio)" class="px-2 py-0.5 rounded-full text-xs font-medium">{{ formatEmailEstado(currentOpenEmailLog.estado_envio) }}</span></p>
                <p v-if="currentOpenEmailLog.fecha_envio_real"><strong>Enviado:</strong> {{ formatSimpleDateTime(currentOpenEmailLog.fecha_envio_real) }}</p>
                <p v-else-if="currentOpenEmailLog.fecha_programada_envio"><strong>Programado:</strong> {{ formatSimpleDateTime(currentOpenEmailLog.fecha_programada_envio) }}</p>
                <div class="flex items-center">
                  <strong class="min-w-[70px]">Abierto:</strong>
                  <span v-if="currentOpenEmailLog.abierto_timestamp" class="flex items-center text-green-600 font-medium">
                    <CheckCircle2 class="h-4 w-4 mr-1.5" />
                    {{ formatSimpleDateTime(currentOpenEmailLog.abierto_timestamp) }}
                  </span>
                  <span v-else class="flex items-center text-red-600 font-medium">
                    <XCircle class="h-4 w-4 mr-1.5" />
                  </span>
                </div>
                <div class="flex items-center">
                  <strong class="min-w-[70px]">Clicado:</strong>
                  <span v-if="currentOpenEmailLog.clickeado_timestamp" class="flex items-center text-green-600 font-medium">
                    <CheckCircle2 class="h-4 w-4 mr-1.5" />
                    {{ formatSimpleDateTime(currentOpenEmailLog.clickeado_timestamp) }}
                  </span>
                  <span v-else class="flex items-center text-red-600 font-medium">
                    <XCircle class="h-4 w-4 mr-1.5" />
                  </span>
                </div>
              </div>
            
              <!-- Barra de Progreso Secuencia -->
              <div v-if="currentOpenEmailLog.total_pasos_en_secuencia && currentOpenEmailLog.total_pasos_en_secuencia > 0 && currentOpenEmailLog.orden_paso_secuencia_actual"
                   class="mt-auto pt-3 border-t border-gray-200">
                  <h5 class="text-sm font-semibold text-gray-700 mb-1.5 flex items-center">
                      <BarChartHorizontal class="h-4 w-4 mr-1.5 text-impacto-blue" />
                      Progreso en secuencia
                  </h5>
                  <div class="text-xs text-gray-600 space-y-1">
                      <p class="font-medium">{{ currentOpenEmailLog.nombre_paso_secuencia_actual || 'Paso actual' }}</p>
                      <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                          <div class="bg-impacto-blue h-2 rounded-full" :style="{ width: calculateSequenceProgress(currentOpenEmailLog.orden_paso_secuencia_actual, currentOpenEmailLog.total_pasos_en_secuencia) + '%' }"></div>
                      </div>
                      <p class="text-xs text-gray-500 text-right">
                          Paso {{ currentOpenEmailLog.orden_paso_secuencia_actual }} de {{ currentOpenEmailLog.total_pasos_en_secuencia }}
                      </p>
                  </div>
              </div>
            </div>
          </div>

          <!-- Columna Derecha: Contenido del Email -->
          <div class="md:col-span-2 bg-white p-4 rounded-lg shadow-md flex flex-col">
            <h4 class="text-base font-semibold text-gray-700 mb-3 flex items-center">
                <FileText class="h-5 w-5 mr-2 text-impacto-blue" />
                Contenido del email
            </h4>
            <div class="flex-grow min-h-[350px] max-h-[calc(90vh-280px)] overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50">
              <div v-if="isLoadingEmailContent" class="flex flex-col items-center justify-center h-full py-10">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2" :style="{ borderColor: '#ed8725' }"></div>
                <p class="mt-3 text-gray-600">Cargando contenido...</p>
              </div>
              <div v-else-if="emailModalError" class="bg-red-50 text-red-700 p-4 rounded-md">
                <p class="font-semibold">Error:</p>
                <p class="text-sm">{{ emailModalError }}</p>
                <button @click="currentOpenEmailLog && openEmailContentModal(currentOpenEmailLog)" class="mt-2 text-sm text-red-600 hover:text-red-800 underline">Reintentar</button>
              </div>
              <div v-else-if="selectedEmailBodyHtml" class="email-content-wrapper">
                <div class="email-content" v-html="selectedEmailBodyHtml"></div>
              </div>
              <div v-else class="text-center text-gray-500 py-10">
                No se pudo cargar el contenido del email o no hay contenido para mostrar.
              </div>
            </div>
          </div>

        </div>
        
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 text-right">
          <button @click="closeEmailContentModal" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-orange">
            Cerrar
          </button>
        </div>
      </div>
    </div>
    
    <div v-if="showConfirmCancelModal" class="fixed inset-0 z-[100] overflow-y-auto bg-gray-800 bg-opacity-75 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirmar Cancelación</h3>
        <p class="text-sm text-gray-600 mb-6">
          ¿Estás seguro de que quieres cancelar el envío programado de este email? Esta acción no se puede deshacer.
        </p>
        <div class="flex justify-end space-x-3">
          <button @click="showConfirmCancelModal = false" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
            No, mantener programado
          </button>
          <button @click="executeCancelScheduledEmail" class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md">
            Sí, cancelar envío
          </button>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { apiFetch } from '@/utils/apiFetch';
import { useToast } from '@/composables/useToast';
import { 
  AlertTriangle, MailX, Send, Users, Clock, Contact, SearchX, 
  ChevronLeft, ChevronRight, X, User, Mail, BarChartHorizontal,
  PlaySquare, Sparkles, XCircle, PauseCircle, PlayCircle, FileText, Eye, CheckCircle2
} from 'lucide-vue-next';
import { format, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';

interface AgencyEmailLogItem {
  id: number;
  asunto_final: string | null;
  estado_envio: string;
  fecha_envio_real: string | null;
  fecha_programada_envio: string | null;
  lead_nombre: string;
  lead_email: string;
  lead_id: number;
  lead_estado_secuencia: string | null;
  tipo_email: string | null; 
  abierto_timestamp: string | null; 
  clickeado_timestamp: string | null; 
  // Campos para la barra de progreso de secuencia
  lead_id_secuencia_asignada: number | null;
  nombre_paso_secuencia_actual: string | null;
  orden_paso_secuencia_actual: number | null;
  total_pasos_en_secuencia: number | null;
}

interface EmailStatWithValueAndChange {
  value: number;
  changePercent: number | null; // Ej: 10 para +10%, -5 para -5%. Null si no hay comparación.
}

interface BasicEmailStats {
  emailsSentThisMonth: EmailStatWithValueAndChange;
  activeNurturingLeads: EmailStatWithValueAndChange;
  totalScheduledEmails: EmailStatWithValueAndChange;
  uniqueLeadsContactedThisMonth: EmailStatWithValueAndChange;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  limit: number;
}

interface EmailLogApiResponse {
  success: boolean;
  data: AgencyEmailLogItem[];
  pagination: PaginationInfo;
  basic_stats?: BasicEmailStats;
  message?: string;
}

interface EmailContent {
  asunto: string;
  cuerpoHTML: string;
}

interface EmailContentApiResponse {
  success: boolean;
  data: EmailContent;
  message?: string;
}

interface SimpleApiResponse {
  success: boolean;
  message?: string;
  new_status?: 'active' | 'paused';
  newState?: 'active' | 'paused';
}


const emailsLog = ref<AgencyEmailLogItem[]>([]);
const totalAgencyEmailCount = ref<number | null>(null);
const isLoading = ref<boolean>(false);
const errorLoading = ref<string | null>(null);
const { showToast } = useToast();

const currentPage = ref<number>(1);
const totalPages = ref<number>(1);
const itemsPerPage = ref<number>(25);
const totalItems = ref<number>(0);

const basicStats = ref<BasicEmailStats | null>(null);

const isEmailModalVisible = ref(false);
const isLoadingEmailContent = ref(false);
const emailModalError = ref<string | null>(null);
const selectedEmailBodyHtml = ref<string | null>(null);

// Función para sanitizar y preparar el HTML del email
const sanitizeEmailHtml = (html: string): string => {
  // Aquí podrías añadir lógica adicional para sanitizar el HTML si es necesario
  return html;
};
const currentOpenEmailLog = ref<AgencyEmailLogItem | null>(null);

const showConfirmCancelModal = ref(false);


const currentStatusFilter = ref<string>('all');
const statusFilters: { label: string, value: string }[] = [
  { label: 'Todos', value: 'all' },
  { label: 'Programados', value: 'scheduled' },
  { label: 'Pendientes', value: 'pending_initial' },
  { label: 'Enviando', value: 'sending' },
  { label: 'Enviados', value: 'sent' },
  { label: 'Fallidos', value: 'failed' },
  { label: 'Cancelados', value: 'cancelled' },
];

const changeStatusFilter = (status: string) => {
  currentStatusFilter.value = status;
  fetchEmailsLog(1); // Reset to page 1 when filter changes
};

const getChangeIndicator = (changePercent: number | null): { class: string; icon: string; text: string } => {
  if (changePercent === null || changePercent === undefined) {
    return { class: 'text-gray-500', icon: '', text: '' };
  }
  if (changePercent > 0) {
    return { class: 'text-green-500', icon: '▲', text: `+${changePercent}%` };
  }
  if (changePercent < 0) {
    return { class: 'text-red-500', icon: '▼', text: `${changePercent}%` };
  }
  return { class: 'text-gray-500', icon: '', text: '0%' }; // Sin cambio
};

const calculateSequenceProgress = (currentStep: number | null, totalSteps: number | null): number => {
  if (currentStep && totalSteps && totalSteps > 0) {
    return Math.max(0, Math.min(100, (currentStep / totalSteps) * 100));
  }
  return 0;
};

const fetchEmailsLog = async (page: number = currentPage.value) => {
  isLoading.value = true;
  errorLoading.value = null;
  
  // Solo reseteamos las stats en la primera página (cuando se cambia de filtro)
  if (page === 1) {
    basicStats.value = null;
  }

  try {
    let url = `agency_email_dashboard.php?page=${page}&limit=${itemsPerPage.value}`;
    if (currentStatusFilter.value !== 'all') {
      url += `&status=${currentStatusFilter.value}`;
    }
    const response = await apiFetch<EmailLogApiResponse>(url);

    if (response.success) {
            emailsLog.value = response.data;

      currentPage.value = response.pagination.currentPage;
      totalPages.value = response.pagination.totalPages;
      totalItems.value = response.pagination.totalItems;

      if (currentStatusFilter.value === 'all') {
        totalAgencyEmailCount.value = response.pagination.totalItems;
      }

      if (response.basic_stats) {
        basicStats.value = response.basic_stats;
      } else {
        // Fallback or default if stats are not provided by backend yet
        basicStats.value = {
          emailsSentThisMonth: { value: 0, changePercent: null },
          activeNurturingLeads: { value: 0, changePercent: null },
          totalScheduledEmails: { value: 0, changePercent: null },
          uniqueLeadsContactedThisMonth: { value: 0, changePercent: null },
        };
      }
    } else {
      throw new Error(response.message || 'Error al cargar el historial de emails.');
    }
  } catch (e: any) {
    console.error('Error fetching agency email log:', e);
    const errorMessage = e instanceof Error ? e.message : String(e);
    errorLoading.value = errorMessage;
    showToast(errorMessage, 'error', 5000);
  } finally {
    isLoading.value = false;
  }
};

const openEmailContentModal = async (logItem: AgencyEmailLogItem) => {
  currentOpenEmailLog.value = logItem;
  isEmailModalVisible.value = true;
  isLoadingEmailContent.value = true;
  emailModalError.value = null;
  selectedEmailBodyHtml.value = null;

  

  try {
    const response = await apiFetch<EmailContentApiResponse>(`get_email_content.php?email_id=${logItem.id}`);
    if (response.success && response.data) {
      // Sanitizar y preparar el HTML antes de asignarlo
      selectedEmailBodyHtml.value = sanitizeEmailHtml(response.data.cuerpoHTML || '');
    } else {
      emailModalError.value = response.message || 'Error al cargar el contenido del email';
    }
  } catch (e: any) {
    console.error('Error fetching email content:', e);
    emailModalError.value = e instanceof Error ? e.message : String(e);
    showToast(emailModalError.value, 'error');
  } finally {
    isLoadingEmailContent.value = false;
  }
};

const closeEmailContentModal = () => {
  isEmailModalVisible.value = false;
  currentOpenEmailLog.value = null;
  selectedEmailBodyHtml.value = null;
  emailModalError.value = null;
  showConfirmCancelModal.value = false;
};

const mejorarContenidoEmail = () => {
  showToast('Función "Mejorar Contenido con IA" estará disponible próximamente.', 'info');
};

const confirmCancelarEnvioProgramado = () => {
  showConfirmCancelModal.value = true;
};

const executeCancelScheduledEmail = async () => {
  if (!currentOpenEmailLog.value || currentOpenEmailLog.value.estado_envio !== 'scheduled') {
    showToast('Este email no está programado o no se puede cancelar.', 'warning');
    showConfirmCancelModal.value = false;
    return;
  }

  const emailIdToCancel = currentOpenEmailLog.value.id;  
  showConfirmCancelModal.value = false;

  try {
    const response = await apiFetch<SimpleApiResponse>(`cancel_scheduled_email.php`, {
      method: 'POST',
      body: JSON.stringify({ historial_id: emailIdToCancel }),
    });

    if (response.success) {
      showToast(response.message || 'Envío programado cancelado exitosamente.', 'success');
      const logItemInTable = emailsLog.value.find(item => item.id === emailIdToCancel);
      if (logItemInTable) {
          logItemInTable.estado_envio = response.new_status || 'cancelado';
      }
      if (currentOpenEmailLog.value) { 
          currentOpenEmailLog.value.estado_envio = response.new_status || 'cancelado';
      }
    } else {
      throw new Error(response.message || 'Error al cancelar el envío.');
    }
  } catch (e: any) {
    console.error('Error cancelling scheduled email:', e);
    showToast(e instanceof Error ? e.message : String(e), 'error');
  }
};

const updateLeadSequenceState = async (action: 'pause' | 'resume') => {
  if (!currentOpenEmailLog.value) return;

  const leadId = currentOpenEmailLog.value.lead_id;
  const endpoint = action === 'pause' ? 'lead_email_sequence_pause.php' : 'lead_email_sequence_resume.php';
  const successMessage = action === 'pause' ? 'Secuencia pausada correctamente.' : 'Secuencia reanudada correctamente.';
  const errorMessage = action === 'pause' ? 'Error al pausar la secuencia.' : 'Error al reanudar la secuencia.';

  try {
    const response = await apiFetch<SimpleApiResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify({ lead_id: leadId }),
    });

    // La API puede devolver success: false incluso en operaciones exitosas.
    // Un indicador más fiable de éxito es la presencia de `new_status`.
        const newStatus = response.newState ?? response.new_status;
    if (newStatus) {

      // Actualizar todos los logs de este lead en la tabla principal
      const itemsToUpdate = emailsLog.value.filter(item => item.lead_id === leadId);
      itemsToUpdate.forEach(item => {
        item.lead_estado_secuencia = newStatus;
      });

      // Actualizar el log del modal abierto
      if (currentOpenEmailLog.value && currentOpenEmailLog.value.lead_id === leadId) {
        currentOpenEmailLog.value.lead_estado_secuencia = newStatus;
      }

            showToast(response.message || successMessage, 'success');
    } else {
      throw new Error(response.message || errorMessage);
    }
  } catch (e: any) {
    console.error(`Error updating lead sequence state to ${action}:`, e);
    showToast(e instanceof Error ? e.message : String(e), 'error');
  }
};


const formatSimpleDateTime = (isoDate: string | null): string => {
  if (!isoDate) return 'N/A';
  try {
    return format(parseISO(isoDate), "dd/MM/yyyy HH:mm", { locale: es });
  } catch (error) {
    console.error(`Error formatting date: ${isoDate}`, error);
    return 'Fecha inválida';
  }
};

const formatEmailEstado = (estado: string | null): string => {
  if (!estado) return 'Desconocido';
  const estados: { [key: string]: string } = {
    'sent': 'Enviado',
    'failed': 'Fallido',
    'scheduled': 'Programado',
    'cancelled': 'Cancelado',
    'sending': 'Enviando',
    'pending_initial': 'Pendiente'
  };
  return estados[estado] || estado.charAt(0).toUpperCase() + estado.slice(1);
};

const getEmailEstadoClass = (estado: string | null): string => {
  if (!estado) return 'bg-gray-100 text-gray-800';
  const classes: { [key: string]: string } = {
    'sent': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'scheduled': 'bg-blue-100 text-blue-800',
    'cancelled': 'bg-gray-200 text-gray-800',
    'sending': 'bg-yellow-100 text-yellow-800',
    'pending_initial': 'bg-purple-100 text-purple-800',
    'abierto': 'bg-sky-100 text-sky-800', // Mantengo estos por si vienen de otro lado
    'clickeado': 'bg-purple-100 text-purple-800'
  };
  return classes[estado] || 'bg-gray-100 text-gray-800';
};

const formatLeadSequenceStatus = (status: string | null): string => {
  if (!status) return 'No Asignada';
  const statuses: { [key: string]: string } = {
    'active': 'Activa',
    'paused_by_user': 'Pausada por usuario',
    'paused_by_system': 'Pausada por sistema',
    'completed': 'Completada',
    'unsubscribed': 'De-suscrito',
    'error': 'Error',
    'pending_activation': 'Pendiente de activación'
  };
  return statuses[status] || status.charAt(0).toUpperCase() + status.slice(1);
};

const getLeadSequenceStatusClass = (status: string | null): string => {
  if (!status) return 'bg-gray-100 text-gray-600';
  const classes: { [key: string]: string } = {
    'active': 'bg-green-100 text-green-800',
    'paused_by_user': 'bg-yellow-100 text-yellow-800',
    'paused_by_system': 'bg-orange-100 text-orange-800',
    'completed': 'bg-blue-100 text-blue-800',
    'unsubscribed': 'bg-red-100 text-red-800',
    'error': 'bg-red-200 text-red-900',
    'pending_activation': 'bg-orange-100 text-orange-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const prevPage = () => {
  if (currentPage.value > 1) {
    fetchEmailsLog(currentPage.value - 1);
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    fetchEmailsLog(currentPage.value + 1);
  }
};

onMounted(() => {
  fetchEmailsLog();
});
</script>

<style scoped>
/* Estilos para el contenedor del email */
.email-content-wrapper {
  padding: 20px;
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px;
}

/* Estilos aislados para el contenido del email */
.email-content {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* Resetear estilos para asegurar que el contenido del email no se vea afectado */
.email-content :deep(*) {
  box-sizing: border-box;
  max-width: 100%;
  font-family: inherit;
}

.email-content :deep(p) {
  margin-bottom: 16px;
}

.email-content :deep(a) {
  color: #0066cc;
  text-decoration: underline;
}

.email-content :deep(img) {
  max-width: 100%;
  height: auto;
}

.email-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
}

.email-content :deep(td), .email-content :deep(th) {
  padding: 8px;
}

/* Estilos para hacer que el contenido se vea como un email real */
.email-content :deep(.email-header) {
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.email-content :deep(.email-footer) {
  border-top: 1px solid #eaeaea;
  padding-top: 15px;
  margin-top: 20px;
  font-size: 12px;
  color: #666;
}

.prose :where(div):not(:where([class~="not-prose"] *)) {
   margin-top: 0;
   margin-bottom: 0;
}
.prose-sm {
  font-size: 0.875rem; 
  line-height: 1.5;
}

/* Mejoras para el scrollbar en el contenido del email */
.max-h-\[50vh\]::-webkit-scrollbar {
  width: 8px;
}
.max-h-\[50vh\]::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}
.max-h-\[50vh\]::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 10px;
}
.max-h-\[50vh\]::-webkit-scrollbar-thumb:hover {
  background: #a5a5a5;
}
</style> 