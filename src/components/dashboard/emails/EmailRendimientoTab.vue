<template>
  <div class="space-y-8">
    <div v-if="isLoading" class="text-center py-20">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto" :style="{ borderColor: '#ed8725' }"></div>
      <p class="mt-4 text-lg font-semibold text-gray-700">Cargando estadísticas...</p>
    </div>

    <div v-else-if="errorLoading" class="bg-red-50 border-l-4 border-red-600 text-red-800 p-6 rounded-xl shadow-md">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <AlertTriangle class="h-8 w-8 text-red-600" />
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-bold">Error al cargar el rendimiento</h3>
          <p class="text-sm mt-1">{{ errorLoading }}</p>
          <button @click="fetchEmailPerformanceStats()" class="mt-3 px-3 py-1.5 text-sm bg-red-700 text-white rounded-md hover:bg-red-800 transition-colors font-medium">Reintentar</button>
        </div>
      </div>
    </div>

    <template v-else-if="performanceData">
      <div>
        <h2 class="text-2xl font-semibold text-[#051f33]">Rendimiento de la estrategia de nutrición</h2>
        <p class="mt-1 text-gray-600">
          Analiza cómo tus emails están cultivando a tus leads.
        </p>
      </div>

      <!-- KPIs Clave -->
      <section>
        <h3 class="text-xl font-semibold text-[#051f33] mb-4">KPIs clave de email</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
          <KPICard 
            title="Emails enviados (mes)"
            :value="performanceData.kpis.emailsEnviadosPeriodo.toLocaleString('es-ES')"
            :trend="shouldHideTrend(performanceData.kpis.emailsEnviadosPeriodo, performanceData.kpis.tendenciaEmailsEnviados) ? undefined : (performanceData.kpis.tendenciaEmailsEnviados >= 0 ? 'up' : 'down')"
            :trend_value="shouldHideTrend(performanceData.kpis.emailsEnviadosPeriodo, performanceData.kpis.tendenciaEmailsEnviados) ? '' : `${Math.abs(performanceData.kpis.tendenciaEmailsEnviados)}%`"
            insight="vs. mes anterior"
          />
          <KPICard 
            title="Tasa de apertura promedio"
            :value="`${performanceData.kpis.tasaAperturaPromedio.toFixed(1)}%`" 
            :trend="shouldHideTrend(performanceData.kpis.tasaAperturaPromedio, performanceData.kpis.tendenciaTasaApertura) ? undefined : (performanceData.kpis.tendenciaTasaApertura >= 0 ? 'up' : 'down')"
            :trend_value="shouldHideTrend(performanceData.kpis.tasaAperturaPromedio, performanceData.kpis.tendenciaTasaApertura) ? '' : `${Math.abs(performanceData.kpis.tendenciaTasaApertura).toFixed(1)}%`"
            :insight_highlight="performanceData.kpis.tasaAperturaPromedio > 40 ? '¡Muy buena tasa!' : (performanceData.kpis.emailsEnviadosTotal > 0 ? 'Podría mejorar' : 'Sin datos suficientes')"
            :insight_type="performanceData.kpis.tasaAperturaPromedio > 40 ? 'positive' : (performanceData.kpis.emailsEnviadosTotal > 0 ? 'neutral' : 'info')"
            is_percentage
          />
          <KPICard 
            title="Tasa de clics promedio"
            :value="`${performanceData.kpis.tasaClicsPromedio.toFixed(1)}%`"
            :trend="shouldHideTrend(performanceData.kpis.tasaClicsPromedio, performanceData.kpis.tendenciaTasaClics) ? undefined : (performanceData.kpis.tendenciaTasaClics >= 0 ? 'up' : 'down')"
            :trend_value="shouldHideTrend(performanceData.kpis.tasaClicsPromedio, performanceData.kpis.tendenciaTasaClics) ? '' : `${Math.abs(performanceData.kpis.tendenciaTasaClics).toFixed(1)}%`"
            :insight_highlight="performanceData.kpis.tasaClicsPromedio > 5 ? '¡Excelente CTR!' : (performanceData.kpis.emailsEnviadosTotal > 0 ? 'Objetivo: >5%' : 'Sin datos suficientes')"
            :insight_type="performanceData.kpis.tasaClicsPromedio > 5 ? 'positive' : (performanceData.kpis.emailsEnviadosTotal > 0 ? 'neutral' : 'info')"
            is_percentage
          />
          <KPICard 
            title="Leads en nutrición activa"
            :value="performanceData.kpis.leadsEnNutricionActiva.toLocaleString('es-ES')"
            insight_type="info"
            insight_highlight="Leads recibiendo secuencias"
          />
        </div>
        <div v-if="performanceData.kpis.insightDestacado" class="mt-5 p-4 bg-sky-50 border border-sky-200 rounded-lg shadow-sm">
          <p class="text-sm text-sky-700">
            <span class="font-semibold text-sky-800">Observación destacada:</span>
            {{ performanceData.kpis.insightDestacado }}
          </p>
        </div>
      </section>

      <!-- Gráficos Interactivos -->
      <section class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white p-5 rounded-xl shadow-lg border border-gray-200/70 min-h-[350px] flex flex-col">
          <h4 class="text-lg font-semibold text-[#051f33] mb-1">Rendimiento de aperturas y clics</h4>
          <p class="text-xs text-gray-500 mb-4">Últimos 30 días</p>
          <div v-if="rendimientoDiarioData.labels && rendimientoDiarioData.labels.length > 0 && performanceData.kpis.emailsEnviadosTotal > 0" class="h-72 flex-grow">
            <Line :data="rendimientoDiarioData" :options="chartOptions" />
          </div>
           <div v-else class="h-72 flex-grow flex flex-col items-center justify-center text-center p-4">
            <BarChart2 class="h-16 w-16 text-gray-400 mb-4" />
            <h5 class="font-semibold text-gray-600 mb-1">Analíticas pendientes</h5>
            <p class="text-sm text-gray-500">Cuando se envíen tus primeros emails, podrás ver su rendimiento detallado aquí.</p>
          </div>
        </div>
        <div class="bg-white p-5 rounded-xl shadow-lg border border-gray-200/70 min-h-[350px] flex flex-col">
          <h4 class="text-lg font-semibold text-[#051f33] mb-1">Rendimiento por secuencia</h4>
          <p class="text-xs text-gray-500 mb-4">Tasas de apertura y clics</p>
           <div v-if="rendimientoSecuenciaData.labels && rendimientoSecuenciaData.labels.length > 0 && performanceData.kpis.emailsEnviadosTotal > 0" class="h-72 flex-grow">
            <Bar :data="rendimientoSecuenciaData" :options="chartOptionsBar" />
          </div>
          <div v-else class="h-72 flex-grow flex flex-col items-center justify-center text-center p-4">
            <ClipboardList class="h-16 w-16 text-gray-400 mb-4" />
            <h5 class="font-semibold text-gray-600 mb-1">Datos de secuencias no disponibles</h5>
            <p class="text-sm text-gray-500">El rendimiento de tus secuencias de email aparecerá aquí una vez que haya actividad.</p>
          </div>
        </div>
      </section>
    </template>
     <div v-else class="text-center py-10">
        <p class="text-gray-500">No hay datos de rendimiento de emails disponibles.</p>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, defineAsyncComponent } from 'vue';
import { apiFetch } from '@/utils/apiFetch';
import { AlertTriangle, BarChart2, ClipboardList } from 'lucide-vue-next';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Colors,
  Filler
} from 'chart.js';
import { Line, Bar } from 'vue-chartjs';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Colors,
  Filler
);

const KPICard = defineAsyncComponent(() => 
  import('@/components/dashboard/emails/KPICard.vue')
);

interface KPIStats {
  emailsEnviadosTotal: number;
  emailsEnviadosPeriodo: number;
  tendenciaEmailsEnviados: number;
  tasaAperturaPromedio: number;
  tendenciaTasaApertura: number;
  tasaClicsPromedio: number;
  tendenciaTasaClics: number;
  leadsEnNutricionActiva: number;
  insightDestacado?: string;
}

interface RendimientoDiarioItem {
  fecha: string;
  aperturas: number;
  clics: number;
}

interface RendimientoSecuenciaItem {
  nombreSecuencia: string;
  tasaApertura: number;
  tasaClics: number;
  totalEnviadosSecuencia: number;
}

interface EmailPerformanceData {
  kpis: KPIStats;
  rendimientoDiario: RendimientoDiarioItem[];
  rendimientoPorSecuencia: RendimientoSecuenciaItem[];
}

interface ApiResponse {
  success: boolean;
  data: EmailPerformanceData;
  message?: string;
}

const performanceData = ref<EmailPerformanceData | null>(null);
const isLoading = ref<boolean>(true);
const errorLoading = ref<string | null>(null);

const fetchEmailPerformanceStats = async () => {
  isLoading.value = true;
  errorLoading.value = null;
  try {
    const response = await apiFetch<ApiResponse>('/api/get_email_performance_stats.php');
    if (response.success && response.data) {
      performanceData.value = response.data;
    } else {
      throw new Error(response.message || 'Error al cargar estadísticas de rendimiento de emails.');
    }
  } catch (e: any) {
    console.error('Error fetching email performance stats:', e);
    const errorMessage = e instanceof Error ? e.message : String(e);
    errorLoading.value = errorMessage;
  } finally {
    isLoading.value = false;
  }
};

// Helper function to decide if trend should be hidden
const shouldHideTrend = (currentValue: number, trendValue: number): boolean => {
  // If currentValue is 0 and trend is also 0 (meaning previous value was also 0 or not applicable)
  // or if the specific KPI for trend calculation (e.g. emailsEnviadosPeriodo) is 0 and trend indicates no change from 0.
  // The backend sends trend as 0 if previous was 0 and current is 0.
  // Or if current is >0 but previous was 0, trend is 100.
  // We hide trend if current value is 0 and trend is 0, meaning no activity now and no activity before for comparison.
  return currentValue === 0 && trendValue === 0;
};

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true,
      ticks: { color: '#6b7280' }, // text-gray-500
      grid: { color: '#e5e7eb' } // border-gray-200
    },
    x: {
      ticks: { color: '#6b7280' },
      grid: { display: false }
    }
  },
  plugins: {
    legend: {
      position: 'top' as const,
      labels: { color: '#1f2937' } // text-gray-800
    },
    tooltip: {
      backgroundColor: '#051f33',
      titleColor: '#ffffff',
      bodyColor: '#e5e7eb',
      borderColor: '#ed8725',
      borderWidth: 1
    }
  }
}));

const chartOptionsBar = computed(() => ({
  ...chartOptions.value,
  plugins: {
    ...chartOptions.value.plugins,
    tooltip: {
        ...chartOptions.value.plugins.tooltip,
        callbacks: {
            label: function(context: any) {
                let label = context.dataset.label || '';
                if (label) {
                    label += ': ';
                }
                if (context.parsed.y !== null) {
                    label += context.parsed.y.toFixed(1) + '%';
                }
                return label;
            }
        }
    }
  }
}));

const rendimientoDiarioData = computed(() => {
  if (!performanceData.value || !performanceData.value.rendimientoDiario) {
    return { labels: [], datasets: [] };
  }
  const labels = performanceData.value.rendimientoDiario.map(item => 
    new Date(item.fecha + 'T00:00:00').toLocaleDateString('es-ES', { day: 'numeric', month: 'short' })
  );
  return {
    labels,
    datasets: [
      {
        label: 'Aperturas',
        backgroundColor: 'rgba(237, 135, 37, 0.2)', // impacto-orange with opacity
        borderColor: '#ed8725', // impacto-orange
        tension: 0.2,
        data: performanceData.value.rendimientoDiario.map(item => item.aperturas),
        fill: true,
      },
      {
        label: 'Clics',
        backgroundColor: 'rgba(5, 31, 51, 0.2)', // impacto-blue-dark with opacity
        borderColor: '#051f33', // impacto-blue-dark
        tension: 0.2,
        data: performanceData.value.rendimientoDiario.map(item => item.clics),
        fill: true,
      }
    ]
  };
});

const rendimientoSecuenciaData = computed(() => {
  if (!performanceData.value || !performanceData.value.rendimientoPorSecuencia) {
    return { labels: [], datasets: [] };
  }
  const labels = performanceData.value.rendimientoPorSecuencia.map(item => item.nombreSecuencia);
  return {
    labels,
    datasets: [
      {
        label: 'Tasa Apertura (%)',
        backgroundColor: '#fdba74', // orange-300
        borderColor: '#f97316', // orange-500
        borderWidth: 1,
        data: performanceData.value.rendimientoPorSecuencia.map(item => item.tasaApertura)
      },
      {
        label: 'Tasa Clics (%)',
        backgroundColor: '#67e8f9', // cyan-300
        borderColor: '#06b6d4', // cyan-500
        borderWidth: 1,
        data: performanceData.value.rendimientoPorSecuencia.map(item => item.tasaClics)
      }
    ]
  };
});

onMounted(() => {
  fetchEmailPerformanceStats();
});

</script>

<style scoped>
/* Estilos específicos si son necesarios */
</style> 