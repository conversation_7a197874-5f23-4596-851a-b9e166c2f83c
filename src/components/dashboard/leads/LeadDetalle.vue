<template>
  <div class="p-4 sm:p-6 bg-slate-50">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- <PERSON><PERSON><PERSON>erda: Información Principal y Notas Editables -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Card: Información del Lead -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200/80 p-6">
          <div class="flex flex-col sm:flex-row sm:items-center mb-6">
            <div class="h-16 w-16 sm:h-20 sm:w-20 rounded-full bg-impacto-blue/10 text-impacto-blue flex items-center justify-center mr-0 sm:mr-5 mb-3 sm:mb-0 text-2xl sm:text-3xl font-semibold ring-4 ring-impacto-blue/20 shrink-0">
              {{ getInitials(currentLeadData.nombre) }}
            </div>
            <div class="text-center sm:text-left flex-grow">
              <h3 class="text-2xl sm:text-3xl font-bold text-impacto-blue-dark">{{ currentLeadData.nombre }}</h3>
              <p class="text-gray-600 mt-1">
                <span :class="getSecuenciaEstadoClass(currentLeadData.lead_estado_secuencia)" class="px-3 py-1 rounded-full text-xs font-semibold tracking-wide">
                  {{ formatSecuenciaEstado(currentLeadData.lead_estado_secuencia) }}
                </span>
              </p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-5 text-sm mb-6">
            <div>
              <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1.5">Contacto</h4>
              <div class="space-y-2.5">
                <div class="flex items-start">
                  <MailIcon class="h-5 w-5 text-gray-400 mr-2.5 mt-0.5 shrink-0" />
                  <a v-if="currentLeadData.email" :href="`mailto:${currentLeadData.email}`" class="text-impacto-blue hover:underline break-all">{{ currentLeadData.email }}</a>
                  <span v-else class="text-gray-500 italic">Email no disponible</span>
                </div>
                <div v-if="currentLeadData.telefono" class="flex items-start">
                  <PhoneIcon class="h-5 w-5 text-gray-400 mr-2.5 mt-0.5 shrink-0" />
                  <a :href="`tel:${currentLeadData.telefono}`" class="text-gray-700 hover:text-impacto-blue">{{ formatPhone(currentLeadData.telefono) }}</a>
                </div>
              </div>
            </div>
            <div>
              <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1.5">Detalles del lead</h4>
              <div class="space-y-2.5">
                <div class="flex items-start">
                  <CalendarIcon class="h-5 w-5 text-gray-400 mr-2.5 mt-0.5 shrink-0" />
                  <span class="text-gray-700">Capturado: {{ formatDate(currentLeadData.fecha_captura) }}</span>
                </div>
                <div class="flex items-start">
                  <FileTextIcon class="h-5 w-5 text-gray-400 mr-2.5 mt-0.5 shrink-0" />
                  <span class="text-gray-700">{{ currentLeadData.necesidad || 'Necesidad no especificada' }}</span>
                </div>
                <div class="flex items-start">
                  <HomeIcon class="h-5 w-5 text-gray-400 mr-2.5 mt-0.5 shrink-0" />
                  <span class="text-gray-700">{{ currentLeadData.num_valoraciones }} valoracione(s)</span>
                </div>
                 <div v-if="currentLeadData.valorador_nombre" class="flex items-start">
                  <Settings2Icon class="h-5 w-5 text-gray-400 mr-2.5 mt-0.5 shrink-0" />
                  <span class="text-gray-700">Origen: {{ currentLeadData.valorador_nombre }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Sección de Notas del Lead -->
          <div class="mt-6 pt-6 border-t border-gray-200">
            <div class="flex justify-between items-center mb-2">
                <h4 class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Notas del lead</h4>
                <button v-if="notasChanged" @click="saveLeadNotes" 
                        :disabled="isSavingNotes"
                        class="px-3 py-1 text-xs font-medium text-white bg-impacto-orange rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-impacto-orange transition-colors disabled:opacity-60">
                    {{ isSavingNotes ? 'Guardando...' : 'Guardar notas' }}
                </button>
            </div>
            <textarea v-model="editableNotes"
                      rows="4"
                      placeholder="Añade tus notas aquí..."
                      class="w-full p-2.5 text-sm text-gray-700 bg-slate-50 rounded-lg border border-slate-300 focus:ring-1 focus:ring-impacto-blue focus:border-impacto-blue transition-shadow resize-none"></textarea>
          </div>
        </div>

        <!-- Card: Seguimiento de Emails -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200/80 p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-impacto-blue-dark flex items-center">
              <MailsIcon class="h-6 w-6 mr-2.5 text-impacto-orange" />
              Nutrición de emails
            </h3>
             <span :class="getSecuenciaEstadoClass(currentLeadData.lead_estado_secuencia)" class="px-3 py-1 rounded-full text-xs font-semibold tracking-wide">
                {{ formatSecuenciaEstado(currentLeadData.lead_estado_secuencia) }}
             </span>
          </div>
          
          <div class="mb-5 flex flex-col sm:flex-row gap-3">
            <button 
              v-if="['active', 'pending_activation'].includes(currentLeadData.lead_estado_secuencia || '')"
              @click="updateSequenceState('pause', currentLeadData.lead_id)"
              :disabled="isLoadingEmailHistory || isLoadingValoraciones || isUpdatingSequence" 
              class="flex-1 sm:flex-none justify-center px-4 py-2.5 text-sm font-medium text-white bg-amber-500 rounded-lg hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center"
            >
              <PauseCircleIcon class="h-5 w-5 mr-2" />
              Pausar secuencia
            </button>
            <button 
              v-else-if="currentLeadData.lead_estado_secuencia === 'paused_by_user'"
              @click="updateSequenceState('resume', currentLeadData.lead_id)"
              :disabled="isLoadingEmailHistory || isLoadingValoraciones || isUpdatingSequence"
              class="flex-1 sm:flex-none justify-center px-4 py-2.5 text-sm font-medium text-white bg-green-500 rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center"
            >
              <PlayCircleIcon class="h-5 w-5 mr-2" />
              Reanudar secuencia
            </button>
            <p v-if="['completed', 'unsubscribed', 'paused_by_system', 'error'].includes(currentLeadData.lead_estado_secuencia || '')" class="text-sm text-gray-500 italic py-2 flex-grow text-center sm:text-left">
              La secuencia no está activa.
            </p>
             <p v-else-if="!currentLeadData.lead_estado_secuencia" class="text-sm text-gray-500 italic py-2 flex-grow text-center sm:text-left">
              Secuencia no iniciada.
            </p>
          </div>

          <div v-if="isLoadingEmailHistory" class="text-center py-8">
            <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-impacto-orange mx-auto mb-3"></div>
            <p class="text-gray-600">Cargando historial de emails...</p>
          </div>
          <div v-else-if="emailHistoryError" class="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
            <p class="font-semibold">Error al cargar historial:</p>
            <p class="text-sm">{{ emailHistoryError }}</p>
            <button @click="fetchEmailHistory(currentLeadData.lead_id)" class="mt-2 px-3 py-1.5 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors">Reintentar</button>
          </div>
          <div v-else-if="emailHistory.length === 0" class="text-center py-8 px-4 bg-slate-50 rounded-lg">
            <SearchXIcon class="h-12 w-12 text-slate-400 mx-auto mb-3" />
            <h4 class="font-semibold text-slate-700">Sin historial de emails</h4>
            <p class="text-sm text-slate-500">No hay emails registrados para este lead.</p>
          </div>
          <div v-else>
            <!-- Filtro de historial de emails -->
            <div class="mb-4">
              <label for="email-search" class="sr-only">Buscar en historial</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <SearchIcon class="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="email-search"
                  id="email-search"
                  v-model="emailSearchQuery"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-impacto-blue focus:border-impacto-blue sm:text-sm"
                  placeholder="Buscar por asunto, estado, tipo..."
                />
              </div>
            </div>

            <!-- Mensaje de no resultados del filtro -->
            <div v-if="emailSearchQuery && filteredEmailHistory.length === 0" class="text-center py-8 px-4 bg-slate-50 rounded-lg">
              <SearchXIcon class="h-12 w-12 text-slate-400 mx-auto mb-3" />
              <h4 class="font-semibold text-slate-700">No se encontraron emails</h4>
              <p class="text-sm text-slate-500">Prueba con otro término de búsqueda.</p>
            </div>

            <!-- Lista de emails -->
            <div class="max-h-80 overflow-y-auto space-y-3 pr-2 -mr-2">
              <div v-for="emailItem in filteredEmailHistory" :key="emailItem.id" class="p-3.5 bg-slate-50 rounded-lg border border-slate-200 hover:border-slate-300 transition-colors">
                <div class="flex justify-between items-start mb-1">
                  <h5 class="text-sm font-semibold text-gray-700 break-words mr-2" :title="emailItem.asunto_final ?? 'Asunto no disponible'">
                    {{ emailItem.asunto_final || 'Email sin Asunto' }}
                  </h5>
                  <span :class="getEmailEstadoClass(emailItem.estado_envio)" class="px-2 py-0.5 rounded-full text-xs font-medium inline-block whitespace-nowrap shrink-0">
                    {{ formatEmailEstado(emailItem.estado_envio) }}
                  </span>
                </div>
                <p class="text-xs text-gray-500 mb-1">
                  <span v-if="emailItem.tipo_email === 'EMAIL_SECUENCIA_IA' && emailItem.nombre_paso_display">Sec. IA: {{ emailItem.nombre_paso_display }}</span>
                  <span v-else-if="emailItem.tipo_email">{{ formatTipoEmail(emailItem.tipo_email) }}</span>
                  <span v-else>Tipo desconocido</span>
                </p>
                <p class="text-xs text-gray-500">
                  <span v-if="emailItem.fecha_envio_real">Enviado: {{ formatDateTime(emailItem.fecha_envio_real) }}</span>
                  <span v-else-if="emailItem.fecha_programada_envio">Programado: {{ formatDateTime(emailItem.fecha_programada_envio) }}</span>
                  <span v-else>Fecha no disponible</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Columna Derecha: Sugerencias y Valoraciones -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Card: Sugerencias -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200/80 p-6">
          <h4 class="text-base font-semibold text-impacto-blue-dark mb-4 flex items-center">
            <LightbulbIcon class="h-5 w-5 mr-2 text-impacto-orange" />
            Sugerencias
          </h4>
          <div class="space-y-3 text-sm">
            <div v-if="currentLeadData.ia_interest_score && currentLeadData.ia_interest_score >= 75" class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p class="text-blue-700"><span class="font-semibold">Alto interés detectado ({{currentLeadData.ia_interest_score}}%).</span> Considera una acción de seguimiento personalizada pronto.</p>
            </div>
            <div v-if="currentLeadData.num_valoraciones > 1" class="p-3 bg-green-50 border border-green-200 rounded-lg">
              <p class="text-green-700"><span class="font-semibold">Múltiples valoraciones ({{currentLeadData.num_valoraciones}}).</span> El lead podría estar comparando activamente. Revisa sus valoraciones.</p>
            </div>
            <div v-if="currentLeadData.lead_estado_secuencia === 'paused_by_user'" class="p-3 bg-amber-50 border border-amber-200 rounded-lg flex items-center justify-between">
              <p class="text-amber-700">La nutrición automática está pausada.</p>
              <button @click="updateSequenceState('resume', currentLeadData.lead_id)" 
                      :disabled="isUpdatingSequence" 
                      class="px-3 py-1 text-xs font-medium text-white bg-green-500 rounded-md hover:bg-green-600 transition-colors disabled:opacity-60">
                Reanudar
              </button>
            </div>
            <p v-if="!(currentLeadData.ia_interest_score && currentLeadData.ia_interest_score >= 75) && !(currentLeadData.num_valoraciones > 1) && !(currentLeadData.lead_estado_secuencia === 'paused_by_user')" 
               class="text-gray-500 italic">
              No hay sugerencias específicas en este momento. Mantén la nutrición activa.
            </p>
          </div>
        </div>

        <!-- Card: Valoraciones Relacionadas -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200/80 p-6">
          <h3 class="text-lg font-semibold text-impacto-blue-dark mb-4 flex items-center">
            <ListTodoIcon class="h-6 w-6 mr-2.5 text-impacto-orange" />
            Valoraciones realizadas ({{ valoracionesDelLead.length }})
          </h3>
          <div v-if="isLoadingValoraciones" class="text-center py-8">
             <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-impacto-orange mx-auto mb-3"></div>
            <p class="text-gray-600">Cargando valoraciones...</p>
          </div>
          <div v-else-if="errorValoraciones" class="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
            <p class="font-semibold">Error al cargar valoraciones:</p>
            <p class="text-sm">{{ errorValoraciones }}</p>
            <button @click="fetchValoracionesDelLead(currentLeadData.lead_id)" class="mt-2 px-3 py-1.5 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors">Reintentar</button>
          </div>
          <div v-else-if="valoracionesDelLead.length === 0" class="text-center py-8 px-4 bg-slate-50 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-slate-400 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7H7m0 0v3m0-3c0-1.1.9-2 2-2h3M7 10h3m0 0v3m0-3c0 1.1.9 2 2 2h3m-3-2v3m0 0V7" />
            </svg>
            <h4 class="font-semibold text-slate-700">Sin Valoraciones</h4>
            <p class="text-sm text-slate-500">Este lead aún no ha realizado valoraciones.</p>
          </div>
          <div v-else class="max-h-96 overflow-y-auto space-y-3 pr-2 -mr-2">
            <div v-for="valoracion in valoracionesDelLead" :key="valoracion.id" 
                class="p-3.5 bg-slate-50 rounded-lg border border-slate-200 hover:border-slate-300 transition-colors">
              <div class="flex justify-between items-start">
                  <div class="min-w-0">
                    <p class="text-sm font-semibold text-gray-700 break-words" :title="valoracion.direccion">{{ valoracion.direccion }}</p>
                    <p class="text-xs text-gray-500">
                      {{ valoracion.tipo_principal }} <span v-if="valoracion.subtipo">- {{ valoracion.subtipo }}</span>
                    </p>
                     <p class="text-xs text-gray-500">{{ formatDate(valoracion.fecha_creacion) }}</p>
                  </div>
              </div>
               <p class="text-xs mt-1.5 text-green-700 font-medium"
                 v-if="valoracion.valor_estimado_min && valoracion.valor_estimado_max">
                  {{ formatCurrency(valoracion.valor_estimado_min) }} - {{ formatCurrency(valoracion.valor_estimado_max) }}
               </p>
              <div class="mt-2 text-right">
                <button @click="verDetalleDeValoracion(valoracion.id)" class="text-xs font-medium text-impacto-orange hover:underline transition-colors flex items-center">
                    Ver detalle
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, watch, defineEmits, computed } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useToast } from '@/composables/useToast';
import { useRouter } from 'vue-router';
import { apiFetch } from '@/utils/apiFetch';
import { 
  MailIcon, 
  PhoneIcon, 
  CalendarIcon, 
  FileTextIcon, 
  HomeIcon, 
  Settings2Icon,
  MailsIcon,
  PauseCircleIcon,
  PlayCircleIcon,
  SearchIcon,
  SearchXIcon,
  LightbulbIcon,
  ListTodoIcon,
} from 'lucide-vue-next';

// Interfaces
interface Lead {
  lead_id: number;
  uuid: string;
  client_id: string;
  nombre: string;
  email: string | null;
  telefono: string | null;
  necesidad: string | null;
  estado: string;
  num_valoraciones: number;
  notas: string | null;
  valoracion_id?: number | null;
  fecha_captura: string;
  fecha_modificacion?: string | null;
  lead_estado_secuencia?: string | null;
  lead_id_secuencia_asignada?: number | null;
  direccion_propiedad?: string | null;
  tipo_propiedad?: string | null;
  valor_estimado_min?: number | null;
  valor_estimado_max?: number | null;
  valorador_nombre?: string;
  ia_nutrition_status?: string;
  ia_interest_score?: number;
  ia_last_interaction_type?: string;
  ia_last_interaction_date?: string;
}

interface EmailHistoryItem {
  id: number;
  uuid?: string;
  tipo_email: string;
  nombre_paso_display?: string | null;
  asunto_final: string | null;
  cuerpo_final_html?: string | null;
  estado_envio: string;
  fecha_programada_envio: string | null;
  fecha_envio_real: string | null;
}

const props = defineProps<{
  lead: Lead;
}>();

const emit = defineEmits(['close', 'valoracionGuardada', 'verValoracion', 'verLeadRelacionado', 'notasActualizadas']);

const authStore = useAuthStore();
const { showToast } = useToast();
const router = useRouter();

const currentLeadData = ref<Lead>({ ...props.lead });

const emailHistory = ref<EmailHistoryItem[]>([]);
const isLoadingEmailHistory = ref(false);
const emailHistoryError = ref<string | null>(null);
const isUpdatingSequence = ref(false);
const emailSearchQuery = ref('');

const filteredEmailHistory = computed(() => {
  if (!emailSearchQuery.value) {
    return emailHistory.value;
  }
  const query = emailSearchQuery.value.toLowerCase().trim();
  return emailHistory.value.filter(email => {
    const subject = (email.asunto_final || '').toLowerCase();
    const status = (email.estado_envio || '').toLowerCase();
    const type = (email.tipo_email || '').toLowerCase();
    const stepName = (email.nombre_paso_display || '').toLowerCase();

    return subject.includes(query) || status.includes(query) || type.includes(query) || stepName.includes(query);
  });
});

const valoracionesDelLead = ref<any[]>([]);
const isLoadingValoraciones = ref(false);
const errorValoraciones = ref<string | null>(null);

const editableNotes = ref(props.lead.notas || '');
const isSavingNotes = ref(false);
const notasChanged = computed(() => editableNotes.value !== (currentLeadData.value.notas || ''));

const fetchEmailHistory = async (leadId: number) => {
  if (!leadId) return;
  isLoadingEmailHistory.value = true;
  emailHistoryError.value = null;
  try {
    const data = await apiFetch(`lead_email_history.php?lead_id=${leadId}`);
    if (data.success) {
      emailHistory.value = data.history;
    } else {
      throw new Error(data.message || 'Error del servidor al obtener historial de emails.');
    }
  } catch (error: any) {
    console.error("Error fetching email history:", error);
    emailHistoryError.value = error.message || 'No se pudo cargar el historial de emails.';
  } finally {
    isLoadingEmailHistory.value = false;
  }
};

const updateSequenceState = async (action: 'pause' | 'resume', leadIdToUpdate: number) => {
  if (!authStore.token || !currentLeadData.value) {
    showToast("Error: No autenticado o datos del lead no disponibles.", "error");
    return;
  }
  isUpdatingSequence.value = true;
  const endpoint = action === 'pause' ? 'lead_email_sequence_pause.php' : 'lead_email_sequence_resume.php';
  try {
    const data = await apiFetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({ lead_id: leadIdToUpdate })
    });
     
    // La API puede devolver success: false incluso en operaciones exitosas.
    // Un indicador más fiable de éxito es la presencia de `newState`.
    if (data.newState !== undefined && data.message) {
      // Éxito: Actualizar UI y mostrar toast verde
      if (currentLeadData.value) {
        const newStateValue = (data.newState && typeof data.newState === 'string' && data.newState.trim() !== '') ? data.newState : null;
        currentLeadData.value.lead_estado_secuencia = newStateValue as (string | null);
      }
      showToast(data.message, 'success');
    } else {
      // Error: Lanzar excepción para el bloque catch
      throw new Error(data.message || `Error al ${action === 'pause' ? 'pausar' : 'reanudar'} la secuencia.`);
    }
  } catch (e: any) {
    console.error(`Error ${action === 'pause' ? 'pausing' : 'resuming'} sequence:`, e);
    showToast(e.message || `Ocurrió un error al ${action === 'pause' ? 'pausar' : 'reanudar'} la secuencia.`, "error");
  } finally {
    isUpdatingSequence.value = false;
  }
};

const fetchValoracionesDelLead = async (leadId: number) => {
  if (!leadId) return;
  isLoadingValoraciones.value = true;
  errorValoraciones.value = null;
  try {
    const data = await apiFetch(`get_valoraciones_por_lead.php?lead_id=${leadId}`);
    if (data.success) {
      valoracionesDelLead.value = data.valoraciones;
    } else {
      throw new Error(data.message || 'Error del servidor al obtener las valoraciones.');
    }
  } catch (error: any) {
    console.error("Error fetching lead's valoraciones:", error);
    errorValoraciones.value = error.message || 'No se pudieron cargar las valoraciones.';
  } finally {
    isLoadingValoraciones.value = false;
  }
};

const saveLeadNotes = async () => {
  if (!currentLeadData.value) return;
  isSavingNotes.value = true;
  try {
    const result = await apiFetch('update_lead_notes.php', {
      method: 'POST',
      body: JSON.stringify({
        lead_id: currentLeadData.value.lead_id,
        notas: editableNotes.value
      })
    });

    if (!result.success) {
      throw new Error(result.message || 'Error al guardar las notas');
    }

    currentLeadData.value.notas = editableNotes.value;
    currentLeadData.value.fecha_modificacion = new Date().toISOString();
    const leadActualizado = { ...props.lead, notas: editableNotes.value, fecha_modificacion: currentLeadData.value.fecha_modificacion };
    emit('notasActualizadas', leadActualizado);

    showToast('Notas guardadas correctamente.', 'success');
  } catch (error: any) {
    showToast(error.message || 'Error al guardar las notas.', 'error');
    console.error("Error saving notes:", error);
  } finally {
    isSavingNotes.value = false;
  }
};

watch(() => props.lead, (newLead, oldLead) => {
  currentLeadData.value = { ...newLead };
  editableNotes.value = newLead.notas || ''; // Sincronizar editableNotes cuando el lead cambie
  // notasOriginales.value = newLead.notas || ''; // Sincronizar para el computed `notasChanged`

  if (newLead && newLead.lead_id && newLead.lead_id !== oldLead?.lead_id && authStore.token) {
    fetchEmailHistory(newLead.lead_id); 
    fetchValoracionesDelLead(newLead.lead_id); 
  } else if (!authStore.token) {
    emailHistoryError.value = "No autenticado. No se puede cargar el historial de emails.";
    errorValoraciones.value = "No autenticado. No se puede cargar el historial de valoraciones.";
  } else if (!newLead || !newLead.lead_id) {
    emailHistory.value = []; 
    valoracionesDelLead.value = []; 
  }
}, { deep: true, immediate: true });

// Funciones de utilidad (existentes y nuevas)
const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Fecha inválida';
    return date.toLocaleDateString('es-ES', {
      year: 'numeric', month: 'short', day: 'numeric'
    });
  } catch (e) {
    return 'Fecha inválida';
  }
};

const formatDateTime = (dateString: string | null | undefined) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Fecha inválida';
    return date.toLocaleString('es-ES', {
      year: 'numeric', month: '2-digit', day: '2-digit',
      hour: '2-digit', minute: '2-digit',
      hour12: false
    });
  } catch (e) {
    return 'Fecha inválida';
  }
};

const formatPhone = (phone?: string | null) => {
  if (!phone) return '';
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 9) { // Típico español
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  }
  return phone;
};

const getInitials = (name: string) => {
  if (!name) return '??';
  return name.split(' ').map(n => n[0]).slice(0, 2).join('').toUpperCase();
};

const getSecuenciaEstadoClass = (status?: string | null): string => {
  if (!status) return 'bg-gray-100 text-gray-600';
  const classes: { [key: string]: string } = {
    'active': 'bg-green-100 text-green-800',
    'paused_by_user': 'bg-yellow-100 text-yellow-800',
    'paused_by_system': 'bg-orange-100 text-orange-800',
    'completed': 'bg-blue-100 text-blue-800',
    'unsubscribed': 'bg-red-100 text-red-800',
    'error': 'bg-red-200 text-red-900',
    'pending_activation': 'bg-orange-100 text-orange-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const formatSecuenciaEstado = (status?: string | null): string => {
  if (!status) return 'No Asignada';
  const statuses: { [key: string]: string } = {
    'active': 'Activa',
    'paused_by_user': 'Pausada por usuario',
    'paused_by_system': 'Pausada por sistema',
    'completed': 'Completada',
    'unsubscribed': 'Dado de baja',
    'error': 'Error',
    'pending_activation': 'Pendiente de activación'
  };
  return statuses[status] || status.charAt(0).toUpperCase() + status.slice(1);
};

const formatTipoEmail = (tipo?: string) => {
  if (!tipo) return 'Desconocido';
  const MapeoTipos: { [key: string]: string } = {
    'EMAIL_SECUENCIA_IA': 'Email de Secuencia IA',
    'EMAIL_MANUAL': 'Email Manual',
    'EMAIL_CONFIRMACION': 'Email de Confirmación',
  };
  return MapeoTipos[tipo] || tipo.replace(/_/g, ' ');
}

const formatEmailEstado = (estado?: string | null): string => {
  if (!estado) return 'Desconocido';
  const estados: { [key: string]: string } = {
    'sent': 'Enviado',
    'failed': 'Fallido',
    'scheduled': 'Programado',
    'cancelled': 'Cancelado',
    'sending': 'Enviando',
    'pending_initial': 'Pendiente',
  };
  return estados[estado] || estado.charAt(0).toUpperCase() + estado.slice(1);
};

const getEmailEstadoClass = (estado?: string | null): string => {
  if (!estado) return 'bg-gray-100 text-gray-800';
  const classes: { [key: string]: string } = {
    'sent': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'scheduled': 'bg-blue-100 text-blue-800',
    'cancelled': 'bg-gray-200 text-gray-800',
    'sending': 'bg-yellow-100 text-yellow-800',
    'pending_initial': 'bg-purple-100 text-purple-800',
  };
  return classes[estado] || 'bg-gray-100 text-gray-800';
};

const formatCurrency = (value: number | null | undefined) => {
  if (value === null || typeof value === 'undefined') return 'N/A';
  return new Intl.NumberFormat('es-ES', {
    maximumFractionDigits: 0
  }).format(value) + '€';
};

const verDetalleDeValoracion = (valoracionId: number) => {
  if (!valoracionId) {
    showToast('ID de valoración no disponible.', 'warning');
    return;
  }
  // Navegar a la vista de valoraciones y pasar el ID de la valoración para abrirla
  router.push({ name: 'Valoraciones', query: { openValoracionId: valoracionId.toString() } });
  emit('close'); // Cerrar el modal actual del lead
};
</script>

<style scoped>
/* Mejorar el scrollbar para las listas internas */
.max-h-80::-webkit-scrollbar, .max-h-96::-webkit-scrollbar, .max-h-40::-webkit-scrollbar {
  width: 6px;
}
.max-h-80::-webkit-scrollbar-track, .max-h-96::-webkit-scrollbar-track, .max-h-40::-webkit-scrollbar-track {
  background: theme('colors.slate.100');
  border-radius: 3px;
}
.max-h-80::-webkit-scrollbar-thumb, .max-h-96::-webkit-scrollbar-thumb, .max-h-40::-webkit-scrollbar-thumb {
  background: theme('colors.slate.400');
  border-radius: 3px;
}
.max-h-80::-webkit-scrollbar-thumb:hover, .max-h-96::-webkit-scrollbar-thumb:hover, .max-h-40::-webkit-scrollbar-thumb:hover {
  background: theme('colors.slate.500');
}
</style>
