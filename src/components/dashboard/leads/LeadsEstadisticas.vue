<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-5 mb-8">
    
    <!-- Total de Leads -->
    <div v-if="estadisticas.total" class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
      <div class="flex items-center justify-between mb-3">
        <p class="text-sm font-medium text-gray-500 capitalize-first">Total de leads</p>
        <div class="p-2.5 bg-sky-500/10 rounded-lg">
          <UsersIcon class="h-6 w-6 text-sky-600" />
        </div>
      </div>
      <h3 class="text-3xl font-bold text-gray-800">{{ estadisticas.total.valor }}</h3>
      <p v-if="estadisticas.total.mesActual !== undefined" class="mt-1 text-xs text-gray-500">
        <span class="font-bold text-gray-700">{{ estadisticas.total.mesActual }}</span> este mes ·
        <span :class="estadisticas.total.incremento >= 0 ? 'text-green-600' : 'text-red-600'" class="font-semibold">
          <span v-if="estadisticas.total.incremento > 0">+</span>{{ estadisticas.total.incremento }}%
        </span>
      </p>
    </div>

    <!-- Leads en Nutrición Activa -->
    <div v-if="estadisticas.enNutricion" class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
      <div class="flex items-center justify-between mb-3">
        <p class="text-sm font-medium text-gray-500 capitalize-first">Nutrición activa</p>
        <div class="p-2.5 bg-amber-500/10 rounded-lg">
          <SparklesIcon class="h-6 w-6 text-amber-600" />
        </div>
      </div>
      <h3 class="text-3xl font-bold text-gray-800">{{ estadisticas.enNutricion.valor }}</h3>
      <p v-if="estadisticas.enNutricion.mesActual !== undefined" class="mt-1 text-xs text-gray-500">
        <span class="font-bold text-gray-700">{{ estadisticas.enNutricion.mesActual }}</span> este mes ·
        <span :class="estadisticas.enNutricion.incremento >= 0 ? 'text-green-600' : 'text-red-600'" class="font-semibold">
          <span v-if="estadisticas.enNutricion.incremento > 0">+</span>{{ estadisticas.enNutricion.incremento }}%
        </span>
      </p>
    </div>
    
    <!-- Leads en Espera -->
    <div v-if="estadisticas.enEspera" class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
      <div class="flex items-center justify-between mb-3">
        <p class="text-sm font-medium text-gray-500 capitalize-first">En espera</p>
        <div class="p-2.5 bg-gray-500/10 rounded-lg">
          <PauseCircleIcon class="h-6 w-6 text-gray-600" />
        </div>
      </div>
      <h3 class="text-3xl font-bold text-gray-800">{{ estadisticas.enEspera.valor }}</h3>
      <p v-if="estadisticas.enEspera.mesActual !== undefined" class="mt-1 text-xs text-gray-500">
        <span class="font-bold text-gray-700">{{ estadisticas.enEspera.mesActual }}</span> este mes ·
        <span :class="estadisticas.enEspera.incremento >= 0 ? 'text-green-600' : 'text-red-600'" class="font-semibold">
          <span v-if="estadisticas.enEspera.incremento > 0">+</span>{{ estadisticas.enEspera.incremento }}%
        </span>
      </p>
    </div>

    <!-- Leads Completados -->
    <div v-if="estadisticas.completados" class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
      <div class="flex items-center justify-between mb-3">
        <p class="text-sm font-medium text-gray-500 capitalize-first">Completados</p>
        <div class="p-2.5 bg-emerald-500/10 rounded-lg">
          <CheckCircle2Icon class="h-6 w-6 text-emerald-600" />
        </div>
      </div>
      <h3 class="text-3xl font-bold text-gray-800">{{ estadisticas.completados.valor }}</h3>
      <p v-if="estadisticas.completados.mesActual !== undefined" class="mt-1 text-xs text-gray-500">
        <span class="font-bold text-gray-700">{{ estadisticas.completados.mesActual }}</span> este mes ·
        <span :class="estadisticas.completados.incremento >= 0 ? 'text-green-600' : 'text-red-600'" class="font-semibold">
          <span v-if="estadisticas.completados.incremento > 0">+</span>{{ estadisticas.completados.incremento }}%
        </span>
      </p>
    </div>
    
    <!-- Leads Perdidos -->
    <div v-if="estadisticas.perdidos" class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
      <div class="flex items-center justify-between mb-3">
        <p class="text-sm font-medium text-gray-500 capitalize-first">Perdidos</p>
        <div class="p-2.5 bg-red-500/10 rounded-lg">
          <XCircleIcon class="h-6 w-6 text-red-600" />
        </div>
      </div>
      <h3 class="text-3xl font-bold text-gray-800">{{ estadisticas.perdidos.valor }}</h3>
      <p v-if="estadisticas.perdidos.mesActual !== undefined" class="mt-1 text-xs text-gray-500">
        <span class="font-bold text-gray-700">{{ estadisticas.perdidos.mesActual }}</span> este mes ·
        <span :class="estadisticas.perdidos.incremento >= 0 ? 'text-green-600' : 'text-red-600'" class="font-semibold">
          <span v-if="estadisticas.perdidos.incremento > 0">+</span>{{ estadisticas.perdidos.incremento }}%
        </span>
      </p>
    </div>

  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import {
  UsersIcon,
  SparklesIcon,
  PauseCircleIcon,
  CheckCircle2Icon,
  XCircleIcon
} from 'lucide-vue-next';

interface EstadisticaDetalle {
  valor: number;
  mesActual: number;
  incremento: number;
}

interface EstadisticasLeads {
  total: EstadisticaDetalle;
  enNutricion: EstadisticaDetalle;
  enEspera: EstadisticaDetalle;
  completados: EstadisticaDetalle;
  perdidos: EstadisticaDetalle;
}

defineProps<{
  estadisticas: EstadisticasLeads;
}>();
</script>

<style scoped>
/* Estilos adicionales si es necesario */
</style>
