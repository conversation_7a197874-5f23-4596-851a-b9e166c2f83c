<template>
  <div class="bg-white rounded-xl shadow-lg p-4 md:p-6 mb-6 border border-gray-200/75">
    <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
      <h3 class="text-xl font-semibold text-[#051f33] mb-2 sm:mb-0"><PERSON>lt<PERSON> y Búsqueda</h3>
      <div class="flex space-x-3 items-center">
        <button 
          @click="limpiarFiltros" 
          class="text-xs font-medium text-gray-500 hover:text-[#ed8725] transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          :disabled="!hayFiltrosActivos"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
          Limpiar
        </button>
        <button 
          @click="toggleFiltrosAvanzados" 
          class="text-xs font-medium text-[#051f33] hover:text-[#ed8725] transition-colors flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          {{ mostrarFiltrosAvanzados ? 'Simples' : 'Avanzados' }}
        </button>
      </div>
    </div>

    <!-- Filtros básicos -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-3">
      <div>
        <label for="busqueda" class="block text-xs font-medium text-gray-600 mb-1">Buscar</label>
        <div class="relative">
          <input 
            type="text" 
            id="busqueda" 
            v-model="filtros.busqueda" 
            placeholder="Nombre, email..." 
            class="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-[#051f33] focus:border-[#051f33] transition-colors"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div>
        <label for="estado" class="block text-xs font-medium text-gray-600 mb-1">Estado</label>
        <select 
          id="estado" 
          v-model="filtros.estado" 
          class="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-[#051f33] focus:border-[#051f33] transition-colors"
        >
          <option value="">Todos</option>
          <option value="Nuevo">Nuevo</option>
          <option value="Contactado">Contactado</option>
          <option value="Interesado">Interesado</option>
          <option value="No interesado">No interesado</option>
          <option value="Convertido">Convertido</option>
        </select>
      </div>

      <div>
        <label for="fecha" class="block text-xs font-medium text-gray-600 mb-1">Periodo</label>
        <select 
          id="fecha" 
          v-model="filtros.periodo" 
          class="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-[#051f33] focus:border-[#051f33] transition-colors"
        >
          <option value="">Cualquiera</option>
          <option value="hoy">Hoy</option>
          <option value="semana">Últ. semana</option>
          <option value="mes">Últ. mes</option>
          <option value="trimestre">Últ. trimestre</option>
          <option value="año">Últ. año</option>
        </select>
      </div>
    </div>

    <!-- Filtros avanzados (colapsables) -->
    <div v-if="mostrarFiltrosAvanzados" class="border-t border-gray-200 pt-4 mt-4">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        <div>
          <label for="necesidad" class="block text-xs font-medium text-gray-600 mb-1">Necesidad</label>
          <select 
            id="necesidad" 
            v-model="filtros.necesidad" 
            class="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-[#051f33] focus:border-[#051f33] transition-colors"
          >
            <option value="">Todas</option>
            <option value="Comprar">Comprar</option>
            <option value="Vender">Vender</option>
            <option value="Alquilar">Alquilar</option>
            <option value="Invertir">Invertir</option>
            <option value="Información">Información</option>
          </select>
        </div>

        <div>
          <label for="valoraciones" class="block text-xs font-medium text-gray-600 mb-1">Nº Valoraciones</label>
          <select 
            id="valoraciones" 
            v-model="filtros.valoraciones" 
            class="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-[#051f33] focus:border-[#051f33] transition-colors"
          >
            <option value="">Cualquier nº</option>
            <option value="0">Sin valoraciones</option>
            <option value="1">1 valoración</option>
            <option value="2+">2 o más</option>
          </select>
        </div>

        <div>
          <label for="telefono" class="block text-xs font-medium text-gray-600 mb-1">Teléfono</label>
          <select 
            id="telefono" 
            v-model="filtros.telefono" 
            class="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:ring-1 focus:ring-[#051f33] focus:border-[#051f33] transition-colors"
          >
            <option value="">Todos</option>
            <option value="con">Con teléfono</option>
            <option value="sin">Sin teléfono</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, watch } from 'vue';

interface Filtros {
  busqueda: string;
  estado: string;
  periodo: string;
  necesidad: string;
  valoraciones: string;
  telefono: string;
}

const emit = defineEmits(['filtrar']);

const mostrarFiltrosAvanzados = ref(false);
const filtros = ref<Filtros>({
  busqueda: '',
  estado: '',
  periodo: '',
  necesidad: '',
  valoraciones: '',
  telefono: ''
});

const hayFiltrosActivos = computed(() => {
  return filtros.value.busqueda !== '' ||
    filtros.value.estado !== '' ||
    filtros.value.periodo !== '' ||
    filtros.value.necesidad !== '' ||
    filtros.value.valoraciones !== '' ||
    filtros.value.telefono !== '';
});

const toggleFiltrosAvanzados = () => {
  mostrarFiltrosAvanzados.value = !mostrarFiltrosAvanzados.value;
};

const limpiarFiltros = () => {
  filtros.value = {
    busqueda: '',
    estado: '',
    periodo: '',
    necesidad: '',
    valoraciones: '',
    telefono: ''
  };
  aplicarFiltros();
};

const aplicarFiltros = () => {
  // Convertir el periodo a fechas reales
  let fechaDesde = null;
  if (filtros.value.periodo) {
    const hoy = new Date();
    fechaDesde = new Date();
    
    switch (filtros.value.periodo) {
      case 'hoy':
        fechaDesde.setHours(0, 0, 0, 0);
        break;
      case 'semana':
        fechaDesde.setDate(hoy.getDate() - 7);
        break;
      case 'mes':
        fechaDesde.setMonth(hoy.getMonth() - 1);
        break;
      case 'trimestre':
        fechaDesde.setMonth(hoy.getMonth() - 3);
        break;
      case 'año':
        fechaDesde.setFullYear(hoy.getFullYear() - 1);
        break;
    }
  }

  // Emitir evento con los filtros aplicados
  emit('filtrar', {
    ...filtros.value,
    fechaDesde: fechaDesde ? fechaDesde.toISOString() : null
  });
};

// Aplicar filtros automáticamente cuando cambian los filtros básicos o avanzados
watch(filtros, () => {
  aplicarFiltros();
}, { deep: true, flush: 'post' }); // deep: true para observar cambios en propiedades de objeto

// Se eliminó la implementación manual de debounce no utilizada
</script>

<style scoped>
/* Reducir tamaño de fuente y padding de los selects para hacerlos más compactos */
select,
input[type="text"] {
  font-size: 0.875rem; /* text-sm */
  padding-top: 0.375rem; /* py-1.5 approx */
  padding-bottom: 0.375rem;
}
</style>
