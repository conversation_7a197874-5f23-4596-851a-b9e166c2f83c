<template>
  <div class="bg-white shadow-xl rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('fecha_creacion')">
              <div class="flex items-center">
                Fecha
                <span v-if="ordenActual.campo === 'fecha_creacion'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('nombre')">
              <div class="flex items-center">
                Nombre
                <span v-if="ordenActual.campo === 'nombre'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Contacto
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('necesidad')">
              <div class="flex items-center">
                Necesidad
                <span v-if="ordenActual.campo === 'necesidad'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('estado')">
              <div class="flex items-center">
                Estado
                <span v-if="ordenActual.campo === 'estado'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('num_valoraciones')">
              <div class="flex items-center">
                Valoraciones
                <span v-if="ordenActual.campo === 'num_valoraciones'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Acciones
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="lead in leads" :key="lead.id" class="hover:bg-gray-50 transition-colors duration-150">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              {{ formatDate(lead.fecha_creacion) }}
            </td>
            <td class="px-6 py-4 text-sm text-gray-800 font-medium">
              <div class="flex items-center">
                <div class="h-8 w-8 rounded-full bg-impacto-blue/20 text-impacto-blue flex items-center justify-center mr-3">
                  {{ getInitials(lead.nombre) }}
                </div>
                <div>
                  {{ lead.nombre }}
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
              <div class="flex flex-col">
                <a :href="`mailto:${lead.email}`" class="text-impacto-blue hover:underline flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  {{ lead.email }}
                </a>
                <a v-if="lead.telefono" :href="`tel:${lead.telefono}`" class="text-gray-600 hover:text-impacto-blue mt-1 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  {{ formatPhone(lead.telefono) }}
                </a>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
              {{ lead.necesidad || 'No especificada' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span :class="getEstadoClass(lead.estado)" class="px-2 py-1 rounded-full text-xs font-medium">
                {{ lead.estado }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
              <span class="font-medium text-impacto-blue">{{ lead.num_valoraciones }}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end space-x-2">
                <button @click="$emit('ver', lead)" class="text-impacto-blue hover:text-impacto-orange transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </button>
                <button @click="$emit('editar', lead)" class="text-amber-600 hover:text-amber-800 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
                <button @click="$emit('exportar', lead)" class="text-green-600 hover:text-green-800 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
      <div class="flex-1 flex justify-between sm:hidden">
        <button 
          @click="$emit('pagina', paginaActual - 1)" 
          :disabled="paginaActual === 1"
          :class="[
            paginaActual === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50',
            'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md'
          ]"
        >
          Anterior
        </button>
        <button 
          @click="$emit('pagina', paginaActual + 1)" 
          :disabled="paginaActual === totalPaginas"
          :class="[
            paginaActual === totalPaginas ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50',
            'ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md'
          ]"
        >
          Siguiente
        </button>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            Mostrando <span class="font-medium">{{ (paginaActual - 1) * porPagina + 1 }}</span> a <span class="font-medium">{{ Math.min(paginaActual * porPagina, totalItems) }}</span> de <span class="font-medium">{{ totalItems }}</span> resultados
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <button 
              @click="$emit('pagina', paginaActual - 1)" 
              :disabled="paginaActual === 1"
              :class="[
                paginaActual === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-500 hover:bg-gray-50',
                'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 text-sm font-medium'
              ]"
            >
              <span class="sr-only">Anterior</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
            
            <template v-for="pagina in paginas" :key="pagina">
              <button 
                v-if="pagina !== '...'"
                @click="$emit('pagina', pagina)" 
                :class="[
                  pagina === paginaActual ? 'z-10 bg-impacto-blue text-white' : 'bg-white text-gray-500 hover:bg-gray-50',
                  'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium'
                ]"
              >
                {{ pagina }}
              </button>
              <span 
                v-else
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
              >
                ...
              </span>
            </template>
            
            <button 
              @click="$emit('pagina', paginaActual + 1)" 
              :disabled="paginaActual === totalPaginas"
              :class="[
                paginaActual === totalPaginas ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-500 hover:bg-gray-50',
                'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 text-sm font-medium'
              ]"
            >
              <span class="sr-only">Siguiente</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

interface Lead {
  id: number;
  uuid: string;
  client_id: string;
  nombre: string;
  email: string;
  telefono?: string;
  necesidad?: string;
  estado: string;
  num_valoraciones: number;
  notas?: string;
  valoracion_id?: number;
  fecha_creacion: string;
  fecha_modificacion?: string;
}

interface Orden {
  campo: string;
  direccion: 'asc' | 'desc';
}

const props = defineProps<{
  leads: Lead[];
  paginaActual: number;
  porPagina: number;
  totalItems: number;
  ordenActual: Orden;
}>();

const emit = defineEmits(['ver', 'editar', 'exportar', 'ordenar', 'pagina']);

const totalPaginas = computed(() => {
  return Math.ceil(props.totalItems / props.porPagina);
});

// Generar array de páginas para la paginación
const paginas = computed(() => {
  const result = [];
  const maxVisiblePages = 5;
  
  if (totalPaginas.value <= maxVisiblePages) {
    // Si hay menos páginas que el máximo visible, mostrar todas
    for (let i = 1; i <= totalPaginas.value; i++) {
      result.push(i);
    }
  } else {
    // Siempre mostrar la primera página
    result.push(1);
    
    // Calcular el rango de páginas a mostrar alrededor de la página actual
    let start = Math.max(2, props.paginaActual - 1);
    let end = Math.min(totalPaginas.value - 1, props.paginaActual + 1);
    
    // Ajustar para mostrar siempre 3 páginas en el medio
    if (start === 2) end = Math.min(4, totalPaginas.value - 1);
    if (end === totalPaginas.value - 1) start = Math.max(2, totalPaginas.value - 3);
    
    // Añadir elipsis si es necesario
    if (start > 2) result.push('...');
    
    // Añadir páginas del medio
    for (let i = start; i <= end; i++) {
      result.push(i);
    }
    
    // Añadir elipsis si es necesario
    if (end < totalPaginas.value - 1) result.push('...');
    
    // Siempre mostrar la última página
    result.push(totalPaginas.value);
  }
  
  return result;
});

// Cambiar el orden de la tabla
const cambiarOrden = (campo: string) => {
  let nuevaDireccion: 'asc' | 'desc' = 'asc';
  
  if (props.ordenActual.campo === campo) {
    // Si ya estamos ordenando por este campo, invertir la dirección
    nuevaDireccion = props.ordenActual.direccion === 'asc' ? 'desc' : 'asc';
  }
  
  emit('ordenar', {
    campo,
    direccion: nuevaDireccion
  });
};

// Funciones de utilidad
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    year: 'numeric', month: '2-digit', day: '2-digit'
  });
};

const formatPhone = (phone?: string) => {
  if (!phone) return '';
  
  // Eliminar caracteres no numéricos
  const cleaned = phone.replace(/\D/g, '');
  
  // Formatear según el patrón español (por ejemplo: 123 456 789)
  if (cleaned.length === 9) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  }
  
  return phone; // Devolver original si no se puede formatear
};

const getInitials = (name: string) => {
  if (!name) return '';
  
  // Obtener iniciales de cada palabra
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2) // Tomar solo las primeras dos iniciales
    .join('');
};

const getEstadoClass = (estado: string) => {
  const classes = {
    'Nuevo': 'bg-blue-100 text-blue-800',
    'Contactado': 'bg-yellow-100 text-yellow-800',
    'Interesado': 'bg-green-100 text-green-800',
    'No interesado': 'bg-red-100 text-red-800',
    'Convertido': 'bg-purple-100 text-purple-800',
    'default': 'bg-gray-100 text-gray-800'
  };
  
  return classes[estado as keyof typeof classes] || classes.default;
};
</script>

<style scoped>
/* Estilos específicos si son necesarios */
</style>
