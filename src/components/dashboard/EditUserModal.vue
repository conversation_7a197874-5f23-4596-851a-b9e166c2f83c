<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">Editar Usuario</h2>
      <form @submit.prevent="submitUpdate">
        <label class="block mb-2">
          Nombre Completo:
          <input v-model="editableUser.nombre_completo" type="text" required class="border rounded w-full px-3 py-2" />
        </label>
        <label class="block mb-4">
          Email:
          <input v-model="editableUser.email" type="email" required class="border rounded w-full px-3 py-2" />
        </label>
        <div class="flex justify-end gap-2">
          <button type="button" @click="$emit('close')" class="px-4 py-2 bg-gray-300 rounded">Cancelar</button>
          <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded">Guardar</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue';
import { apiFetch } from '@/utils/apiFetch';

interface User {
  id: number;
  nombre_completo: string;
  email: string;
  // Añade otros campos si son necesarios
}

const props = defineProps<{
  user: User;
}>();

const emit = defineEmits(['close', 'updated']);

const editableUser = ref<User>({ ...props.user });

watch(() => props.user, (newUser) => {
  editableUser.value = { ...newUser };
}, { deep: true });

const submitUpdate = async () => {
  try {
    const result = await apiFetch('updateUser.php', {
      method: 'POST',
      body: JSON.stringify(editableUser.value),
    });
    
    if (result.success) {
      emit('updated', editableUser.value);
      emit('close');
    } else {
      alert(result.message || 'Ocurrió un error al actualizar.');
    }
  } catch (error: any) {
    console.error('Error al actualizar el usuario:', error);
    alert(error.data?.message || error.message || 'Error de conexión al actualizar el usuario.');
  }
};
</script>
