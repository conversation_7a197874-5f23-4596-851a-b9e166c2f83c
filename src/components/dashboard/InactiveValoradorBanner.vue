<template>
  <div v-if="shouldShowBanner" class="mb-6">
    <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg shadow-sm">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-red-800">
            Valorador inactivo
          </h3>
          <div class="mt-1 text-sm text-red-700">
            <p>{{ getBannerMessage() }}</p>
          </div>
          <div class="mt-3">
            <router-link
              to="/suscripcion"
              class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-800 bg-red-100 border border-red-300 rounded-md hover:bg-red-200 hover:border-red-400 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Solucionar
            </router-link>
          </div>
        </div>
        <div class="ml-4 flex-shrink-0">
          <button
            @click="dismissBanner"
            class="inline-flex rounded-md p-1.5 text-red-400 hover:text-red-600 hover:bg-red-100 transition-colors"
          >
            <span class="sr-only">Cerrar</span>
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { apiFetch } from '@/utils/apiFetch';

// Estado del valorador
const valoradorStatus = ref<'not_configured' | 'active' | 'inactive' | 'loading'>('loading');
const inactiveReason = ref<string | null>(null);
const subscriptionStatus = ref<string | null>(null);
const isLoading = ref(true);
const isDismissed = ref(false);

// Computed para determinar si mostrar el banner
const shouldShowBanner = computed(() => {
  return !isLoading.value && 
         !isDismissed.value && 
         valoradorStatus.value === 'inactive' && 
         inactiveReason.value === 'subscription_issue';
});

// Función para obtener el estado del valorador
const fetchValoradorStatus = async () => {
  try {
    isLoading.value = true;
    const data = await apiFetch('valorador-config.php');
    
    if (data.success) {
      if (data.config && data.config.client_identifier) {
        // Valorador configurado
        if (data.config.is_active) {
          valoradorStatus.value = 'active';
        } else {
          valoradorStatus.value = 'inactive';
          inactiveReason.value = data.config.inactive_reason || 'unknown';
          subscriptionStatus.value = data.config.subscription_status || null;
        }
      } else {
        // Valorador no configurado
        valoradorStatus.value = 'not_configured';
      }
    }
  } catch (error) {
    console.error('Error fetching valorador status:', error);
    // En caso de error, no mostrar el banner
    valoradorStatus.value = 'active';
  } finally {
    isLoading.value = false;
  }
};

// Función para obtener el mensaje del banner
const getBannerMessage = () => {
  if (inactiveReason.value === 'subscription_issue') {
    switch (subscriptionStatus.value) {
      case 'past_due':
        return 'Tu valorador no puede recibir nuevas valoraciones porque tu suscripción tiene pagos pendientes. Puedes seguir accediendo a tus datos existentes.';
      case 'unpaid':
        return 'Tu valorador no puede recibir nuevas valoraciones porque tu suscripción no se pudo renovar. Actualiza tu método de pago para reactivarlo.';
      case 'canceled':
        return 'Tu valorador no puede recibir nuevas valoraciones porque tu suscripción fue cancelada. Reactiva tu suscripción para continuar.';
      case 'incomplete':
        return 'Tu valorador no puede recibir nuevas valoraciones porque tu suscripción no se pudo completar. Completa el proceso de pago para reactivarlo.';
      default:
        return 'Tu valorador no puede recibir nuevas valoraciones debido a un problema con tu suscripción. Revisa el estado de tu suscripción para reactivarlo.';
    }
  } else if (inactiveReason.value === 'no_subscription') {
    return 'Tu valorador no puede recibir nuevas valoraciones porque no tienes una suscripción activa. Suscríbete para reactivarlo.';
  } else {
    return 'Tu valorador está temporalmente inactivo y no puede recibir nuevas valoraciones. Contacta con soporte si necesitas ayuda.';
  }
};

// Función para cerrar el banner
const dismissBanner = () => {
  isDismissed.value = true;
  // Guardar en localStorage para que no se muestre de nuevo en esta sesión
  localStorage.setItem('valorador_banner_dismissed', 'true');
};

// Verificar si el banner fue cerrado previamente en esta sesión
const checkDismissedStatus = () => {
  const dismissed = localStorage.getItem('valorador_banner_dismissed');
  if (dismissed === 'true') {
    isDismissed.value = true;
  }
};

onMounted(() => {
  checkDismissedStatus();
  fetchValoradorStatus();
});
</script>
