<template>
  <div v-if="showModal" class="fixed inset-0 z-50 overflow-y-auto bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 transition-opacity duration-300 ease-in-out" :class="showModal ? 'opacity-100' : 'opacity-0 pointer-events-none'">
    <div class="relative bg-white rounded-xl shadow-2xl w-full max-w-4xl transform transition-all duration-300 ease-in-out" :class="showModal ? 'scale-100 opacity-100' : 'scale-95 opacity-0'" role="dialog" aria-modal="true" aria-labelledby="modal-title">
      <!-- Cabecera del Modal -->
      <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <h3 id="modal-title" class="text-xl font-semibold text-impacto-blue">{{ props.isSubscriptionActive ? 'Selecciona tu Nuevo Plan' : 'Elige tu Plan' }}</h3>
        <button @click="closeModal" class="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full focus:outline-none focus:ring-2 focus:ring-impacto-orange">
          <svg xmlns='http://www.w3.org/2000/svg' class='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12' />
          </svg>
        </button>
      </div>

      <!-- Contenido del Modal -->
      <div class="p-6 space-y-6">
        <p v-if="!props.isSubscriptionActive && !showInvoicePreview" class="text-md text-gray-700 mb-4 text-center">
          ¡Bienvenido! Elige tu plan para empezar a usar el Valorador IA.
        </p>
        <p v-else-if="!showInvoicePreview" class="text-sm text-gray-600">
          Elige el plan y el ciclo de facturación que mejor se adapte a tus necesidades.
        </p>

        <!-- Vista de Previsualización de Factura (NUEVO) -->
        <div v-if="showInvoicePreview" class="space-y-4">
          <h4 class="text-lg font-semibold text-impacto-blue">Revisa los Cambios en tu Suscripción</h4>
          
          <div v-if="isLoadingPreview" class="text-center py-6">
            <svg class="animate-spin h-8 w-8 text-impacto-orange mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p class="mt-2 text-sm text-gray-600">Calculando detalles de facturación...</p>
          </div>

          <div v-if="previewError" class="bg-red-50 p-4 rounded-md border border-red-200">
            <div class="flex">
              <div class="flex-shrink-0">
                <ExclamationTriangleIcon class="h-5 w-5 text-red-400" aria-hidden="true" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error al obtener la vista previa</h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>{{ previewError }}</p>
                </div>
              </div>
            </div>
          </div>

          <div v-if="invoicePreviewData && !isLoadingPreview && !previewError" class="bg-gray-50 p-4 rounded-lg border border-gray-200 space-y-3 text-sm">
            <div class="grid grid-cols-2 gap-x-4 gap-y-2">
              <div>
                <p class="font-medium text-gray-500">Plan seleccionado:</p>
                <p class="font-semibold text-gray-800">{{ planDetailsData[selectedPlanForConfirmation!.planId]?.name }} ({{ selectedPlanForConfirmation!.billingCycle === 'annual' ? 'Anual' : 'Mensual' }})</p>
              </div>
              <div class="text-right">
                <!-- Título del Monto Principal (Dinámico) -->
                <p class="font-medium text-gray-500">
                  {{ displayInfo.title }}
                </p>
                <p class="text-2xl font-bold" :class="displayInfo.colorClass">
                  {{ displayInfo.sign }}{{ displayInfo.amount }} {{ displayInfo.currency }}
                </p>
              </div>
            </div>

            <!-- Mensajes Detallados por Escenario -->
            <div class="pt-3 space-y-2">
              <!-- Escenario: Nueva Suscripción con Prueba Gratuita -->
              <div v-if="!invoicePreviewData.is_change && invoicePreviewData.trial_ends_at" class="bg-blue-50 p-3 rounded-md border border-blue-200 text-blue-700">
                <p class="font-semibold flex items-center">
                  <CheckIcon class="h-5 w-5 inline mr-1.5 text-blue-500 flex-shrink-0" />
                  ¡Bienvenido/a! Estás iniciando tu prueba gratuita.
                </p>
                <p>Tu prueba terminará el <span class="font-medium">{{ formatDate(invoicePreviewData.trial_ends_at) }}</span>.</p>
                <p>No se realizará ningún cargo hoy. El primer pago recurrente de <span class="font-medium">{{ invoicePreviewData.new_plan_price_next_cycle.toFixed(2) }} {{ invoicePreviewData.currency }}</span> por el plan {{ invoicePreviewData.new_plan_name }} se realizará aproximadamente el <span class="font-medium">{{ formatDate(invoicePreviewData.next_payment_date) }}</span>.</p>
              </div>

              <!-- Escenario: Cambio (Upgrade/Downgrade/Ciclo) Durante Período de Prueba -->
              <div v-else-if="invoicePreviewData.is_change && invoicePreviewData.trial_ends_at" class="bg-sky-50 p-3 rounded-md border border-sky-200 text-sky-700">
                <p class="font-semibold flex items-center">
                  <ExclamationTriangleIcon class="h-5 w-5 inline mr-1.5 text-sky-500 flex-shrink-0" />
                  Cambio de plan durante prueba
                </p>
                <p>Tu período de prueba actual para el plan <span class="font-medium">{{ invoicePreviewData.new_plan_name }}</span> continúa hasta el <span class="font-medium">{{ formatDate(invoicePreviewData.trial_ends_at) }}</span>.</p>
                <p v-if="invoicePreviewData.amount_due_today === 0" class="mt-1">
                  No se realizarán cargos adicionales hoy por este cambio.
                </p>
                <p v-else-if="invoicePreviewData.amount_due_today > 0" class="mt-1">
                  Hoy se aplicará un cargo estimado por Stripe de <span class="font-medium">{{ invoicePreviewData.amount_due_today.toFixed(2) }} {{ invoicePreviewData.currency }}</span>.
                </p>
                 <p v-else-if="invoicePreviewData.amount_due_today < 0" class="mt-1">
                  Se aplicará un crédito estimado por Stripe de <span class="font-medium">{{ Math.abs(invoicePreviewData.amount_due_today).toFixed(2) }} {{ invoicePreviewData.currency }}</span>.
                </p>
                <p class="mt-1">
                  Después de que finalice tu prueba, el primer pago recurrente para el plan <span class="font-medium">{{ invoicePreviewData.new_plan_name }}</span> será de <span class="font-medium">{{ invoicePreviewData.new_plan_price_next_cycle.toFixed(2) }} {{ invoicePreviewData.currency }}</span> ({{ invoicePreviewData.new_plan_billing_cycle === 'year' ? 'anual' : 'mensual' }}), aproximadamente el 
                  <span class="font-medium">{{ formatDate(invoicePreviewData.next_payment_date) }}</span>.
                </p>
              </div>

              <!-- Escenario: Upgrade (Fuera de Prueba) -->
              <div v-else-if="invoicePreviewData.is_change && !invoicePreviewData.trial_ends_at && invoicePreviewData.is_calculated_upgrade" class="bg-yellow-50 p-3 rounded-md border border-yellow-200 text-yellow-700">
                  <p class="font-semibold flex items-center">
                    <ExclamationTriangleIcon class="h-5 w-5 inline mr-1.5 text-yellow-500 flex-shrink-0" />
                    Actualización de plan
                  </p>
                  <p>Estás cambiando al plan superior {{ invoicePreviewData.new_plan_name }}.</p>
                  <div class="mt-2 space-y-1 text-sm">
                    <p>
                        Ajuste por cambio en el ciclo actual (cargo hoy): 
                        <span class="font-medium text-base">{{ invoicePreviewData.amount_due_today.toFixed(2) }} {{ invoicePreviewData.currency }}</span>.
                    </p>
                     <p v-if="invoicePreviewData.proration_calculation_details" class="pl-3 text-xs">
                        (Crédito por plan anterior: {{ invoicePreviewData.proration_calculation_details.credit_for_old_plan.toFixed(2) }} {{ invoicePreviewData.currency }}; 
                        Cargo por nuevo plan este ciclo: {{ invoicePreviewData.proration_calculation_details.charge_for_new_plan_current_cycle.toFixed(2) }} {{ invoicePreviewData.currency }})
                    </p>
                  </div>
                  <p class="mt-3">Tu plan cambiará a <span class="font-semibold">{{ invoicePreviewData.new_plan_name }}</span>. A partir de tu próximo pago, el <span class="font-medium">{{ formatDate(invoicePreviewData.next_payment_date) }}</span>, se cobrarán <span class="font-medium">{{ invoicePreviewData.new_plan_price_next_cycle.toFixed(2) }} {{ invoicePreviewData.currency }}</span> ({{ invoicePreviewData.new_plan_billing_cycle === 'year' ? 'anual' : 'mensual' }}).</p>
              </div>

              <!-- Escenario: Downgrade (Fuera de Prueba) -->
              <div v-else-if="invoicePreviewData.is_change && !invoicePreviewData.trial_ends_at && invoicePreviewData.is_calculated_downgrade" class="bg-indigo-50 p-3 rounded-md border border-indigo-200 text-indigo-700">
                <p class="font-semibold flex items-center">
                  <ExclamationTriangleIcon class="h-5 w-5 inline mr-1.5 text-indigo-500 flex-shrink-0" />
                  Cambio a plan inferior
                </p>
                <p>Estás cambiando al plan {{ invoicePreviewData.new_plan_name }}.</p>
                <div class="mt-2 space-y-1 text-sm">
                    <p>
                        Ajuste por cambio en el ciclo actual (crédito a tu favor): 
                        <span class="font-medium text-base text-green-700">{{ Math.abs(invoicePreviewData.proration_calculation_details ? invoicePreviewData.proration_calculation_details.net_proration_amount : 0).toFixed(2) }} {{ invoicePreviewData.currency }}</span>.
                    </p>
                    <p v-if="invoicePreviewData.proration_calculation_details" class="pl-3 text-xs">
                        (Crédito por plan anterior: {{ invoicePreviewData.proration_calculation_details.credit_for_old_plan.toFixed(2) }} {{ invoicePreviewData.currency }}; 
                        Cargo por nuevo plan este ciclo: {{ invoicePreviewData.proration_calculation_details.charge_for_new_plan_current_cycle.toFixed(2) }} {{ invoicePreviewData.currency }})
                    </p>
                     <p class="mt-1 italic text-xs">
                        Este crédito se aplicará automáticamente a tus próximas facturas hasta que se consuma.
                    </p>
                </div>
                <p class="mt-3">Tu plan cambiará a <span class="font-semibold">{{ invoicePreviewData.new_plan_name }}</span>. A partir de tu próximo pago, el <span class="font-medium">{{ formatDate(invoicePreviewData.next_payment_date) }}</span>, se cobrarán <span class="font-medium">{{ invoicePreviewData.new_plan_price_next_cycle.toFixed(2) }} {{ invoicePreviewData.currency }}</span> ({{ invoicePreviewData.new_plan_billing_cycle === 'year' ? 'anual' : 'mensual' }}), menos cualquier crédito restante.</p>
              </div>

              <!-- Escenario: Cambio Lateral (Fuera de Prueba, sin prorrateo neto o sin detalles de prorrateo y sin cargo hoy) -->
              <div v-else-if="invoicePreviewData.is_change && !invoicePreviewData.trial_ends_at && !invoicePreviewData.is_calculated_upgrade && !invoicePreviewData.is_calculated_downgrade" 
                   class="bg-blue-50 p-3 rounded-md border border-blue-200 text-blue-700">
                <p class="font-semibold flex items-center">
                  <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5 inline mr-1.5 text-blue-500 flex-shrink-0' fill='none' viewBox='0 0 24 24' stroke='currentColor' stroke-width='2'><path stroke-linecap='round' stroke-linejoin='round' d='M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4' /></svg>
                  Cambio de plan
                </p>
                <p>Estás cambiando al plan {{ invoicePreviewData.new_plan_name }}.</p>
                <p class="mt-1">No hay cargos ni créditos inmediatos asociados con este cambio.</p>
                <p class="mt-3">Tu plan cambiará a <span class="font-semibold">{{ invoicePreviewData.new_plan_name }}</span>. A partir de tu próximo pago, el <span class="font-medium">{{ formatDate(invoicePreviewData.next_payment_date) }}</span>, se cobrarán <span class="font-medium">{{ invoicePreviewData.new_plan_price_next_cycle.toFixed(2) }} {{ invoicePreviewData.currency }}</span> ({{ invoicePreviewData.new_plan_billing_cycle === 'year' ? 'anual' : 'mensual' }}).</p>
              </div>
              
              <!-- Escenario: Nueva Suscripción Sin Prueba (Pago Inmediato) -->
              <div v-else-if="!invoicePreviewData.is_change && !invoicePreviewData.trial_ends_at && invoicePreviewData.amount_due_today > 0" class="bg-green-50 p-3 rounded-md border border-green-200 text-green-700">
                <p class="font-semibold flex items-center">
                  <CheckIcon class="h-5 w-5 inline mr-1.5 text-green-500 flex-shrink-0" />
                  ¡Bienvenido/a! Estás suscrito al plan {{ invoicePreviewData.new_plan_name }}.
                </p>
                <p>Tu próximo pago será de <span class="font-medium">{{ invoicePreviewData.amount_due_today.toFixed(2) }} {{ invoicePreviewData.currency }}</span> el <span class="font-medium">{{ formatDate(invoicePreviewData.next_payment_date) }}</span>.</p>
              </div>

              <!-- Mensaje Genérico si ningún otro escenario coincide (para depuración o casos borde) -->
              <div v-else class="bg-gray-100 p-3 rounded-md border border-gray-300 text-gray-700">
                  <p class="font-semibold">Resumen de cambios:</p>
                  <p v-if="invoicePreviewData.amount_due_today !== 0">Monto a pagar/recibir hoy: <span class="font-medium">{{ invoicePreviewData.amount_due_today.toFixed(2) }} {{ invoicePreviewData.currency }}</span>.</p>
                  <p v-else>No hay cargos inmediatos hoy.</p>
                  <p>Próximo ciclo de pago comienza el <span class="font-medium">{{ formatDate(invoicePreviewData.next_payment_date) }}</span> por un total de <span class="font-medium">{{ invoicePreviewData.new_plan_price_next_cycle.toFixed(2) }} {{ invoicePreviewData.currency }}</span>.</p>
              </div>
            </div>

            <!-- Detalles de la Factura Estimada de Stripe - ELIMINADO PARA CAMBIOS FUERA DE PRUEBA -->
            <!-- Solo mostrar detalles de factura si estamos en un escenario de prueba activa Y Stripe los devuelve -->
            <div v-if="invoicePreviewData.trial_ends_at && invoicePreviewData.lines && invoicePreviewData.lines.length > 0">
              <div class="space-y-1 pt-3 border-t border-gray-300">
                <p class="font-medium text-gray-700">Detalles estimados de Stripe para cambio en prueba:</p>
                <ul class="list-disc list-inside pl-2 space-y-0.5 text-gray-600">
                  <li v-for="(line, index) in invoicePreviewData.lines" :key="line.id || index">
                    {{ line.description }} ({{ line.quantity || 1 }} x {{ (line.amount / (line.quantity || 1)).toFixed(2) }} {{ invoicePreviewData.currency }}) : <span class="font-semibold">{{ line.amount.toFixed(2) }} {{ invoicePreviewData.currency }}</span>
                    <span v-if="line.proration && line.period_start && line.period_end" class="block pl-5 text-xs text-gray-500">(Prorrateo del {{ formatDate(line.period_start) }} al {{ formatDate(line.period_end) }})</span>
                  </li>
                </ul>
              </div>
              <!-- ... otros detalles como descuentos y saldos podrían ir aquí si son relevantes para Invoice::upcoming en prueba ... -->
            </div>

             <p class="text-xs text-gray-500 pt-3 border-t border-gray-300">
              Al confirmar, aceptas los <a href="/terms" target="_blank" class="underline text-impacto-blue hover:text-orange-600">Términos de Servicio</a>.
               <span v-if="!invoicePreviewData.trial_ends_at">El monto mostrado hoy es el ajuste por el cambio en tu ciclo actual. El costo completo del nuevo plan se aplicará en tu próxima fecha de facturación.</span>
               <span v-else>Los montos para cambios en prueba son estimaciones de Stripe.</span>
            </p>
          </div>

          <div class="mt-6 flex flex-col sm:flex-row-reverse gap-3">
            <button
              @click="confirmPlanSelection"
              :disabled="isProcessingChange || isLoadingPreview || !!previewError"
              class="w-full sm:w-auto flex items-center justify-center px-6 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-60 disabled:cursor-not-allowed"
            >
              <svg v-if="isProcessingChange" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
                <circle class='opacity-25' cx='12' cy='12' r='10' stroke='currentColor' stroke-width='4'></circle>
                <path class='opacity-75' fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
              </svg>
              {{ isProcessingChange ? 'Procesando...' : 'Confirmar y Cambiar Plan' }}
            </button>
            <button
              @click="cancelPlanSelection" 
              :disabled="isProcessingChange || isLoadingPreview"
              class="w-full sm:w-auto mt-2 sm:mt-0 flex items-center justify-center px-6 py-2.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue disabled:opacity-60 disabled:cursor-not-allowed"
            >
              Volver / Cancelar
            </button>
          </div>
        </div>

        <!-- Selector de ciclo de facturación (solo si no se muestra la vista previa de factura) -->
        <div v-if="!showInvoicePreview" class="mb-6 flex justify-center items-center space-x-3">
          <div class="relative flex items-center p-0.5 bg-impacto-blue/10 rounded-full shadow-sm">
            <button
              @click="selectedBillingCycleForModal = 'monthly'"
              :class="['px-4 py-1.5 text-xs sm:text-sm font-medium rounded-full transition-colors duration-200 ease-in-out relative z-10',
                      selectedBillingCycleForModal === 'monthly' ? 'bg-impacto-orange text-white shadow' : 'text-gray-600 hover:text-impacto-blue']"
            >
              Mensual
            </button>
            <button
              @click="selectedBillingCycleForModal = 'annual'"
              :class="['px-4 py-1.5 text-xs sm:text-sm font-medium rounded-full transition-colors duration-200 ease-in-out relative z-10 flex items-center gap-1.5',
                      selectedBillingCycleForModal === 'annual' ? 'bg-impacto-orange text-white shadow' : 'text-gray-600 hover:text-impacto-blue']"
            >
              Anual
              <span class="bg-green-500 text-white text-[0.6rem] font-bold px-1.5 py-0.5 rounded-sm leading-none">
                AHORRO
              </span>
            </button>
          </div>
        </div>

        <!-- Grid de Planes Disponibles (solo si no se muestra la vista previa de factura) -->
        <div v-if="!showInvoicePreview" class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div
            v-for="plan in planDetailsData"
            :key="plan.id"
            :class="[
              'p-6 border rounded-lg flex flex-col transition-all duration-300 hover:shadow-xl relative',
              (props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal) ? 'border-impacto-orange ring-2 ring-impacto-orange shadow-lg bg-orange-50/30' : 'border-gray-200 bg-white',
              isProcessingChange || (plan.id === selectedPlanForConfirmation?.planId && selectedBillingCycleForModal === selectedPlanForConfirmation?.billingCycle) ? 'opacity-70 cursor-not-allowed' : ''
            ]"
          >
            <div v-if="plan.isPopular && !(props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal)" 
                 class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 z-10">
              <span class="inline-flex items-center px-3 py-0.5 rounded-full text-xs font-semibold bg-impacto-orange text-white shadow-md">
                RECOMENDADO
              </span>
            </div>
            <div v-if="(props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal)" class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2">
              <span class="inline-flex items-center px-3 py-0.5 rounded-full text-xs font-semibold bg-gray-700 text-white shadow-md">
                PLAN ACTUAL
              </span>
            </div>
            
            <h4 class="text-lg font-semibold text-impacto-blue pt-3">{{ plan.name }}</h4>
            <div class="mt-2 mb-4">
              <span class="text-3xl font-extrabold text-gray-900">
                {{ getPriceForDisplay(plan.id).mainPrice }}
              </span>
              <span class="text-sm font-medium text-gray-500">/mes</span>
              <p v-if="selectedBillingCycleForModal === 'annual'" class="text-xs text-gray-500">
                Total {{ getPriceForDisplay(plan.id).annualTotal }}€ al año
              </p>
            </div>
            <ul class="space-y-2 text-sm text-gray-600 mb-6">
              <li v-for="(benefit, index) in plan.benefits" :key="index" class="flex items-start">
                <CheckIcon class="flex-shrink-0 h-5 w-5 text-green-500 mr-2 mt-0.5" />
                <span>{{ benefit }}</span>
              </li>
            </ul>
            <button
              @click="selectPlan(plan.id, selectedBillingCycleForModal)"
              :disabled="isProcessingChange || (props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal) || (plan.id === selectedPlanForConfirmation?.planId && selectedBillingCycleForModal === selectedPlanForConfirmation?.billingCycle)"
              :class="[
                'mt-auto w-full py-2.5 px-4 text-sm font-semibold rounded-md transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2',
                (props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal) 
                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed' 
                  : 'bg-impacto-blue text-white hover:bg-impacto-blue/90 focus:ring-impacto-blue shadow-md hover:shadow-lg'
              ]"
            >
              {{ (props.currentPlanId === plan.id && props.currentBillingCycle === selectedBillingCycleForModal) ? 'Plan Actual' : 'Seleccionar Plan' }}
            </button>
          </div>
        </div>

        <!-- Mensaje de Error General para la selección de plan -->
        <div v-if="generalError && !showInvoicePreview" class="mt-4 bg-red-50 p-3 rounded-md border border-red-200">
            <div class="flex">
              <div class="flex-shrink-0">
                <ExclamationTriangleIcon class="h-5 w-5 text-red-400" aria-hidden="true" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error</h3>
                <div class="mt-1 text-sm text-red-700">
                  <p>{{ generalError }}</p>
                </div>
              </div>
            </div>
          </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineEmits, defineProps, computed } from 'vue';
import type { PropType } from 'vue';
// import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'; // Comentado o eliminado
import { CheckIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/outline';
// import { StarIcon as StarIconSolid } from '@heroicons/vue/20/solid'; // Ya estaba comentado
import { apiFetch } from '@/utils/apiFetch';
// Stripe imports (asegúrate de que vue-stripe-js y @stripe/stripe-js estén instalados)
// Puede que necesites ajustar estas importaciones según tu configuración exacta y si usas Stripe Elements directamente aquí.
// import { StripeElements, StripeElement } from 'vue-stripe-js'; 
// import { loadStripe, Stripe, StripeElements as StripeElementsType, StripeCardElement } from '@stripe/stripe-js';

// --- Props --- 
const props = defineProps({
  showModal: {
    type: Boolean,
    required: true,
  },
  currentPlanId: {
    type: String as PropType<string | null>,
    default: null,
  },
  currentBillingCycle: {
    type: String as PropType<'monthly' | 'annual' | null>,
    default: null,
  },
  isSubscriptionActive: { // Indica si el usuario ya tiene una suscripción activa (incluso en prueba)
    type: Boolean,
    default: false,
  },
  currentSubscriptionDetails: { // Detalles completos de la suscripción actual (UserSubscription)
    type: Object as PropType<any | null>, // Reemplazar 'any' con la interfaz UserSubscription
    default: null,
  }
});

// --- Emits --
const emit = defineEmits(['close', 'plan-changed', 'change-initiated', 'change-error', 'stripe-action-required']);

interface InvoicePreviewProrationDetails {
  credit_for_old_plan: number;
  charge_for_new_plan_current_cycle: number;
  net_proration_amount: number; 
}
interface InvoicePreviewLineItem {
  id: string;
  description: string;
  amount: number;
  quantity: number;
  period_start?: number; 
  period_end?: number;   
  proration?: boolean;
  // is_change: boolean; // Movido a InvoicePreviewData
  // is_actual_upgrade?: boolean; // Eliminado
  // is_actual_downgrade?: boolean; // Eliminado
  // proration_calculation_details?: InvoicePreviewProrationDetails; // Movido a InvoicePreviewData
  // lines?: InvoicePreviewLineItem[]; // Movido a InvoicePreviewData
}
interface InvoicePreviewData {
  amount_due_today: number; 
  currency: string;
  next_payment_date: number; 
  new_plan_price_next_cycle: number; 
  new_plan_name: string;
  new_plan_billing_cycle: 'month' | 'year';
  trial_ends_at: number | null; 
  is_change: boolean; 
  // is_actual_upgrade?: boolean; // Eliminado
  // is_actual_downgrade?: boolean; // Eliminado
  proration_calculation_details?: InvoicePreviewProrationDetails; 
  lines?: InvoicePreviewLineItem[]; 
  is_same_plan?: boolean; // Añadido por si acaso, aunque el backend ya lo maneja
  current_plan_name?: string; // Añadido para consistencia
  customer_balance_cents?: number; // Añadido
  balance_applied_to_due_amount_cents?: number; // Añadido
  is_calculated_upgrade?: boolean; // Añadido
  is_calculated_downgrade?: boolean; // Añadido
}

// --- Refs ---
const selectedBillingCycleForModal = ref<'monthly' | 'annual'>('monthly');
const selectedPlanForConfirmation = ref<{ planId: string; billingCycle: 'monthly' | 'annual' } | null>(null);
const showInvoicePreview = ref(false);
const invoicePreviewData = ref<InvoicePreviewData | null>(null);
const isLoadingPreview = ref(false);
const previewError = ref<string | null>(null);
const generalError = ref<string | null>(null); // Para errores generales en el modal
const isProcessingChange = ref(false);

// --- Computed property for top display summary ---
const displayInfo = computed(() => {
  const data = invoicePreviewData.value;
  if (!data) {
    return { title: 'Total hoy estimado:', amount: '0.00', currency: 'EUR', colorClass: 'text-gray-800', sign: '' };
  }

  const isChangeNoTrial = data.is_change && !data.trial_ends_at;
  const netProration = data.proration_calculation_details?.net_proration_amount;
  const hasProrationDetails = typeof netProration === 'number';

  // Case 1: Downgrade (fuera de prueba) con crédito neto de prorrateo
  if (isChangeNoTrial && data.is_calculated_downgrade && hasProrationDetails && netProration > 0) {
    return {
      title: 'Crédito por cambio (a tu favor):',
      amount: Math.abs(netProration).toFixed(2),
      currency: data.currency,
      colorClass: 'text-green-600',
      sign: '' // Label implies credit
    };
  }

  // Case 2: Upgrade (fuera de prueba) con cargo inmediato
  if (isChangeNoTrial && data.is_calculated_upgrade && data.amount_due_today > 0) {
    return {
      title: 'Cargo inmediato por cambio:',
      amount: Math.abs(data.amount_due_today).toFixed(2),
      currency: data.currency,
      colorClass: 'text-impacto-orange',
      sign: ''
    };
  }

  // Case 3: Cambio lateral (fuera de prueba) sin costo/crédito inmediato Y sin prorrateo neto o prorrateo es cero
  if (isChangeNoTrial && !data.is_calculated_upgrade && !data.is_calculated_downgrade && data.amount_due_today === 0 && (!hasProrationDetails || netProration === 0) ) {
     return {
      title: 'Ajuste por cambio:',
      amount: Math.abs(data.amount_due_today).toFixed(2), // Should be 0.00
      currency: data.currency,
      colorClass: 'text-gray-800',
      sign: ''
    };
  }
  
  // Case 4: Cambio durante prueba (puede tener amount_due_today != 0 para algunos tipos de cambio en prueba)
  if (data.is_change && data.trial_ends_at) {
    return {
      title: 'Total hoy (cambio en prueba):',
      amount: Math.abs(data.amount_due_today).toFixed(2),
      currency: data.currency,
      colorClass: data.amount_due_today === 0 ? 'text-gray-800' : (data.amount_due_today > 0 ? 'text-impacto-orange' : 'text-green-600'),
      sign: data.amount_due_today < 0 ? '-' : ''
    };
  }

  // Case 5: Nueva suscripción con prueba (amount_due_today debería ser 0)
  if (!data.is_change && data.trial_ends_at) {
     return {
      title: 'Total hoy (inicio de prueba):',
      amount: Math.abs(data.amount_due_today).toFixed(2), 
      currency: data.currency,
      colorClass: 'text-gray-800',
      sign: ''
    };
  }

  // Default / Nueva suscripción con pago inmediato / Otros casos no cubiertos explícitamente
  // This will also catch upgrades where amount_due_today is 0 due to customer balance.
  let defaultTitle = 'Total hoy estimado:';
  if (isChangeNoTrial && data.is_calculated_upgrade && data.amount_due_today === 0 && hasProrationDetails && netProration > 0) {
      defaultTitle = 'Cargo cubierto por saldo/crédito:'; // Upgrade cost fully covered by existing balance or proration credit from a previous downgrade
  } else if (isChangeNoTrial && data.is_calculated_upgrade) {
      defaultTitle = 'Cargo inmediato por cambio:';
  }


  return {
    title: defaultTitle,
    amount: Math.abs(data.amount_due_today).toFixed(2),
    currency: data.currency,
    colorClass: data.amount_due_today > 0 && !data.trial_ends_at ? 'text-impacto-orange' : 'text-gray-800',
    sign: '' 
  };
});

// --- Plan Details (Hardcoded - similar a ContratacionPage) ---
const planDetailsData: Record<string, { 
  id: string; 
  name: string; 
  price_monthly: number; 
  price_annual: number; 
  benefits: string[]; 
  stripe_price_id_monthly: string; 
  stripe_price_id_annual: string; 
  isPopular?: boolean;
}> = {
  'ia-starter': {
    id: 'ia-starter', name: 'IA Starter', price_monthly: 49, price_annual: 420, 
    stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_STARTER_MONTHLY || '',
    stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_STARTER_ANNUAL || '',
    benefits: ['Valorador IA personalizado', 'Informes de valoración básicos', 'Secuencia IA estándar', '1 Usuario en Dashboard'],
  },
  'ia-pro': {
    id: 'ia-pro', name: 'IA Pro', price_monthly: 89, price_annual: 780, 
    stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_PRO_MONTHLY || '',
    stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_PRO_ANNUAL || '',
    benefits: ['Todo en IA Starter', 'Secuencias IA avanzadas', 'Hasta 3 Usuarios en Dashboard', 'Emails desde tu dominio', 'Soporte prioritario'], 
    isPopular: true,
  },
  'ia-elite': {
    id: 'ia-elite', name: 'IA Élite', price_monthly: 149, price_annual: 1320, 
    stripe_price_id_monthly: import.meta.env.VITE_STRIPE_PRICE_ID_ELITE_MONTHLY || '',
    stripe_price_id_annual: import.meta.env.VITE_STRIPE_PRICE_ID_ELITE_ANNUAL || '',
    benefits: ['Todo en IA Pro', 'Secuencias IA personalizables y múltiples', 'Usuarios ilimitados', 'Exportación de datos', 'Configuración asistida'],
  }
};

const getPriceForDisplay = (planId: keyof typeof planDetailsData) => {
  const plan = planDetailsData[planId];
  if (!plan) return { mainPrice: 'N/A', annualTotal: 'N/A' };

  let mainPriceToShow: number;
  if (selectedBillingCycleForModal.value === 'monthly') {
    mainPriceToShow = plan.price_monthly;
  } else {
    mainPriceToShow = parseFloat((plan.price_annual / 12).toFixed(2));
  }
  
  // Formatear igual que en PricingSection (sin decimales para números enteros)
  const formattedMainPrice = Number.isInteger(mainPriceToShow) ? mainPriceToShow.toFixed(0) : mainPriceToShow.toFixed(2);
  const annualTotal = Number.isInteger(plan.price_annual) ? plan.price_annual.toFixed(0) : plan.price_annual.toFixed(2);

  return {
    mainPrice: `€${formattedMainPrice}`,
    annualTotal: annualTotal
  };
};

const closeModal = () => {
  if (isProcessingChange.value) return;
  resetModalState();
  emit('close');
};

const resetModalState = () => {
  showInvoicePreview.value = false;
  invoicePreviewData.value = null;
  selectedPlanForConfirmation.value = null;
  previewError.value = null;
  generalError.value = null;
  // No resetear selectedBillingCycleForModal aquí para mantener la última selección del usuario
  // si vuelve a abrir el modal en la misma sesión.
};

const selectPlan = async (planId: string, billingCycle: 'monthly' | 'annual') => {
  if (isProcessingChange.value) return;
  if (props.currentPlanId === planId && props.currentBillingCycle === billingCycle) {
    generalError.value = "Ya estás suscrito a este plan y ciclo de facturación.";
    return;
  }

  generalError.value = null;
  previewError.value = null;
  selectedPlanForConfirmation.value = { planId, billingCycle };
  showInvoicePreview.value = true;
  isLoadingPreview.value = true;
  invoicePreviewData.value = null; // Limpiar datos previos

  try {
    // Lógica para obtener la vista previa de la factura
    const plan = planDetailsData[planId];
    if (!plan) {
      throw new Error('Detalles del plan no encontrados.');
    }

    const stripePriceId = billingCycle === 'monthly' 
      ? plan.stripe_price_id_monthly 
      : plan.stripe_price_id_annual;

    if (!stripePriceId) {
      throw new Error('ID de precio de Stripe no encontrado para el plan y ciclo seleccionados.');
    }

    const payload = {
      new_plan_slug: planId,
      new_billing_cycle: billingCycle,
      new_stripe_price_id: stripePriceId,
    };
    const response = await apiFetch('/api/preview-subscription-change.php', {
      method: 'POST',
      body: JSON.stringify(payload),
    });

    if (!response.success) {
      throw new Error(response.message || 'No se pudo obtener la vista previa del cambio de plan.');
    }
    
    invoicePreviewData.value = response.data as InvoicePreviewData;

  } catch (error: any) {
    console.error("Error al obtener la vista previa de la factura:", error);
    previewError.value = error.message || 'Ocurrió un error al calcular los detalles del cambio.';
  } finally {
    isLoadingPreview.value = false;
  }
};

const confirmPlanSelection = async () => {
  if (!selectedPlanForConfirmation.value || !invoicePreviewData.value) {
    generalError.value = "No hay un plan seleccionado para confirmar o faltan datos de la vista previa.";
    return;
  }
  
  isProcessingChange.value = true;
  generalError.value = null; // Limpiar errores generales
  emit('change-initiated');

  try {
    const payload = {
      new_plan_slug: selectedPlanForConfirmation.value.planId,
      new_billing_cycle: selectedPlanForConfirmation.value.billingCycle,
    };

    // const response = await updateSubscriptionPlan(payload); // Si tuvieras el servicio
    const response = await apiFetch('/api/update-subscription-plan.php', {
      method: 'POST',
      body: JSON.stringify(payload)
    });

    if (response.success) {
      if (response.client_secret) { // Stripe requiere acción adicional
        emit('stripe-action-required', response.client_secret);
        resetModalState(); // Resetea y cierra el modal
        emit('close'); // Asegurar cierre
      } else { // Cambio de plan exitoso sin acción de Stripe
        emit('plan-changed', { 
          newPlanId: selectedPlanForConfirmation.value.planId,
          newBillingCycle: selectedPlanForConfirmation.value.billingCycle,
          message: response.message || 'Plan actualizado correctamente.' 
        });
        resetModalState(); // Resetea y cierra el modal
        emit('close'); // Asegurar cierre
      }
    } else {
      throw new Error(response.message || 'No se pudo actualizar la suscripción.');
    }
  } catch (error: any) {
    console.error("Error al confirmar el cambio de plan:", error);
    previewError.value = error.message || 'Ocurrió un error al procesar el cambio.';
    emit('change-error', error.message || 'Ocurrió un error');
  } finally {
    isProcessingChange.value = false;
  }
};

const cancelPlanSelection = () => {
  if (isProcessingChange.value) return;
  showInvoicePreview.value = false;
  invoicePreviewData.value = null;
  selectedPlanForConfirmation.value = null;
  previewError.value = null;
};

const formatDate = (timestamp: number | string | null | undefined): string => {
  if (!timestamp) return 'N/A';

  let date;
  // Verificar si el timestamp es una cadena y no es puramente numérico
  if (typeof timestamp === 'string' && isNaN(Number(timestamp))) {
    // Intentar crear la fecha directamente desde la cadena (ej: "2025-07-02 19:45:42")
    // Reemplazar espacios con 'T' para mejorar compatibilidad con ISO 8601 si es necesario, aunque muchos navegadores lo manejan.
    date = new Date(timestamp.replace(' ', 'T')); 
  } else {
    // Si es numérico o una cadena numérica, tratarlo como timestamp en segundos
    date = new Date(Number(timestamp) * 1000);
  }

  // Comprobar si la fecha es válida
  if (isNaN(date.getTime())) {
    return 'Fecha inválida'; // O manejar el error como prefieras
  }

  return date.toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

watch(() => props.showModal, (newValue) => {
  if (newValue) { // Cuando el modal se abre
    resetModalState(); // Siempre resetea al abrir
    if (props.currentBillingCycle) {
      selectedBillingCycleForModal.value = props.currentBillingCycle;
    } else {
      selectedBillingCycleForModal.value = 'monthly'; // Default si no hay ciclo actual
    }
  }
});

onMounted(() => {
  if (props.currentBillingCycle) {
    selectedBillingCycleForModal.value = props.currentBillingCycle;
  }
});

</script>

<style scoped>
/* Estilos para la transición del modal */
.transition-opacity {
  transition-property: opacity;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.duration-300 {
  transition-duration: 300ms;
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transition-all {
  transition-property: all;
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: var(--tw-translate-x, 0) var(--tw-translate-y, 0) var(--tw-rotate, 0) var(--tw-skew-x, 0) var(--tw-skew-y, 0) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: var(--tw-translate-x, 0) var(--tw-translate-y, 0) var(--tw-rotate, 0) var(--tw-skew-x, 0) var(--tw-skew-y, 0) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.pointer-events-none {
  pointer-events: none;
}

</style> 