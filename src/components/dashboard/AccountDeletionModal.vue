<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Overlay de fondo -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="closeModal"></div>

      <!-- Centrar el modal -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <!-- Contenido del modal -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Confirmar eliminación de cuenta
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Esta acción es <strong class="font-semibold text-red-600">permanente e irreversible</strong>.
                  Toda tu información, incluyendo valoraciones, leads, configuración de suscripción y datos personales, será eliminada.
                </p>
                <p class="mt-3 text-sm text-gray-500">
                  Para confirmar, por favor escribe la frase <strong class="text-gray-700">eliminar mi cuenta</strong> en el campo de abajo:
                </p>
                <input 
                  type="text" 
                  v-model="confirmationText"
                  placeholder="eliminar mi cuenta"
                  class="mt-2 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm"
                  :class="{'border-red-500': confirmationError}"
                />
                <p v-if="confirmationError" class="mt-1 text-xs text-red-500">{{ confirmationError }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            type="button" 
            :disabled="!isConfirmationTextValid || isLoading"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            @click="confirmDeletion"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isLoading ? 'Procesando...' : 'Eliminar mi cuenta permanentemente' }}
          </button>
          <button 
            type="button" 
            :disabled="isLoading"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-impacto-blue sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            @click="closeModal"
          >
            Cancelar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Props {
  show: boolean;
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  isLoading: false,
});

const emit = defineEmits<{ 
  (e: 'close'): void;
  (e: 'confirm'): void;
}>();

const confirmationText = ref('');
const confirmationError = ref<string | null>(null);
const requiredConfirmationPhrase = 'eliminar mi cuenta';

const isConfirmationTextValid = computed(() => {
  return confirmationText.value === requiredConfirmationPhrase;
});

function closeModal() {
  if (!props.isLoading) {
    confirmationText.value = '';
    confirmationError.value = null;
    emit('close');
  }
}

function confirmDeletion() {
  if (isConfirmationTextValid.value) {
    confirmationError.value = null;
    emit('confirm');
  } else {
    confirmationError.value = `Debes escribir \"${requiredConfirmationPhrase}\" para confirmar.`;
  }
}
</script>

<style scoped>
/* Estilos adicionales si son necesarios */
</style> 