<template>
  <aside class="w-64 text-white flex flex-col h-full overflow-hidden relative" style="background-color: #051f33;">
    <!-- Fondo con patrón tecnológico sutil -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute top-0 left-0 w-full h-full bg-pattern-dots"></div>
    </div>
    
    <!-- Logo y nombre de la aplicación -->
    <div class="p-4 mb-2 relative z-10">
      <div class="flex items-center justify-center mb-4">
        <img src="/img/logo-impacto-automation-valorador-white.svg" alt="Impacto Automation" class="h-8" />
      </div>
      <!-- Badge de administrador si corresponde -->
      <div v-if="isAdmin" class="flex justify-center mb-2">
        <span class="bg-impacto-orange text-white text-xs font-bold px-2 py-1 rounded-md flex items-center gap-1.5">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          ADMINISTRADOR
        </span>
      </div>
      <div class="h-px bg-white/20 w-full my-2"></div>
    </div>
    
    <nav class="flex-1 p-2 relative z-10 overflow-y-auto">
      <!-- Sección de administración (solo visible para administradores) -->
      <div v-if="isAdmin" class="mb-4">
        <div class="px-3 py-2">
          <h3 class="text-xs font-semibold text-white/70 uppercase tracking-wider">Administración</h3>
        </div>
        <ul class="space-y-1">
          <li>
            <router-link 
              :to="{ name: 'AdminDashboard' }"
              class="dashboard-link block transition-all duration-300"
              exact
              @click="handleNavigationInternal"
            >
              <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
                <span class="flex items-center gap-3">
                  <div class="p-2 bg-impacto-orange/20 rounded-lg border border-impacto-orange/30">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <span class="font-medium">Panel general</span>
                </span>
              </div>
            </router-link>
          </li>
          <li>
            <router-link 
              :to="{ name: 'AdminUsuarios' }"
              class="dashboard-link block transition-all duration-300"
              @click="handleNavigationInternal"
            >
              <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
                <span class="flex items-center gap-3">
                  <div class="p-2 bg-impacto-orange/20 rounded-lg border border-impacto-orange/30">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <span class="font-medium">Usuarios</span>
                </span>
              </div>
            </router-link>
          </li>
          <li>
            <router-link 
              :to="{ name: 'AdminValoradores' }"
              class="dashboard-link block transition-all duration-300"
              @click="handleNavigationInternal"
            >
              <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
                <span class="flex items-center gap-3">
                  <div class="p-2 bg-impacto-orange/20 rounded-lg border border-impacto-orange/30">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <span class="font-medium">Valoradores</span>
                </span>
              </div>
            </router-link>
          </li>
          <li>
            <router-link 
              :to="{ name: 'AdminValoraciones' }"
              class="dashboard-link block transition-all duration-300"
              @click="handleNavigationInternal"
            >
              <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
                <span class="flex items-center gap-3">
                  <div class="p-2 bg-impacto-orange/20 rounded-lg border border-impacto-orange/30">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  </div>
                  <span class="font-medium">Valoraciones</span>
                </span>
              </div>
            </router-link>
          </li>
          <li>
            <router-link 
              :to="{ name: 'AdminLeads' }"
              class="dashboard-link block transition-all duration-300"
              @click="handleNavigationInternal"
            >
              <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
                <span class="flex items-center gap-3">
                  <div class="p-2 bg-impacto-orange/20 rounded-lg border border-impacto-orange/30">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <span class="font-medium">Leads</span>
                </span>
              </div>
            </router-link>
          </li>
          <li>
            <router-link 
              :to="{ name: 'AdminFacturacion' }"
              class="dashboard-link block transition-all duration-300"
              @click="handleNavigationInternal"
            >
              <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
                <span class="flex items-center gap-3">
                  <div class="p-2 bg-impacto-orange/20 rounded-lg border border-impacto-orange/30">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span class="font-medium">Facturación</span>
                </span>
              </div>
            </router-link>
          </li>
          <li>
            <router-link
              :to="{ name: 'AdminSequences' }"
              class="dashboard-link block transition-all duration-300"
              @click="handleNavigationInternal"
            >
              <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
                <span class="flex items-center gap-3">
                  <div class="p-2 bg-impacto-orange/20 rounded-lg border border-impacto-orange/30">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-impacto-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0h10a2 2 0 012 2" />
                    </svg>
                  </div>
                  <span class="font-medium">Secuencias</span>
                </span>
              </div>
            </router-link>
          </li>
          <li>
            <router-link
              :to="{ name: 'AdminHtmlTemplates' }"
              class="dashboard-link block transition-all duration-300"
              @click="handleNavigationInternal"
            >
              <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
                <span class="flex items-center gap-3">
                  <div class="p-2 bg-purple-500/20 rounded-lg border border-purple-500/30">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <span class="font-medium">Plantillas HTML</span>
                </span>
              </div>
            </router-link>
          </li>
        </ul>
        <div class="h-px bg-white/20 w-full my-4"></div>
      </div>

      <!-- SKELETON LOADER PARA LA NAVEGACIÓN PRINCIPAL DEL USUARIO -->
      <ul v-if="authStore.isLoadingState" class="space-y-1">
        <li v-for="i in 8" :key="`skel-item-${i}`">
          <div class="p-3 rounded-lg animate-pulse">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-white/5 rounded-lg h-9 w-9"></div>
              <div class="h-4 bg-white/10 rounded w-3/4"></div>
            </div>
          </div>
        </li>
      </ul>

      <!-- NAVEGACIÓN PRINCIPAL REAL DEL USUARIO (se muestra cuando isLoadingState es false) -->
      <ul v-else class="space-y-1">
        <li>
          <router-link 
            :to="{ name: 'DashboardHome' }"
            class="dashboard-link block transition-all duration-300"
            exact
            @click="handleNavigationInternal"
          >
            <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
              <span class="flex items-center gap-3">
                <div class="p-2 bg-white/10 rounded-lg border border-white/5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                </div>
                <span class="font-medium">Inicio</span>
              </span>
            </div>
          </router-link>
        </li>
        <li>
          <router-link 
            :to="{ name: 'DashboardConfiguracion' }"
            class="dashboard-link block transition-all duration-300"
            @click="handleNavigationInternal"
          >
            <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
              <span class="flex items-center gap-3">
                <div class="p-2 bg-white/10 rounded-lg border border-white/5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <span class="font-medium">Valorador</span>
              </span>
            </div>
          </router-link>
        </li>
        <li>
          <router-link 
            :to="{ name: 'Valoraciones' }"
            class="dashboard-link block transition-all duration-300"
            @click="handleNavigationInternal"
          >
            <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
              <span class="flex items-center gap-3">
                <div class="p-2 bg-white/10 rounded-lg border border-white/5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <span class="font-medium">Valoraciones</span>
              </span>
            </div>
          </router-link>
        </li>
        <li>
          <router-link 
            :to="{ name: 'Leads' }"
            class="dashboard-link block transition-all duration-300"
            @click="handleNavigationInternal"
          >
            <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
              <span class="flex items-center gap-3">
                <div class="p-2 bg-white/10 rounded-lg border border-white/5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <span class="font-medium">Leads</span>
              </span>
            </div>
          </router-link>
        </li>
        <li>
          <router-link 
            :to="{ name: 'Emails' }"
            class="dashboard-link block transition-all duration-300"
            @click="handleNavigationInternal"
          >
            <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
              <span class="flex items-center gap-3">
                <div class="p-2 bg-white/10 rounded-lg border border-white/5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <span class="font-medium">Emails</span>
              </span>
            </div>
          </router-link>
        </li>
        <!-- Skeleton para Suscripción -->
        <li v-if="authStore.isLoadingState">
          <div class="p-3 rounded-lg animate-pulse">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-white/5 rounded-lg h-9 w-9"></div>
              <div class="h-4 bg-white/10 rounded w-3/4"></div>
            </div>
          </div>
        </li>
        <!-- Elemento Suscripción real -->
        <li v-if="!authStore.isLoadingState && authStore.isAgencyOwner">
          <router-link 
            :to="{ name: 'DashboardSuscripcion' }"
            class="dashboard-link block transition-all duration-300"
            @click="handleNavigationInternal"
          >
            <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
              <span class="flex items-center gap-3">
                <div class="p-2 bg-white/10 rounded-lg border border-white/5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <span class="font-medium">Suscripción</span>
              </span>
            </div>
          </router-link>
        </li>
        <!-- Skeleton para Mi Agencia -->
        <li v-if="authStore.isLoadingState">
          <div class="p-3 rounded-lg animate-pulse">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-white/5 rounded-lg h-9 w-9"></div>
              <div class="h-4 bg-white/10 rounded w-3/4"></div>
            </div>
          </div>
        </li>
        <!-- Elemento Mi Agencia real -->
        <li v-if="!authStore.isLoadingState && authStore.isAgencyOwner && authStore.maxDashboardUsers > 1">
          <router-link 
            :to="{ name: 'DashboardMiAgencia' }"
            class="dashboard-link block transition-all duration-300"
            @click="handleNavigationInternal"
          >
            <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
              <span class="flex items-center gap-3">
                <div class="p-2 bg-white/10 rounded-lg border border-white/5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm-9 5a2 2 0 100-4 2 2 0 000 4z" />
                  </svg>
                </div>
                <span class="font-medium">Mi Agencia</span>
              </span>
            </div>
          </router-link>
        </li>
        <li>
          <router-link 
            :to="{ name: 'DashboardProfileManagement' }"
            class="dashboard-link block transition-all duration-300"
            @click="handleNavigationInternal"
          >
            <div class="p-3 rounded-lg hover:bg-white/10 transition-all duration-200 ease-in-out">
              <span class="flex items-center gap-3">
                <div class="p-2 bg-white/10 rounded-lg border border-white/5">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <span class="font-medium">Mi Perfil</span>
              </span>
            </div>
          </router-link>
        </li>
      </ul>
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth';
import { useAdmin } from '@/composables/useAdmin';

const authStore = useAuthStore();
const { isAdmin } = useAdmin();

const handleNavigationInternal = () => {
  // Lógica de navegación si es necesaria
};
</script>

<style scoped>
/* Estilo para el enlace activo */
.dashboard-link.router-link-exact-active > div {
  background-color: rgba(237, 135, 37, 0.2); /* Naranja impacto con opacidad */
}

.dashboard-link:hover > div {
  background-color: rgba(255, 255, 255, 0.08);
}

/* Patrón de fondo sutil */
.bg-pattern-dots {
  background-image: radial-gradient(circle, rgba(255,255,255,0.05) 1px, transparent 1px);
  background-size: 8px 8px;
}

/* Scrollbar styling */
nav::-webkit-scrollbar {
  width: 6px;
}
nav::-webkit-scrollbar-track {
  background: transparent; 
}
nav::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}
nav::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Estilos para la animación de pulso del skeleton */
.animate-pulse .bg-white\/5 { /* Targeting the icon placeholder */
  animation: pulse-bg 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse .bg-white\/10 { /* Targeting the text placeholder */
  animation: pulse-bg 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 0.2s; /* Pequeño desfase para que no pulsen exactamente igual */
}

@keyframes pulse-bg {
  0%, 100% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.9;
  }
}
</style>
