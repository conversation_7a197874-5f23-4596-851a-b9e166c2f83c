<template>
  <div class="bg-white rounded-xl shadow-lg border border-gray-200/50 overflow-hidden">
    <div class="p-5 text-white" style="background-color: #051f33;">
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-bold flex items-center gap-2">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Configura tu página de valoración
        </h2>
        <div v-if="!isLoading && !hasCompletedFirstTimeSetup" class="px-3 py-1 rounded-full text-sm font-medium text-white" style="background-color: rgba(237, 135, 37, 0.8);">
          Paso {{ currentStep + 1 }} de {{ configSteps.length }}
        </div>
      </div>
    </div>
    
    <div class="p-5">
      <div v-if="isLoading" class="flex justify-center items-center py-20">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-impacto-blue"></div>
      </div>
      
      <div v-else-if="hasCompletedFirstTimeSetup" class="p-8 bg-gradient-to-br from-[#fffaf0] to-[#fef5e7] border border-[#f0e4d0] rounded-xl shadow-lg animate-fadeIn text-center">
        <div class="flex flex-col items-center">
          <div class="bg-[#ed8725]/10 rounded-full p-4 mb-4">
            <svg class="w-12 h-12 text-[#ed8725]" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-[#051f33] mb-2">¡Configuración guardada!</h3>
          <p class="text-gray-700 mb-6 max-w-md mx-auto">Tu valorador inmobiliario ha sido actualizado con la nueva configuración.</p>
          <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6 w-full max-w-lg">
            <p class="text-gray-600 mb-2 text-sm">URL de tu valorador:</p>
            <div class="flex items-center justify-center gap-2 bg-gray-50 p-3 rounded border border-gray-200">
              <svg class="w-5 h-5 text-[#ed8725] flex-shrink-0" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13.828 10.172a4 4 0 015.656 5.656l-3.535 3.535a4 4 0 01-5.656-5.656M1.414 1.414L3.535 3.536m0 0L9.9 9.899M12.02 12.021L6.364 6.364M12.02 12.021l5.657 5.657" />
              </svg>
              <span class="font-mono text-[#051f33] text-sm md:text-base tracking-tight select-all break-all">
                https://<span class="font-bold text-[#ed8725]">{{ formData.client_identifier || 'tuagencia' }}</span>.inmoautomation.com/valora/
              </span>
            </div>
          </div>
          <div class="flex flex-col sm:flex-row gap-4">
            <a :href="valoradorUrl" 
               target="_blank" 
               class="px-6 py-3 text-white rounded-lg font-semibold transition-all duration-200 flex items-center justify-center gap-2 shadow-md hover:shadow-lg text-base"
               :style="{ backgroundColor: '#ed8725', '--impacto-orange-darker': '#c05621' }"
               onmouseover="this.style.backgroundColor=this.style.getPropertyValue('--impacto-orange-darker')"
               onmouseout="this.style.backgroundColor='#ed8725'">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" /></svg>
              Ver mi valorador
            </a>
            <button type="button" @click="resetFormAndReturnToEdit" 
                    class="px-6 py-3 border-2 border-[#051f33] text-[#051f33] rounded-lg font-semibold transition-all duration-200 flex items-center justify-center gap-2 hover:bg-[#051f33]/5 active:bg-[#051f33]/10 shadow-sm hover:shadow-md text-base">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" /></svg>
              Volver a editar
            </button>
          </div>
        </div>
      </div>
      
      <form v-else @submit.prevent="handleSubmit" class="pb-10">
        <div>
          <div class="px-1 sm:px-3 py-4 mb-8">
            <div class="flex items-start justify-between">
              <div v-for="(step, index) in configSteps" :key="index" class="flex-1 relative group text-center">
                <div class="relative h-8 mb-1">
                  <div v-if="index > 0"
                       class="absolute top-1/2 left-0 w-1/2 h-1.5 -translate-y-1/2"
                       :class="{
                          'bg-[#ed8725]': currentStep >= index,
                          'bg-gray-300 group-hover:bg-[#ed8725]/30': currentStep < index
                       }">
                  </div>
                  <div v-if="index < configSteps.length - 1"
                       class="absolute top-1/2 right-0 w-1/2 h-1.5 -translate-y-1/2"
                       :class="{
                          'bg-[#ed8725]': currentStep > index,
                          'bg-gray-300 group-hover:bg-[#ed8725]/30': currentStep <= index
                       }">
                  </div>
                  <div class="w-8 h-8 mx-auto rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300 border-2 relative z-10"
                       :class="{
                          'bg-[#ed8725] text-white border-[#ed8725]': currentStep >= index,
                          'bg-gray-200 text-gray-700 border-gray-300 group-hover:border-[#ed8725]/70 group-hover:bg-[#ed8725]/20 group-hover:text-[#ed8725]': currentStep < index
                       }">
                      {{ index + 1 }}
                  </div>
                </div>
                <div class="text-xs transition-all duration-300"
                     :class="{
                        'font-semibold text-[#ed8725]': currentStep === index,
                        'text-gray-500 group-hover:text-[#ed8725]': currentStep !== index
                     }">
                    {{ step }}
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="isFirstTimeSetup && currentStep === 0" class="p-4 mb-6 rounded-lg border-l-4 animate-fadeIn" style="border-color: #ed8725; background-color: #fffaf0;">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5" style="color: #ed8725;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm" style="color: #c05621;">Configura tu valorador inmobiliario en unos sencillos pasos. Personaliza la apariencia, datos de contacto y enlaces para ofrecer una experiencia única a tus clientes.</p>
              </div>
            </div>
          </div>
          
          <div class="relative overflow-hidden">
            <section v-show="currentStep === 0" class="p-7 md:p-9 bg-white rounded-xl shadow-lg border border-gray-200/75 space-y-8 animate-fadeIn" ref="agencySection">
              <div class="text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-impacto-blue to-impacto-blue/80 rounded-full mb-4">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Identidad de tu agencia</h3>
                <p class="text-gray-600 max-w-md mx-auto">Define el nombre, identificador único y descripción que representarán tu agencia en el valorador.</p>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-1 gap-x-8 gap-y-6">
                <div>
                  <div class="flex items-center justify-between mb-2">
                    <label for="agencyNameInput" class="text-sm font-medium text-gray-800">Nombre comercial de la agencia *</label>
                    <div v-if="showFieldTooltips && isFirstTimeSetup" class="text-xs text-impacto-blue bg-blue-50 px-2 py-1 rounded-full animate-fadeIn">
                      <span class="flex items-center">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        Campo obligatorio
                      </span>
                    </div>
                  </div>
                  <input id="agencyNameInput" v-model="formData.nombre_display" class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50 transition-all duration-200 ease-in-out hover:border-gray-400 disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed invalid:border-red-400 invalid:text-red-500 focus:invalid:border-red-500 focus:invalid:ring-red-500/50" required placeholder="Ej: Inmobiliaria Futuro" />
                </div>
                <div>
                  <div class="flex items-center justify-between mb-2">
                    <label for="desiredSubdomainInput" class="text-sm font-medium text-gray-800">Identificador único para URL *</label>
                    <div v-if="showFieldTooltips && isFirstTimeSetup" class="text-xs text-impacto-blue bg-blue-50 px-2 py-1 rounded-full animate-fadeIn">
                      <span class="flex items-center">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        Parte final de la URL de tu valorador
                      </span>
                    </div>
                  </div>
                  <!-- Diseño responsive para el identificador -->
                  <div class="space-y-3">
                    <!-- Vista móvil: campos apilados -->
                    <div class="block sm:hidden">
                      <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                        <p class="text-xs text-gray-600 mb-2 font-mono">https://</p>
                        <input
                          id="desiredSubdomainInput"
                          v-model="formData.client_identifier"
                          @input="handleClientIdentifierChange"
                          @blur="validateClientIdentifier"
                          class="font-mono block w-full px-3 py-2 bg-white border text-base shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 rounded-lg"
                          :class="getClientIdentifierInputClasses()"
                          required
                          placeholder="tuagencia"
                          autocomplete="off"
                        />
                        <p class="text-xs text-gray-600 mt-2 font-mono">.inmoautomation.com/valora/</p>
                      </div>
                    </div>

                    <!-- Vista desktop: campos en línea -->
                    <div class="hidden sm:flex items-center">
                      <span class="inline-flex items-center px-3.5 py-3 text-sm text-gray-600 bg-gray-100 border border-r-0 border-gray-300 rounded-l-lg font-mono tracking-tight">https://</span>
                      <input
                        v-model="formData.client_identifier"
                        @input="handleClientIdentifierChange"
                        @blur="validateClientIdentifier"
                        class="font-mono block w-full px-4 py-3 bg-white border text-base shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 flex-1 min-w-0"
                        :class="getClientIdentifierInputClasses()"
                        required
                        placeholder="tuagencia"
                        autocomplete="off"
                      />
                      <span class="inline-flex items-center px-3.5 py-3 text-sm text-gray-600 bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg font-mono tracking-tight">.inmoautomation.com/valora/</span>
                    </div>
                  </div>
                  <div class="mt-1.5">
                    <p class="text-xs text-gray-500">Solo letras, números y guiones. Mínimo 3 caracteres.</p>
                    <div v-if="clientIdentifierValidation.checking" class="flex items-center gap-2 text-xs text-blue-600 mt-1">
                      <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                      <span>Verificando disponibilidad...</span>
                    </div>
                    <p v-else-if="clientIdentifierValidation.message"
                       class="text-xs mt-1"
                       :class="clientIdentifierValidation.isValid ? 'text-green-600' : 'text-red-600'">
                      {{ clientIdentifierValidation.message }}
                    </p>
                  </div>
                </div>

                <!-- Campo de descripción breve -->
                <div>
                  <div class="flex items-center justify-between mb-2">
                    <label for="descripcionBreveInput" class="text-sm font-medium text-gray-800">Descripción breve</label>
                    <div v-if="showFieldTooltips && isFirstTimeSetup" class="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full animate-fadeIn">
                      <span class="flex items-center">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        Opcional
                      </span>
                    </div>
                  </div>
                  <textarea
                    id="descripcionBreveInput"
                    v-model="formData.descripcion_breve"
                    rows="3"
                    class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50 transition-all duration-200 ease-in-out hover:border-gray-400 resize-none"
                    placeholder="Ej: Especialistas en propiedades de lujo en el centro de Madrid. Más de 15 años de experiencia en el sector inmobiliario."
                  ></textarea>
                  <p class="text-xs text-gray-500 mt-1">Esta descripción aparecerá en tu landing page, informes de valoración y emails a tus leads.</p>
                </div>
              </div>
            </section>
            
            <section v-show="currentStep === 1" class="p-7 md:p-9 bg-white rounded-xl shadow-lg border border-gray-200/75 space-y-8 animate-fadeIn" ref="brandSection">
              <div class="text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-impacto-blue to-impacto-blue/80 rounded-full mb-4">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Imagen de marca</h3>
                <p class="text-gray-600 max-w-md mx-auto">Personaliza el logo y colores que representarán tu agencia en el valorador.</p>
              </div>
              <div class="space-y-4">
                <div class="flex flex-col sm:flex-row items-end gap-4">
                  <div class="w-full sm:w-auto sm:flex-shrink-0">
                    <label for="logoSelectionMethodSelect" class="block mb-1.5 text-sm font-medium text-gray-800">Origen del logo</label>
                    <select id="logoSelectionMethodSelect" v-model="formData.logoSelectionMethod" class="pr-10 block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50 transition-all duration-200 ease-in-out hover:border-gray-400 disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed">
                      <option value="url">Desde URL</option>
                      <option value="upload">Subir archivo</option>
                    </select>
                  </div>
                  <div class="flex-1 min-w-0">
                    <label v-if="formData.logoSelectionMethod === 'url'" for="agencyLogoUrlInput" class="block mb-1.5 text-sm font-medium text-gray-800">URL del logo</label>
                    <label v-else for="agencyLogoFileInput" class="block mb-1.5 text-sm font-medium text-gray-800">Subir archivo de logo</label>
                    <input v-if="formData.logoSelectionMethod === 'url'" id="agencyLogoUrlInput" v-model="formData.logo_url" class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50 transition-all duration-200 ease-in-out hover:border-gray-400" placeholder="https://ejemplo.com/logo.png" />
                    <input v-else id="agencyLogoFileInput" type="file" accept="image/png, image/jpeg, image/svg+xml, image/webp" @change="onFileChange" class="input-file-modern block w-full" />
                  </div>
                </div>
                <p class="text-xs text-gray-500">Sube tu logo (PNG, JPG, SVG, WebP). Recomendado: SVG o PNG transparente.</p>
                <div v-if="previewLogoUrl" class="mt-4 p-3 border border-gray-200 rounded-lg bg-gray-50 inline-block">
                  <img :src="previewLogoUrl" alt="Vista previa del logo" class="max-h-20 rounded"/>
                </div>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-x-10 gap-y-8 pt-6 border-t border-gray-200">
                <div class="space-y-3">
                  <label class="block text-sm font-medium text-gray-800">Color primario</label>
                  <div class="relative group" v-click-outside="closePrimaryColorPicker">
                    <div @click="() => { togglePrimaryColorPicker = !togglePrimaryColorPicker; toggleSecondaryColorPicker = false; }" class="cursor-pointer w-full h-12 rounded-lg border border-gray-300 flex items-center px-3 shadow-sm" :style="{ backgroundColor: formData.color_primario }">
                       <span class="block truncate font-mono text-sm" :style="{ color: getContrastingTextColor(formData.color_primario || '#FFFFFF') }">{{ formData.color_primario || 'Seleccionar...' }}</span>
                    </div>
                    <Sketch v-if="togglePrimaryColorPicker" v-model="primaryColorObject" class="absolute z-10 mt-1 right-0 origin-top-right sketch-picker-custom" />
                  </div>
                   <p class="text-xs text-gray-500">Para botones principales y acentos.</p>
                </div>
                <div class="space-y-3">
                  <label class="block text-sm font-medium text-gray-800">Color secundario</label>
                  <div class="relative group" v-click-outside="closeSecondaryColorPicker">
                    <div @click="() => { toggleSecondaryColorPicker = !toggleSecondaryColorPicker; togglePrimaryColorPicker = false; }" class="cursor-pointer w-full h-12 rounded-lg border border-gray-300 flex items-center px-3 shadow-sm" :style="{ backgroundColor: formData.color_secundario }">
                      <span class="block truncate font-mono text-sm" :style="{ color: getContrastingTextColor(formData.color_secundario || '#FFFFFF') }">{{ formData.color_secundario || 'Seleccionar...' }}</span>
                    </div>
                    <Sketch v-if="toggleSecondaryColorPicker" v-model="secondaryColorObject" class="absolute z-10 mt-1 right-0 origin-top-right sketch-picker-custom" />
                  </div>
                   <p class="text-xs text-gray-500">Para elementos complementarios.</p>
                </div>
              </div>
            </section>
            
            <!-- Paso 3: Información de contacto -->
            <section v-show="currentStep === 2" class="p-7 md:p-9 bg-white rounded-xl shadow-lg border border-gray-200/75 space-y-8 animate-fadeIn" ref="contactSection">
              <div class="text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-impacto-blue to-impacto-blue/80 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Información de contacto</h3>
                <p class="text-gray-600 max-w-md mx-auto">Configura tu información de contacto y notificaciones para leads.</p>
              </div>

              <!-- Email de notificaciones (obligatorio) -->
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                  <label for="emailNotificacionesLeadsInput" class="text-sm font-semibold text-blue-900 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Email para notificaciones de leads *
                  </label>
                  <span class="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded-full">Obligatorio</span>
                </div>
                <input
                  id="emailNotificacionesLeadsInput"
                  v-model="formData.email_notificaciones_leads"
                  type="email"
                  class="block w-full px-4 py-3 bg-white border border-blue-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50"
                  required
                  placeholder="<EMAIL>"
                />
                <p class="text-xs text-blue-700 mt-2">Recibirás una notificación inmediata cada vez que alguien complete una valoración.</p>
              </div>

              <!-- Dirección física -->
              <div class="space-y-2">
                <div class="flex items-center">
                  <label for="direccionFisicaInput" class="block text-sm font-semibold text-gray-700">Dirección física</label>
                  <div class="ml-2">
                    <div class="group relative">
                      <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 cursor-help">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        Aparecerá en landing page, informes PDF y emails
                      </span>
                    </div>
                  </div>
                </div>
                <input id="direccionFisicaInput" v-model="formData.direccion_fisica" type="text" class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50" placeholder="Calle Principal 123, 28001 Madrid" />
              </div>

              <!-- Teléfono y WhatsApp en la misma fila -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <label for="telefonoContactoInput" class="block text-sm font-semibold text-gray-700">Teléfono de contacto</label>
                  <input id="telefonoContactoInput" v-model="formData.telefono_contacto" type="tel" class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50" placeholder="+34 912 345 678" />
                </div>
                <div class="space-y-2">
                  <label for="whatsappNumeroInput" class="block text-sm font-semibold text-gray-700">WhatsApp</label>
                  <input id="whatsappNumeroInput" v-model="formData.whatsapp_numero" type="tel" class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50" placeholder="+34 612 345 678" />
                </div>
              </div>

              <!-- Email público y sitio web en la misma fila -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <div class="flex items-center">
                    <label for="emailContactoPublicoInput" class="block text-sm font-semibold text-gray-700">Email público</label>
                    <div class="ml-2">
                      <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        Puede ser diferente al de notificaciones
                      </span>
                    </div>
                  </div>
                  <input id="emailContactoPublicoInput" v-model="formData.email_contacto_publico" type="email" class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50" placeholder="<EMAIL>" />
                </div>

              </div>


            </section>

            <section v-show="currentStep === 3" class="p-7 md:p-9 bg-white rounded-xl shadow-lg border border-gray-200/75 space-y-8 animate-fadeIn" ref="linksSection">
              <div class="text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-impacto-blue to-impacto-blue/80 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Enlaces importantes</h3>
                <p class="text-gray-600 max-w-md mx-auto">Configura los enlaces que aparecerán en tu valorador y comunicaciones.</p>
              </div>

              <div class="space-y-2">
                <div class="flex items-center">
                  <label for="ctaContactoUrlInput" class="block text-sm font-semibold text-gray-700">URL de contacto / CTA principal</label>
                  <div class="ml-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700">
                      <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                      Aparece en landing page, informes y emails
                    </span>
                  </div>
                </div>
                <input id="ctaContactoUrlInput" v-model="formData.cta_contacto_url" type="url" class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50" placeholder="https://tuagencia.com/contacto" />
                <p class="text-xs text-gray-500">Esta URL aparecerá como botón principal en tu landing page, informes de valoración y emails a leads.</p>
              </div>

              <!-- Sitio web -->
              <div class="space-y-2">
                <div class="flex items-center">
                  <label for="sitioWebUrlInput" class="block text-sm font-semibold text-gray-700">Sitio web</label>
                  <div class="ml-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700">
                      <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                      Aparece en landing page, informes y emails
                    </span>
                  </div>
                </div>
                <input id="sitioWebUrlInput" v-model="formData.sitio_web_url" type="url" class="block w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-base shadow-sm placeholder-gray-400 text-gray-800 focus:outline-none focus:border-impacto-blue focus:ring-2 focus:ring-impacto-blue/50" placeholder="https://www.tuagencia.com" />
                <p class="text-xs text-gray-500">Tu página web principal que aparecerá en tu landing page, informes y emails para que los leads puedan conocer más sobre tu agencia.</p>
              </div>


            </section>
          </div>
          
          <div class="flex justify-between items-center mt-8 px-2">
            <button type="button" class="px-5 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 flex items-center gap-2 border-2" 
                    :disabled="currentStep === 0" 
                    :class="{
                      'text-gray-400 border-gray-300 cursor-not-allowed': currentStep === 0, 
                      'text-[#051f33] border-[#051f33] hover:bg-[#051f33]/5 active:bg-[#051f33]/10': currentStep > 0 
                    }"
                    @click="goToPreviousStep">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
              Anterior
            </button>
            <div class="flex space-x-2">
              <button v-if="currentStep < configSteps.length - 1" type="button" 
                      class="px-5 py-2.5 text-sm font-medium text-white rounded-lg transition-all duration-200 flex items-center gap-2 shadow-md hover:shadow-lg" 
                      :style="{ backgroundColor: '#ed8725', '--impacto-orange-darker': '#c05621' }"
                      :class="{'opacity-60 cursor-not-allowed': !canProceedToNextStep}"
                      :disabled="!canProceedToNextStep" 
                      @click="goToNextStep"
                      onmouseover="this.style.backgroundColor=this.style.getPropertyValue('--impacto-orange-darker')"
                      onmouseout="this.style.backgroundColor='#ed8725'">
                Siguiente
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
              </button>
              <button v-else type="submit" 
                      class="px-5 py-2.5 text-sm font-medium text-white rounded-lg transition-all duration-200 flex items-center gap-2 shadow-md hover:shadow-lg" 
                      :style="{ backgroundColor: '#ed8725', '--impacto-orange-darker': '#c05621' }"
                      :class="{'opacity-60 cursor-not-allowed': loading || !hasChanges}"
                      :disabled="loading || !hasChanges"
                      onmouseover="this.style.backgroundColor=this.style.getPropertyValue('--impacto-orange-darker')"
                      onmouseout="this.style.backgroundColor='#ed8725'">
                {{ loading ? 'Guardando...' : (isFirstTimeSetup ? 'Finalizar configuración' : 'Guardar Cambios') }}
                <svg v-if="!loading" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                <svg v-else class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" /></svg>
              </button>
            </div>
          </div>
        </div>

        <div class="mt-6">
          <div v-if="error" class="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 animate-fadeIn">
            <svg class="w-6 h-6 mr-3 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="font-medium">{{ error }}</span>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { getValoradorConfig, saveValoradorConfig, uploadAgencyLogo, checkClientIdentifierAvailability } from '@/api/valoradorConfig';
import { Sketch } from '@ckpack/vue-color';
import type { ColorInput } from '@ctrl/tinycolor';
import { useToast } from '@/composables/useToast';

const { showToast } = useToast();

const emits = defineEmits(['config-saved', 'config-error']);

// Helper para construir la URL completa para la previsualización
const getFullImageUrl = (path?: string | null): string | null => {
  if (!path) {
    return null;
  }
  // Si ya es una URL absoluta (http, https) o una data URL, devolverla tal cual.
  if (path.startsWith('http') || path.startsWith('data:')) {
    return path;
  }
  // Si path es, por ejemplo, '/uploads/logos/file.png'
  // window.location.origin es 'http://localhost:5173' (o el dominio de producción)
  // El resultado será 'http://localhost:5173/uploads/logos/file.png'
  return `${window.location.origin}${path}`; 
};

interface ValoradorFormData {
  nombre_display: string;
  client_identifier: string;
  logo_url?: string;
  logoSelectionMethod: 'url' | 'upload';
  agencyLogoFile?: File | null;
  email_notificaciones_leads: string;
  direccion_fisica?: string;
  telefono_contacto?: string;
  email_contacto_publico?: string;
  sitio_web_url?: string;
  whatsapp_numero?: string;
  descripcion_breve?: string;
  color_primario?: string;
  color_secundario?: string;
  cta_contacto_url?: string;
  termsAccepted: boolean;
}

const isLoading = ref(true);
const loading = ref(false);
const error = ref('');
const isFirstTimeSetup = ref(false);
const hasCompletedFirstTimeSetup = ref(false);
const showFieldTooltips = ref(true);
const animateSections = ref(false);

const valoradorUrl = computed(() => {
  const identifier = formData.value.client_identifier || 'tuagencia';

  // Siempre usar la URL de producción ya que el valorador está desplegado
  return `https://${identifier}.inmoautomation.com/valora/`;
});

const initialFormData = ref<Partial<ValoradorFormData>>({});

const formData = ref<ValoradorFormData>({
  nombre_display: '',
  client_identifier: '',
  logo_url: '',
  logoSelectionMethod: 'url',
  agencyLogoFile: null as File | null,
  email_notificaciones_leads: '',
  direccion_fisica: '',
  telefono_contacto: '',
  email_contacto_publico: '',
  sitio_web_url: '',
  whatsapp_numero: '',
  descripcion_breve: '',
  color_primario: '#003B6D',
  color_secundario: '#FF6B00',
  cta_contacto_url: '',
  termsAccepted: false,
});

const hasChanges = computed(() => {
  if (isFirstTimeSetup.value && !formData.value.termsAccepted) {
    return true;
  }
  return JSON.stringify(formData.value) !== JSON.stringify(initialFormData.value);
});

const configSteps = ['Identidad', 'Marca', 'Contacto', 'Enlaces'];
const currentStep = ref(0);
const previewLogoUrl = ref<string | null>(null);

// Validación del client_identifier
const clientIdentifierValidation = ref({
  checking: false,
  isValid: false,
  message: ''
});

let validationTimeout: number | null = null;

const canProceedToNextStep = computed(() => {
  if (currentStep.value === 0) {
    // Paso 1: Identidad - nombre y identificador son obligatorios
    return !!(formData.value.nombre_display && formData.value.client_identifier && clientIdentifierValidation.value.isValid);
  } else if (currentStep.value === 1) {
    // Paso 2: Marca - opcional, siempre puede continuar
    return true;
  } else if (currentStep.value === 2) {
    // Paso 3: Contacto - email de notificaciones es obligatorio
    return !!formData.value.email_notificaciones_leads;
  } else if (currentStep.value === 3) {
    // Paso 4: Enlaces - opcional
    return true;
  }
  return false;
});

const hexToColorObject = (hex: string | undefined): string => {
  if (!hex) return '#000000';
  if (/^#[0-9A-F]{6}$/i.test(hex)) return hex;
  if (/^#[0-9A-F]{8}$/i.test(hex)) return hex.substring(0, 7);
  return '#000000';
};

const primaryColorObject = ref<ColorInput>(formData.value.color_primario || '#003B6D');
const secondaryColorObject = ref<ColorInput>(formData.value.color_secundario || '#FF6B00');
const togglePrimaryColorPicker = ref(false);
const toggleSecondaryColorPicker = ref(false);

watch(primaryColorObject, (newColor: any) => {
  if (newColor && typeof newColor === 'object' && newColor.hex) {
    formData.value.color_primario = newColor.hex.toUpperCase();
  } else if (typeof newColor === 'string') {
    formData.value.color_primario = newColor.toUpperCase();
  }
}, { deep: true });

watch(secondaryColorObject, (newColor: any) => {
  if (newColor && typeof newColor === 'object' && newColor.hex) {
    formData.value.color_secundario = newColor.hex.toUpperCase();
  } else if (typeof newColor === 'string') {
    formData.value.color_secundario = newColor.toUpperCase();
  }
}, { deep: true });

watch(() => formData.value.color_primario, (newVal) => {
  primaryColorObject.value = hexToColorObject(newVal);
});
watch(() => formData.value.color_secundario, (newVal) => {
  secondaryColorObject.value = hexToColorObject(newVal);
});

// Cuando cambia formData.logo_url (ej, si el usuario escribe en el input de URL)
// actualizamos la previsualización.
watch(() => formData.value.logo_url, (newUrl) => {
  if (formData.value.logoSelectionMethod === 'url') {
    previewLogoUrl.value = getFullImageUrl(newUrl); 
  }
  // Si es 'upload', onFileChange maneja previewLogoUrl con data:URL
  // y handleSubmit lo actualiza con la URL del servidor (también via getFullImageUrl)
});

const closePrimaryColorPicker = () => { if (togglePrimaryColorPicker.value) togglePrimaryColorPicker.value = false; };
const closeSecondaryColorPicker = () => { if (toggleSecondaryColorPicker.value) toggleSecondaryColorPicker.value = false; };

const agencySection = ref<HTMLElement | null>(null);
const brandSection = ref<HTMLElement | null>(null);
const contactSection = ref<HTMLElement | null>(null);
const linksSection = ref<HTMLElement | null>(null);

const goToPreviousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
    nextTick(scrollToCurrentSection);
  }
};

const goToNextStep = () => {
  if (currentStep.value < configSteps.length - 1) {
    currentStep.value++;
    nextTick(scrollToCurrentSection);
  }
};

const scrollToCurrentSection = () => {
  const sectionRefs = [agencySection, brandSection, contactSection, linksSection];
  const currentSectionRef = sectionRefs[currentStep.value];
  if (currentSectionRef && currentSectionRef.value) {
    currentSectionRef.value.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
};

// Funciones de validación del client_identifier
const handleClientIdentifierChange = () => {
  // Limpiar timeout anterior
  if (validationTimeout) {
    clearTimeout(validationTimeout);
  }

  // Resetear validación
  clientIdentifierValidation.value = {
    checking: false,
    isValid: false,
    message: ''
  };

  // Validar después de 500ms de inactividad
  validationTimeout = setTimeout(() => {
    validateClientIdentifier();
  }, 500);
};

const validateClientIdentifier = async () => {
  const identifier = formData.value.client_identifier?.trim();

  if (!identifier) {
    clientIdentifierValidation.value = {
      checking: false,
      isValid: false,
      message: ''
    };
    return;
  }

  // Validación de formato
  if (!/^[a-zA-Z0-9_-]+$/.test(identifier)) {
    clientIdentifierValidation.value = {
      checking: false,
      isValid: false,
      message: 'Solo se permiten letras, números, guiones y guiones bajos'
    };
    return;
  }

  // Validación de longitud
  if (identifier.length < 3) {
    clientIdentifierValidation.value = {
      checking: false,
      isValid: false,
      message: 'Mínimo 3 caracteres requeridos'
    };
    return;
  }

  // Verificar disponibilidad en el servidor
  clientIdentifierValidation.value.checking = true;

  try {
    const result = await checkClientIdentifierAvailability(identifier);

    if (result.success) {
      clientIdentifierValidation.value = {
        checking: false,
        isValid: result.available,
        message: result.available ? '✓ Identificador disponible' : '✗ Identificador ya en uso'
      };
    } else {
      clientIdentifierValidation.value = {
        checking: false,
        isValid: false,
        message: result.message || 'Error verificando disponibilidad'
      };
    }
  } catch (error) {
    clientIdentifierValidation.value = {
      checking: false,
      isValid: false,
      message: 'Error de conexión al verificar disponibilidad'
    };
  }
};

const getClientIdentifierInputClasses = () => {
  const baseClasses = 'text-gray-800 border-gray-300 focus:border-impacto-blue focus:ring-impacto-blue/50';

  if (clientIdentifierValidation.value.checking) {
    return `${baseClasses} border-blue-300`;
  }

  if (formData.value.client_identifier && clientIdentifierValidation.value.message) {
    if (clientIdentifierValidation.value.isValid) {
      return `${baseClasses} border-green-300 focus:border-green-500 focus:ring-green-500/50`;
    } else {
      return `${baseClasses} border-red-300 focus:border-red-500 focus:ring-red-500/50`;
    }
  }

  return baseClasses;
};

onMounted(async () => {
  isLoading.value = true;
  error.value = '';
  hasCompletedFirstTimeSetup.value = false;
  currentStep.value = 0;
  setTimeout(() => { animateSections.value = true; }, 300);
  await loadConfigData();
});

const getContrastingTextColor = (backgroundColorHex: string): string => {
  if (!backgroundColorHex) return '#000000';
  try {
    const rgb = parseInt(backgroundColorHex.slice(1), 16);
    const r = (rgb >> 16) & 0xff; const g = (rgb >> 8) & 0xff; const b = (rgb >> 0) & 0xff;
    const luma = 0.2126 * r + 0.7152 * g + 0.0722 * b;
    return luma < 140 ? '#FFFFFF' : '#000000';
  } catch (error) { return '#000000'; }
};

const onFileChange = (e: Event) => {
  const file = (e.target as HTMLInputElement).files?.[0];
  if (file) {
    formData.value.agencyLogoFile = file;
    const reader = new FileReader();
    reader.onload = (re) => {
      previewLogoUrl.value = re.target?.result as string; // Esto es una data:URL, getFullImageUrl la pasará tal cual
      formData.value.logo_url = ''; // Limpiar para indicar que hay un archivo nuevo y no una URL escrita
    };
    reader.readAsDataURL(file);
  } else {
    formData.value.agencyLogoFile = null;
    // Revertir al logo inicial o al valor actual de formData.logo_url si se estaba editando una URL
    const initialOrExistingUrl = initialFormData.value?.logo_url || formData.value.logo_url || null;
    formData.value.logo_url = initialOrExistingUrl || ''; // Actualizar formData también
    previewLogoUrl.value = getFullImageUrl(initialOrExistingUrl);
  }
};

const resetFormAndReturnToEdit = () => {
  hasCompletedFirstTimeSetup.value = false;
  isFirstTimeSetup.value = false; 
  currentStep.value = 0;
  formData.value.agencyLogoFile = null; 
  loadConfigData(); 
};

async function handleSubmit() {
  loading.value = true;
  error.value = '';

  if (!formData.value.nombre_display || !formData.value.client_identifier || !formData.value.email_notificaciones_leads) {
    showToast('Por favor, completa todos los campos obligatorios (*).', 'error');
    if (!formData.value.nombre_display || !formData.value.client_identifier) currentStep.value = 0;
    else if (!formData.value.email_notificaciones_leads) currentStep.value = 2;
    loading.value = false;
    return;
  }

  // Verificar que el identificador sea válido
  if (!clientIdentifierValidation.value.isValid) {
    showToast('El identificador único no es válido o no está disponible.', 'error');
    currentStep.value = 0;
    loading.value = false;
    return;
  }


  try {
    // Procesamiento del logo (subida de archivo o URL externa)
    if (formData.value.client_identifier) {
      // Verificar si necesitamos procesar el logo
      let needsLogoProcessing = false;
      
      if (formData.value.logoSelectionMethod === 'upload' && formData.value.agencyLogoFile) {
        // Caso 1: Subida de archivo nuevo
        needsLogoProcessing = true;
      } else if (formData.value.logoSelectionMethod === 'url' && formData.value.logo_url && 
                (!initialFormData.value?.logo_url || formData.value.logo_url !== initialFormData.value.logo_url)) {
        // Caso 2: URL nueva o diferente a la inicial
        // Solo procesamos si la URL es diferente a la que ya teníamos guardada
        needsLogoProcessing = true;
      }
      
      if (needsLogoProcessing) {
        showToast('Procesando logo...', 'info');
        try {
          // Determinar qué enviar a la API según el método de selección
          const logoSource = formData.value.logoSelectionMethod === 'upload' 
            ? formData.value.agencyLogoFile as File // Aseguramos que es File
            : formData.value.logo_url as string;   // Aseguramos que es string
            
          const uploadResult = await uploadAgencyLogo(logoSource, formData.value.client_identifier);
          
          if (uploadResult.success && uploadResult.logoUrl) {
            formData.value.logo_url = uploadResult.logoUrl; // Guardar URL del servidor
            previewLogoUrl.value = getFullImageUrl(uploadResult.logoUrl); // Usar URL completa para previsualización
            showToast('Logo procesado con éxito.', 'success');
          } else {
            throw new Error(uploadResult.error || 'No se pudo obtener la URL del logo procesado.');
          }
        } catch (uploadError: any) {
          error.value = `Error al procesar el logo: ${uploadError.message}. Se intentará guardar la configuración sin el nuevo logo o con el anterior.`;
          showToast(error.value, 'error');
          formData.value.logo_url = initialFormData.value?.logo_url || ''; 
          previewLogoUrl.value = getFullImageUrl(initialFormData.value?.logo_url); // Usar URL completa para previsualización
        }
      } else if (formData.value.logoSelectionMethod === 'url' && formData.value.logo_url === '') {
        // Si el usuario borró la URL, limpiamos el logo_url
        formData.value.logo_url = '';
        previewLogoUrl.value = '';
      } else if (formData.value.logoSelectionMethod === 'upload' && !formData.value.agencyLogoFile && !initialFormData.value?.logo_url) {
        // Si seleccionó subir archivo pero no eligió ninguno y no había logo previo
        formData.value.logo_url = '';
        previewLogoUrl.value = '';
      }
      // En cualquier otro caso, mantenemos el valor actual de logo_url
    } else if (formData.value.logoSelectionMethod === 'upload' && formData.value.agencyLogoFile) {
      showToast('El identificador del cliente es necesario para procesar el logo. Por favor, completa el paso 1.', 'error');
      currentStep.value = 0;
      loading.value = false;
      return;
    }

    const dataToSave: {
      client_identifier?: string;
      nombre_display?: string;
      logo_url?: string | null;
      color_primario?: string;
      color_secundario?: string;
      email_notificaciones_leads?: string;
      direccion_fisica?: string;
      telefono_contacto?: string;
      email_contacto_publico?: string;
      sitio_web_url?: string;
      whatsapp_numero?: string;
      descripcion_breve?: string;
      cta_contacto_url?: string;
    } = {
      client_identifier: formData.value.client_identifier,
      nombre_display: formData.value.nombre_display,
      logo_url: formData.value.logo_url,
      color_primario: formData.value.color_primario,
      color_secundario: formData.value.color_secundario,
      email_notificaciones_leads: formData.value.email_notificaciones_leads,
      direccion_fisica: formData.value.direccion_fisica,
      telefono_contacto: formData.value.telefono_contacto,
      email_contacto_publico: formData.value.email_contacto_publico,
      sitio_web_url: formData.value.sitio_web_url,
      whatsapp_numero: formData.value.whatsapp_numero,
      descripcion_breve: formData.value.descripcion_breve,
      cta_contacto_url: formData.value.cta_contacto_url,
    };

    await saveValoradorConfig(JSON.parse(JSON.stringify(dataToSave)) as unknown as ValoradorFormData); 

    hasCompletedFirstTimeSetup.value = true; 
    showToast('¡Configuración guardada con éxito!', 'success');
    emits('config-saved');
    isFirstTimeSetup.value = false;
    
    initialFormData.value = JSON.parse(JSON.stringify(formData.value));     

    previewLogoUrl.value = getFullImageUrl(formData.value.logo_url);

  } catch (e: any) {
    error.value = e.message || 'No se pudo guardar la configuración.';
    showToast(error.value, 'error');
    emits('config-error', error.value);
  } finally {
    loading.value = false;
  }
}

async function loadConfigData() {
  isLoading.value = true;
  try {
    const apiResponse = await getValoradorConfig();
    const config = apiResponse.config;
    if (apiResponse && apiResponse.success && config && config.client_identifier) {
      isFirstTimeSetup.value = false;
      formData.value = {
        ...formData.value,
        nombre_display: config.nombre_display || '',
        client_identifier: config.client_identifier || '',
        logo_url: config.logo_url || '', // Esta es la URL relativa del servidor o la URL completa si se ingresó así
        logoSelectionMethod: config.logo_url ? 'url' : (formData.value.agencyLogoFile ? 'upload' : 'url'),
        color_primario: (config.color_primario && /^#[0-9A-F]{6}$/i.test(config.color_primario)) 
                      ? config.color_primario 
                      : formData.value.color_primario,
        color_secundario: (config.color_secundario && /^#[0-9A-F]{6}$/i.test(config.color_secundario))
                        ? config.color_secundario
                        : formData.value.color_secundario,
        email_notificaciones_leads: config.email_notificaciones_leads || '',
        direccion_fisica: config.direccion_fisica || '',
        telefono_contacto: config.telefono_contacto || '',
        email_contacto_publico: config.email_contacto_publico || '',
        sitio_web_url: config.sitio_web_url || '',
        whatsapp_numero: config.whatsapp_numero || '',
        descripcion_breve: config.descripcion_breve || '',
        cta_contacto_url: config.cta_contacto_url || '',
        termsAccepted: true
      };
      previewLogoUrl.value = getFullImageUrl(config.logo_url); // Actualizar preview con URL completa
      showFieldTooltips.value = false;

      // Validar el identificador existente
      if (config.client_identifier) {
        clientIdentifierValidation.value = {
          checking: false,
          isValid: true,
          message: '✓ Identificador actual'
        };
      }
    } else {
      isFirstTimeSetup.value = true;
      formData.value.termsAccepted = false;
      showFieldTooltips.value = true;
    }
  } catch (err: any) {
    error.value = 'Error al cargar la configuración. Intentando con valores por defecto.';
    showToast(error.value, 'error');
    isFirstTimeSetup.value = true;
    formData.value.termsAccepted = false;
    showFieldTooltips.value = true;
  } finally {
    initialFormData.value = JSON.parse(JSON.stringify(formData.value));
    isLoading.value = false;
  }
}
</script>

<style scoped>
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Definiendo los colores como CSS custom properties para fácil uso en Tailwind y estilos */
:root {
  --color-primary-brand: #051f33;
  --color-secondary-brand: #ed8725;
  --color-secondary-brand-darker: #c05621; /* Para hover en botones naranjas */
  --color-secondary-brand-lighter: #fffaf0; /* Para fondos de alerta/info */
  --color-secondary-brand-transparent: rgba(237, 135, 37, 0.8);
}

/* Aplicando estilo a la barra de progreso y otros elementos con los colores de marca */
.bg-impacto-blue {
  background-color: var(--color-primary-brand) !important;
}
.text-impacto-blue {
  color: var(--color-primary-brand) !important;
}
.border-impacto-blue {
  border-color: var(--color-primary-brand) !important;
}
.focus\:ring-impacto-blue\/50:focus {
  --tw-ring-opacity: 0.5;
  --tw-ring-color: var(--color-primary-brand);
   box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}
.focus\:border-impacto-blue:focus{
    border-color: var(--color-primary-brand) !important;
}


.bg-impacto-orange {
  background-color: var(--color-secondary-brand) !important;
}
.text-impacto-orange {
  color: var(--color-secondary-brand) !important;
}
.border-impacto-orange {
  border-color: var(--color-secondary-brand) !important;
}
.bg-impacto-orange\/20 {
    background-color: rgba(237, 135, 37, 0.2) !important;
}
.text-impacto-orange\/80{
    color: rgba(237, 135, 37, 0.8) !important;
}
.hover\:bg-impacto-orange\/30:hover{
    background-color: rgba(237, 135, 37, 0.3) !important;
}
.group-hover\:border-impacto-orange\/50:hover{
   border-color: rgba(237, 135, 37, 0.5) !important;
}


/* Estilo moderno para botones principales (Naranja) */
.btn-modern-primary {
  background-color: var(--color-secondary-brand);
  color: white;
  font-weight: 500;
  padding: 0.625rem 1.25rem; /* 10px 20px */
  border-radius: 0.5rem; /* 8px */
  transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.btn-modern-primary:hover {
  background-color: var(--color-secondary-brand-darker);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.btn-modern-primary:disabled {
  background-color: #f3f4f6; /* gray-100 */
  color: #9ca3af; /* gray-400 */
  cursor: not-allowed;
  box-shadow: none;
}

/* Estilo moderno para botones secundarios (Borde Azul Oscuro) */
.btn-modern-secondary {
  background-color: transparent;
  color: var(--color-primary-brand);
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  border-radius: 0.5rem;
  border: 2px solid var(--color-primary-brand);
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}
.btn-modern-secondary:hover {
  background-color: rgba(5, 31, 51, 0.05); /* Azul oscuro muy transparente */
}
.btn-modern-secondary:disabled {
  color: #9ca3af; /* gray-400 */
  border-color: #d1d5db; /* gray-300 */
  cursor: not-allowed;
  background-color: transparent;
}


@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulseHighlight {
  0% { box-shadow: 0 0 0 0 rgba(237, 135, 37, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(237, 135, 37, 0); }
  100% { box-shadow: 0 0 0 0 rgba(237, 135, 37, 0); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-pulse-highlight {
  animation: pulseHighlight 2s infinite;
}

.sketch-picker-custom {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.input-file-modern {
  background-color: #fff;
  border: 1px solid #D1D5DB;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition-property: border-color, box-shadow;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
  font-size: 0.875rem;
  color: #4B5563;
}

.input-file-modern:hover {
  border-color: #9CA3AF;
}

.input-file-modern:focus-within {
  outline: none;
  border-color: #003B6D;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(0, 59, 109, 0.5);
}

.input-file-modern::file-selector-button {
  border: none; 
  padding: 0.75rem 1.5rem;
  margin-right: 1rem;
  border-top-left-radius: calc(0.5rem - 1px);
  border-bottom-left-radius: calc(0.5rem - 1px);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  font-weight: 600;
  color: white;
  background-image: linear-gradient(to right, #FF8C42, #FF6B00);
  cursor: pointer;
  transition: filter 0.2s ease-in-out;
}

.input-file-modern::file-selector-button:hover {
  filter: brightness(110%);
}

.input {
  width: 100%;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  outline: none;
  transition: border 0.2s;
  background: #f9fafb;
}
.input:focus {
  border-color: #003B6D;
}
.input-color {
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: #f9fafb;
  display: inline-block;
  vertical-align: middle;
  margin-top: 0.25rem;
}
</style>
