<template>
  <div class="space-y-8">
    <!-- Fila Superior Skeleton -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
      <!-- <PERSON><PERSON><PERSON> Principal Skeleton (lg:col-span-2) -->
      <div class="lg:col-span-2 bg-slate-100 p-6 rounded-2xl shadow-lg">
        <!-- Título y subtítulo -->
        <div class="h-8 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2 mb-6 animate-pulse"></div>

        <div class="grid sm:grid-cols-2 gap-4 mb-6">
          <!-- Dos sub-bloques de stats -->
          <div class="space-y-4">
            <div class="bg-gray-200/50 p-4 rounded-xl min-h-[100px] animate-pulse">
              <div class="h-6 bg-gray-300 rounded w-1/3 mb-2"></div>
              <div class="h-3 bg-gray-300 rounded w-2/3"></div>
            </div>
            <div class="bg-gray-200/50 p-4 rounded-xl min-h-[100px] animate-pulse">
              <div class="h-6 bg-gray-300 rounded w-1/3 mb-2"></div>
              <div class="h-3 bg-gray-300 rounded w-2/3"></div>
            </div>
          </div>
          <!-- Botones -->
          <div class="flex flex-col space-y-3">
            <div class="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div class="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div class="h-10 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
        
        <!-- Sugerencia IA Skeleton -->
        <div class="bg-gray-200/50 p-3.5 rounded-xl mt-auto animate-pulse">
          <div class="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
          <div class="h-3 bg-gray-300 rounded w-full mb-1"></div>
          <div class="h-3 bg-gray-300 rounded w-5/6"></div>
        </div>
      </div>

      <!-- Bloque Secundario Skeleton (Asesor IA) -->
      <div class="bg-white p-6 rounded-2xl shadow-lg border border-gray-200/50">
        <div class="h-6 bg-gray-200 rounded w-1/2 mb-2 animate-pulse"></div>
        <div class="h-4 bg-gray-200 rounded w-3/4 mb-5 animate-pulse"></div>
        <div class="space-y-3.5">
          <div class="p-4 bg-slate-100 rounded-xl border border-slate-200 animate-pulse">
            <div class="h-4 bg-gray-300 rounded w-1/3 mb-1.5"></div>
            <div class="h-3 bg-gray-300 rounded w-full mb-2"></div>
            <div class="h-3 bg-gray-300 rounded w-3/4"></div>
          </div>
          <div class="p-4 bg-slate-100 rounded-xl border border-slate-200 animate-pulse">
            <div class="h-4 bg-gray-300 rounded w-1/3 mb-1.5"></div>
            <div class="h-3 bg-gray-300 rounded w-full mb-2"></div>
            <div class="h-3 bg-gray-300 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Fila Inferior Skeleton (Visión General del Rendimiento) -->
    <div>
      <div class="h-6 bg-gray-200 rounded w-1/3 mb-6 animate-pulse"></div>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
        <!-- Tarjeta KPI Skeleton (repetir 3 veces) -->
        <div v-for="i in 3" :key="i" class="bg-white p-6 rounded-2xl shadow-lg border border-gray-200/50">
          <div class="flex items-center mb-4">
            <div class="p-3.5 bg-gray-200 rounded-xl mr-4 animate-pulse">
              <div class="h-7 w-7 bg-gray-300 rounded"></div>
            </div>
            <div class="h-5 bg-gray-300 rounded w-1/2 animate-pulse"></div>
          </div>
          <div class="h-8 bg-gray-300 rounded w-1/3 mb-1.5 animate-pulse"></div>
          <div class="h-3 bg-gray-300 rounded w-2/3 mb-1 animate-pulse"></div>
          <div class="h-3 bg-gray-300 rounded w-1/2 mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-200 rounded mb-4 animate-pulse"></div>
          <div class="h-10 bg-gray-300 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// No se necesita script específico para un componente de skeleton simple
</script>

<style scoped>
/* Puedes añadir estilos específicos aquí si Tailwind no es suficiente, 
   aunque para skeletons usualmente las clases de utilidad son adecuadas. */
/* Asegurar que animate-pulse se aplique bien, Tailwind lo incluye por defecto */
</style> 