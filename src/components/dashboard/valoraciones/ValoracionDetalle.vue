<template>
  <div v-if="valoracion" class="bg-slate-50 rounded-xl shadow-xl overflow-hidden flex flex-col max-h-[95vh]">
    <!-- Cabecera con información principal -->
    <div class="bg-impacto-blue text-white p-4 sm:p-5 shrink-0">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div class="mb-3 sm:mb-0 flex-grow min-w-0 pr-4">
          <h3 class="text-xl sm:text-2xl font-bold tracking-tight truncate" :title="valoracion.direccion">
            {{ valoracion.direccion }}
          </h3>
          <div class="flex items-center mt-1.5 text-xs opacity-90">
            <span class="bg-white/20 rounded-full px-2.5 py-0.5">
              Ref. Catastral: {{ valoracion.referencia_catastral || 'No disponible' }}
            </span>
            <span class="ml-2.5 hidden md:inline">
              Creada el {{ formatDate(valoracion.fecha_creacion) }}
            </span>
          </div>
        </div>
        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center sm:text-right w-full sm:w-auto shrink-0">
          <div class="text-xs opacity-80 mb-0.5">Valor estimado</div>
          <div class="text-lg sm:text-xl lg:text-2xl font-bold">
            {{ formatCurrency(valoracion.valor_estimado_min) }} - {{ formatCurrency(valoracion.valor_estimado_max) }}
          </div>
        </div>
      </div>
      <div class="mt-1.5 text-xs opacity-90 md:hidden">
        Creada el {{ formatDate(valoracion.fecha_creacion) }}
      </div>
    </div>

    <!-- Contenido principal scrollable -->
    <div class="p-4 sm:p-5 overflow-y-auto flex-grow">
      <div class="grid grid-cols-1 lg:grid-cols-5 gap-4 sm:gap-5">
        
        <!-- Columna Principal (3/5): Características y Apuntes -->
        <div class="lg:col-span-3 space-y-4 sm:space-y-5">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200/80 p-3.5 sm:p-4">
            <h4 class="text-sm sm:text-base font-semibold text-impacto-blue-dark mb-4 capitalize-first">Características del inmueble</h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-5 gap-y-4 text-xs sm:text-sm">
              
              <div v-if="valoracion.tipo_principal" class="flex items-start">
                <HomeIcon class="h-5 w-5 mr-2.5 text-impacto-blue shrink-0 mt-px" />
                <div><p class="text-gray-500">Tipo</p><p class="text-gray-800 font-medium">{{ valoracion.tipo_principal }}</p></div>
              </div>

              <div v-if="valoracion.subtipo" class="flex items-start">
                <TagIcon class="h-5 w-5 mr-2.5 text-impacto-blue shrink-0 mt-px" />
                <div><p class="text-gray-500">Subtipo</p><p class="text-gray-800 font-medium">{{ valoracion.subtipo }}</p></div>
              </div>

              <div v-if="valoracion.superficie" class="flex items-start">
                <ArrowsPointingOutIcon class="h-5 w-5 mr-2.5 text-impacto-blue shrink-0 mt-px" />
                <div><p class="text-gray-500">Superficie</p><p class="text-gray-800 font-medium">{{ valoracion.superficie }} m²</p></div>
              </div>

              <div v-if="valoracion.superficie_parcela" class="flex items-start">
                 <BoxSelectIcon class="h-5 w-5 mr-2.5 text-impacto-blue shrink-0 mt-px" />
                <div><p class="text-gray-500">Sup. parcela</p><p class="text-gray-800 font-medium">{{ valoracion.superficie_parcela }} m²</p></div>
              </div>
              
              <div v-if="valoracion.habitaciones !== null && valoracion.habitaciones !== undefined" class="flex items-start">
                <BedIcon class="h-5 w-5 mr-2.5 text-impacto-blue shrink-0 mt-px" />
                <div><p class="text-gray-500">Habitaciones</p><p class="text-gray-800 font-medium">{{ valoracion.habitaciones ?? 'N/A' }}</p></div>
              </div>
              
              <div v-if="valoracion.banos !== null && valoracion.banos !== undefined" class="flex items-start">
                <BathIcon class="h-5 w-5 mr-2.5 text-impacto-blue shrink-0 mt-px" />
                <div><p class="text-gray-500">Baños</p><p class="text-gray-800 font-medium">{{ valoracion.banos ?? 'N/A' }}</p></div>
              </div>

              <div v-if="valoracion.estado" class="flex items-start">
                <CheckBadgeIcon class="h-5 w-5 mr-2.5 text-impacto-blue shrink-0 mt-px" />
                <div><p class="text-gray-500">Estado</p><p class="text-gray-800 font-medium">{{ valoracion.estado }}</p></div>
              </div>

              <div v-if="valoracion.planta" class="flex items-start">
                <LayersIcon class="h-5 w-5 mr-2.5 text-impacto-blue shrink-0 mt-px" />
                <div><p class="text-gray-500">Planta</p><p class="text-gray-800 font-medium">{{ valoracion.planta }}</p></div>
              </div>
            
            </div>
          </div>

          <div v-if="parsedExtras && Object.keys(parsedExtras).length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200/80 p-3.5 sm:p-4">
            <h4 class="text-sm sm:text-base font-semibold text-impacto-blue-dark mb-3 capitalize-first">Extras y equipamiento</h4>
            <div class="grid grid-cols-2 gap-x-4 gap-y-1.5 text-xs sm:text-sm">
              <div v-for="(value, key) in parsedExtras" :key="key" class="flex items-center">
                <CheckCircleIcon v-if="value" class="h-4 w-4 text-green-500 mr-1.5 shrink-0" />
                <XCircleIcon v-else class="h-4 w-4 text-red-400 mr-1.5 shrink-0" />
                <span class="text-gray-700">{{ formatExtraKey(key) }}</span>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200/80 p-3.5 sm:p-4">
            <h4 class="text-sm sm:text-base font-semibold text-impacto-blue-dark mb-3 capitalize-first">Notas del agente</h4>
            <textarea
              :value="editableNotasAgente"
              @input="editableNotasAgente = ($event.target as HTMLTextAreaElement).value"
              placeholder="Añadir notas internas sobre la valoración..."
              class="w-full h-24 p-2 border-gray-300 rounded-md text-xs sm:text-sm focus:ring-impacto-blue focus:border-impacto-blue resize-y"
            ></textarea>
            <div v-if="notasAgenteChanged" class="mt-2 text-right">
              <button
                @click="saveNotasAgente"
                :disabled="isSavingNotasAgente"
                class="px-3 py-1.5 text-xs bg-impacto-orange text-white rounded-md hover:bg-orange-600 transition-colors disabled:opacity-50"
              >
                {{ isSavingNotasAgente ? 'Guardando...' : 'Guardar notas' }}
              </button>
            </div>
             <p v-else-if="!editableNotasAgente && !isSavingNotasAgente" class="text-xs sm:text-sm text-gray-500 italic mt-1">
              No hay notas para esta valoración.
            </p>
          </div>
        </div>
        
        <!-- Columna Lateral (2/5): Ubicación y Lead -->
        <div class="lg:col-span-2 space-y-4 sm:space-y-5">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200/80 p-3.5 sm:p-4">
            <h4 class="text-sm sm:text-base font-semibold text-impacto-blue-dark mb-3 capitalize-first">Ubicación</h4>
            <div class="aspect-video bg-slate-200 rounded-md border border-slate-300 relative overflow-hidden group">
              <GoogleMap
                v-if="isMapKeyConfigured && markerCoordinates"
                :api-key="GOOGLE_MAPS_API_KEY"
                style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;"
                :center="mapCenter"
                :zoom="15"
              >
                <Marker :options="{ position: markerCoordinates }" />
              </GoogleMap>
              <div v-else-if="!isMapKeyConfigured" class="absolute inset-0 flex flex-col items-center justify-center text-center p-4 bg-yellow-50 text-yellow-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <p class="font-semibold text-sm">API Key de Google Maps no configurada.</p>
                <p class="text-xs mt-1">Por favor, configura la API Key en el archivo .env o directamente en el código.</p>
              </div>
              <div v-else class="absolute inset-0 flex items-center justify-center text-center p-4">
                 <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto mb-1.5 text-impacto-blue/70" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                </svg>
                <p class="text-xs text-slate-500 italic">Coordenadas no disponibles para el mapa.</p>
              </div>
            </div>
            <a v-if="typeof valoracion.latitud === 'number' && typeof valoracion.longitud === 'number'"
              :href="`https://www.google.com/maps/search/?api=1&query=${valoracion.latitud},${valoracion.longitud}`"
              target="_blank"
              rel="noopener noreferrer"
              class="mt-2.5 block w-full text-center px-3 py-2 bg-impacto-blue/10 hover:bg-impacto-blue/20 text-impacto-blue-dark text-xs sm:text-sm font-medium rounded-md transition-colors"
            >
              Abrir en Google Maps
            </a>
          </div>
          
          <div v-if="lead" class="bg-white rounded-lg shadow-sm border border-gray-200/80 p-3.5 sm:p-4">
            <h4 class="text-sm sm:text-base font-semibold text-impacto-blue-dark mb-3 capitalize-first">Información del lead</h4>
            <div class="flex items-center mb-3">
              <div class="h-10 w-10 rounded-full bg-impacto-orange/10 text-impacto-orange flex items-center justify-center mr-3 text-base font-semibold ring-1 ring-impacto-orange/20 shrink-0">
                {{ getInitials(lead.nombre) }}
              </div>
              <div>
                <div class="font-semibold text-gray-800 text-sm leading-tight">{{ lead.nombre }}</div>
                <div class="text-xs text-gray-500">Registrado: {{ formatDate(lead.fecha_creacion) }}</div>
              </div>
            </div>
            <dl class="space-y-2 text-xs sm:text-sm">
              <div v-if="lead.email" class="flex items-center">
                <MailIcon class="h-4 w-4 mr-2 text-gray-400 shrink-0" />
                <a :href="'mailto:' + lead.email" class="text-impacto-blue hover:underline break-all">{{ lead.email }}</a>
              </div>
               <div v-else class="flex items-center">
                <MailIcon class="h-4 w-4 mr-2 text-gray-400 shrink-0" />
                <span class="text-gray-500 italic">Email no disponible</span>
              </div>

              <div v-if="lead.telefono" class="flex items-center">
                <PhoneIcon class="h-4 w-4 mr-2 text-gray-400 shrink-0" />
                <span class="text-gray-700">{{ formatPhone(lead.telefono) }}</span>
              </div>
              <div v-else class="flex items-center">
                <PhoneIcon class="h-4 w-4 mr-2 text-gray-400 shrink-0" />
                <span class="text-gray-500 italic">Teléfono no disponible</span>
              </div>

              <div v-if="lead.necesidad" class="flex items-start">
                 <MessageSquareTextIcon class="h-4 w-4 mr-2 text-gray-400 shrink-0 mt-0.5" />
                <div>
                  <span class="text-gray-500">Necesidad:</span>
                  <p class="text-gray-700">{{ lead.necesidad }}</p>
                </div>
              </div>
            </dl>
            <div class="flex justify-end mt-2.5">
              <button @click="$emit('verLead', lead.id)" class="text-xs font-medium text-impacto-orange hover:underline transition-colors flex items-center">
                Ver perfil completo
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2.5">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </button>
            </div>
          </div>
           <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200/80 p-3.5 sm:p-4">
             <h4 class="text-sm sm:text-base font-semibold text-impacto-blue-dark mb-3 capitalize-first">Información del lead</h4>
            <p class="text-xs sm:text-sm text-gray-500 italic">Esta valoración no está asociada a ningún lead específico.</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Pie con acciones -->
    <div class="border-t border-gray-200 p-3 bg-white flex flex-col sm:flex-row justify-between items-center gap-2 shrink-0">
      <div class="text-xs text-gray-400 italic truncate shrink min-w-0" :title="'ID Valoración: ' + valoracion.uuid">
        ID: {{ valoracion.uuid }}
      </div>
      <div class="flex space-x-2">
        <button @click="$emit('close')" class="px-4 py-2 border border-gray-300 rounded-lg text-xs sm:text-sm font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-gray-400 transition-colors">
          Cerrar
        </button>
        <button @click="descargarInformePDF(valoracion)" class="px-4 py-2 bg-impacto-blue text-white rounded-lg text-xs sm:text-sm font-medium hover:bg-impacto-blue-dark focus:outline-none focus:ring-1 focus:ring-impacto-blue transition-colors flex items-center">
          <DownloadIcon class="h-3.5 w-3.5 mr-1.5" />
          Exportar PDF
        </button>
      </div>
    </div>
  </div>
  <div v-else class="bg-white rounded-xl shadow-xl p-6 sm:p-8 text-center border border-gray-200/80">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 sm:h-12 sm:w-12 text-slate-400 mx-auto mb-3 sm:mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
    <h3 class="text-lg sm:text-xl font-semibold text-gray-700 mb-1 sm:mb-2">No se encontró la valoración</h3>
    <p class="text-xs sm:text-sm text-gray-500">La valoración solicitada no está disponible o ha sido eliminada.</p>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch } from 'vue';
import { useToast } from '@/composables/useToast'; 
import { GoogleMap, Marker } from 'vue3-google-map';
import { apiFetch } from '@/utils/apiFetch';
import { 
  HomeIcon,
  TagIcon,
  ArrowsPointingOutIcon,
  CheckBadgeIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/vue/24/outline'
import { 
  Bed as BedIcon,
  Bath as BathIcon,
  BoxSelect as BoxSelectIcon,
  Layers as LayersIcon,
  Mail as MailIcon,
  Phone as PhoneIcon,
  MessageSquareText as MessageSquareTextIcon,
  Download as DownloadIcon,
} from 'lucide-vue-next';

// INTERFACES
interface Lead {
  id: number;
  uuid: string;
  nombre: string;
  email: string | null;
  telefono?: string | null;
  necesidad?: string | null;
  notas?: string | null;
  fecha_creacion: string;
}

interface Valoracion {
  id: number;
  uuid: string;
  cliente_valorador_id: number;
  client_identifier?: string;
  lead_id?: number | null;
  referencia_catastral?: string | null;
  direccion: string;
  latitud?: number | null;
  longitud?: number | null;
  tipo_principal: string;
  subtipo?: string | null;
  superficie: number;
  superficie_parcela?: number | null;
  habitaciones?: number | null;
  banos?: number | null;
  estado?: string | null;
  planta?: string | null;
  extras?: string | null;
  valor_estimado_min: number;
  valor_estimado_max: number;
  notas_agente?: string | null;
  // Nuevos campos de datos enriquecidos
  ano_construccion_catastro?: number | null;
  precio_m2_promedio?: number | null;
  tamano_promedio?: number | null;
  porcentaje_con_piscina?: number | null;
  porcentaje_con_parking?: number | null;
  porcentaje_con_ascensor?: number | null;
  porcentaje_con_terraza?: number | null;
  porcentaje_reformadas?: number | null;
  numero_propiedades_analizadas?: number | null;
  distancia_al_centroide_km?: number | null;
  precio_vs_zona_porcentaje?: number | null;
  tamano_vs_zona_porcentaje?: number | null;
  fecha_creacion: string;
  fecha_modificacion?: string | null;
}

// PROPS & EMITS
const props = defineProps<{
  valoracion: Valoracion | null;
  lead: Lead | null;
}>();

const emit = defineEmits(['close', 'verLead', 'update:notasAgente']);
const { showToast } = useToast();

const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
// Array de placeholders conocidos para la API Key
const API_KEY_PLACEHOLDERS = ['YOUR_GOOGLE_MAPS_API_KEY', 'YOUR_GOOGLE_MAPS_API_KEY_PLACEHOLDER'];

// FUNCIONES DE UTILIDAD Y FORMATO (definidas antes de su uso en watchers/computed)
const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return 'Fecha inválida';
  return date.toLocaleDateString('es-ES', {
    year: 'numeric', month: 'short', day: 'numeric',
  });
};

const formatCurrency = (value: number | null | undefined) => {
  if (value == null || isNaN(value)) return 'N/A';
  // Formatear como número y añadir el símbolo € para evitar el espacio.
  return new Intl.NumberFormat('es-ES', { maximumFractionDigits: 0 }).format(value) + '€';
};

const formatPhone = (phone?: string | null) => {
  if (!phone) return 'No disponible';
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 9) {
    return `+34 ${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  }
  return phone;
};

const getInitials = (name: string | null | undefined) => {
  if (!name) return '--';
  return name.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('') || '--';
};

// ESTADO REACTIVO
const editableNotasAgente = ref('');
const isSavingNotasAgente = ref(false);
const notasAgenteOriginal = ref('');

// WATCHERS (después de definir las funciones que usan)
watch(() => props.valoracion, (newVal) => {
  if (newVal) {
    const newNotes = newVal.notas_agente || '';
    editableNotasAgente.value = newNotes;
    notasAgenteOriginal.value = newNotes;
  }
}, { immediate: true, deep: true });

// COMPUTED PROPS
const isMapKeyConfigured = computed(() => {
  // Comprueba si la API key está definida y no es uno de los placeholders conocidos.
  return GOOGLE_MAPS_API_KEY && !API_KEY_PLACEHOLDERS.includes(GOOGLE_MAPS_API_KEY);
});

const mapCenter = computed(() => {
  if (props.valoracion && typeof props.valoracion.latitud === 'number' && typeof props.valoracion.longitud === 'number') {
    return { lat: props.valoracion.latitud, lng: props.valoracion.longitud };
  }
  return { lat: 40.416775, lng: -3.703790 }; // Coordenadas de respaldo (Madrid)
});

const markerCoordinates = computed(() => {
  if (props.valoracion && typeof props.valoracion.latitud === 'number' && typeof props.valoracion.longitud === 'number') {
    return { lat: props.valoracion.latitud, lng: props.valoracion.longitud };
  }
  return null;
});

const notasAgenteChanged = computed(() => {
  return editableNotasAgente.value !== notasAgenteOriginal.value;
});

const extrasMap = new Map<string, string>([
  ['Ascensor', 'Ascensor'],
  ['Parking', 'Parking'],
  ['Piscina', 'Piscina'],
  ['Terraza', 'Terraza'],
  ['Trastero', 'Trastero'],
  ['Jardin', 'Jardín'],
  ['AireAcondicionado', 'Aire acondicionado'],
  ['nlp_esta_reformado', 'Está reformado'],
  ['nlp_tiene_vistas_mar', 'Vistas al mar'],
  ['nlp_es_primera_linea', 'Primera línea de playa'],
  ['nlp_es_luminoso', 'Luminoso'],
  ['nlp_tiene_calefaccion', 'Tiene calefacción'],
  ['nlp_es_exterior', 'Es exterior'],
]);

const parsedExtras = computed(() => {
  const extras = props.valoracion?.extras;
  if (!extras) return null;

  let extrasData;
  if (typeof extras === 'string' && extras.trim()) {
    try {
      extrasData = JSON.parse(extras);
    } catch (e) {
      console.error("Error parsing `extras` JSON in ValoracionDetalle:", e);
      return null;
    }
  } else if (typeof extras === 'object' && extras !== null) {
    extrasData = extras;
  } else {
    return null;
  }

  // Filtramos el objeto para quedarnos solo con los extras válidos
  const validExtrasList = Array.from(extrasMap.keys());
  let finalObject: Record<string, boolean> = {};

  if (Array.isArray(extrasData)) {
    extrasData.forEach(item => {
      if (typeof item === 'string' && validExtrasList.includes(item)) {
        finalObject[item] = true;
      }
    });
  } else if (typeof extrasData === 'object' && extrasData !== null) {
     for (const key in extrasData) {
        if (Object.prototype.hasOwnProperty.call(extrasData, key) && validExtrasList.includes(key)) {
            finalObject[key] = extrasData[key];
        }
    }
  }

  if (Object.keys(finalObject).length > 0) {
    return finalObject;
  }

  return null;
});

const formatExtraKey = (key: string) => {
  return extrasMap.get(key) || key.replace(/([A-Z])/g, ' $1').trim();
};

const descargarInformePDF = (valoracion: Valoracion | null) => {
  if (!valoracion || !valoracion.uuid) {
    showToast('No se puede descargar el informe: UUID no encontrado.', 'error');
    console.error('No se puede descargar el informe porque la valoración o su UUID no están disponibles.');
    return;
  }
  const url = `https://storage.googleapis.com/inmoautomation-reports/reports/inmovalor/informe_valoracion_v7_${valoracion.uuid}.pdf`;
  window.open(url, '_blank');
};

// MÉTODOS
const saveNotasAgente = async () => {
  if (!props.valoracion) {
    showToast('No se puede guardar, la valoración no está disponible.', 'error')
    return;
  }
  isSavingNotasAgente.value = true
  try {
    const response = await apiFetch('update_valoracion_datos_adicionales.php', {
      method: 'POST',
      body: JSON.stringify({
        valoracion_id: props.valoracion.id,
        notas_agente: editableNotasAgente.value
      })
    });

    if (!response.success) {
      throw new Error(response.message || 'Error en la respuesta del servidor');
    }

    notasAgenteOriginal.value = editableNotasAgente.value;
    emit('update:notasAgente', props.valoracion.id, editableNotasAgente.value);
    showToast(response.message || 'Notas guardadas correctamente.', 'success');

  } catch (error: any) {
    console.error('Error guardando notas del agente:', error);
    showToast(error.data?.message || error.message || 'Error al guardar las notas.', 'error');
    // Si falla, revertimos al estado original para que el usuario pueda intentarlo de nuevo sin perder el texto original.
    editableNotasAgente.value = notasAgenteOriginal.value;
  } finally {
    isSavingNotasAgente.value = false;
  }
};

</script>

<style scoped>
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.aspect-video {
  aspect-ratio: 16 / 9; /* O el aspect ratio que prefieras para el mapa, ej 4/3 */
}
</style>
