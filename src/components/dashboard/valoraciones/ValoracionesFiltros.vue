<template>
  <div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-impacto-blue mb-3 md:mb-0">Filt<PERSON> y búsqueda</h3>
      <div class="flex space-x-2">
        <button 
          @click="limpiarFiltros" 
          class="text-sm text-gray-600 hover:text-impacto-blue transition-colors flex items-center"
          :disabled="!hayFiltrosActivos"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          Limpiar filtros
        </button>
        <button 
          @click="toggleFiltrosAvanzados" 
          class="text-sm text-impacto-blue hover:text-impacto-orange transition-colors flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
          </svg>
          {{ mostrarFiltrosAvanzados ? 'Ocultar filtros avanzados' : 'Mostrar filtros avanzados' }}
        </button>
      </div>
    </div>

    <!-- Filtros básicos -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <!-- Búsqueda por dirección -->
      <div>
        <label for="busqueda" class="block text-sm font-medium text-gray-700 mb-1">Dirección</label>
        <div class="relative">
          <input 
            type="text" 
            id="busqueda" 
            v-model="filtros.direccion" 
            placeholder="Buscar por dirección..." 
            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Filtro por tipo -->
      <div>
        <label for="tipo" class="block text-sm font-medium text-gray-700 mb-1">Tipo de inmueble</label>
        <select 
          id="tipo" 
          v-model="filtros.tipo_principal" 
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
        >
          <option value="">Todos los tipos</option>
          <option value="Piso">Piso</option>
          <option value="Casa">Casa</option>
          <option value="Chalet">Chalet</option>
          <option value="Ático">Ático</option>
          <option value="Local">Local</option>
          <option value="Oficina">Oficina</option>
          <option value="Terreno">Terreno</option>
          <option value="Garaje">Garaje</option>
          <option value="Trastero">Trastero</option>
        </select>
      </div>

      <!-- Filtro por fecha -->
      <div>
        <label for="fecha" class="block text-sm font-medium text-gray-700 mb-1">Periodo</label>
        <select 
          id="fecha" 
          v-model="filtros.periodo" 
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
        >
          <option value="">Cualquier fecha</option>
          <option value="hoy">Hoy</option>
          <option value="semana">Última semana</option>
          <option value="mes">Último mes</option>
          <option value="trimestre">Último trimestre</option>
          <option value="año">Último año</option>
        </select>
      </div>
    </div>

    <!-- Filtros avanzados (colapsables) -->
    <div v-if="mostrarFiltrosAvanzados" class="border-t border-gray-200 pt-4 mt-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <!-- Rango de superficie -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Superficie (m²)</label>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <input 
                type="number" 
                v-model="filtros.superficie_min" 
                placeholder="Mín" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              />
            </div>
            <div>
              <input 
                type="number" 
                v-model="filtros.superficie_max" 
                placeholder="Máx" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              />
            </div>
          </div>
        </div>

        <!-- Rango de habitaciones -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Habitaciones</label>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <input 
                type="number" 
                v-model="filtros.habitaciones_min" 
                placeholder="Mín" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              />
            </div>
            <div>
              <input 
                type="number" 
                v-model="filtros.habitaciones_max" 
                placeholder="Máx" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              />
            </div>
          </div>
        </div>

        <!-- Rango de baños -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Baños</label>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <input 
                type="number" 
                v-model="filtros.banos_min" 
                placeholder="Mín" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              />
            </div>
            <div>
              <input 
                type="number" 
                v-model="filtros.banos_max" 
                placeholder="Máx" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              />
            </div>
          </div>
        </div>

        <!-- Rango de precio -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Valor estimado (€)</label>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <input 
                type="number" 
                v-model="filtros.valor_min" 
                placeholder="Mín" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              />
            </div>
            <div>
              <input 
                type="number" 
                v-model="filtros.valor_max" 
                placeholder="Máx" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Estado -->
        <div>
          <label for="estado" class="block text-sm font-medium text-gray-700 mb-1">Estado</label>
          <select 
            id="estado" 
            v-model="filtros.estado" 
            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
          >
            <option value="">Cualquier estado</option>
            <option value="Nuevo">Nuevo</option>
            <option value="Buen estado">Buen estado</option>
            <option value="A reformar">A reformar</option>
            <option value="Reformado">Reformado</option>
          </select>
        </div>

        <!-- Planta -->
        <div>
          <label for="planta" class="block text-sm font-medium text-gray-700 mb-1">Planta</label>
          <select 
            id="planta" 
            v-model="filtros.planta" 
            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
          >
            <option value="">Cualquier planta</option>
            <option value="Bajo">Bajo</option>
            <option value="Entreplanta">Entreplanta</option>
            <option value="1">1ª</option>
            <option value="2">2ª</option>
            <option value="3">3ª</option>
            <option value="4">4ª</option>
            <option value="5+">5ª o superior</option>
          </select>
        </div>

        <!-- Referencia catastral -->
        <div>
          <label for="referencia" class="block text-sm font-medium text-gray-700 mb-1">Referencia catastral</label>
          <input 
            type="text" 
            id="referencia" 
            v-model="filtros.referencia_catastral" 
            placeholder="Buscar por referencia..." 
            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-impacto-blue focus:border-impacto-blue"
          />
        </div>
      </div>
    </div>

    <!-- Botones de acción -->
    <div class="flex justify-end mt-4">
      <button 
        @click="aplicarFiltros" 
        class="px-4 py-2 bg-impacto-blue text-white rounded-md hover:bg-impacto-blue/90 transition-colors flex items-center"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
        </svg>
        Aplicar filtros
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits, watch } from 'vue';

interface Filtros {
  direccion: string;
  tipo_principal: string;
  periodo: string;
  superficie_min: number | null;
  superficie_max: number | null;
  habitaciones_min: number | null;
  habitaciones_max: number | null;
  banos_min: number | null;
  banos_max: number | null;
  valor_min: number | null;
  valor_max: number | null;
  estado: string;
  planta: string;
  referencia_catastral: string;
}

const emit = defineEmits(['filtrar']);

const mostrarFiltrosAvanzados = ref(false);
const filtros = ref<Filtros>({
  direccion: '',
  tipo_principal: '',
  periodo: '',
  superficie_min: null,
  superficie_max: null,
  habitaciones_min: null,
  habitaciones_max: null,
  banos_min: null,
  banos_max: null,
  valor_min: null,
  valor_max: null,
  estado: '',
  planta: '',
  referencia_catastral: ''
});

const hayFiltrosActivos = computed(() => {
  return filtros.value.direccion !== '' ||
    filtros.value.tipo_principal !== '' ||
    filtros.value.periodo !== '' ||
    filtros.value.superficie_min !== null ||
    filtros.value.superficie_max !== null ||
    filtros.value.habitaciones_min !== null ||
    filtros.value.habitaciones_max !== null ||
    filtros.value.banos_min !== null ||
    filtros.value.banos_max !== null ||
    filtros.value.valor_min !== null ||
    filtros.value.valor_max !== null ||
    filtros.value.estado !== '' ||
    filtros.value.planta !== '' ||
    filtros.value.referencia_catastral !== '';
});

const toggleFiltrosAvanzados = () => {
  mostrarFiltrosAvanzados.value = !mostrarFiltrosAvanzados.value;
};

const limpiarFiltros = () => {
  filtros.value = {
    direccion: '',
    tipo_principal: '',
    periodo: '',
    superficie_min: null,
    superficie_max: null,
    habitaciones_min: null,
    habitaciones_max: null,
    banos_min: null,
    banos_max: null,
    valor_min: null,
    valor_max: null,
    estado: '',
    planta: '',
    referencia_catastral: ''
  };
  aplicarFiltros();
};

const aplicarFiltros = () => {
  // Convertir el periodo a fechas reales
  let fechaDesde = null;
  if (filtros.value.periodo) {
    const hoy = new Date();
    fechaDesde = new Date();
    
    switch (filtros.value.periodo) {
      case 'hoy':
        fechaDesde.setHours(0, 0, 0, 0);
        break;
      case 'semana':
        fechaDesde.setDate(hoy.getDate() - 7);
        break;
      case 'mes':
        fechaDesde.setMonth(hoy.getMonth() - 1);
        break;
      case 'trimestre':
        fechaDesde.setMonth(hoy.getMonth() - 3);
        break;
      case 'año':
        fechaDesde.setFullYear(hoy.getFullYear() - 1);
        break;
    }
  }

  // Emitir evento con los filtros aplicados
  emit('filtrar', {
    ...filtros.value,
    fechaDesde: fechaDesde ? fechaDesde.toISOString() : null
  });
};

// Aplicar filtros automáticamente cuando cambian los filtros básicos
watch([
  () => filtros.value.direccion,
  () => filtros.value.tipo_principal,
  () => filtros.value.periodo
], () => {
  aplicarFiltros();
}, { flush: 'post' });

// Se eliminó la implementación manual de debounce no utilizada
</script>

<style scoped>
/* Estilos específicos si son necesarios */
</style>
