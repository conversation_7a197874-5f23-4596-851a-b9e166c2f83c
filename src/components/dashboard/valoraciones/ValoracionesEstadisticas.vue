<template>
  <div>
    <!-- Sección de Estadísticas Clave -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
      <div class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
        <div class="flex items-center justify-between mb-3">
          <p class="text-sm font-medium text-gray-500 capitalize-first">Total de valoraciones</p>
          <div class="p-2.5 bg-sky-500/10 rounded-lg">
            <BuildingOffice2Icon class="h-6 w-6 text-sky-600" />
          </div>
        </div>
        <h3 class="text-3xl font-bold text-gray-800">{{ estadisticas.total }}</h3>
        <p class="mt-1 text-xs text-gray-500">
          <span class="font-bold text-gray-700">{{ estadisticas.totalEsteMes }}</span> este mes ·
          <span :class="estadisticas.incremento >= 0 ? 'text-green-600' : 'text-red-600'" class="font-semibold">
            <span v-if="estadisticas.incremento > 0">+</span>{{ estadisticas.incremento }}%
          </span>
          vs. mes anterior
        </p>
      </div>

      <div class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
        <div class="flex items-center justify-between mb-3">
          <p class="text-sm font-medium text-gray-500 capitalize-first">Valor medio estimado</p>
          <div class="p-2.5 bg-amber-500/10 rounded-lg">
            <CurrencyEuroIcon class="h-6 w-6 text-amber-600" />
          </div>
        </div>
        <h3 class="text-3xl font-bold text-gray-800">{{ formatCurrency(estadisticas.valorMedio) }}</h3>
        <p class="mt-1 text-xs text-gray-500 truncate" :title="`Rango: ${formatCurrency(estadisticas.valorMinimo)} - ${formatCurrency(estadisticas.valorMaximo)}`">
          Rango: {{ formatCurrency(estadisticas.valorMinimo) }} - {{ formatCurrency(estadisticas.valorMaximo) }}
        </p>
      </div>
      
      <div class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
        <div class="flex items-center justify-between mb-3">
          <p class="text-sm font-medium text-gray-500 capitalize-first">Superficie media</p>
          <div class="p-2.5 bg-emerald-500/10 rounded-lg">
            <ArrowsRightLeftIcon class="h-6 w-6 text-emerald-600" />
          </div>
        </div>
        <h3 class="text-3xl font-bold text-gray-800">{{ estadisticas.superficieMedia }} <span class="text-xl">m²</span></h3>
        <p class="mt-1 text-xs text-gray-500">
          Tipo más común: <span class="font-semibold">{{ estadisticas.tipoMasComun || 'N/A' }}</span>
        </p>
      </div>

      <!-- Tarjeta Modificada: Zona de Interés Principal -->
      <div class="bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
        <div class="flex items-center justify-between mb-3">
          <p class="text-sm font-medium text-gray-500 capitalize-first">Zona de interés principal</p> 
           <div class="p-2.5 bg-indigo-500/10 rounded-lg">
            <MapPinIcon class="h-6 w-6 text-indigo-600" />
          </div>
        </div>
        <h3 class="text-3xl font-bold text-gray-800 truncate" :title="estadisticas.zonaPrincipal?.nombre || 'Calculando...'">{{ estadisticas.zonaPrincipal?.nombre || 'Calculando...' }}</h3>
        <p class="mt-1 text-xs text-gray-500">
          <span class="font-semibold">{{ estadisticas.zonaPrincipal?.cantidad || 0 }}</span> valoraciones en esta zona
        </p>
      </div>

    </div>

    <!-- Sección de Gráficos: Gráfico de líneas y MAPA DE CALOR -->
    <div class="grid grid-cols-1 lg:grid-cols-5 gap-5 mb-8">
      <div class="lg:col-span-3 bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
        <h4 class="text-base font-semibold text-gray-700 mb-4 capitalize-first">Valoraciones en los últimos 30 días</h4>
        <div style="height: 300px;">
          <Line v-if="lineChartData.labels && lineChartData.labels.length" :data="lineChartData" :options="chartOptions" />
          <div v-else class="flex items-center justify-center h-full text-gray-500 italic">
            No hay suficientes datos para el gráfico.
          </div>
        </div>
      </div>
      <div class="lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-200/70 p-5 hover:shadow-xl transition-shadow duration-300">
        <h4 class="text-base font-semibold text-gray-700 mb-4 capitalize-first">Mapa de valoraciones</h4>
        <div style="height: 300px;" class="rounded-lg overflow-hidden border border-gray-300">
          <GoogleMap
            v-if="isMapKeyConfigured"
            :api-key="GOOGLE_MAPS_API_KEY"
            :libraries="['visualization']"
            style="width: 100%; height: 100%;"
            :center="mapCenter"
            :zoom="mapZoom"
          >
            <!-- Usar HeatmapLayer con datos dinámicos -->
            <HeatmapLayer 
              v-if="isMapKeyConfigured && heatmapDataPoints.length > 0" 
              :options="{ 
                data: heatmapDataPoints,
                radius: 35,      // Empezar con un radio moderado
                dissipating: true,
                maxIntensity: 8, // Permitir una mayor intensidad si hay concentración
                opacity: 0.7
              }" 
            />
            
            <!-- Comentar o eliminar los Markers individuales por ahora -->
            <!-- 
            <template v-if="valoracionesConCoordenadasParaMarcadores.length > 0">
              <Marker 
                v-for="valoracion in valoracionesConCoordenadasParaMarcadores" 
                :key="valoracion.id" 
                :options="getMarkerOptions(valoracion)" 
              />
            </template>
            -->

          </GoogleMap>
          <div v-else-if="!isMapKeyConfigured" class="h-full flex flex-col items-center justify-center text-center p-4 bg-yellow-50 text-yellow-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p class="font-semibold text-sm">API Key de Google Maps no configurada.</p>
            <p class="text-xs mt-1">Configura la API Key para ver el mapa.</p>
          </div>
          <div v-else class="h-full flex flex-col items-center justify-center text-gray-500 italic p-4 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto mb-1.5 text-impacto-blue/70" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
            </svg>
            <p>No hay valoraciones con coordenadas para mostrar en el mapa.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Colors,
  ArcElement
} from 'chart.js';
import { Line } from 'vue-chartjs';
import { GoogleMap, HeatmapLayer } from 'vue3-google-map';
import {
  BuildingOffice2Icon,
  CurrencyEuroIcon,
  ArrowsRightLeftIcon,
  MapPinIcon,
} from '@heroicons/vue/24/outline';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Colors,
  ArcElement
);

interface ValoracionParaMapa {
  id: number;
  direccion: string;
  latitud: number | null | undefined;
  longitud: number | null | undefined;
  valor_estimado_min: number;
  valor_estimado_max: number;
}

interface ZonaPrincipal {
  nombre: string;
  cantidad: number;
}

interface Estadisticas {
  total: number;
  totalEsteMes: number;
  incremento: number;
  valorMedio: number;
  valorMinimo: number;
  valorMaximo: number;
  superficieMedia: number;
  tipoMasComun: string | null;
  zonaPrincipal: ZonaPrincipal | null;
  valoracionesUltimoMesPorDia: { dia: string; cantidad: number }[];
  todasValoraciones: ValoracionParaMapa[];
}

const props = defineProps<{
  estadisticas: Estadisticas;
}>();

const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';
const API_KEY_PLACEHOLDERS = ['YOUR_GOOGLE_MAPS_API_KEY', 'YOUR_GOOGLE_MAPS_API_KEY_PLACEHOLDER', ''];

const isMapKeyConfigured = computed(() => {
  return GOOGLE_MAPS_API_KEY && !API_KEY_PLACEHOLDERS.includes(GOOGLE_MAPS_API_KEY);
});

// Regenerar heatmapDataPoints para el HeatmapLayer
const heatmapDataPoints = computed(() => {
  return props.estadisticas.todasValoraciones
    .filter(v => typeof v.latitud === 'number' && typeof v.longitud === 'number' && !isNaN(v.latitud) && !isNaN(v.longitud))
    .map(v => ({ lat: v.latitud!, lng: v.longitud! }));
});

const valoracionesConCoordenadasParaMarcadores = computed(() => {
  return props.estadisticas.todasValoraciones
    .filter(v => 
        typeof v.latitud === 'number' && !isNaN(v.latitud) &&
        typeof v.longitud === 'number' && !isNaN(v.longitud)
    );
});

const mapCenter = computed(() => {
  const puntosVisibles = valoracionesConCoordenadasParaMarcadores.value;
  if (puntosVisibles.length > 0) {
    const avgLat = puntosVisibles.reduce((sum, p) => sum + p.latitud!, 0) / puntosVisibles.length;
    const avgLng = puntosVisibles.reduce((sum, p) => sum + p.longitud!, 0) / puntosVisibles.length;
    if (!isNaN(avgLat) && !isNaN(avgLng)) {
        return { lat: avgLat, lng: avgLng };
    }
  }
  return { lat: 40.416775, lng: -3.703790 }; 
});

const mapZoom = computed(() => {
  const puntos = valoracionesConCoordenadasParaMarcadores.value;
  const numPuntos = puntos.length;

  if (numPuntos === 0) return 5; 
  if (numPuntos === 1) return 12; 

  let minLat = puntos[0].latitud!;
  let maxLat = puntos[0].latitud!;
  let minLng = puntos[0].longitud!;
  let maxLng = puntos[0].longitud!;

  for (let i = 1; i < numPuntos; i++) {
    if (puntos[i].latitud! < minLat) minLat = puntos[i].latitud!;
    if (puntos[i].latitud! > maxLat) maxLat = puntos[i].latitud!;
    if (puntos[i].longitud! < minLng) minLng = puntos[i].longitud!;
    if (puntos[i].longitud! > maxLng) maxLng = puntos[i].longitud!;
  }

  const latDiff = maxLat - minLat;
  const lngDiff = maxLng - minLng;

  if (isNaN(latDiff) || isNaN(lngDiff)) return 6; // Fallback si hay algún NaN

  if (latDiff < 0.05 && lngDiff < 0.05) return 13; 
  if (latDiff < 0.2 && lngDiff < 0.2) return 11; 
  if (latDiff < 0.5 && lngDiff < 0.5) return 9;  
  if (latDiff < 2 && lngDiff < 2) return 7;    
  
  return 6; 
});

const lineChartData = computed(() => {
  const labels = props.estadisticas.valoracionesUltimoMesPorDia.map(item => item.dia);
  const data = props.estadisticas.valoracionesUltimoMesPorDia.map(item => item.cantidad);
  
  return {
    labels,
    datasets: [
      {
        label: 'Valoraciones Realizadas',
        backgroundColor: 'rgba(2, 132, 199, 0.2)',
        borderColor: 'rgb(2, 132, 199)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(2, 132, 199)',
        pointHoverRadius: 6,
        tension: 0.3,
        fill: true,
        data,
      },
    ],
  };
});

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
      labels: {
        padding: 15,
        boxWidth: 12,
        font: {
          size: 11
        }
      }
    },
    tooltip: {
      backgroundColor: '#27374D',
      titleFont: { size: 14, weight: 'bold' as const },
      bodyFont: { size: 12 },
      padding: 10,
      cornerRadius: 6,
      displayColors: true,
      boxPadding: 4
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(200, 200, 200, 0.2)'
      },
      ticks: {
        font: { size: 10 },
        precision: 0
      }
    },
    x: {
      grid: {
        display: false
      },
      ticks: {
        font: { size: 10 }
      }
    }
  }
}));

const formatCurrency = (value: number | null | undefined) => {
  if (value === null || value === undefined) return 'N/A';
  // Formatear como número y añadir el símbolo € para evitar el espacio.
  return new Intl.NumberFormat('es-ES', { maximumFractionDigits: 0 }).format(value) + '€';
};

</script>

<style scoped>
/* Puedes añadir estilos adicionales aquí si es necesario */
</style>
