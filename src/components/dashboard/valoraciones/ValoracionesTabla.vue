<template>
  <div class="bg-white shadow-xl rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('fecha_creacion')">
              <div class="flex items-center">
                Fecha
                <span v-if="ordenActual.campo === 'fecha_creacion'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('direccion')">
              <div class="flex items-center">
                Dirección
                <span v-if="ordenActual.campo === 'direccion'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('tipo_principal')">
              <div class="flex items-center">
                Tipo
                <span v-if="ordenActual.campo === 'tipo_principal'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('superficie')">
              <div class="flex items-center">
                Superficie (m²)
                <span v-if="ordenActual.campo === 'superficie'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" @click="cambiarOrden('valor_estimado_min')">
              <div class="flex items-center">
                Valor Estimado (€)
                <span v-if="ordenActual.campo === 'valor_estimado_min'" class="ml-1">
                  <svg v-if="ordenActual.direccion === 'asc'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Acciones
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="valoracion in valoraciones" :key="valoracion.id" class="hover:bg-gray-50 transition-colors duration-150">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
              {{ formatDate(valoracion.fecha_creacion) }}
            </td>
            <td class="px-6 py-4 text-sm text-gray-800 font-medium">
              <div class="flex items-center">
                <div class="h-8 w-1 bg-impacto-blue rounded-full mr-3"></div>
                <div>
                  {{ valoracion.direccion }}
                  <div v-if="valoracion.referencia_catastral" class="text-xs text-gray-500 mt-1">
                    Ref: {{ valoracion.referencia_catastral }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
              <div class="flex items-center">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ valoracion.tipo_principal }}
                </span>
                <span v-if="valoracion.subtipo" class="ml-2 text-xs text-gray-400">{{ valoracion.subtipo }}</span>
              </div>
              <div class="flex items-center mt-1 text-xs text-gray-500">
                <span v-if="valoracion.habitaciones">{{ valoracion.habitaciones }} hab.</span>
                <span v-if="valoracion.habitaciones && valoracion.banos" class="mx-1">·</span>
                <span v-if="valoracion.banos">{{ valoracion.banos }} baños</span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-center">
              <span class="font-medium">{{ valoracion.superficie }}</span>
              <span v-if="valoracion.superficie_parcela" class="block text-xs text-gray-500">
                Parcela: {{ valoracion.superficie_parcela }} m²
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <div class="font-semibold text-impacto-blue">
                {{ formatCurrency(valoracion.valor_estimado_min) }} - {{ formatCurrency(valoracion.valor_estimado_max) }}
              </div>
              <div v-if="valoracion.estado" class="text-xs text-gray-500 mt-1">
                Estado: {{ valoracion.estado }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end space-x-2">
                <button @click="$emit('ver', valoracion)" class="text-impacto-blue hover:text-impacto-orange transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </button>
                <button @click="descargarInformePDF(valoracion)" class="text-green-600 hover:text-green-800 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
      <div class="flex-1 flex justify-between sm:hidden">
        <button 
          @click="$emit('pagina', paginaActual - 1)" 
          :disabled="paginaActual === 1"
          :class="[
            paginaActual === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50',
            'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md'
          ]"
        >
          Anterior
        </button>
        <button 
          @click="$emit('pagina', paginaActual + 1)" 
          :disabled="paginaActual === totalPaginas"
          :class="[
            paginaActual === totalPaginas ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50',
            'ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md'
          ]"
        >
          Siguiente
        </button>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            Mostrando <span class="font-medium">{{ (paginaActual - 1) * porPagina + 1 }}</span> a <span class="font-medium">{{ Math.min(paginaActual * porPagina, totalItems) }}</span> de <span class="font-medium">{{ totalItems }}</span> resultados
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <button 
              @click="$emit('pagina', paginaActual - 1)" 
              :disabled="paginaActual === 1"
              :class="[
                paginaActual === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-500 hover:bg-gray-50',
                'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 text-sm font-medium'
              ]"
            >
              <span class="sr-only">Anterior</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
            
            <template v-for="pagina in paginas" :key="pagina">
              <button 
                v-if="pagina !== '...'"
                @click="$emit('pagina', pagina)" 
                :class="[
                  pagina === paginaActual ? 'z-10 bg-impacto-blue text-white' : 'bg-white text-gray-500 hover:bg-gray-50',
                  'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium'
                ]"
              >
                {{ pagina }}
              </button>
              <span 
                v-else
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
              >
                ...
              </span>
            </template>
            
            <button 
              @click="$emit('pagina', paginaActual + 1)" 
              :disabled="paginaActual === totalPaginas"
              :class="[
                paginaActual === totalPaginas ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-500 hover:bg-gray-50',
                'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 text-sm font-medium'
              ]"
            >
              <span class="sr-only">Siguiente</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref } from 'vue';

interface Valoracion {
  id: number;
  uuid: string;
  cliente_valorador_id: number;
  client_identifier?: string;
  lead_id?: number | null;
  referencia_catastral?: string | null;
  direccion: string;
  latitud?: number | null;
  longitud?: number | null;
  tipo_principal: string;
  subtipo?: string | null | undefined;
  superficie: number;
  superficie_parcela?: number | null | undefined;
  habitaciones?: number | null | undefined;
  banos?: number | null | undefined;
  estado?: string | null | undefined;
  planta?: string | null | undefined;
  extras?: string | null | undefined;
  valor_estimado_min: number;
  valor_estimado_max: number;
  datos_adicionales?: string | null | undefined;
  fecha_creacion: string;
  fecha_modificacion?: string | null | undefined;
}

interface Orden {
  campo: string;
  direccion: 'asc' | 'desc';
}

const props = defineProps<{
  valoraciones: Valoracion[];
  paginaActual: number;
  porPagina: number;
  totalItems: number;
}>();

const emit = defineEmits(['ver', 'exportar', 'ordenar', 'pagina']);

const ordenActual = ref<Orden>({
  campo: 'fecha_creacion',
  direccion: 'desc'
});

const totalPaginas = computed(() => {
  return Math.ceil(props.totalItems / props.porPagina);
});

// Generar array de páginas para la paginación
const paginas = computed(() => {
  const result = [];
  const maxVisiblePages = 5;
  
  if (totalPaginas.value <= maxVisiblePages) {
    // Si hay menos páginas que el máximo visible, mostrar todas
    for (let i = 1; i <= totalPaginas.value; i++) {
      result.push(i);
    }
  } else {
    // Siempre mostrar la primera página
    result.push(1);
    
    // Calcular el rango de páginas a mostrar alrededor de la página actual
    let start = Math.max(2, props.paginaActual - 1);
    let end = Math.min(totalPaginas.value - 1, props.paginaActual + 1);
    
    // Ajustar para mostrar siempre 3 páginas en el medio
    if (start === 2) end = Math.min(4, totalPaginas.value - 1);
    if (end === totalPaginas.value - 1) start = Math.max(2, totalPaginas.value - 3);
    
    // Añadir elipsis si es necesario
    if (start > 2) result.push('...');
    
    // Añadir páginas del medio
    for (let i = start; i <= end; i++) {
      result.push(i);
    }
    
    // Añadir elipsis si es necesario
    if (end < totalPaginas.value - 1) result.push('...');
    
    // Siempre mostrar la última página
    result.push(totalPaginas.value);
  }
  
  return result;
});

// Cambiar el orden de la tabla
const cambiarOrden = (campo: string) => {
  let nuevaDireccion: 'asc' | 'desc' = 'asc';
  
  if (ordenActual.value.campo === campo) {
    // Si ya estamos ordenando por este campo, invertir la dirección
    nuevaDireccion = ordenActual.value.direccion === 'asc' ? 'desc' : 'asc';
  }
  
  ordenActual.value = {
    campo,
    direccion: nuevaDireccion
  };
  
  emit('ordenar', ordenActual.value);
};

// Funciones de utilidad para formateo
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('es-ES', {
    year: 'numeric', month: '2-digit', day: '2-digit'
  });
};

const formatCurrency = (value: number | null | undefined) => {
  if (value == null) return 'N/A';
  return new Intl.NumberFormat('es-ES', { maximumFractionDigits: 0 }).format(value) + '€';
};

const descargarInformePDF = (valoracion: Valoracion) => {
  if (!valoracion.uuid) {
    console.error('No se puede descargar el informe porque la valoración no tiene UUID.');
    // Opcional: usar un toast para notificar al usuario.
    return;
  }
  const url = `https://storage.googleapis.com/inmoautomation-reports/reports/inmovalor/informe_valoracion_v7_${valoracion.uuid}.pdf`;
  window.open(url, '_blank');
};
</script>

<style scoped>
/* Estilos específicos si son necesarios */
</style>
