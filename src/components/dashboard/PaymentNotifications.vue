<template>
  <div v-if="notifications.length > 0" class="space-y-3 mb-6">
    <div 
      v-for="notification in notifications" 
      :key="notification.id"
      class="rounded-lg border-l-4 p-4 shadow-sm transition-all duration-300 hover:shadow-md"
      :class="getNotificationClasses(notification.type)"
    >
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg 
            class="h-5 w-5 mt-0.5" 
            :class="getIconColor(notification.type)"
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              v-if="notification.type === 'critical'" 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
            />
            <path 
              v-else-if="notification.type === 'warning'" 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
            />
            <path 
              v-else 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
            />
          </svg>
        </div>
        
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium" :class="getTitleColor(notification.type)">
            {{ notification.title }}
          </h3>
          <p class="mt-1 text-sm" :class="getMessageColor(notification.type)">
            {{ notification.message }}
          </p>
          
          <!-- Detalles adicionales si existen -->
          <div v-if="notification.details" class="mt-2 text-xs" :class="getDetailsColor(notification.type)">
            {{ notification.details }}
          </div>
          
          <!-- Botones de acción -->
          <div v-if="notification.actions && notification.actions.length > 0" class="mt-3 flex flex-wrap gap-2">
            <button
              v-for="action in notification.actions"
              :key="action.id"
              @click="handleAction(action)"
              class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md transition-colors"
              :class="getActionButtonClasses(notification.type, action.primary)"
            >
              {{ action.label }}
            </button>
          </div>
        </div>
        
        <!-- Botón de cerrar -->
        <div class="ml-4 flex-shrink-0">
          <button
            @click="dismissNotification(notification.id)"
            class="inline-flex rounded-md p-1.5 transition-colors"
            :class="getCloseButtonClasses(notification.type)"
          >
            <span class="sr-only">Cerrar</span>
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { Subscription } from '@/api/subscriptionService';

interface NotificationAction {
  id: string;
  label: string;
  action: string;
  primary?: boolean;
}

interface PaymentNotification {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  details?: string;
  actions?: NotificationAction[];
  dismissible?: boolean;
  autoHide?: boolean;
  hideAfter?: number; // milliseconds
}

interface Props {
  subscription?: Subscription | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  action: [actionType: string, data?: any];
}>();

const notifications = ref<PaymentNotification[]>([]);
const dismissedNotifications = ref<Set<string>>(new Set());

// Generar notificaciones basadas en el estado de la suscripción
const generateNotifications = () => {
  notifications.value = [];
  
  if (!props.subscription) return;
  
  const sub = props.subscription;
  const detailedStatus = sub.detailedStatus;
  
  // Notificación para autenticación 3DS requerida
  if (detailedStatus?.detailed_status === 'payment_authentication_required') {
    notifications.value.push({
      id: 'auth-required',
      type: 'critical',
      title: 'Autenticación requerida',
      message: 'Tu banco requiere autenticación adicional (3D Secure) para procesar el pago.',
      details: 'Haz clic en "Completar autenticación" para actualizar tu método de pago y completar el proceso.',
      actions: [
        {
          id: 'complete-auth',
          label: 'Completar autenticación',
          action: 'update_payment_method',
          primary: true
        }
      ],
      dismissible: false
    });
  }
  
  // Notificación para fallos de pago recuperables
  else if (detailedStatus?.detailed_status === 'payment_failed_recoverable') {
    notifications.value.push({
      id: 'payment-failed',
      type: 'warning',
      title: 'Problema con el pago',
      message: 'El último intento de pago falló, pero tu suscripción puede ser recuperada.',
      details: sub.hasPaymentMethod 
        ? 'Puedes reintentar el pago o actualizar tu método de pago si es necesario.'
        : 'Necesitas agregar un método de pago válido.',
      actions: [
        {
          id: 'retry-payment',
          label: sub.hasPaymentMethod ? 'Reintentar pago' : 'Agregar método de pago',
          action: 'update_payment_method',
          primary: true
        }
      ],
      dismissible: true
    });
  }
  
  // Notificación para método de pago requerido
  else if (detailedStatus?.detailed_status === 'payment_method_required') {
    notifications.value.push({
      id: 'payment-method-required',
      type: 'critical',
      title: 'Método de pago requerido',
      message: 'Tu suscripción está vencida porque no tienes un método de pago configurado.',
      details: 'Agrega un método de pago para reactivar tu suscripción inmediatamente.',
      actions: [
        {
          id: 'add-payment-method',
          label: 'Agregar método de pago',
          action: 'update_payment_method',
          primary: true
        }
      ],
      dismissible: false
    });
  }
  
  // Notificación para múltiples fallos de pago
  else if (detailedStatus?.detailed_status === 'payment_failed_multiple_attempts') {
    notifications.value.push({
      id: 'multiple-failures',
      type: 'critical',
      title: 'Suscripción suspendida',
      message: 'Tu suscripción está suspendida debido a múltiples fallos de pago.',
      details: 'Actualiza tu método de pago para reactivar tu suscripción.',
      actions: [
        {
          id: 'update-payment-method',
          label: 'Actualizar método de pago',
          action: 'update_payment_method',
          primary: true
        },
        {
          id: 'contact-support',
          label: 'Contactar soporte',
          action: 'contact_support'
        }
      ],
      dismissible: false
    });
  }
  
  // Notificación para trial terminando pronto sin método de pago
  else if (detailedStatus?.detailed_status === 'trial_ending_soon' && !sub.hasPaymentMethod) {
    const daysRemaining = sub.trialPeriod?.daysRemaining || 0;
    notifications.value.push({
      id: 'trial-ending',
      type: 'warning',
      title: 'Período de prueba terminando',
      message: `Tu período de prueba termina en ${daysRemaining} día${daysRemaining !== 1 ? 's' : ''}.`,
      details: 'Agrega un método de pago para continuar usando el servicio sin interrupciones.',
      actions: [
        {
          id: 'add-payment-trial',
          label: 'Agregar método de pago',
          action: 'update_payment_method',
          primary: true
        }
      ],
      dismissible: true
    });
  }
  
  // Filtrar notificaciones ya descartadas
  notifications.value = notifications.value.filter(n => !dismissedNotifications.value.has(n.id));
};

// Funciones de estilo
const getNotificationClasses = (type: string): string => {
  switch (type) {
    case 'critical':
      return 'bg-red-50 border-red-400';
    case 'warning':
      return 'bg-amber-50 border-amber-400';
    case 'info':
      return 'bg-blue-50 border-blue-400';
    default:
      return 'bg-gray-50 border-gray-400';
  }
};

const getIconColor = (type: string): string => {
  switch (type) {
    case 'critical':
      return 'text-red-400';
    case 'warning':
      return 'text-amber-400';
    case 'info':
      return 'text-blue-400';
    default:
      return 'text-gray-400';
  }
};

const getTitleColor = (type: string): string => {
  switch (type) {
    case 'critical':
      return 'text-red-800';
    case 'warning':
      return 'text-amber-800';
    case 'info':
      return 'text-blue-800';
    default:
      return 'text-gray-800';
  }
};

const getMessageColor = (type: string): string => {
  switch (type) {
    case 'critical':
      return 'text-red-700';
    case 'warning':
      return 'text-amber-700';
    case 'info':
      return 'text-blue-700';
    default:
      return 'text-gray-700';
  }
};

const getDetailsColor = (type: string): string => {
  switch (type) {
    case 'critical':
      return 'text-red-600';
    case 'warning':
      return 'text-amber-600';
    case 'info':
      return 'text-blue-600';
    default:
      return 'text-gray-600';
  }
};

const getActionButtonClasses = (type: string, primary?: boolean): string => {
  if (primary) {
    switch (type) {
      case 'critical':
        return 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500';
      case 'warning':
        return 'bg-amber-600 text-white hover:bg-amber-700 focus:ring-amber-500';
      case 'info':
        return 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';
      default:
        return 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500';
    }
  } else {
    switch (type) {
      case 'critical':
        return 'bg-red-100 text-red-800 hover:bg-red-200 focus:ring-red-500';
      case 'warning':
        return 'bg-amber-100 text-amber-800 hover:bg-amber-200 focus:ring-amber-500';
      case 'info':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200 focus:ring-blue-500';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-500';
    }
  }
};

const getCloseButtonClasses = (type: string): string => {
  switch (type) {
    case 'critical':
      return 'text-red-400 hover:text-red-600 hover:bg-red-100 focus:ring-red-500';
    case 'warning':
      return 'text-amber-400 hover:text-amber-600 hover:bg-amber-100 focus:ring-amber-500';
    case 'info':
      return 'text-blue-400 hover:text-blue-600 hover:bg-blue-100 focus:ring-blue-500';
    default:
      return 'text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:ring-gray-500';
  }
};

// Manejar acciones
const handleAction = (action: NotificationAction) => {
  emit('action', action.action, action);
};

// Descartar notificación
const dismissNotification = (id: string) => {
  dismissedNotifications.value.add(id);
  notifications.value = notifications.value.filter(n => n.id !== id);
};

// Regenerar notificaciones cuando cambie la suscripción
const updateNotifications = () => {
  generateNotifications();
};

// Exponer función para uso externo
defineExpose({
  updateNotifications
});

onMounted(() => {
  generateNotifications();
});

// Regenerar cuando cambie la suscripción
const subscription = computed(() => props.subscription);
const detailedStatus = computed(() => props.subscription?.detailedStatus);

// Watch para cambios en el estado detallado
import { watch } from 'vue';
watch([subscription, detailedStatus], () => {
  generateNotifications();
}, { deep: true });
</script>
