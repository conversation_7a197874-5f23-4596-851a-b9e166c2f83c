import { ref } from 'vue';

interface Toast {
  id: number;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration: number;
}

// Estado global para los toasts
const toasts = ref<Toast[]>([]);
let nextId = 0;

export function useToast() {
  // Mostrar un nuevo toast
  const showToast = (
    message: string, 
    type: 'success' | 'error' | 'info' | 'warning' = 'info', 
    duration: number = 5000
  ) => {
    const id = nextId++;
    
    // Añadir el nuevo toast
    toasts.value.push({
      id,
      message,
      type,
      duration
    });
    
    // Eliminar el toast después de la duración especificada
    setTimeout(() => {
      removeToast(id);
    }, duration);
    
    return id;
  };
  
  // Eliminar un toast específico
  const removeToast = (id: number) => {
    const index = toasts.value.findIndex(toast => toast.id === id);
    if (index !== -1) {
      toasts.value.splice(index, 1);
    }
  };
  
  return {
    toasts,
    showToast,
    removeToast
  };
}
