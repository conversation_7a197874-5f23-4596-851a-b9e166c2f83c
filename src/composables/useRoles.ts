import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth';
// @ts-ignore - Asegúrate de instalar axios con: npm install axios
import axios from 'axios';

export default function useRoles() {
  const auth = useAuthStore();
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const roleCheckResult = ref<any>(null);

  // Comprobar si el usuario tiene un rol específico
  const checkRole = async (role: string = 'admin') => {
    if (!auth.isAuthenticated || !auth.token) {
      error.value = 'Usuario no autenticado';
      return false;
    }

    isLoading.value = true;
    error.value = null;

    try {
      // Enviar el token tanto en el encabezado como en la URL para mayor compatibilidad
      const response = await axios.get(`/api/check-roles.php?action=check&role=${role}&token=${auth.token}`, {
        headers: {
          Authorization: `Bearer ${auth.token}`
        }
      });

      roleCheckResult.value = response.data;
      
      if (response.data.status === 'success') {
        // Si los roles en la base de datos son diferentes a los del localStorage,
        // actualizar el usuario en el store
        if (auth.user && response.data.allRoles) {
          // Solo actualizar si hay diferencias
          const currentRoles = Array.isArray(auth.user.roles) 
            ? auth.user.roles 
            : (typeof auth.user.roles === 'string' 
              ? [auth.user.roles] 
              : []);
          
          const dbRoles = response.data.allRoles;
          
          // Verificar si hay diferencias
          const needsUpdate = JSON.stringify(currentRoles.sort()) !== JSON.stringify(dbRoles.sort());
          
          if (needsUpdate) {
            // Actualizar roles en el usuario
            const updatedUser = { ...auth.user, roles: dbRoles };
            
            // Actualizar en el store y localStorage
            auth.user = updatedUser;
            localStorage.setItem('user', JSON.stringify(updatedUser));
          }
        }
        
        return response.data.hasRole;
      }
      
      return false;
    } catch (err: any) {
      console.error('Error al verificar rol:', err);
      error.value = err.message || 'Error al verificar rol';
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  // Añadir un rol al usuario
  const addRole = async (role: string = 'admin') => {
    if (!auth.isAuthenticated || !auth.token) {
      error.value = 'Usuario no autenticado';
      return false;
    }

    isLoading.value = true;
    error.value = null;

    try {
      // Enviar el token tanto en el encabezado como en la URL para mayor compatibilidad
      const response = await axios.get(`/api/check-roles.php?action=add&role=${role}&token=${auth.token}`, {
        headers: {
          Authorization: `Bearer ${auth.token}`
        }
      });
      
      if (response.data.status === 'success') {
        // Actualizar el usuario en el store con los nuevos roles
        if (auth.user && response.data.allRoles) {
          const updatedUser = { ...auth.user, roles: response.data.allRoles };
          auth.user = updatedUser;
          localStorage.setItem('user', JSON.stringify(updatedUser));
        }
        
        return true;
      }
      
      return false;
    } catch (err: any) {
      console.error('Error al añadir rol:', err);
      error.value = err.message || 'Error al añadir rol';
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  // Quitar un rol al usuario
  const removeRole = async (role: string = 'admin') => {
    if (!auth.isAuthenticated || !auth.token) {
      error.value = 'Usuario no autenticado';
      return false;
    }

    isLoading.value = true;
    error.value = null;

    try {
      // Enviar el token tanto en el encabezado como en la URL para mayor compatibilidad
      const response = await axios.get(`/api/check-roles.php?action=remove&role=${role}&token=${auth.token}`, {
        headers: {
          Authorization: `Bearer ${auth.token}`
        }
      });
      
      if (response.data.status === 'success') {
        // Actualizar el usuario en el store sin el rol eliminado
        if (auth.user && response.data.allRoles) {
          const updatedUser = { ...auth.user, roles: response.data.allRoles };
          auth.user = updatedUser;
          localStorage.setItem('user', JSON.stringify(updatedUser));
        }
        
        return true;
      }
      
      return false;
    } catch (err: any) {
      console.error('Error al quitar rol:', err);
      error.value = err.message || 'Error al quitar rol';
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  // Comprobar si el usuario tiene rol de admin
  const checkAdminRole = async () => {
    return await checkRole('admin');
  };

  // Añadir rol de admin al usuario
  const addAdminRole = async () => {
    return await addRole('admin');
  };

  // Quitar rol de admin al usuario
  const removeAdminRole = async () => {
    return await removeRole('admin');
  };

  return {
    isLoading,
    error,
    roleCheckResult,
    checkRole,
    addRole,
    removeRole,
    checkAdminRole,
    addAdminRole,
    removeAdminRole
  };
}
