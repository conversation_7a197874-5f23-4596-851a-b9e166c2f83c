import { computed, ref } from 'vue';
import { useAuthStore } from '@/stores/auth';
import useRoles from './useRoles';

/**
 * Composable para verificar si el usuario actual tiene rol de administrador
 * @returns Objeto con propiedades y métodos para verificar el rol de administrador
 */
export function useAdmin() {
  const auth = useAuthStore();
  const rolesService = useRoles();
  const isCheckingRole = ref(false);
  
  /**
   * Comprueba si el usuario actual tiene rol de administrador
   * basado en la información almacenada en localStorage
   */
  const isAdmin = computed(() => {
    // Si no hay usuario o no tiene roles, no es admin
    if (!auth.user || !auth.user.roles) {
      return false;
    }
    
    const userRoles = auth.user.roles;
    
    // Comprobar el formato de roles y buscar 'admin'
    if (Array.isArray(userRoles)) {
      // Si es un array, comprobar si contiene 'admin'
      const hasAdmin = userRoles.includes('admin');
      return hasAdmin;
    } else if (typeof userRoles === 'string') {
      // Si es un string, comprobar si es 'admin' o si contiene 'admin' como JSON
      if (userRoles === 'admin') {
        return true;
      }
      
      // Intentar parsear como JSON
      try {
        const parsedRoles = JSON.parse(userRoles);
        if (Array.isArray(parsedRoles)) {
          const hasAdmin = parsedRoles.includes('admin');
          return hasAdmin;
        }
      } catch {
        // Si no es JSON válido, comprobar si contiene 'admin'
        const rolesStr = userRoles as string;
        const hasAdmin = rolesStr.includes('admin');
        return hasAdmin;
      }
    }
    
    return false;
  });

  /**
   * Verifica el rol de administrador contra la base de datos
   * y actualiza el localStorage si es necesario
   */
  const verifyAdminRoleWithDB = async (): Promise<boolean> => {
    if (!auth.isAuthenticated) {
      return false;
    }

    isCheckingRole.value = true;
    try {
      // Usar el nuevo endpoint para verificar el rol
      const hasAdminRole = await rolesService.checkAdminRole();
      return hasAdminRole;
    } catch (error) {
      console.error('useAdmin: Error al verificar rol admin con DB:', error);
      return false;
    } finally {
      isCheckingRole.value = false;
    }
  };

  /**
   * Función para depurar el estado de administrador
   * Devuelve información detallada sobre el usuario y sus roles
   */
  const debugAdminStatus = async () => {
    const user = auth.user;
    const isAuthenticated = auth.isAuthenticated;
    
    // Verificar con la base de datos
    const dbHasAdminRole = await verifyAdminRoleWithDB();
    
    // Información básica
    const info: Record<string, any> = {
      isAuthenticated,
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: isAdmin.value,
      dbHasAdminRole
    };
    
    // Información detallada sobre roles
    if (user && user.roles) {
      const userRoles = user.roles;
      info.rolesType = typeof userRoles;
      
      if (Array.isArray(userRoles)) {
        info.rolesIsArray = true;
        info.rolesContent = userRoles;
        info.containsAdmin = userRoles.includes('admin');
      } else if (typeof userRoles === 'string') {
        info.rolesIsString = true;
        info.rolesContent = userRoles;
        info.containsAdminString = (userRoles as string).includes('admin');
        
        try {
          const parsedRoles = JSON.parse(userRoles as string);
          info.isParseable = true;
          info.parsedType = typeof parsedRoles;
          info.parsedIsArray = Array.isArray(parsedRoles);
          
          if (Array.isArray(parsedRoles)) {
            info.parsedContent = parsedRoles;
            info.parsedContainsAdmin = parsedRoles.includes('admin');
          }
        } catch {
          // Ignoramos el error de parseo y registramos que no es parseable
          info.isParseable = false;
        }
      } else {
        info.rolesIsOther = true;
        info.rolesContent = JSON.stringify(userRoles);
      }
    } else {
      info.hasRoles = false;
    }
    
    return info;
  }

  return {
    isAdmin,
    isCheckingRole,
    debugAdminStatus,
    verifyAdminRoleWithDB
  };
}
