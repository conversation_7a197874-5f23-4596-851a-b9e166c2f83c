import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth';
import { useAdmin } from '@/composables/useAdmin';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginPage.vue'),
    meta: { 
      title: 'Iniciar sesión - InmoAutomation',
      isPublic: true 
    },
    beforeEnter: (_to, _from, next) => {
      const auth = useAuthStore();
      if (auth.isAuthenticated) {
        next({ name: 'DashboardHome' });
      } else {
        next();
      }
    }
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: () => import('@/views/ResetPasswordPage.vue'),
    meta: {
      title: 'Restablecer contraseña',
      isPublic: true
    }
  },
  {
    path: '/',
    component: () => import('@/views/DashboardPage.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'DashboardHome',
        component: () => import('@/views/dashboard/DashboardHome.vue'),
        meta: { title: 'Dashboard - Resumen' }
      },
      {
        path: 'configuracion',
        name: 'DashboardConfiguracion',
        component: () => import('@/views/dashboard/ConfiguracionValorador.vue'),
        meta: { title: 'Configuración del valorador' }
      },
      {
        path: 'mi-agencia',
        name: 'DashboardMiAgencia',
        component: () => import('@/views/dashboard/MyAgencyView.vue'),
        meta: { title: 'Mi agencia', requiresAuth: true }
      },
      {
        path: 'suscripcion',
        name: 'DashboardSuscripcion',
        component: () => import('@/views/dashboard/Suscripcion.vue'),
        meta: { title: 'Facturación' }
      },
      {
        path: 'valoraciones',
        name: 'Valoraciones',
        component: () => import('@/views/dashboard/Valoraciones.vue'),
        meta: { title: 'Valoraciones' }
      },
      {
        path: 'leads',
        name: 'Leads',
        component: () => import('@/views/dashboard/Leads.vue'), 
        meta: { title: 'Leads' }
      },
      {
        path: 'leads/:id', 
        name: 'DashboardLeadDetail',
        component: () => import('@/views/dashboard/Leads.vue'),
        props: true, 
        meta: { title: 'Detalle del lead', requiresAuth: true }
      },
      {
        path: 'emails',
        name: 'Emails',
        component: () => import('@/views/dashboard/Emails.vue'), 
        meta: { title: 'Emails' }
      },
      {
        path: 'profile',
        name: 'DashboardProfileManagement',
        component: () => import('@/views/dashboard/ProfileManagement.vue'),
        meta: { title: 'Gestionar perfil', requiresAuth: true }
      },
      {
        path: 'admin-debug',
        name: 'AdminDebug',
        component: () => import('@/views/dashboard/AdminDebug.vue'),
        meta: { 
          title: 'Depuración de Roles de Administrador', 
          requiresAuth: true
        }
      },
      {
        path: 'admin',
        name: 'AdminDashboard',
        component: () => import('@/views/dashboard/admin/AdminDashboard.vue'),
        meta: { 
          title: 'Panel de Administración', 
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'admin/usuarios',
        name: 'AdminUsuarios',
        component: () => import('@/views/dashboard/admin/AdminUsuarios.vue'),
        meta: { 
          title: 'Administración de Usuarios', 
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      // ... (el resto de rutas de admin que estaban en el original)
      {
        path: 'admin/valoradores',
        name: 'AdminValoradores',
        component: () => import('@/views/dashboard/admin/AdminValoradores.vue'),
        meta: { 
          title: 'Administración de Valoradores', 
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'admin/valoraciones',
        name: 'AdminValoraciones',
        component: () => import('@/views/dashboard/admin/AdminValoraciones.vue'),
        meta: { 
          title: 'Administración de Valoraciones', 
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'admin/leads',
        name: 'AdminLeads',
        component: () => import('@/views/dashboard/admin/AdminLeads.vue'),
        meta: { 
          title: 'Administración de Leads', 
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'admin/facturacion',
        name: 'AdminFacturacion',
        component: () => import('@/views/dashboard/admin/AdminFacturacion.vue'),
        meta: { 
          title: 'Administración de Facturación', 
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'admin/sequences',
        name: 'AdminSequences',
        component: () => import('@/views/dashboard/admin/AdminSequences.vue'),
        meta: {
          title: 'Administración de Secuencias',
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'admin/html-templates',
        name: 'AdminHtmlTemplates',
        component: () => import('@/views/dashboard/admin/AdminHtmlTemplates.vue'),
        meta: {
          title: 'Gestión de Plantillas HTML',
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'admin/sequence/:uuid',
        name: 'AdminSequenceSteps',
        component: () => import('@/views/dashboard/admin/AdminSequenceSteps.vue'),
        props: true,
        meta: { 
          title: 'Gestionar Pasos de Secuencia', 
          requiresAuth: true,
          requiresAdmin: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior: () => ({ top: 0 })
})

router.beforeEach((to, _from, next) => {
  if (to.meta && to.meta.title) {
    document.title = to.meta.title as string;
  }
  
  const auth = useAuthStore();
  const { isAdmin } = useAdmin();

  if (to.meta.requiresAuth && !auth.isAuthenticated) {
    next({ name: 'Login' });
    return;
  }

  if (to.meta.requiresAdmin && !isAdmin.value) {
    next({ name: 'DashboardHome' });
  } else {
    next();
  }
});

export default router; 