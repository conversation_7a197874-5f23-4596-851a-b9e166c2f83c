// src/router/landing.ts
import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import LandingView from '@/views/LandingView.vue'
import ContratacionPage from '@/views/ContratacionPage.vue';
import NotFoundView from '@/views/NotFoundView.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Landing',
    component: LandingView,
    meta: {
      title: 'InmoAutomation: Sistema de captación y nutrición con IA | Genera leads en piloto automático',
    }
  },
  {
    path: '/contratacion',
    name: 'ContratacionPage',
    component: ContratacionPage,
    props: route => ({ plan: route.query.plan })
  },
  {
    path: '/gracias',
    name: 'ThankYou',
    component: () => import('@/views/ThankYouView.vue'),
    meta: {
      title: 'Gracias por tu compra - InmoAutomation',
    }
  },
  {
    path: '/politica-de-privacidad',
    name: 'PrivacyPolicy',
    component: () => import('@/views/PrivacyPolicyView.vue'),
    meta: {
      title: 'Política de Privacidad - InmoAutomation',
    }
  },
  {
    path: '/terminos-de-servicio',
    name: 'TermsOfService',
    component: () => import('@/views/TermsOfServiceView.vue'),
    meta: {
      title: 'Términos de Servicio - InmoAutomation',
    }
  },
  {
    path: '/:catchAll(.*)*',
    name: 'NotFound',
    component: NotFoundView,
    meta: {
      title: 'Página no Encontrada - InmoAutomation',
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ el: to.hash, behavior: 'smooth' });
        }, 500);
      });
    } else {
      return { top: 0, behavior: 'smooth' }
    }
  }
})

router.beforeEach((to, _from, next) => {
  if (to.meta && to.meta.title) {
    document.title = to.meta.title as string;
  }
  
  // No es necesario comprobar el login aquí ya que se ha movido a la app.

  next();
});

export default router; 