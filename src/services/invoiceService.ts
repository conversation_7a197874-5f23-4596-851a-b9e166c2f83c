import { apiFetch } from '@/utils/apiFetch';
import type { Invoice } from '@/types/invoice';

export interface ApiInvoice {
  id: string;
  date: number;
  amount: string;
  status: string;
  pdfUrl: string;
}

/**
 * Obtiene el historial de facturas del usuario
 */
export async function getInvoices(): Promise<Invoice[]> {
  try {
    const response = await apiFetch('invoices.php');
    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }
    const apiInvoices = await response.json() as ApiInvoice[];
    return apiInvoices.map(invoice => ({
      id: invoice.id,
      number: invoice.id.replace('in_', '#'),
      date: new Date(invoice.date * 1000).toISOString(),
      amount: invoice.amount,
      status: invoice.status,
      pdfUrl: invoice.pdfUrl
    }));
  } catch (error) {
    console.error('Error al obtener las facturas:', error);
    throw error;
  }
}

/**
 * Obtiene la URL para descargar una factura en formato PDF
 */
export async function getInvoicePdf(invoiceId: string): Promise<string> {
  try {
    const response = await apiFetch(`invoice-pdf.php?invoice_id=${invoiceId}`);
    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }
    const data = await response.json() as { url: string };
    return data.url;
  } catch (error) {
    console.error('Error al obtener el PDF de la factura:', error);
    throw error;
  }
}
