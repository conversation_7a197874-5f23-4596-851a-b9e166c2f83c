import { apiFetch } from '@/utils/apiFetch';
import type { UserPlanFeatures } from '@/stores/auth';

export interface Invoice {
  id: string;
  amount_paid: number;
  currency: string;
  created: number;
  status: 'paid' | 'unpaid' | 'void' | 'draft' | 'open';
  invoice_pdf: string;
  number: string;
  hosted_invoice_url: string;
  lines: {
    data: Array<{
      id: string;
      amount: number;
      description: string;
      period: {
        start: number;
        end: number;
      };
    }>;
  };
}

export interface Plan {
  id: string;
  name: string;
  amount: number;
  currency: string;
  interval: 'day' | 'week' | 'month' | 'year';
  product?: string;
}

export interface PaymentMethod {
  card: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
}

export interface Subscription {
  id: string;
  status: 'active' | 'past_due' | 'unpaid' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'trialing';
  current_period_start: number;
  current_period_end: number;
  cancel_at_period_end: boolean;
  canceled_at: number | null;
  plan: Plan;
  payment_method: PaymentMethod;
  features: string[];
  plan_features: UserPlanFeatures | null;
  trial_end?: number | null;
  trial_start?: number | null;
}

export interface BillingPortalSession {
  url: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

/**
 * Obtiene la información de la suscripción actual del usuario
 * @returns {Promise<Subscription>} Información de la suscripción
 * @throws {Error} Si no se puede cargar la información de la suscripción
 */
export async function getCurrentSubscription(): Promise<Subscription> {
  try {
    const response = await apiFetch<ApiResponse<{ subscription: Subscription }>>('/api/subscription.php');
    
    if (!response.success || !response.data?.subscription) {
      if (response.error && response.error.includes("plan_features")) {
        console.warn('Advertencia al obtener la suscripción: No se encontraron plan_features. Esto podría ser esperado para usuarios sin plan activo o en ciertas condiciones.');
      }
      if (!response.data?.subscription) {
        throw new Error(response.error || 'No se pudo cargar la información de la suscripción');
      }
    }
    
    return response.data.subscription;
  } catch (error) {
    console.error('Error al obtener la suscripción:', error);
    throw new Error(
      error instanceof Error 
        ? error.message 
        : 'Ocurrió un error al cargar la información de la suscripción'
    );
  }
}

/**
 * Crea una sesión del portal de facturación de Stripe
 * @returns {Promise<BillingPortalSession>} URL del portal de facturación
 * @throws {Error} Si no se puede crear la sesión del portal
 */
export async function createBillingPortalSession(): Promise<BillingPortalSession> {
  try {
    const response = await apiFetch<ApiResponse<{ url: string }>>(
      '/api/create-billing-portal-session.php', 
      { method: 'POST' }
    );
    
    if (!response.success || !response.data?.url) {
      throw new Error(response.error || 'No se pudo crear la sesión del portal de facturación');
    }
    
    return { url: response.data.url };
  } catch (error) {
    console.error('Error al crear la sesión del portal de facturación:', error);
    throw new Error(
      error instanceof Error 
        ? error.message 
        : 'Ocurrió un error al crear la sesión del portal de facturación'
    );
  }
}

/**
 * Cancela la suscripción actual del usuario
 * @param {boolean} [cancelAtPeriodEnd=true] - Si es true, la suscripción se cancelará al final del período actual
 * @returns {Promise<{ success: boolean; message: string; subscription_status: string; cancel_at_period_end: boolean }>} Resultado de la operación
 * @throws {Error} Si no se puede procesar la solicitud de cancelación
 */
export async function cancelSubscription(
  cancelAtPeriodEnd: boolean = true
): Promise<{ 
  success: boolean; 
  message: string; 
  subscription_status: string; 
  cancel_at_period_end: boolean;
}> {
  try {
    const response = await apiFetch<ApiResponse<{
      subscription_status: string;
      cancel_at_period_end: boolean;
    }>>('/api/cancel-subscription.php', {
      method: 'POST',
      body: JSON.stringify({ cancel_at_period_end: cancelAtPeriodEnd }),
    });
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'No se pudo procesar la solicitud de cancelación');
    }
    
    return {
      success: true,
      message: 'Solicitud de cancelación procesada correctamente',
      subscription_status: response.data.subscription_status,
      cancel_at_period_end: response.data.cancel_at_period_end,
    };
  } catch (error) {
    console.error('Error al cancelar la suscripción:', error);
    throw new Error(
      error instanceof Error 
        ? error.message 
        : 'Ocurrió un error al procesar la cancelación de la suscripción'
    );
  }
}

/**
 * Obtiene el historial de facturas del usuario
 * @returns {Promise<Invoice[]>} Lista de facturas
 * @throws {Error} Si no se pueden cargar las facturas
 */
export async function getInvoices(): Promise<Invoice[]> {
  try {
    const response = await apiFetch<ApiResponse<{ invoices: Invoice[] }>>('invoices.php');
    
    if (!response.success || !response.data?.invoices) {
      throw new Error(response.error || 'No se pudieron cargar las facturas');
    }
    
    return response.data.invoices;
  } catch (error) {
    console.error('Error al obtener las facturas:', error);
    throw new Error(
      error instanceof Error 
        ? error.message 
        : 'Ocurrió un error al cargar las facturas'
    );
  }
}
