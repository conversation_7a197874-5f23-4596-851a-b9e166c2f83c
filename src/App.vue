<template>
  <!-- El loader global se ha movido implícitamente a main.ts (la app no se monta hasta que auth esté listo) -->
  <!-- Por lo tanto, el v-if/v-else aquí ya no es necesario -->
  <div id="app-layout" class="flex flex-col min-h-screen">
    <!-- Componente Toast para notificaciones -->
    <Toast />
    <!-- 
      AppHeader y AppFooter solo se muestran si la compilación NO es para el dashboard.
    -->
    <template v-if="!isDashboardApp">
      <AppHeader />
    </template>

    <!-- 
      RouterView es el componente de Vue Router que renderiza
      el componente correspondiente a la ruta actual.
      El 'flex-grow' asegura que esta sección principal se expanda para ocupar
      el espacio disponible, empujando el footer hacia abajo.
    -->
    <main :class="['flex-grow', {'bg-impacto-blue': !isDashboardApp}]">
      <RouterView v-slot="{ Component, route }">
        <Transition name="page-fade" mode="out-in">
          <component :is="Component" :key="route.path" />
        </Transition>
      </RouterView>
    </main>

    <!-- 
      AppFooter solo se muestra si la compilación NO es para el dashboard.
    -->
    <template v-if="!isDashboardApp">
      <AppFooter />
    </template>
  </div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { computed } from 'vue'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'
import Toast from '@/components/ui/Toast.vue'

// Determinar si la compilación actual es para el dashboard basándose en la variable de entorno.
// Esto es más robusto que comprobar la ruta.
const isDashboardApp = computed(() => {
  return import.meta.env.VITE_APP_TYPE === 'app';
})

// --- Lógica a nivel de aplicación (App.vue) ---
// Aquí App.vue sirve como un shell de layout que decide qué componentes mostrar
// basado en la ruta actual.
</script>

<style scoped>
/* 
  Estilos específicos para el layout principal de App.vue.
  Usamos 'scoped' para que estos estilos no se filtren a otros componentes.
*/

#app-layout {
  /* 
    El color de fondo `bg-impacto-blue` se aplica aquí como fondo general de la "aplicación"
    que envuelve las vistas. Esto podría ser útil si las vistas mismas
    no siempre cubren toda la pantalla o si quieres un color de fondo "detrás" de todo.
    Sin embargo, el `body` ya tiene `bg-white` y `text-impacto-blue` en `style.css`.
    Si la intención es que el fondo general *detrás* de las secciones blancas de la landing
    sea el azul oscuro, esta configuración está bien.
    Si cada vista/sección va a controlar su propio fondo por completo (ej. alternando blanco y azul),
    podrías quitar el `bg-impacto-blue` de aquí y dejarlo neutro.
    Dado que la paleta indica "Azul Principal (Oscuro): #051f33 (Usado para fondos principales, secciones de impacto...)"
    y "Blanco: #ffffff (Usado para fondos de secciones claras...", parece razonable que algunas vistas
    o secciones sean blancas y otras azules. Si este es un "lienzo" de fondo, está bien.
    Confirmaremos esto al diseñar el Header/Footer y las secciones.
  */
}

/* 
  Estilos para las transiciones de página.
  La clase `page-fade` se usará en el componente <Transition>.
*/
.page-fade-enter-active,
.page-fade-leave-active {
  transition: opacity 0.3s ease;
}

.page-fade-enter-from,
.page-fade-leave-to {
  opacity: 0;
}

/*
  Asegurar que el layout ocupe toda la altura de la pantalla:
  - `min-h-screen` en el div raíz.
  - `flex flex-col` en el div raíz para organizar header, main, footer verticalmente.
  - `flex-grow` en el elemento <main> para que ocupe el espacio restante.
  Esto ayuda a mantener el footer al final de la ventana, incluso en páginas con poco contenido.
*/
</style>