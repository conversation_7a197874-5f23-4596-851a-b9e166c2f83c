import { useAuthStore } from '@/stores/auth';

// URL base de la API
// Usar la variable de entorno VITE_API_BASE_URL definida en .env
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Interfaz para el error de la API
interface ApiError extends Error {
  status?: number;
  data?: any;
}

export async function apiFetch<T = any>(url: string, options: RequestInit = {}): Promise<T> {
  const auth = useAuthStore();
  
  // Verificar si hay un token disponible
  const token = auth.token || '';
  
  // Configuración de cabeceras
  const headers = new Headers(); // Inicializar Headers vacío

  // No establecer Content-Type por defecto si el body es FormData
  // El navegador lo manejará automáticamente, incluyendo el boundary.
  if (!(options.body instanceof FormData)) {
    headers.set('Content-Type', 'application/json');
  }
  
  headers.set('Accept', 'application/json, text/plain, text/html, */*');
  
  // Añadir las cabeceras adicionales si existen
  if (options.headers) {
    Object.entries(options.headers).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(v => headers.append(key, v));
      } else if (value) {
        headers.set(key, String(value));
      }
    });
  }
  
  // Añadir el token de autenticación si existe
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  
  // Construir la URL completa
  let apiUrl;
  const isAbsolute = url.startsWith('http://') || url.startsWith('https://');

  if (isAbsolute) {
    // URL absoluta, usar tal como está
    apiUrl = url;
  } else if (API_BASE_URL) {
    // Si VITE_API_BASE_URL está configurada
    if (API_BASE_URL.startsWith('/')) {
      // URL relativa (desarrollo con proxy)
      // Limpiar la URL de entrada
      let cleanUrl = url.startsWith('/api/') ? url.substring(5) : url;
      cleanUrl = cleanUrl.startsWith('/') ? cleanUrl.substring(1) : cleanUrl;

      // Construir URL para proxy
      apiUrl = `${API_BASE_URL}/${cleanUrl}`;
    } else {
      // URL absoluta (producción)
      let cleanUrl = url.startsWith('/api/') ? url.substring(5) : url;
      cleanUrl = cleanUrl.startsWith('/') ? cleanUrl.substring(1) : cleanUrl;

      const base = API_BASE_URL.endsWith('/') ? API_BASE_URL.slice(0, -1) : API_BASE_URL;
      apiUrl = `${base}/${cleanUrl}`;
    }
  } else {
    // Fallback: construir URL relativa para proxy
    if (url.startsWith('/api/')) {
      apiUrl = url;
    } else {
      apiUrl = `/api/${url.startsWith('/') ? url.substring(1) : url}`;
    }
  }



  try {
    const response = await fetch(apiUrl, {
      ...options,
      headers,
      credentials: 'include' // Importante para enviar cookies de autenticación
    });

    // Obtener el texto de la respuesta primero
    const responseText = await response.text();

    // Si la respuesta no es exitosa, manejar el error
    if (!response.ok) {
      let errorData: any;
      try {
        // Intentar parsear como JSON
        errorData = JSON.parse(responseText);
      } catch {
        // Si no es JSON, usar el texto
        errorData = responseText;
      }

      const error: ApiError = new Error(
        (typeof errorData === 'object' && errorData?.message) ||
        `Error ${response.status}: ${response.statusText}`
      );
      error.status = response.status;
      error.data = errorData;
      throw error;
    }

    // Intentar parsear como JSON
    if (responseText.trim()) {
      try {
        const parsed = JSON.parse(responseText) as T;
        return parsed;
      } catch {
        // Si no es JSON y no esperamos un objeto, lanzar un error
        const error: ApiError = new Error('La respuesta no es un JSON válido');
        error.status = response.status;
        error.data = responseText;
        throw error;
      }
    } else {
      // Respuesta vacía, devolver un objeto vacío o null según el tipo esperado
      return (typeof {} as T === 'object') ? {} as T : null as T;
    }
  } catch (error) {
    console.error(`[apiFetch] Error en ${apiUrl}:`, error);
    throw error;
  }
}

export async function fetchUsers() {
  return apiFetch('getUserData.php');
}

export async function updateUser(user: any) {
  return apiFetch(`updateUser.php`, {
    method: 'POST',
    body: JSON.stringify(user),
  });
}
