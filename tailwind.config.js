/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme');

export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}", // Escanea todos los archivos relevantes en src
  ],
  theme: {
    extend: {
      colors: {
        'impacto-blue': {
          DEFAULT: '#051f33', // <PERSON><PERSON><PERSON> Principal (Oscuro)
          // Podríamos añadir variantes si fueran necesarias, ej: light, dark
        },
        'impacto-orange': {
          DEFAULT: '#ed8725', // Naran<PERSON> de Acento
          // Podríamos añadir variantes si fueran necesarias, ej: light, dark
        },
        'impacto-gray': {
          light: '#f0f4f8', // Un ejemplo de gris claro, podríamos refinar esto
          DEFAULT: '#a0aec0', // Un gris medio para texto secundario
          dark: '#718096',   // Un gris más oscuro
        },
        // Mantener acceso a la paleta de Tailwind por defecto si es necesario
        // white: '#ffffff', // Ya está en la paleta por defecto
      },
      fontFamily: {
        sans: ['Poppins', ...defaultTheme.fontFamily.sans], // Poppins para títulos y elementos principales de UI
        body: ['Inter', ...defaultTheme.fontFamily.sans],   // Inter para cuerpo de texto
      },
      // Aquí podríamos extender otras propiedades de Tailwind como
      // spacing, borderRadius, boxShadows, keyframes para animaciones, etc.
      // Ejemplo de patrones de fondo tecnológicos (como gradientes o formas SVG)
      // backgroundImage: {
      //   'tech-gradient': 'linear-gradient(to right, #051f33, #0a2a4a)',
      // }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio'),
  ],
}