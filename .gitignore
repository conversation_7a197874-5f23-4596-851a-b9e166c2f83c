# =============================================================================
# GENERAL
# =============================================================================
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# OS-generated files
.DS_Store
*.swo
*.swp
*.swn

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# =============================================================================
# FRONTEND (VUE)
# =============================================================================
# Dependencies
/node_modules

# Build output
/dist
/dist-ssr
/.vite

# =============================================================================
# BACKEND (PHP/API)
# =============================================================================
# Composer dependencies
/api/vendor/

# Logs and debug files
/api/error_log
/api/debug_*.log

# =============================================================================
# ENVIRONMENT & SECRETS (IMPORTANT)
# =============================================================================
# Ignore all .env files and other sensitive information
.env
.env.*
.env.local

# Except for the example files which are safe to commit
!*.env.example
!api/.env.example

# Ignore GCP local config files
.gcloud/

# Ignore Firebase cache files
.firebase/*.cache

# Ignore Windsurf specific files
.cursorignoreapi/valorador/OLD_api_handler.php
