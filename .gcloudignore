# Archivos y directorios a ignorar en Cloud Build
node_modules/
.git/
.gitignore
.env.local
.env.development
.env.development.local
# Permitir archivos de producción
!.env.production
!.env.landing.production
!.env.app.production
!api/.env.production
*.log
logs/
dist/
.vscode/
.idea/
*.md
README.md
.DS_Store
Thumbs.db

# Archivos de desarrollo
# src/ - necesario para build
# public/ - necesario para build
# *.config.js - necesario para build
# *.config.ts - necesario para build
# package-lock.json - necesario para build
# tsconfig*.json - necesario para build

# Permitir archivos de configuración necesarios para build
!tsconfig*.json
!vite.config.ts
!tailwind.config.js
!postcss.config.js

# Archivos específicos del proyecto
api/debug_*.log
api/error_log
api/vendor/
api/composer.lock
