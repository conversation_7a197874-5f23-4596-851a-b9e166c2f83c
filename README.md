# Valorador Inmobiliario IA - InmoAutomation

## 1. Visión General

Plataforma SaaS completa para agencias inmobiliarias que incluye un valorador inmobiliario con IA, sistema de gestión de leads, campañas de email automatizadas y dashboard de administración. El sistema permite a las agencias generar leads ilimitados a través de valoradores personalizados y nutrir automáticamente a los leads con secuencias de email inteligentes.

## 2. Arquitectura de Producción

### 2.1 Dominios y Servicios

- **Landing Page**: `https://inmoautomation.com` → Firebase Hosting
- **Dashboard**: `https://app.inmoautomation.com` → Firebase Hosting
- **API Backend**: `https://api.inmoautomation.com` → Cloud Run (PHP)
- **Valoradores Públicos**: `https://{client}.inmoautomation.com/valora/` → Subdominios wildcard (proyecto separado a excepción de api_handler.php que controla su funcionamiento)
- **Base de Datos**: Cloud SQL for MySQL

### 2.2 Infraestructura GCP

- **Proyecto**: `inmoautomation`
- **Región**: `us-central1`
- **Servicios**:
  - Firebase Hosting (frontend multi-site)
  - Cloud Run (API backend serverless)
  - Cloud SQL (base de datos MySQL gestionada)
  - Cloud Build (CI/CD automático)
  - Cloud Storage (almacenamiento de informes PDF y otros activos)
  - Cloud Tasks (procesamiento asíncrono de tareas como generación de informes y envío de emails)
  - **Cloud Scheduler**: Para disparar el motor de nutrición IA periódicamente.
- **Conexión DB (Producción)**: Socket Unix (`/cloudsql/inmoautomation:us-central1:inmoautomation-db`)
- **Costos**: ~$7/mes (solo VM MySQL, resto en free tier)

## 3. Componentes Clave y Dependencias

### 3.1 Tareas Asíncronas (Cloud Tasks)

- **Propósito**: Para operaciones de larga duración que no deben bloquear la respuesta de la API, como generar el PDF del informe y enviarlo por email.
- **Flujo**: La API principal (`api_handler.php`) crea una tarea en una cola de Cloud Tasks. La tarea es una petición HTTP que invoca un endpoint específico en Cloud Run (`procesar_informe_task.php`).
- **Autenticación**: La invocación de Cloud Run desde Cloud Tasks está securizada mediante OIDC. Se requiere una **Cuenta de Servicio** con el rol `roles/run.invoker`.

### 3.2 Generación de PDF

- **Librería**: Se utiliza `wkhtmltopdf` para convertir plantillas HTML en informes PDF.
- **Instalación**: La dependencia se instala directamente en la imagen de Docker, como se especifica en el `Dockerfile`.
- **Permisos**: Es crítico asegurar que el wrapper de `wkhtmltopdf` tenga permisos de ejecución (`chmod +x`) dentro del contenedor.

### 3.3 Emails Transaccionales y Nutrición IA

- **Proveedor**: Se utiliza **Brevo (antes Sendinblue)** para el envío de todos los emails transaccionales y de secuencia.
- **Servicio**: `Api\lib\BrevoService` para el envío de emails.
- **Plantillas**: Las plantillas de email se gestionan en la base de datos (`email_plantillas_html`) y se procesan en PHP antes de enviarlas, incluyendo lógica condicional simple.
- **Remitente dinámico para leads**: cuando el correo está dirigido a un lead (ej. informe de valoración inicial o pasos de la secuencia IA) el sistema construye automáticamente el remitente como `{{nombre_display}} <{{client_identifier}}@inmoautomation.com>`, garantizando la marca de la agencia.
- **Remitente genérico para notificaciones internas**: correos del dashboard (p. ej. alta de nuevo miembro, avisos de pago) no suministran remitente personalizado, por lo que se usa el par por defecto configurado en `.env` (`DEFAULT_SENDER_EMAIL` / `DEFAULT_SENDER_NAME`).

### 3.4 Sistema de Nutrición de Emails con IA

- **Propósito**: Enviar secuencias de emails hiper-personalizados a los leads de forma automática, sin intervención de la agencia.
- **Activación**: La secuencia se activa cuando el lead hace clic en el enlace de descarga del informe de valoración en el email inicial (detectado por el webhook de Brevo).
- **Secuencias**: Pre-construidas por InmoAutomation y almacenadas en las tablas `sequences` y `sequence_steps`.
- **Asignación**: Automática, basada en el plan de suscripción de la agencia (`plan_sequence_assignments`).
- **Generación de Contenido**: Utiliza **OpenAI** (`Api\lib\OpenAIService`) para generar el cuerpo de los emails, basándose en prompts predefinidos y datos específicos del lead y la propiedad.
- **Motor de Procesamiento**: Un cron job en Cloud Scheduler invoca periódicamente el endpoint `api/cron/process_sequences.php` para identificar leads que necesitan un email, generar el contenido con IA, enviarlo y actualizar el progreso del lead en la secuencia.
- **Seguimiento**: El progreso de cada lead en su secuencia se registra en la tabla `lead_sequence_tracking`.

## 4. Desarrollo Local

### 4.1 Prerrequisitos

- Node.js (v18+)
- PHP (v8.1+) con Apache
- Composer
- MySQL (local o a través del Cloud SQL Auth Proxy)
- [Cloud SQL Auth Proxy](https://cloud.google.com/sql/docs/mysql/connect-auth-proxy)

### 4.2 Configuración Backend (API)

#### Conexión a Cloud SQL para Desarrollo Local

Para conectar tu entorno de desarrollo local a la instancia de Cloud SQL de forma segura sin exponerla a internet, utiliza el **Cloud SQL Auth Proxy**.

1.  **Instala el Proxy**: Sigue las [instrucciones oficiales de Google Cloud](https://cloud.google.com/sql/docs/mysql/connect-auth-proxy#install).

2.  **Autentícate en gcloud**:
    ```bash
    gcloud auth application-default login
    ```

3.  **Inicia el Proxy**:
    Abre una terminal y ejecuta el siguiente comando. Déjalo corriendo en segundo plano mientras desarrollas.
    ```bash
    ./cloud_sql_proxy inmoautomation:us-central1:inmoautomation-db --port=3306
    ```

4.  **Configura tu `.env` local**:
    Asegúrate de que tu fichero `api/.env` apunte a la conexión local que el proxy ha creado.
    ```env
    # Dejar DB_SOCKET vacío o comentarlo
    # DB_SOCKET=

    DB_HOST=127.0.0.1
    DB_PORT=3306
    DB_NAME=s_valorador
    DB_USER=inmoautomation-db # O tu usuario de desarrollo
    DB_PASS=TU_CONTRASEÑA_DE_BD
    ```


```bash
# Instalar dependencias PHP
cd api/
composer install

# Configurar variables de entorno
cp .env.example .env
# Editar api/.env con tus configuraciones

# Configurar Apache VirtualHost
# DocumentRoot debe apuntar al directorio raíz del proyecto
# Habilitar mod_rewrite para .htaccess
```

### 4.3 Configuración Frontend

```bash
# Instalar dependencias Node.js
npm install

# Desarrollo Landing Page
npm run dev:landing  # http://localhost:5173

# Desarrollo Dashboard
npm run dev:app      # http://localhost:5174
```

## 5. Variables de Entorno

El backend utiliza dos ficheros de configuración principales:

1.  **`api/.env`**: Para el núcleo de la aplicación (Dashboard, API principal).
2.  **`api/valorador/.env`**: Específico para el endpoint del valorador público, que puede tener configuraciones distintas.

### 5.1. Backend Principal (`api/.env`)

```env
# Entorno (production o development)
APP_ENV=production

# URLs de la aplicación
API_URL="https://api.inmoautomation.com"
LANDING_PAGE_URL="https://inmoautomation.com"
DASHBOARD_URL="https://app.inmoautomation.com"

# Base de Datos
# En producción, DB_SOCKET es inyectado por Cloud Run y tiene prioridad.
# En local, usa DB_HOST y DB_PORT para conectar vía Cloud SQL Auth Proxy.
DB_SOCKET=
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=inmoautomation-db
DB_PASS="TU_CONTRASEÑA"
DB_NAME=s_valorador
DB_CHARSET=utf8mb4

# JWT
JWT_SECRET="TU_CLAVE_SECRETA_SUPER_LARGA_Y_SEGURA"
JWT_ISSUER="inmoautomation.com"

# Stripe
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Brevo (Sendinblue)
BREVO_API_KEY="xkeysib-..."

# Google Cloud
# Email de la cuenta de servicio con rol 'run.invoker' para autenticar Cloud Tasks
CLOUD_RUN_INVOKER_SERVICE_ACCOUNT="<EMAIL>"

# Endpoint del propio servicio de Cloud Run que procesa las tareas
# Generalmente es la URL del servicio 'dashboard-api'
TASK_HANDLER_ENDPOINT_URL="https://dashboard-api-....run.app"

# OpenAI API Key para generación de contenido IA
OPENAI_API_KEY="sk-..."
```

### 5.2. Valorador Público (`api/valorador/.env`)

Este fichero es utilizado por `api/valorador/api_handler.php`.

```env
# Endpoint de la API específica para el valorador (puede ser diferente al principal)
VALORADOR_API_ENDPOINT_URL="https://api-valorador.inmoautomation.com/valorar-vnext"

# Otras variables que puedan ser específicas para el valorador...
```

# APIs terceros
OPENAI_API_KEY="sk-..."
BREVO_API_KEY="..."
DEFAULT_SENDER_EMAIL="<EMAIL>"

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT_ID="inmoautomation"
GOOGLE_CLOUD_STORAGE_BUCKET="inmoautomation-logos"
GOOGLE_CLOUD_REPORTS_BUCKET="inmoautomation-reports"
LOGOS_BASE_URL="https://storage.googleapis.com/inmoautomation-logos"
REPORTS_BASE_URL="https://storage.googleapis.com/inmoautomation-reports"

# wkhtmltopdf
WKHTMLTOPDF_PATH="/usr/local/bin/wkhtmltopdf-wrapper"

# Google Maps (para mapas en PDFs)
GOOGLE_PLACES_API_KEY="AIzaSy..."
```

### 5.3 Frontend

**Archivos por modo:**
- `.env` - Variables comunes
- `.env.app` - Dashboard específico
- `.env.landing` - Landing específico

**Variables principales:**

```env
# Común (.env)
VITE_STRIPE_PUBLISHABLE_KEY="pk_test_..."
VITE_GOOGLE_MAPS_API_KEY="AIzaSyCruwqMDgL5RWj0o7S_zjuOUl61C3sWKOI"

# Dashboard (.env.app)
VITE_APP_TYPE="app"
VITE_API_BASE_URL="https://api.inmoautomation.com"
VITE_APP_BASE_URL="https://app.inmoautomation.com"

# Landing (.env.landing)
VITE_APP_TYPE="landing"
VITE_API_BASE_URL="https://api.inmoautomation.com"
VITE_LANDING_BASE_URL="https://inmoautomation.com"
```

## 5. Estructura del Proyecto

```
valorador-landing-vue/
├── api/                          # Backend PHP
│   ├── lib/                      # Clases y utilidades
│   │   ├── OpenAIService.php     # Servicio para interactuar con OpenAI
│   │   ├── PaymentFailureLogger.php # Logging detallado de fallos de pago
│   │   └── SubscriptionStatusService.php # Estados granulares de suscripción
│   ├── valorador/                # API valorador público
│   ├── .env                      # Variables entorno backend
│   ├── cloudbuild.yaml           # CI/CD Cloud Build
│   ├── procesar_informe_task.php # Endpoint para Cloud Tasks
│   ├── update-payment-method-v2.php # Endpoint mejorado con soporte 3DS
│   ├── cron/                     # Scripts de tareas programadas
│   │   └── process_sequences.php # Motor de nutrición IA
│   └── *.php                     # Otros endpoints API
├── src/                          # Frontend Vue.js
│   ├── components/               # Componentes reutilizables
│   │   ├── landing/              # Componentes landing
│   │   └── dashboard/            # Componentes dashboard
│   │       └── PaymentNotifications.vue # Notificaciones de problemas de pago
│   ├── views/                    # Páginas principales
│   │   ├── landing/              # Páginas landing
│   │   └── dashboard/            # Páginas dashboard
│   ├── stores/                   # Pinia stores (estado)
│   ├── router/                   # Vue Router
│   ├── utils/                    # Utilidades
│   └── main.ts                   # Entry point
├── public/                       # Archivos estáticos
├── .env*                         # Variables entorno frontend
├── firebase.json                 # Config Firebase Hosting
├── vite.config.ts                # Config Vite
├── index.html                    # HTML base
└── package.json                  # Dependencias Node.js
```

## 6. Despliegue en Producción

### 6.1 Arquitectura GCP

**Frontend (Firebase Hosting)**
- Landing: `inmoautomation-landing` target
- Dashboard: `inmoautomation-app` target
- Build: `dist/landing/` y `dist/app/`

**Backend (Cloud Run)**
- Servicio: `dashboard-api` (us-central1)
- Dominio: `api.inmoautomation.com`
- Variables: `api/.env.production`
- Configuración: 1GB RAM, 1 CPU, timeout 300s

**Base de Datos (Cloud SQL)**
- Instancia: `inmoautomation-db`
- Tipo: `db-f1-micro`
- Conexión: Vía Socket Unix en Cloud Run (no requiere IP pública ni VPC Connector)

**Almacenamiento (Google Cloud Storage)**
- Logos: `inmoautomation-logos` bucket
- Reportes PDF: `inmoautomation-reports` bucket
- URLs públicas: `https://storage.googleapis.com/[bucket-name]/`

**Cloud Scheduler**
- Job: `process-sequences-cron-job`
- Frecuencia: `*/5 * * * *` (cada 5 minutos)
- Objetivo: Invocar `https://api.inmoautomation.com/cron/process_sequences.php` con autenticación OIDC.

### 6.2 Despliegue Automatizado del Backend (CI/CD)

El despliegue del backend en Cloud Run está completamente automatizado a través de un pipeline de CI/CD con Cloud Build, activado por cada `push` a la rama `main` en GitHub.

**Proceso del Pipeline (`api/cloudbuild.yaml`):**

1.  **Activación (Trigger)**: Un `push` a la rama `main` inicia una nueva compilación en Cloud Build.
2.  **Construcción de Imagen**: Cloud Build utiliza el `api/Dockerfile` para construir la imagen de la aplicación. Este `Dockerfile` es autosuficiente e instala todas las dependencias del sistema (como `wkhtmltopdf`) y de PHP (`composer install`).
3.  **Subida a Artifact Registry**: La imagen recién construida se etiqueta con el hash del commit y se sube a Artifact Registry (`us-central1-docker.pkg.dev`).
4.  **Despliegue en Cloud Run**: Cloud Build despliega la nueva imagen en el servicio `dashboard-api`.
    - **Secretos y Variables**: Durante este paso, las variables de entorno sensibles (como `DB_PASSWORD`, `JWT_SECRET`, `OPENAI_API_KEY`, etc.) se inyectan de forma segura desde **Google Secret Manager**. Las variables no sensibles se definen directamente en el `cloudbuild.yaml`.
    - **Cuenta de Servicio**: El pipeline se ejecuta utilizando una cuenta de servicio dedicada (`dashboard-api-builder-sa`) con los permisos mínimos necesarios para construir, subir y desplegar, siguiendo el principio de menor privilegio.

Este enfoque garantiza despliegues consistentes, seguros y sin intervención manual.

### 6.3 Sistema de Generación de Informes PDF

**wkhtmltopdf en Cloud Run:**
- Instalado desde repositorios Debian en el Dockerfile
- Wrapper con xvfb para entorno headless: `/usr/local/bin/wkhtmltopdf-wrapper`
- Variable de entorno: `WKHTMLTOPDF_PATH=/usr/local/bin/wkhtmltopdf-wrapper`
- Versión: 0.12.6
- Configuración: A4, márgenes 15mm, UTF-8, DPI 150

**Plantillas de Informes:**
- Ubicación: `api/templates/pdf/informe_valoracion_tpl.phtml`
- Versión actual: v7.0 (iconos de texto, fuentes optimizadas para móvil)
- CSS moderno con variables dinámicas de colores de agencia
- Iconos de texto circulares en lugar de emojis/SVG
- Optimizado para legibilidad móvil

**Almacenamiento:**
- PDFs generados se suben a Google Cloud Storage
- Bucket: `inmoautomation-reports`
- Estructura: `reports/{client_identifier}/informe_valoracion_v7_{uuid}.pdf`
- URLs públicas para descarga directa

### 6.4 Sistema de Emails Automatizados

**Proveedor:** Brevo (anteriormente Sendinblue)
- Configuración: `BREVO_API_KEY` en variables de entorno
- Servicio: `Api\lib\BrevoService`
- Emails transaccionales con tracking automático
- **Remitente dinámico para leads**: cuando el correo está dirigido a un lead (ej. informe de valoración inicial o pasos de la secuencia IA) el sistema construye automáticamente el remitente como `{{nombre_display}} <{{client_identifier}}@inmoautomation.com>`, garantizando la marca de la agencia.
- **Remitente genérico para notificaciones internas**: correos del dashboard (p. ej. alta de nuevo miembro, avisos de pago) no suministran remitente personalizado, por lo que se usa el par por defecto configurado en `.env` (`DEFAULT_SENDER_EMAIL` / `DEFAULT_SENDER_NAME`).

**Plantillas de Email:**
- Almacenadas en base de datos (tabla `email_plantillas_html`)
- Plantilla principal: `informe_valoracion_inicial`
- Soporte para placeholders dinámicos: `{{nombre_lead}}`, `{{logo_agencia_url}}`, etc.
- Lógica condicional: `{{#if variable}}...{{/if}}`

**Procesamiento Asíncrono con Cloud Tasks:**
- **Mecanismo**: Google Cloud Tasks para un procesamiento robusto y en segundo plano.
- **Flujo**:
    1. El `api_handler.php` crea una tarea en Cloud Tasks inmediatamente después de registrar un nuevo lead.
    2. La tarea invoca al endpoint `api/procesar_informe_task.php`.
    3. Este endpoint genera el PDF, lo envía por email y actualiza la base de datos.
- **Beneficios**: Respuesta inmediata al usuario, reintentos automáticos, y desacoplamiento del proceso de envío de email.
- **Cola**: `envio-informes` en la región `us-central1`.
- **Estados de leads**: `pendiente_informe_inicial` → `informe_inicial_enviado`

### 6.5 Sistema de Suscripciones Avanzado

**Gestión Robusta de Pagos:**
- **Detección Inteligente de Métodos de Pago**: Búsqueda en múltiples fuentes (suscripción, cliente, métodos adjuntos)
- **Soporte Completo para 3DS**: Autenticación 3D Secure integrada con SetupIntent de Stripe
- **Estados Granulares**: Sistema de estados detallados (`payment_authentication_required`, `payment_failed_recoverable`, etc.)
- **Recuperación Automática**: Reactivación automática de suscripciones tras resolver problemas de pago

**Notificaciones Contextuales:**
- Sistema de notificaciones inteligentes en el dashboard
- Mensajes específicos según el tipo de problema (3DS, fondos insuficientes, tarjeta expirada)
- Botones de acción directa para resolver problemas
- Niveles de prioridad (crítico, alto, medio) con colores diferenciados

**Logging y Monitoreo:**
- Tabla `payment_failures` para tracking detallado de fallos de pago
- Clasificación automática de tipos de fallo y recuperabilidad
- Webhook mejorado con logging detallado de eventos de Stripe
- Seguimiento de intentos de recuperación y éxito

**Endpoints Mejorados:**
- `update-payment-method-v2.php`: Soporte completo para 3DS con SetupIntent
- `subscription.php`: Detección mejorada de métodos de pago y estados detallados
- Manejo de redirects automático para autenticación bancaria

### 6.6 Configuración de Dominios

**DNS Records:**
- `inmoautomation.com` → Firebase Hosting (landing)
- `app.inmoautomation.com` → Firebase Hosting (dashboard)
- `api.inmoautomation.com` → Cloud Run
- `*.inmoautomation.com` → Wildcard para valoradores públicos

**SSL/TLS:** Automático en Firebase Hosting y Cloud Run

### 6.6 Costos Optimizados
- **Firebase Hosting**: $0 (free tier)
- **Cloud Run**: $0 (free tier)
- **VM MySQL (e2-micro)**: ~$7/mes
- **Cloud Build**: $0 (120 min/día gratis)
- **Total**: ~$7/mes (sin VPC Connector)

## 7. Stack Tecnológico

### 7.1 Frontend
- **Framework**: Vue.js 3 (Composition API, TypeScript)
- **Build**: Vite con modos `landing` y `app`
- **Router**: Vue Router 4
- **Estado**: Pinia stores
- **Estilos**: Tailwind CSS
- **HTTP**: `apiFetch.ts` wrapper
- **Mapas**: Google Maps API + vue3-google-map
- **Gráficos**: Chart.js + vue-chartjs

### 7.2 Backend
- **Lenguaje**: PHP 8.1+ (tipado estricto, namespaces)
- **Dependencias**: Composer
- **Autenticación**: JWT (firebase/php-jwt)
- **Base de Datos**: MySQL con PDO
- **APIs**: Stripe, OpenAI, Brevo
- **CORS**: Configurado para Firebase Hosting

### 7.3 Infraestructura
- **Frontend**: Firebase Hosting (multi-site)
- **Backend**: Cloud Run (serverless)
- **Base de Datos**: MySQL en VM e2-micro
- **CI/CD**: Cloud Build
- **SSL**: Automático
- **Costos**: ~$7/mes optimizados

## 8. Comandos de Desarrollo

### 8.1 Frontend

```bash
# Instalar dependencias
npm install

# Desarrollo - Landing
npm run dev:landing    # Puerto 5173

# Desarrollo - Dashboard
npm run dev:app        # Puerto 5174

# Build para producción
npm run build:landing  # → dist/landing/
npm run build:app      # → dist/app/

# Deploy a Firebase
npm run deploy:landing
npm run deploy:app
```

### 8.2 Backend

```bash
# Desarrollo local
cd api/
composer install
php -S localhost:8000

# Despliegue a Cloud Run (Automatizado)
# El despliegue ahora es automático con cada 'git push' a la rama 'main'.
# No es necesario ejecutar ningún comando manual.
# Puedes monitorear el progreso en la consola de Google Cloud -> Cloud Build.

# Testear creación de tarea (ejecutar una valoración)
# La tarea se puede monitorear en la consola de Google Cloud -> Cloud Tasks.

# Test wkhtmltopdf en Cloud Run
curl "https://api.inmoautomation.com/test_wkhtmltopdf.php"

# Debug información del sistema
curl "https://api.inmoautomation.com/debug_wkhtmltopdf.php"
```

## 9. Funcionalidades Principales

### 9.1 Landing Page
- **Presentación del producto**: Valorador Inmobiliario IA
- **Planes y precios**: Diferentes opciones de suscripción
- **Proceso de registro**: Integración con Stripe
- **Generación de confianza**: Testimonios y casos de éxito

### 9.2 Dashboard de Agencia

**Configuración del Valorador:**
- Personalización visual (logo, colores, datos de contacto)
- Configuración de dominio personalizado
- Activación/desactivación del valorador público

**Gestión de Leads:**
- Listado con filtros y paginación
- Detalle completo de cada lead
- Notas editables y seguimiento
- Navegación cruzada con valoraciones

**Gestión de Valoraciones:**
- Tabla con filtros avanzados (fecha, tipo, valor)
- Mapa de ubicaciones (heatmap/markers)
- Estadísticas y KPIs
- Detalle completo con Google Maps

**Sistema de Emails IA:**
- Dashboard de rendimiento (KPIs, gráficos)
- Historial de emails enviados con visualización nativa del contenido HTML
- Secuencias automatizadas de nutrición (gestionadas por el SaaS, no por la agencia)
- Acciones: pausar, cancelar, reanudar (a nivel de lead individual)

**Administración (Solo Propietarios):**
- Gestión avanzada de suscripción y facturación
- Portal de Stripe integrado con soporte completo para 3DS
- Sistema de notificaciones inteligentes para problemas de pago
- Estados granulares de suscripción con acciones recomendadas
- Detección mejorada de métodos de pago y recuperación automática
- Gestión de equipo (invitaciones, permisos)
- Control de acceso basado en plan

### 9.3 Sistema de Generación de Informes PDF

**Características Técnicas:**
- Generación con wkhtmltopdf 0.12.6 en Cloud Run
- Plantillas HTML/CSS modernas con variables dinámicas
- Personalización automática con colores primario y secundario de agencia
- Optimizado para visualización móvil con textos grandes y legibles
- Uso de emojis Unicode como iconos para máxima compatibilidad
- Ajustes específicos para impresión correcta de colores

**Contenido de Informes:**
- Valoración estimada con rango de precios
- Detalles completos de la propiedad
- Análisis comparativo con la zona
- Estadísticas zonales y características comunes
- Consejos personalizados según necesidad del lead
- CTA personalizado con colores de agencia

**Distribución:**
- Almacenamiento en Google Cloud Storage
- URLs públicas para descarga directa
- Envío automático por email vía Brevo
- Integración con secuencias de nutrición de leads

## 10. Estado del Proyecto

### 10.1 Migración a GCP (Completada ✅)
- **Fecha**: Junio 2025
- **Estado**: Totalmente funcional en producción
- **Arquitectura**: Migrada de cPanel a Google Cloud Platform
- **Beneficios**: Escalabilidad, SSL automático, costos optimizados (~$7/mes)
- **Base de Datos**: Migración completa de relaciones `client_id` → `cliente_valorador_id`

### 10.2 Funcionalidades Implementadas
- ✅ Landing page con registro e integración Stripe
- ✅ Dashboard completo con todas las secciones
- ✅ Sistema de autenticación JWT con roles
- ✅ Gestión de leads y valoraciones
- ✅ Sistema de emails IA automatizado
- ✅ Mapas interactivos con Google Maps API
- ✅ Gráficos y estadísticas con Chart.js
- ✅ Gestión de equipo y permisos
- ✅ Valoradores públicos con subdominios wildcard
- ✅ Generación de PDFs con wkhtmltopdf en Cloud Run
- ✅ Almacenamiento en Google Cloud Storage
- ✅ Plantillas de informes optimizadas para móvil
- ✅ Sistema de emails automatizado con Brevo
- ✅ **Sistema de Nutrición de Emails con IA**: Implementación de la lógica de secuencias, integración con OpenAI y motor de cron en Cloud Scheduler
- ✅ **Sistema de Suscripciones Avanzado**: Soporte completo para 3DS, detección inteligente de métodos de pago, estados granulares y notificaciones contextuales

## 11. Próximos Pasos

### 11.1 Mejoras Técnicas
- **Optimización de rendimiento**: Análisis de consultas SQL lentas
- **Pruebas automatizadas**: Implementar testing unitario y de integración
- **Monitoreo**: Configurar alertas y métricas de rendimiento
- **Seguridad**: Auditoría de seguridad y hardening

### 11.2 Nuevas Funcionalidades
- **Notificaciones push**: Sistema de notificaciones en tiempo real
- **Analytics avanzados**: Más KPIs y métricas de negocio
- **Integración CRM**: Conectores con CRMs populares

### 11.3 Escalabilidad
- **Auto-scaling**: Configuración de escalado automático en Cloud Run
- **CDN**: Implementar Cloud CDN para mejor rendimiento global
- **Multi-región**: Preparación para despliegue multi-región
- **Backup automatizado**: Sistema de respaldos automáticos

---

## 📝 Información del Documento

**Última Actualización**: Julio 2025 (v8.1 - Sistema de Suscripciones Avanzado con 3DS)

**Estado del Proyecto**: ✅ Totalmente funcional en producción

**Arquitectura**: Google Cloud Platform con optimización de costos

**Dominio Principal**: `inmoautomation.com`