# Esquema de la Base de Datos - Proyecto InmoAutomation

Este documento describe la estructura de las tablas de la base de datos del proyecto.

## Tablas

### `agencies`
*   **Columnas:**
    | Nombre                   | Tipo                  |
    | ------------------------ | --------------------- |
    | `id`                     | `bigint unsigned`     |
    | `uuid`                   | `char(36)`            |
    | `name`                   | `varchar(255)`        |
    | `owner_user_id`          | `bigint unsigned`     |
    | `primary_valorador_id`   | `int`                 |
    | `fecha_creacion`         | `timestamp`           |
    | `fecha_modificacion`     | `timestamp`           |

### `clientes_valorador`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `int`                 |
    | `user_id`                       | `bigint unsigned`     |
    | `agency_id`                     | `bigint unsigned`     |
    | `uuid`                          | `char(36)`            |
    | `client_identifier`             | `varchar(50)`         |
    | `nombre_display`                | `varchar(255)`        |
    | `logo_url`                      | `varchar(255)`        |
    | `color_primario`                | `varchar(7)`          |
    | `color_secundario`              | `varchar(7)`          |
    | `email_notificaciones_leads`    | `varchar(255)`        |
    | `direccion_fisica`              | `varchar(255)`        |
    | `telefono_contacto`             | `varchar(20)`         |
    | `email_contacto_publico`        | `varchar(255)`        |
    | `sitio_web_url`                 | `varchar(255)`        |
    | `whatsapp_numero`               | `varchar(20)`         |
    | `descripcion_breve`             | `text`                |
    | `cta_contacto_url`              | `varchar(255)`        |
    | `activo`                        | `tinyint(1)`          |
    | `config_valorador_json`         | `text`                |
    | `config_email_secuencia_json`   | `text`                |
    | `fecha_creacion`                | `timestamp`           |
    | `fecha_modificacion`            | `timestamp`           |
    | `dominios_autorizados_json`     | `longtext`            |

### `email_plantillas_html`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `int unsigned`        |
    | `uuid`                          | `char(36)`            |
    | `nombre_interno_plantilla`      | `varchar(150)`        |
    | `descripcion`                   | `text`                |
    | `asunto_predeterminado`         | `varchar(255)`        |
    | `contenido_html`                | `longtext`            |
    | `contenido_mjml`                | `longtext`            |
    | `tipo_plantilla`                | `varchar(50)`         |
    | `layout_principal_id`           | `int unsigned`        |
    | `version`                       | `int unsigned`        |
    | `activa`                        | `tinyint(1)`          |
    | `tags`                          | `longtext`            |
    | `fecha_creacion`                | `timestamp`           |
    | `fecha_modificacion`            | `timestamp`           |

### `lead_emails_historial`

Registra cada email individual que ha sido enviado, está programado o ha fallado.

- **`estado_envio` ENUM**: Describe el estado de un email específico en el historial. Los valores posibles son:
  - `'scheduled'`: El email está programado para ser enviado en el futuro.
  - `'sending'`: El email está siendo procesado activamente para su envío.
  - `'sent'`: El email ha sido enviado con éxito.
  - `'failed'`: El envío del email falló.
  - `'cancelled'`: El envío programado fue cancelado antes de que ocurriera.
  - `'pending_initial'`: El email está pendiente de ser generado y programado por el sistema (estado inicial).
*   **Descripción:** Almacena un registro de cada email enviado o programado para un lead, incluyendo borradores y versiones finales.
*   **Columnas:**
    | Nombre                          | Tipo                                                                          | Descripción                                                                                               |
    | ------------------------------- | ----------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------- |
    | `id`                            | `bigint unsigned`                                                             | Clave primaria.                                                                                           |
    | `uuid`                          | `char(36)`                                                                    | Identificador único universal.                                                                            |
    | `lead_id`                       | `bigint unsigned`                                                             | FK a `valorador_leads`.                                                                                   |
    | `sequence_step_id`              | `int unsigned`                                                                | FK a `sequence_steps`. El paso de la secuencia que generó este email.                                     |
    | `tipo_email`                    | `varchar(50)`                                                                 | Tipo de email (ej: 'informe_inicial', 'seguimiento_1').                                                   |
    | `estado_envio`                  | `enum('scheduled', 'sending', 'sent', 'failed', 'cancelled', 'pending_initial')` | Estado del email.                                                                                         |
    | `fecha_programada_envio`        | `timestamp`                                                                   | Fecha y hora en que el envío está programado.                                                             |
    | `fecha_envio_real`              | `timestamp`                                                                   | Fecha y hora en que se realizó el envío.                                                                  |
    | `borrador_asunto`               | `varchar(255)`                                                                | Asunto del borrador generado por IA, editable por la agencia.                                             |
    | `borrador_cuerpo_html`          | `longtext`                                                                    | Cuerpo HTML del borrador generado por IA, editable por la agencia.                                        |
    | `asunto_final`                  | `varchar(255)`                                                                | Asunto final con el que se envió el email (puede ser el del borrador o uno modificado).                   |
    | `cuerpo_final_html`             | `longtext`                                                                    | Cuerpo HTML final con el que se envió el email.                                                           |
    | `ia_prompt_usado`               | `text`                                                                        | El prompt exacto que se envió a OpenAI para generar el borrador.                                          |
    | `ia_respuesta_original`         | `longtext`                                                                    | La respuesta JSON cruda completa recibida de OpenAI.                                                      |
    | `instrucciones_mejora_ia`       | `text`                                                                        | Historial de instrucciones de la agencia para la mejora del contenido con IA.                             |
    | `datos_contexto_ia_utilizados`  | `longtext`                                                                    | Snapshot de los datos del lead/propiedad usados para la personalización.                                  |
    | `mensaje_id_esp`                | `varchar(255)`                                                                | ID del mensaje devuelto por el proveedor de email (ej: Brevo).                                            |
    | `error_detalle`                 | `text`                                                                        | Almacena el mensaje de error si el envío falla.                                                           |
    | `abierto_timestamp`             | `timestamp`                                                                   | Fecha y hora de la primera apertura del email.                                                            |
    | `clickeado_timestamp`           | `timestamp`                                                                   | Fecha y hora del primer clic en un enlace del email.                                                      |
    | `fecha_creacion`                | `timestamp`                                                                   |                                                                                                           |
    | `fecha_modificacion`            | `timestamp`                                                                   |                                                                                                           |

### `lead_sequence_tracking`

Esta tabla rastrea el estado de un lead dentro de una secuencia de emails específica.

- **`status` ENUM**: Define el estado actual del lead en la secuencia. Los valores posibles son:
  - `'active'`: La secuencia está en curso y los emails se están enviando según lo programado.
  - `'pending_activation'`: La secuencia está asignada (generalmente después de una valoración), pero en espera de una acción del usuario (como hacer clic en un enlace) para comenzar.
  - `'paused_by_user'`: El usuario ha pausado manualmente la secuencia.
  - `'paused_by_system'`: El sistema ha pausado la secuencia (por ejemplo, después de una respuesta del lead).
  - `'completed'`: El lead ha completado todos los pasos de la secuencia.
  - `'unsubscribed'`: El lead se ha dado de baja de la comunicación.
  - `'error'`: Ocurrió un error que detuvo la secuencia.
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `bigint unsigned`     |
    | `lead_id`                       | `bigint unsigned`     |
    | `sequence_id`                   | `int unsigned`        |
    | `current_step_id`               | `int unsigned`        |
    | `status`                        | `varchar(30)`         |
    | `next_due_date`                 | `timestamp`           |
    | `last_processed_at`             | `timestamp`           |
    | `created_at`                    | `timestamp`           |
    | `updated_at`                    | `timestamp`           |

### `password_reset_tokens`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `int`                 |
    | `user_id`                       | `int`                 |
    | `token`                         | `varchar(128)`        |
    | `expires_at`                    | `datetime`            |
    | `created_at`                    | `datetime`            |

### `plan_sequence_assignments`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `plan_id`                       | `int unsigned`        |
    | `sequence_id`                   | `int unsigned`        |
    | `priority`                      | `int unsigned`        |
    | `created_at`                    | `timestamp`           |
    | `updated_at`                    | `timestamp`           |

### `plans`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `int unsigned`        |
    | `uuid`                          | `char(36)`            |
    | `slug`                          | `varchar(50)`         |
    | `name`                          | `varchar(100)`        |
    | `short_description`             | `varchar(255)`        |
    | `price_monthly`                 | `decimal(10,2)`       |
    | `price_annual`                  | `decimal(10,2)`       |
    | `stripe_price_id_monthly`       | `varchar(255)`        |
    | `stripe_price_id_annual`        | `varchar(255)`        |
    | `is_active`                     | `tinyint(1)`          |
    | `display_order`                 | `int unsigned`        |
    | `max_dashboard_users`           | `int unsigned`        |
    | `max_valoradores`               | `int unsigned`        |
    | `allow_multiple_sequences`      | `tinyint(1)`          |
    | `allow_custom_domain_email`     | `tinyint(1)`          |
    | `has_analytics`                 | `tinyint(1)`          |
    | `additional_config_json`        | `longtext`            |
    | `created_at`                    | `timestamp`           |
    | `updated_at`                    | `timestamp`           |

### `sequences`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `int unsigned`        |
    | `uuid`                          | `char(36)`            |
    | `name`                          | `varchar(100)`        |
    | `description`                   | `text`                |
    | `is_active`                     | `tinyint(1)`          |
    | `trigger_event`                 | `varchar(50)`         |
    | `created_at`                    | `timestamp`           |
    | `updated_at`                    | `timestamp`           |

### `sequence_steps`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `int unsigned`        |
    | `uuid`                          | `char(36)`            |
    | `sequence_id`                   | `int unsigned`        |
    | `step_order`                    | `int unsigned`        |
    | `name`                          | `varchar(150)`        |
    | `delay_days`                    | `int unsigned`        |
    | `email_subject`                 | `varchar(255)`        |
    | `email_template_id`             | `int unsigned`        |
    | `prompt_template`               | `text`                |
    | `is_active`                     | `tinyint(1)`          |
    | `created_at`                    | `timestamp`           |
    | `updated_at`                    | `timestamp`           |

### `suscripciones`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `bigint unsigned`     |
    | `uuid`                          | `char(36)`            |
    | `user_id`                       | `bigint unsigned`     |
    | `plan_id`                       | `int unsigned`        |
    | `agency_id`                     | `bigint unsigned`     |
    | `stripe_subscription_id`        | `varchar(255)`        |
    | `stripe_product_id`             | `varchar(255)`        |
    | `stripe_price_id`               | `varchar(255)`        |
    | `nombre_plan_display`           | `varchar(100)`        |
    | `estado`                        | `varchar(50)`         |
    | `cantidad`                      | `int unsigned`        |
    | `fecha_inicio_prueba`           | `timestamp`           |
    | `fecha_fin_prueba`              | `timestamp`           |
    | `fecha_inicio_periodo_actual`   | `timestamp`           |
    | `fecha_fin_periodo_actual`      | `timestamp`           |
    | `billing_cycle`                 | `varchar(10)`         |
    | `fecha_cancelacion`             | `timestamp`           |
    | `fecha_cancelada_en_stripe`     | `timestamp`           |
    | `motivo_cancelacion`            | `text`                |
    | `metadata_stripe`               | `longtext`            |
    | `fecha_creacion`                | `timestamp`           |
    | `fecha_modificacion`            | `timestamp`           |

### `usuarios`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `bigint unsigned`     |
    | `uuid`                          | `char(36)`            |
    | `nombre_completo`               | `varchar(255)`        |
    | `email`                         | `varchar(255)`        |
    | `password_hash`                 | `varchar(255)`        |
    | `stripe_customer_id`            | `varchar(255)`        |
    | `email_verificado_at`           | `timestamp`           |
    | `fecha_ultimo_login`            | `timestamp`           |
    | `ip_ultimo_login`               | `varchar(45)`         |
    | `roles`                         | `longtext`            |
    | `preferencias`                  | `longtext`            |
    | `activo`                        | `tinyint(1)`          |
    | `agency_id`                     | `bigint unsigned`     |
    | `fecha_creacion`                | `timestamp`           |
    | `fecha_modificacion`            | `timestamp`           |
    | `is_agency_owner`               | `tinyint(1)`          |

### `valorador_leads`
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `bigint unsigned`     |
    | `uuid`                          | `char(36)`            |
    | `nombre`                        | `varchar(100)`        |
    | `email`                         | `varchar(255)`        |
    | `telefono`                      | `varchar(20)`         |
    | `necesidad`                     | `varchar(100)`        |
    | `estado`                        | `varchar(50)`         |
    | `num_valoraciones`              | `int`                 |
    | `notas`                         | `text`                |
    | `valoracion_id`                 | `bigint unsigned`     |
    | `fecha_creacion`                | `timestamp`           |
    | `fecha_modificacion`            | `timestamp`           |
    | `cliente_valorador_id`          | `int`                 |

### `valorador_valoraciones`
*   **Registros Actuales:** 10
*   **Columnas:**
    | Nombre                          | Tipo                  |
    | ------------------------------- | --------------------- |
    | `id`                            | `bigint unsigned`     |
    | `uuid`                          | `char(36)`            |
    | `lead_id`                       | `bigint unsigned`     |
    | `referencia_catastral`          | `varchar(20)`         |
    | `direccion`                     | `varchar(255)`        |
    | `latitud`                       | `decimal(10,8)`       |
    | `longitud`                      | `decimal(11,8)`       |
    | `tipo_principal`                | `varchar(50)`         |
    | `subtipo`                       | `varchar(50)`         |
    | `superficie`                    | `int`                 |
    | `superficie_parcela`            | `int`                 |
    | `ano_construccion_catastro`     | `int`                 |
    | `precio_m2_promedio`            | `decimal(10,2)`       |
    | `tamano_promedio`               | `int`                 |
    | `porcentaje_con_piscina`        | `decimal(5,2)`        |
    | `porcentaje_con_parking`        | `decimal(5,2)`        |
    | `porcentaje_con_ascensor`       | `decimal(5,2)`        |
    | `porcentaje_con_terraza`        | `decimal(5,2)`        |
    | `porcentaje_reformadas`         | `decimal(5,2)`        |
    | `numero_propiedades_analizadas` | `int`                 |
    | `distancia_al_centroide_km`     | `decimal(8,2)`        |
    | `precio_vs_zona_porcentaje`     | `decimal(5,2)`        |
    | `tamano_vs_zona_porcentaje`     | `decimal(5,2)`        |
    | `habitaciones`                  | `int`                 |
    | `banos`                         | `int`                 |
    | `estado`                        | `varchar(50)`         |
    | `planta`                        | `varchar(10)`         |
    | `extras`                        | `text`                |
    | `valor_estimado_min`            | `int`                 |
    | `valor_estimado_max`            | `int`                 |
    | `notas_agente`                  | `text`                |
    | `fecha_creacion`                | `timestamp`           |
    | `fecha_modificacion`            | `timestamp`           |
    | `cliente_valorador_id`          | `int`                 |