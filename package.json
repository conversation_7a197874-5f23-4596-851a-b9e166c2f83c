{"name": "valorador-landing-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:landing": "vite --mode landing --port 5173", "dev:app": "vite --mode app --port 5174", "build": "npm run build:landing && npm run build:app", "build:landing": "vue-tsc -b && vite build --mode landing", "build:app": "vue-tsc -b && vite build --mode app", "preview": "vite preview", "preview:landing": "vite preview --mode landing --port 4173", "preview:app": "vite preview --mode app --port 4174"}, "dependencies": {"@ckpack/vue-color": "^1.6.0", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@stripe/stripe-js": "^7.3.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "click-outside-vue3": "^4.0.1", "date-fns": "^4.1.0", "gsap": "^3.13.0", "jwt-decode": "^4.0.0", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.2", "vibrant": "^0.0.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1", "vue3-google-map": "^0.22.0", "vue3-lottie": "^3.3.1"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.20", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.3", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^2.2.8"}}