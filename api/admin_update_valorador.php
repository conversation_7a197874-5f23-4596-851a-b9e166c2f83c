<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// api/admin_update_valorador.php
// error_reporting(E_ALL); // Manejado por bootstrap.php
// ini_set('display_errors', 0); // Manejado por bootstrap.php
 // <--- INTEGRAR BOOTSTRAP
require_once __DIR__ . '/utils/auth_check.php';
// Use Firebase\JWT\JWT; // auth_check.php o bootstrap.php manejan esto
// Use Firebase\JWT\Key;
header('Content-Type: application/json');
// --- Carga de .env y autoload manejada por bootstrap.php ---
// try { $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); $dotenv->load(); } catch ... (eliminado)
// --- Constantes de BD y JWT_SECRET vienen de bootstrap.php ---
// $dbHost = $_ENV['DB_HOST'] ?? null; (eliminado)
// ... (eliminación de otras asignaciones de variables de entorno)
// Verificar autenticación y rol de administrador
// auth_check.php ahora usará JWT_SECRET de bootstrap.php
$authResult = verifyTokenAndAdminRole(); // <--- SIN ARGUMENTOS
if (!$authResult['success']) {
    http_response_code($authResult['status_code']);
    echo json_encode(['success' => false, 'message' => $authResult['message']]);
    exit();
}
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'Método no permitido. Se esperaba POST.']);
    exit();
}
$data = json_decode(file_get_contents('php://input'), true);
if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'JSON malformado.']);
    exit();
}
$valoradorId = $data['id'] ?? null;
$nombreDisplay = $data['nombre_display'] ?? null;
$logoUrl = $data['logo_url'] ?? null; // Puede ser null o vacío
$nuevoEstado = $data['activo'] ?? null;
if ($valoradorId === null || !is_numeric($valoradorId) || 
    $nombreDisplay === null || trim($nombreDisplay) === '' || 
    $nuevoEstado === null || !is_numeric($nuevoEstado) || ($nuevoEstado != 0 && $nuevoEstado != 1)) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'Datos inválidos: Se requiere ID de valorador, nombre_display y estado (0 o 1).']);
    exit();
}
// Si logo_url es una cadena vacía, la convertimos a null para la BD si el campo lo permite.
if ($logoUrl === '') {
    $logoUrl = null;
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en admin_update_valorador: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

try {
    // Preparamos la consulta para actualizar nombre_display, logo_url y activo
    $stmt = $pdo->prepare("UPDATE clientes_valorador SET nombre_display = ?, logo_url = ?, activo = ? WHERE id = ?");
    
    $stmt->execute([$nombreDisplay, $logoUrl, $nuevoEstado, $valoradorId]);

    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => 'Valorador actualizado correctamente.']);
    } else {
        // Podría ser que no se encontrara el ID o que los datos fueran los mismos
        echo json_encode(['success' => true, 'message' => 'No se realizaron cambios (el valorador no existe o los datos eran los mismos).']);
    }
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error ejecutando statement (admin_update_valorador): " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al actualizar el valorador.']);
}
?>