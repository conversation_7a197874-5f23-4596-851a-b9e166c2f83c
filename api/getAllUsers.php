<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
header('Content-Type: application/json');
require_once __DIR__ . '/utils/auth_check.php'; // Incluir auth_check.php
// Verificar autenticación y rol de administrador
$authResult = verifyTokenAndAdminRole(); // Asume que JWT_SECRET está en bootstrap
if (!$authResult['success']) {
    http_response_code($authResult['status_code']);
    echo json_encode(['success' => false, 'message' => $authResult['message']]);
    exit();
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD (getAllUsers): " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

try {
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : null;
    $sql = "SELECT id, uuid, nombre_completo, email, roles, activo, created_at, fecha_ultimo_login FROM usuarios ORDER BY created_at DESC"; // Ordenar por más recientes
    
    if ($limit && $limit > 0) {
        $sql .= " LIMIT " . $limit; // PDO no necesita bind para LIMIT si es un entero validado
    }
    
    $stmt = $pdo->query($sql);
    $users = [];
    
    while ($row = $stmt->fetch()) {
        // Convertir roles de JSON string a array si es necesario
        if (isset($row['roles'])) {
            $decodedRoles = json_decode($row['roles'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $row['roles'] = $decodedRoles;
            } else {
                $row['roles'] = [$row['roles']]; 
            }
        }
        $users[] = $row;
    }
    
    echo json_encode(['success' => true, 'data' => $users]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al obtener usuarios: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error al obtener la lista de usuarios.']);
    exit();
}
