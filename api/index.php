<?php
// api/index.php - Punto de entrada principal para Cloud Run

// Cargar configuración
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/config/cors.php';

// Obtener la ruta solicitada
$request_uri = $_SERVER['REQUEST_URI'] ?? '/';
$path = parse_url($request_uri, PHP_URL_PATH);

// Manejar rutas del valorador público
if (strpos($path, '/valorador/') === 0) {
    // Ruta del valorador público: /valorador/api_handler.php
    if ($path === '/valorador/api_handler.php') {
        require_once __DIR__ . '/valorador/api_handler.php';
        exit;
    }
    // Otras rutas del valorador en el futuro
    header('Content-Type: application/json');
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'Valorador endpoint not found',
        'requested_path' => $path
    ]);
    exit;
}

// Remover el prefijo /api si existe (para compatibilidad)
if (strpos($path, '/api/') === 0) {
    $path = substr($path, 4);
}

// Remover la barra inicial si existe
if (strpos($path, '/') === 0) {
    $path = substr($path, 1);
}

// Si no hay path o es la raíz, mostrar información de la API
if (empty($path) || $path === '/') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'Valorador API is running',
        'version' => '1.0.0',
        'timestamp' => date('Y-m-d H:i:s'),
        'endpoints' => [
            'login' => '/login.php',
            'subscription' => '/subscription.php',
            'valorador-config' => '/valorador-config.php',
            'health' => '/health.php',
            'valorador-public' => '/valorador/api_handler.php'
        ]
    ]);
    exit;
}

// Verificar si el archivo PHP existe
$file_path = __DIR__ . '/' . $path;

// Si no tiene extensión .php, agregarla
if (!pathinfo($path, PATHINFO_EXTENSION)) {
    $path .= '.php';
    $file_path = __DIR__ . '/' . $path;
}

// Verificar que el archivo existe y es un archivo PHP válido
if (file_exists($file_path) && pathinfo($file_path, PATHINFO_EXTENSION) === 'php') {
    // Incluir el archivo solicitado
    include $file_path;
} else {
    // Archivo no encontrado
    header('Content-Type: application/json');
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'Endpoint not found',
        'requested_path' => $path,
        'available_endpoints' => [
            'login' => '/login.php',
            'subscription' => '/subscription.php',
            'valorador-config' => '/valorador-config.php',
            'health' => '/health.php',
            'get_leads' => '/get_leads.php',
            'get_valoraciones' => '/get_valoraciones.php',
            'valorador-public' => '/valorador/api_handler.php'
        ]
    ]);
}
?>
