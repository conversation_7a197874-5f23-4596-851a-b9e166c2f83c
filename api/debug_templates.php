<?php

declare(strict_types=1);

require_once 'config/bootstrap.php';

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    die('Error de conexión a la base de datos: ' . $e->getMessage());
}

echo "=== VERIFICANDO VARIABLES EN PLANTILLAS HTML ===\n";

try {
    $stmt = $pdo->query('SELECT id, nombre_interno_plantilla, contenido_html FROM email_plantillas_html LIMIT 3');
    
    while ($row = $stmt->fetch()) {
        echo 'Plantilla: ' . $row['nombre_interno_plantilla'] . "\n";
        
        // Buscar variables relacionadas con contenido IA
        if (strpos($row['contenido_html'], '{{contenido_ia_generado}}') !== false) {
            echo "  ✅ Usa {{contenido_ia_generado}}\n";
        }
        if (strpos($row['contenido_html'], '{{email_content}}') !== false) {
            echo "  ✅ Usa {{email_content}}\n";
        }
        if (strpos($row['contenido_html'], '{{contenido_email}}') !== false) {
            echo "  ✅ Usa {{contenido_email}}\n";
        }
        
        // Extraer todas las variables de la plantilla
        preg_match_all('/\{\{([^}]+)\}\}/', $row['contenido_html'], $matches);
        if (!empty($matches[1])) {
            echo '  Variables encontradas: ' . implode(', ', array_unique($matches[1])) . "\n";
        }
        echo "\n";
    }
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . "\n";
}

?>
