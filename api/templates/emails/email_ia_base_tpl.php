<?php
// /api/templates/emails/email_ia_base_tpl.php

// Protección XSS básica para variables que se imprimirán directamente.
// Asumimos que $cuerpoGeneradoIA_HTML ya está sanitizado o es HTML seguro.
if (isset($datosAgencia) && is_array($datosAgencia)) {
    $datosAgencia = array_map(function($value) {
        return is_string($value) ? htmlspecialchars($value, ENT_QUOTES, 'UTF-8') : $value;
    }, $datosAgencia);
}
if (isset($asuntoGeneradoIA) && is_string($asuntoGeneradoIA)) {
    $asuntoGeneradoIA = htmlspecialchars($asuntoGeneradoIA, ENT_QUOTES, 'UTF-8');
}

$nombreAgencia = $datosAgencia['nombre_display'] ?? 'Tu Agencia Inmobiliaria';
$logoUrl = $datosAgencia['logo_url'] ?? '';
$colorPrimarioAgencia = $datosAgencia['color_primario'] ?? '#051f33'; // Azul oscuro por defecto
$enlaceDarseDeBaja = '[URL_PARA_DARSE_DE_BAJA_AQUI]'; // Este debe ser reemplazado dinámicamente

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $asuntoGeneradoIA; ?></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f4f4f4;
        }
        .email-wrapper {
            padding: 20px;
            background-color: #f4f4f4; /* Fondo general del email */
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border: 1px solid #dddddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .email-header {
            background-color: <?php echo $colorPrimarioAgencia; ?>;
            padding: 20px;
            text-align: center;
        }
        .email-header img.logo {
            max-width: 180px;
            max-height: 70px;
            margin-bottom: 10px;
        }
        .email-header h1.agency-name {
            margin: 0;
            font-size: 22px;
            color: #ffffff !important; /* Asegurar color con !important */
            font-weight: bold;
        }
        .email-body {
            padding: 25px 30px;
            font-size: 16px;
            color: #333333;
        }
        .email-body p {
            margin-bottom: 15px;
        }
        .email-body h2 {
            color: <?php echo $colorPrimarioAgencia; ?>;
            font-size: 20px;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .email-body ul,
        .email-body ol {
            margin-bottom: 15px;
            padding-left: 25px;
        }
        .email-body strong {
            font-weight: bold;
        }
        .email-footer {
            background-color: #f0f0f0;
            color: #555555;
            padding: 20px;
            text-align: center;
            font-size: 12px;
        }
        .email-footer p {
            margin: 5px 0;
        }
        .email-footer a {
            color: <?php echo $colorPrimarioAgencia; ?>;
            text-decoration: none;
        }
        .unsubscribe-link {
             margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-container">
            <div class="email-header">
                <?php if ($logoUrl): ?>
                    <img src="<?php echo $logoUrl; ?>" alt="Logo <?php echo $nombreAgencia; ?>" class="logo">
                <?php endif; ?>
                <h1 class="agency-name"><?php echo $nombreAgencia; ?></h1>
            </div>

            <div class="email-body">
                <?php 
                // Asumimos que $cuerpoGeneradoIA_HTML es HTML seguro o ha sido pre-sanitizado.
                // Si necesitas sanitizarlo aquí, usa una librería como HTMLPurifier.
                // Para este ejemplo, lo imprimimos directamente.
                echo $cuerpoGeneradoIA_HTML ?? '<p>No se pudo generar el contenido del email.</p>'; 
                ?>
            </div>

            <div class="email-footer">
                <p>&copy; <?php echo date("Y"); ?> <?php echo $nombreAgencia; ?>. Todos los derechos reservados.</p>
                <p>
                    Si no deseas recibir más emails como este, puedes 
                    <a href="<?php echo $enlaceDarseDeBaja; ?>" class="unsubscribe-link">darte de baja aquí</a>.
                </p>
                <!-- Podrías añadir dirección física de la agencia si es necesario -->
            </div>
        </div>
    </div>
</body>
</html> 