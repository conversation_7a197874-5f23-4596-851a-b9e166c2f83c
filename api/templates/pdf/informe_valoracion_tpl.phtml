<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Informe de Valoración de Inmueble - <?php echo htmlspecialchars($datosAgencia['nombre_display']); ?></title>
    <style>
        /* * ======================================================================
         * INFORME DE VALORACIÓN INMOBILIARIA - PLANTILLA PHTML MODERNA (v5)
         * Optimizada para wkhtmltopdf con emojis y diseño mejorado
         * Descripción: Versión con CSS moderno, emojis, información más digerible
         * y diseño profesional optimizado para usuarios no expertos.
         * ======================================================================
         */

        /* --- Fuentes y Variables CSS --- */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

        :root {
            /* Colores de la agencia inyectados por PHP */
            --color-primario: <?php echo htmlspecialchars($coloresAgencia['primario'] ?? '#2563eb'); ?>;
            --color-secundario: <?php echo htmlspecialchars($coloresAgencia['secundario'] ?? '#1d4ed8'); ?>;

            /* Paleta de grises y de sistema */
            --color-texto-principal: #111827;
            --color-texto-secundario: #4b5563;
            --color-borde: #e5e7eb;
            --color-fondo-claro: #f9fafb;
            --color-fondo-blanco: #ffffff;
            --color-exito: #16a34a;
            --color-exito-fondo: #f0fdf4;
            --color-alerta: #c2410c;
            --color-alerta-fondo: #fffbeb;
            --color-error: #be123c;
            --color-error-fondo: #fef1f2;
        }

        /* --- Estilos Generales --- */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            color: var(--color-texto-principal);
            margin: 0;
            padding: 0;
            background-color: var(--color-fondo-blanco);
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .page-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        /* --- Utilidades --- */
        .text-bold { font-weight: 600; }
        .text-uppercase { text-transform: uppercase; }
        .letter-spacing-sm { letter-spacing: 0.5px; }
        .mt-1 { margin-top: 4px; }
        .mt-2 { margin-top: 8px; }
        .mt-4 { margin-top: 16px; }
        .mb-4 { margin-bottom: 16px; }

        /*
         * ======================================================================
         * Componentes Reutilizables
         * ======================================================================
        */

        /* --- Tarjeta Contenedora --- */
        .card {
            background-color: var(--color-fondo-blanco);
            border: 1px solid var(--color-borde);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            page-break-inside: avoid;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 16px;
        }

        .card-header .icon {
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            color: var(--color-primario);
        }

        .card-header h3 {
            margin: 0;
            font-size: 14pt;
            font-weight: 700;
            color: var(--color-primario);
        }

        /* --- Iconos de texto simples --- */
        .text-icon, .extra-item-icon {
            font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif;
        }

        .text-icon {
            color: var(--color-secundario);
            background-color: var(--color-fondo-claro);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }

        .text-icon svg {
            width: 26px;
            height: 26px;
            stroke-width: 1.8;
        }


        
        /* Asegurar que los emojis se rendericen correctamente */
        .text-icon::before {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        /* Fallback para navegadores que no soportan emojis */
        .no-emoji .text-icon {
            font-family: Arial, sans-serif;
        }

        /* --- Grid de 2 Columnas --- */
        .grid-2-cols {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        /* Características adicionales - compatible con wkhtmltopdf */
        .table-columns {
            width: 100%;
            border-collapse: separate;
            border-spacing: 10px 12px;
            margin-left: -10px;
            margin-right: -10px;
        }
        
        .table-columns td {
            width: 33.33%;
            vertical-align: top;
            padding: 0;
        }
        
        .extra-item {
            display: flex;
            align-items: center;
            background-color: var(--color-fondo-claro);
            padding: 10px 12px;
            border-radius: 8px;
            border: 1px solid var(--color-borde);
            font-size: 11pt;
            margin-bottom: 10px;
        }

        .extra-item-icon {
            display: inline-block;
            margin-right: 10px;
            width: 24px;
            height: 24px;
            text-align: center;
            vertical-align: middle;
            color: var(--color-primario);
            font-weight: bold;
            font-size: 16px;
            line-height: 24px;
            background-color: rgba(37, 99, 235, 0.1);
            border-radius: 50%;
        }

        .extra-item-label {
            font-weight: 600;
            color: var(--color-texto-principal);
        }
        
        /*
         * ======================================================================
         * Estructura del Informe
         * ======================================================================
        */

        /* --- Cabecera --- */
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding-bottom: 20px;
            border-bottom: 4px solid var(--color-primario);
        }

        .main-header .logo {
            max-height: 55px;
            max-width: 220px;
            object-fit: contain;
        }

        .header-info {
            text-align: right;
        }

        .header-info h1 {
            font-size: 19pt;
            font-weight: 700;
            color: var(--color-primario);
            margin: 0 0 4px 0;
        }

        .header-info p {
            font-size: 9pt;
            margin: 2px 0;
            color: var(--color-texto-secundario);
        }

        /* --- Título del Informe --- */
        .report-title {
            margin: 25px 0;
            text-align: left;
        }

        .report-title h2 {
            font-size: 24pt;
            font-weight: 700;
            margin: 0;
        }

        .report-title p {
            font-size: 12pt;
            color: var(--color-texto-secundario);
            margin: 6px 0 0 0;
        }

        /* --- Sección de Valoración Principal --- */
        .valuation-highlight {
            background-color: var(--color-primario);
            color: var(--color-fondo-blanco);
            border-radius: 16px;
            padding: 25px 30px;
            text-align: center;
            margin-top: 20px;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
        }

        .valuation-highlight .label {
            font-size: 14pt;
            font-weight: 600;
            opacity: 0.9;
            letter-spacing: 0.5px;
            margin: 0;
        }

        .valuation-highlight .price-range {
            font-size: 34pt;
            font-weight: 700;
            margin: 10px 0;
            line-height: 1.2;
        }

        .valuation-highlight .sub-values {
            display: flex;
            justify-content: center;
            align-items: center; /* Centra verticalmente */
            gap: 25px; /* Aumenta el espacio */
            margin-top: 15px;
            opacity: 0.95;
            font-size: 11pt; /* Aumenta el tamaño de la fuente */
        }

        .page-break {
            page-break-before: always;
        }
        
        /* --- Listas de Características --- */
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--color-borde);
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list .feature-name {
            display: flex;
            align-items: center;
            /* gap: 10px; */ /* Reemplazado por margen en el icono */
            color: var(--color-texto-secundario);
            font-size: 12pt;
        }

        .feature-list .feature-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }

        .feature-list .feature-icon svg {
            width: 20px;
            height: 20px;
            stroke-width: 1.8;
        }



        .feature-list .feature-value {
            font-weight: 600;
            color: var(--color-texto-principal);
            font-size: 13pt;
        }
        
        /* --- Estadísticas de Zona --- */
        .zone-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .stat-item {
            background-color: var(--color-fondo-claro);
            border: 1px solid var(--color-borde);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .stat-item .value {
            font-size: 20pt;
            font-weight: 700;
            color: var(--color-primario);
            margin: 0 0 4px 0;
        }

        .stat-item .label {
            font-size: 10pt;
            color: var(--color-texto-secundario);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* --- Gráficos de Barras para Características --- */
        .bar-chart-item {
            margin-top: 12px;
        }

        .bar-chart-item .chart-label {
            display: flex;
            justify-content: space-between;
            font-size: 11pt;
            margin-bottom: 6px;
        }
        
        .bar-chart-item .bar-bg {
            background-color: var(--color-borde);
            border-radius: 4px;
            height: 10px;
            overflow: hidden;
        }

        .bar-chart-item .bar-fg {
            background-color: var(--color-secundario);
            height: 100%;
            border-radius: 4px;
        }

        /* --- Análisis Comparativo Mejorado --- */
        .comparison-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-top: 16px;
        }

        .comparison-card {
            background-color: var(--color-fondo-claro);
            border: 2px solid var(--color-borde);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .comparison-card.positive {
            border-color: var(--color-exito);
            background-color: var(--color-exito-fondo);
        }

        .comparison-card.negative {
            border-color: var(--color-error);
            background-color: var(--color-error-fondo);
        }

        .comparison-card .percentage {
            font-size: 24pt;
            font-weight: 700;
            margin: 8px 0;
        }

        .comparison-card.positive .percentage {
            color: var(--color-exito);
        }

        .comparison-card.negative .percentage {
            color: var(--color-error);
        }

        .comparison-card .title {
            font-size: 12pt;
            font-weight: 600;
            color: var(--color-texto-principal);
            margin-bottom: 4px;
        }

        .comparison-card .explanation {
            font-size: 11pt;
            color: var(--color-texto-secundario);
            margin-top: 8px;
            line-height: 1.4;
        }

        /* --- Descargo de Responsabilidad y CTA --- */
        .disclaimer-box {
            background-color: var(--color-alerta-fondo);
            border: 1px solid var(--color-alerta);
            border-left-width: 4px;
            border-radius: 8px;
            padding: 16px;
            margin: 25px 0;
            font-size: 10pt;
            color: var(--color-alerta);
            page-break-inside: avoid;
        }
        
        .custom-text-box {
            background-color: var(--color-fondo-claro);
            border: 1px solid var(--color-borde);
            border-radius: 8px;
            padding: 16px;
            margin: 25px 0;
            font-style: italic;
            color: var(--color-texto-secundario);
        }

        .cta-section {
            background-color: var(--color-primario);
            color: var(--color-fondo-blanco);
            padding: 30px;
            border-radius: 16px;
            text-align: center;
            margin: 25px 0;
            page-break-inside: avoid;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        .cta-section h3 {
            font-size: 18pt;
            margin: 0 0 10px 0;
            font-weight: 700;
        }
        .cta-section p {
            font-size: 13pt;
            margin: 0 0 20px 0;
            opacity: 0.95;
        }
        .cta-section .cta-button {
            display: inline-block;
            background-color: var(--color-fondo-blanco);
            color: var(--color-primario);
            padding: 14px 32px;
            text-decoration: none;
            font-weight: 700;
            border-radius: 10px;
            margin-top: 8px;
            font-size: 13pt;
            border: 2px solid var(--color-fondo-blanco);
        }

        /* --- Pie de Página --- */
        .main-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--color-borde);
            text-align: center;
            font-size: 9pt;
            color: var(--color-texto-secundario);
            page-break-before: auto;
        }

        .main-footer strong {
            color: var(--color-texto-principal);
        }
        
        .main-footer .report-id {
            margin-top: 10px;
            font-size: 7.5pt;
            color: #9ca3af;
        }

    </style>
</head>
<body>
    <div class="page-container">

        <!-- ====================================================================== -->
        <!-- CABECERA -->
        <!-- ====================================================================== -->
        <header class="main-header">
            <div class="header-logo">
                <?php if (!empty($datosAgencia['logo_url'])): ?>
                    <img src="<?php echo htmlspecialchars($datosAgencia['logo_url']); ?>" alt="Logo <?php echo htmlspecialchars($datosAgencia['nombre_display']); ?>" class="logo">
                <?php endif; ?>
            </div>
            <div class="header-info">
                <h1><?php echo htmlspecialchars($datosAgencia['nombre_display']); ?></h1>
                <?php if (!empty($datosAgencia['sitio_web_url'])): ?>
                    <p><?php echo htmlspecialchars($datosAgencia['sitio_web_url']); ?></p>
                <?php endif; ?>
                <?php if (!empty($datosAgencia['telefono_contacto'])): ?>
                    <p>Tel: <?php echo htmlspecialchars($datosAgencia['telefono_contacto']); ?></p>
                <?php endif; ?>
                <?php if (!empty($datosAgencia['email_contacto_publico'])): ?>
                    <p>Email: <?php echo htmlspecialchars($datosAgencia['email_contacto_publico']); ?></p>
                <?php endif; ?>
                <p>Fecha de emisión: <?php echo date('d/m/Y'); ?></p>
            </div>
        </header>

        <main>
            <!-- ====================================================================== -->
            <!-- TÍTULO DEL INFORME Y DIRECCIÓN -->
            <!-- ====================================================================== -->
            <section class="report-title">
                <h2>Informe de valoración online</h2>
                <p>Estimación para la propiedad ubicada en:
                    <strong class="text-bold"><?php echo htmlspecialchars($datosValoracion['direccion'] ?? 'No especificada'); ?></strong>
                </p>
            </section>

            <!-- ====================================================================== -->
            <!-- VALORACIÓN DESTACADA -->
            <!-- ====================================================================== -->
            <section class="valuation-highlight">
                <p class="label">Rango de valoración estimado</p>
                <div class="price-range">
                    <?php echo number_format($datosValoracion['valor_estimado_min'] ?? 0, 0, ',', '.'); ?>€ -
                    <?php echo number_format($datosValoracion['valor_estimado_max'] ?? 0, 0, ',', '.'); ?>€
                </div>
                <?php
                    $valor_medio_calc = 0;
                    if (!empty($datosValoracion['valor_estimado_min']) && !empty($datosValoracion['valor_estimado_max'])) {
                        $valor_medio_calc = ($datosValoracion['valor_estimado_min'] + $datosValoracion['valor_estimado_max']) / 2;
                    }
                    $precio_m2_medio_calc = 0;
                    if ($valor_medio_calc > 0 && !empty($datosValoracion['superficie']) && $datosValoracion['superficie'] > 0) {
                        $precio_m2_medio_calc = $valor_medio_calc / $datosValoracion['superficie'];
                    }
                ?>
                <div class="sub-values">
                    <div style="text-align: center; margin-right: 20px;">
                        <span style="text-transform: uppercase; font-size: 10pt; opacity: 0.8; display: block; font-weight: 600;">Valoración Media</span>
                        <strong style="font-size: 14pt; font-weight: 700;"><?php echo number_format($valor_medio_calc, 0, ',', '.'); ?>€</strong>
                    </div>
                    <div style="text-align: center; margin-left: 20px;">
                        <span style="text-transform: uppercase; font-size: 10pt; opacity: 0.8; display: block; font-weight: 600;">Precio Medio / m²</span>
                        <strong style="font-size: 14pt; font-weight: 700;"><?php echo number_format($precio_m2_medio_calc, 0, ',', '.'); ?>€/m²</strong>
                    </div>
                </div>
            </section>

            <!-- DETALLES DE LA PROPIEDAD -->
            <div class="card">
                <div class="card-header">
                    <span class="text-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg></span>
                    <h3>Detalles de la propiedad</h3>
                </div>
                <ul class="feature-list">
                    <li>
                        <span class="feature-name">
                            <span class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"> <path stroke="none" d="M0 0h24v24H0z" fill="none"></path> <path d="M3 21l18 0"></path> <path d="M9 8l1 0"></path> <path d="M9 12l1 0"></path> <path d="M9 16l1 0"></path> <path d="M14 8l1 0"></path> <path d="M14 12l1 0"></path> <path d="M14 16l1 0"></path> <path d="M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16"></path> </svg></span>
                            Tipo de propiedad
                        </span>
                        <span class="feature-value"><?php echo htmlspecialchars(ucfirst($datosValoracion['tipo_principal'] ?? 'No especificado')); ?></span>
                    </li>
                    <li>
                        <span class="feature-name">
                            <span class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path></svg></span>
                            Superficie
                        </span>
                        <span class="feature-value"><?php echo htmlspecialchars($datosValoracion['superficie'] ?? 'N/A'); ?> m²</span>
                    </li>
                    <li>
                        <span class="feature-name">
                            <span class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.8" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M7 9m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0"></path><path d="M22 17v-3h-20v3"></path><path d="M2 14h20"></path><path d="M17 14v-2a2 2 0 0 0 -2 -2h-6a2 2 0 0 0 -2 2v2"></path></svg></span>
                            Habitaciones
                        </span>
                        <span class="feature-value"><?php echo htmlspecialchars($datosValoracion['habitaciones'] ?? 'N/A'); ?></span>
                    </li>
                    <li>
                        <span class="feature-name">
                            <span class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.8" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M4 12h16a1 1 0 0 1 1 1v3a4 4 0 0 1 -4 4h-10a4 4 0 0 1 -4 -4v-3a1 1 0 0 1 1 -1z"></path><path d="M6 12v-7a2 2 0 0 1 2 -2h3v2.25"></path><path d="M4 21l1 -1.5"></path><path d="M20 21l-1 -1.5"></path></svg></span>
                            Baños
                        </span>
                        <span class="feature-value"><?php echo htmlspecialchars($datosValoracion['banos'] ?? 'N/A'); ?></span>
                    </li>
                    <?php if (!empty($datosValoracion['ano_construccion_catastro']) && $datosValoracion['ano_construccion_catastro'] > 0): ?>
                    <li>
                        <span class="feature-name">
                            <span class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg></span>
                            Año de construcción
                        </span>
                        <span class="feature-value"><?php echo htmlspecialchars($datosValoracion['ano_construccion_catastro']); ?></span>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <?php 
                $extrasPresentes = [];
                if (!empty($datosValoracion['extras'])) {
                    $decodedExtras = json_decode($datosValoracion['extras'], true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($decodedExtras)) {
                        $extrasPresentes = $decodedExtras;
                    }
                }
                ?>

                <?php if (!empty($extrasPresentes)): ?>
                <div style="border-top: 1px solid var(--color-borde); margin-top: 20px; padding-top: 20px;">
                    <h4 style="font-size: 13pt; font-weight: 600; margin-top: 0; margin-bottom: 15px;">Características adicionales</h4>
                    <?php
                        // Array con las etiquetas de las características utilizando un check estándar para todos
                        $extras_map_svg = [
                            'piscina' => ['label' => 'Piscina', 'svg' => '✓'],
                            'garaje' => ['label' => 'Garaje', 'svg' => '✓'],
                            'parking' => ['label' => 'Parking', 'svg' => '✓'],
                            'jardin' => ['label' => 'Jardín', 'svg' => '✓'],
                            'ascensor' => ['label' => 'Ascensor', 'svg' => '✓'],
                            'trastero' => ['label' => 'Trastero', 'svg' => '✓'],
                            'terraza' => ['label' => 'Terraza', 'svg' => '✓'],
                            'aireacondicionado' => ['label' => 'Aire Acond.', 'svg' => '✓'],
                            'nlp_esta_reformado' => ['label' => 'Reformado', 'svg' => '✓'],
                            'nlp_tiene_vistas_mar' => ['label' => 'Vistas al mar', 'svg' => '✓'],
                            'nlp_es_primera_linea' => ['label' => 'Primera línea', 'svg' => '✓'],
                            'nlp_es_luminoso' => ['label' => 'Luminoso', 'svg' => '✓'],
                            'nlp_tiene_calefaccion' => ['label' => 'Calefacción', 'svg' => '✓'],
                            'nlp_es_exterior' => ['label' => 'Exterior', 'svg' => '✓'],
                        ];
                    ?>
                    <table class="table-columns">
                        <tr>
                        <?php 
                            $counter = 0;
                            foreach ($extrasPresentes as $extra):
                                $extraKey = strtolower(preg_replace('/\s+/', '', $extra));
                                if (isset($extras_map_svg[$extraKey])):
                                    // Comienza una nueva fila después de cada 3 elementos
                                    if ($counter > 0 && $counter % 3 == 0) echo '</tr><tr>';
                        ?>
                                <td>
                                    <div class="extra-item">
                                        <span class="extra-item-icon"><?php echo $extras_map_svg[$extraKey]['svg']; ?></span>
                                        <span class="extra-item-label"><?php echo htmlspecialchars($extras_map_svg[$extraKey]['label']); ?></span>
                                    </div>
                                </td>
                        <?php 
                                    $counter++;
                                endif;
                            endforeach; 
                            
                            // Rellena las celdas vacías para mantener la estructura de la tabla
                            while ($counter % 3 != 0) {
                                echo '<td></td>';
                                $counter++;
                            }
                        ?>
                        </tr>
                    </table>
                </div>
                <?php endif; ?>
            </div>

            <!-- SALTO DE PÁGINA PARA LA SEGUNDA PÁGINA -->
            <div class="page-break"></div>

            <!-- ANÁLISIS DE LA ZONA -->
            <div class="card">
                <div class="card-header">
                    <span class="text-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="20" x2="18" y2="10"></line><line x1="12" y1="20" x2="12" y2="4"></line><line x1="6" y1="20" x2="6" y2="14"></line></svg></span>
                    <h3>Análisis de la zona</h3>
                </div>
                <div class="zone-stats-grid" style="grid-template-columns: repeat(4, 1fr);">
                    <div class="stat-item">
                        <div class="value"><?php echo number_format($datosValoracion['precio_m2_promedio'] ?? 0, 0, ',', '.'); ?>€/m²</div>
                        <div class="label">Precio m² zona</div>
                    </div>
                    <div class="stat-item">
                        <div class="value"><?php echo number_format($datosValoracion['tamano_promedio'] ?? 0, 0, ',', '.'); ?> m²</div>
                        <div class="label">Tamaño medio zona</div>
                    </div>
                    <div class="stat-item">
                        <div class="value"><?php echo number_format($datosValoracion['numero_propiedades_analizadas'] ?? 0, 0, ',', '.'); ?></div>
                        <div class="label">Propiedades analizadas</div>
                    </div>
                    <div class="stat-item">
                        <div class="value"><?php echo number_format($datosValoracion['distancia_al_centroide_km'] ?? 0, 1, ',', '.'); ?> km</div>
                        <div class="label">Radio de análisis</div>
                    </div>
                </div>
            </div>

            <!-- COMPARATIVA CON LA ZONA -->
            <div class="card">
                <div class="card-header">
                    <span class="text-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3v18h18"></path><path d="M18.7 8a2.4 2.4 0 0 0-3.4 0L12 11.4l-2.3-2.3a2.4 2.4 0 0 0-3.4 0L3 12.4"></path></svg></span>
                    <h3>Comparativa con la zona</h3>
                </div>
                <div class="comparison-cards">
                    <?php
                        $precio_diff = $datosValoracion['precio_vs_zona_porcentaje'] ?? 0;
                        $precio_vs_zona_class = $precio_diff > 5 ? 'positive' : ($precio_diff < -5 ? 'negative' : '');
                        $precio_vs_zona_formatted = ($precio_diff >= 0 ? '+' : '') . number_format($precio_diff, 1, ',', '.') . '%';
                        $precio_vs_zona_explanation = 'Tu propiedad está en línea con el precio promedio de la zona.';
                        if ($precio_diff > 5) {
                            $precio_vs_zona_explanation = 'El precio por m² de tu propiedad es superior al promedio de la zona.';
                        } elseif ($precio_diff < -5) {
                            $precio_vs_zona_explanation = 'El precio por m² de tu propiedad es inferior al promedio de la zona.';
                        }
                    ?>
                    <div class="comparison-card <?php echo $precio_vs_zona_class; ?>">
                        <div class="title">Precio por m² vs. Zona</div>
                        <div class="percentage"><?php echo $precio_vs_zona_formatted; ?></div>
                        <div class="explanation"><?php echo $precio_vs_zona_explanation; ?></div>
                    </div>

                    <?php
                        $tamano_diff = $datosValoracion['tamano_vs_zona_porcentaje'] ?? 0;
                        $tamano_vs_zona_class = $tamano_diff > 10 ? 'positive' : ($tamano_diff < -10 ? 'negative' : '');
                        $tamano_vs_zona_formatted = ($tamano_diff >= 0 ? '+' : '') . number_format($tamano_diff, 1, ',', '.') . '%';
                        $tamano_vs_zona_explanation = 'Tu propiedad tiene un tamaño similar al promedio de la zona.';
                        if ($tamano_diff > 10) {
                            $tamano_vs_zona_explanation = 'Tu propiedad es más grande que el promedio de la zona.';
                        } elseif ($tamano_diff < -10) {
                            $tamano_vs_zona_explanation = 'Tu propiedad es más pequeña que el promedio de la zona.';
                        }
                    ?>
                    <div class="comparison-card <?php echo $tamano_vs_zona_class; ?>">
                        <div class="title">Tamaño vs. Zona</div>
                        <div class="percentage"><?php echo $tamano_vs_zona_formatted; ?></div>
                        <div class="explanation"><?php echo $tamano_vs_zona_explanation; ?></div>
                    </div>
                </div>
            </div>

            <!-- CARACTERÍSTICAS COMUNES -->
            <div class="card">
                 <div class="card-header">
                    <span class="text-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg></span>
                    <h3>Características comunes de propiedades en la zona</h3>
                </div>
                <div class="grid-2-cols">
                    <div class="bar-chart-item">
                        <div class="chart-label"><span>Con Piscina</span><span><?php echo number_format($datosValoracion['porcentaje_con_piscina'] ?? 0, 0); ?>%</span></div>
                        <div class="bar-bg"><div class="bar-fg" style="width: <?php echo $datosValoracion['porcentaje_con_piscina'] ?? 0; ?>%;"></div></div>
                    </div>
                    <div class="bar-chart-item">
                        <div class="chart-label"><span>Con Parking</span><span><?php echo number_format($datosValoracion['porcentaje_con_parking'] ?? 0, 0); ?>%</span></div>
                        <div class="bar-bg"><div class="bar-fg" style="width: <?php echo $datosValoracion['porcentaje_con_parking'] ?? 0; ?>%;"></div></div>
                    </div>
                    <div class="bar-chart-item">
                        <div class="chart-label"><span>Con Ascensor</span><span><?php echo number_format($datosValoracion['porcentaje_con_ascensor'] ?? 0, 0); ?>%</span></div>
                        <div class="bar-bg"><div class="bar-fg" style="width: <?php echo $datosValoracion['porcentaje_con_ascensor'] ?? 0; ?>%;"></div></div>
                    </div>
                    <div class="bar-chart-item">
                        <div class="chart-label"><span>Con Terraza</span><span><?php echo number_format($datosValoracion['porcentaje_con_terraza'] ?? 0, 0); ?>%</span></div>
                        <div class="bar-bg"><div class="bar-fg" style="width: <?php echo $datosValoracion['porcentaje_con_terraza'] ?? 0; ?>%;"></div></div>
                    </div>
                    <div class="bar-chart-item">
                        <div class="chart-label"><span>Reformadas</span><span><?php echo number_format($datosValoracion['porcentaje_reformadas'] ?? 0, 0); ?>%</span></div>
                        <div class="bar-bg"><div class="bar-fg" style="width: <?php echo $datosValoracion['porcentaje_reformadas'] ?? 0; ?>%;"></div></div>
                    </div>
                </div>
            </div>

            <!-- SALTO DE PÁGINA PARA LA TERCERA PÁGINA -->
            <div class="page-break"></div>

            <!-- CONSEJO DEL ASESOR -->
            <?php if (!empty($textoSegunNecesidad)): ?>
            <div class="card">
                <div class="card-header">
                    <span class="text-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2.5a5.5 5.5 0 0 1 5.5 5.5c0 2.22-1.23 4.1-3 5.02V15.5a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-2.98c-1.77-.92-3-2.8-3-5.02A5.5 5.5 0 0 1 12 2.5z"></path><path d="M12 18.5v3"></path><path d="M8.5 21.5h7"></path></svg></span>
                    <h3>Un consejo de tu asesor</h3>
                </div>
                <p style="font-size: 12pt; line-height: 1.6; color: var(--color-texto-principal); font-style: italic;">
                    <?php echo nl2br(htmlspecialchars($textoSegunNecesidad)); ?>
                </p>
            </div>
            <?php endif; ?>

            <!-- ====================================================================== -->
            <!-- LLAMADA A LA ACCIÓN (CTA) -->
            <!-- ====================================================================== -->
            <section class="cta-section">
                <h3>¿Quieres dar el siguiente paso?</h3>
                <p>Un experto de <?php echo htmlspecialchars($datosAgencia['nombre_display']); ?> puede realizar un análisis presencial y detallado para confirmar el valor de tu propiedad y diseñar la mejor estrategia de venta.</p>
                <?php if (!empty($datosAgencia['descripcion_breve'])): ?>
                    <p style="font-style: italic; margin-top: 10px;"><?php echo htmlspecialchars($datosAgencia['descripcion_breve']); ?></p>
                <?php endif; ?>
                <a href="<?php echo htmlspecialchars($datosAgencia['cta_contacto_url'] ?? '#'); ?>" class="cta-button">Contactar con un asesor experto</a>
            </section>

            <!-- ====================================================================== -->
            <!-- DESCARGO DE RESPONSABILIDAD -->
            <!-- ====================================================================== -->
            <div class="disclaimer-box">
                <p><strong class="text-bold">IMPORTANTE:</strong> Esta valoración es una estimación orientativa generada por un modelo de inteligencia artificial y datos de mercado. No sustituye una tasación profesional y no contempla una inspección física de la propiedad ni sus características específicas no informadas. Para una valoración vinculante, es necesaria la visita de un técnico cualificado.</p>
            </div>
        </main>

        <!-- ====================================================================== -->
        <!-- PIE DE PÁGINA -->
        <!-- ====================================================================== -->
        <footer class="main-footer">
            <p><strong class="text-bold"><?php echo htmlspecialchars($datosAgencia['nombre_display']); ?></strong></p>
            <?php if (!empty($datosAgencia['direccion_fisica'])): ?>
                <p><?php echo htmlspecialchars($datosAgencia['direccion_fisica']); ?></p>
            <?php endif; ?>
            <?php if (!empty($datosAgencia['telefono_contacto'])): ?>
                <p>Tel: <?php echo htmlspecialchars($datosAgencia['telefono_contacto']); ?></p>
            <?php endif; ?>
            <?php if (!empty($datosAgencia['email_contacto_publico'])): ?>
                <p>Email: <?php echo htmlspecialchars($datosAgencia['email_contacto_publico']); ?></p>
            <?php endif; ?>
            <?php if (!empty($datosAgencia['whatsapp_numero'])): ?>
                <p>WhatsApp: <?php echo htmlspecialchars($datosAgencia['whatsapp_numero']); ?></p>
            <?php endif; ?>
            <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($datosAgencia['nombre_display']); ?>. Todos los derechos reservados.</p>
            <p class="report-id">Informe generado el <?php echo date('d/m/Y H:i'); ?> | ID: <?php echo htmlspecialchars($datosValoracion['uuid'] ?? 'N/A'); ?></p>
        </footer>

    </div>
</body>
</html>