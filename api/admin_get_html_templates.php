<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect): ' . $e->getMessage()]);
    exit();
}

try {
    // Obtener parámetros de filtrado y paginación
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(10, (int)$_GET['limit'])) : 20;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $tipo_filter = isset($_GET['tipo']) ? trim($_GET['tipo']) : '';
    $activa_filter = isset($_GET['activa']) ? $_GET['activa'] : '';
    
    $offset = ($page - 1) * $limit;
    
    // Construir consulta base
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(nombre_interno_plantilla LIKE ? OR descripcion LIKE ? OR asunto_predeterminado LIKE ?)";
        $search_param = "%{$search}%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    if (!empty($tipo_filter)) {
        $where_conditions[] = "tipo_plantilla = ?";
        $params[] = $tipo_filter;
    }
    
    if ($activa_filter !== '') {
        $where_conditions[] = "activa = ?";
        $params[] = (int)$activa_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Consulta para contar total
    $count_sql = "SELECT COUNT(*) as total FROM email_plantillas_html $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_count = $count_stmt->fetchColumn();
    
    // Consulta principal con paginación
    $sql = "
        SELECT 
            id,
            nombre_interno_plantilla,
            descripcion,
            asunto_predeterminado,
            tipo_plantilla,
            activa,
            fecha_creacion,
            fecha_modificacion,
            CHAR_LENGTH(contenido_html) as html_size,
            (SELECT COUNT(*) FROM sequence_steps WHERE email_template_id = email_plantillas_html.id) as usage_count
        FROM email_plantillas_html 
        $where_clause
        ORDER BY 
            CASE 
                WHEN tipo_plantilla = 'secuencia_email' THEN 1 
                WHEN tipo_plantilla = 'informe_valoracion' THEN 2
                WHEN tipo_plantilla = 'general' THEN 3
                ELSE 4 
            END,
            nombre_interno_plantilla ASC
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $pdo->prepare($sql);
    
    // Añadir parámetros de paginación
    $pagination_params = $params;
    $pagination_params[] = $limit;
    $pagination_params[] = $offset;
    
    $stmt->execute($pagination_params);
    $templates = $stmt->fetchAll();
    
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => $templates,
        'pagination' => [
            'total_items' => (int)$total_count,
            'total_pages' => ceil($total_count / $limit),
            'current_page' => $page,
            'limit' => $limit
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error en la operación de base de datos: ' . $e->getMessage()]);
    exit;
}
