<?php
declare(strict_types=1);
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Api\lib\Database;
use Api\lib\Logger;
header('Content-Type: application/json');
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
$current_agency_id = null;
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            JWT::$leeway = 60; // Allowance for clock skew
            $decodedToken = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
            $current_agency_id = $decodedToken->agency_id ?? null;
            if (!$current_agency_id) {
                Logger::warning('[get_agency_details.php] Agency ID no encontrado en el token.');
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Token inválido: Agencia no identificada.']);
                exit();
            }
        } catch (Exception $e) {
            Logger::error('[get_agency_details.php] Error de Token: ' . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        }
    } else {
        Logger::warning('[get_agency_details.php] Formato de Authorization header incorrecto.');
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    Logger::warning('[get_agency_details.php] No se proporcionó token de autorización.');
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $pdo = null;
    try {
        $pdo = Database::getInstance()->getConexion();
        $stmt = $pdo->prepare("SELECT name as agency_name FROM agencies WHERE id = ?");
        $stmt->execute([$current_agency_id]);
        $agency = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($agency) {
            http_response_code(200);
            echo json_encode(['success' => true, 'agency_name' => $agency['agency_name']]);
        } else {
            Logger::warning('[get_agency_details.php] Detalles de agencia no encontrados para agency_id: ' . $current_agency_id);
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Detalles de la agencia no encontrados.']);
        }
    } catch (Exception $e) {
        Logger::error('[get_agency_details.php] Error al obtener detalles de la agencia: ' . $e->getMessage(), ['agency_id' => $current_agency_id]);
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error interno al obtener detalles de la agencia.']);
    }
} else {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
}
?> 