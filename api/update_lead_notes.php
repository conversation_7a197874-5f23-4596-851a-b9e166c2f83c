<?php
declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

function get_decoded_jwt_payload_notes_lead() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded;
            } catch (\Throwable $e) {
                error_log("JWT Decode Error en update_lead_notes: " . $e->getMessage());
                if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                    ini_set('display_errors', $previous_display_errors);
                }
            }
        }
    }
    return null;
}

$request_method = $_SERVER["REQUEST_METHOD"];
$content_type = isset($_SERVER["CONTENT_TYPE"]) ? trim($_SERVER["CONTENT_TYPE"]) : '';

if ($request_method !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit;
}

if (strpos($content_type, 'application/json') === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Content-Type debe ser application/json.']);
    exit;
}

$decoded_payload = get_decoded_jwt_payload_notes_lead();
$user_id = $decoded_payload->user_id ?? null;
$agency_id = $decoded_payload->agency_id ?? null;

if (!$user_id || !$agency_id) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Token inválido o información de agencia/usuario faltante.']);
    exit;
}

$raw_data = file_get_contents("php://input");
$data = json_decode($raw_data, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'JSON malformado.']);
    exit;
}

$lead_id = $data['lead_id'] ?? null;
$notas = $data['notas'] ?? null; // Permitir notas vacías o null para borrarlas

if (!is_numeric($lead_id) || $lead_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de lead inválido.']);
    exit;
}

if ($notas !== null && !is_string($notas)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Las notas deben ser un texto.']);
    exit;
}

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Verificar que el lead pertenece a un cliente de la agencia del usuario
    $stmt_verify = $pdo->prepare("
        SELECT vl.id
        FROM valorador_leads vl
        INNER JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
        WHERE vl.id = :lead_id AND cv.agency_id = :agency_id
    ");
    $stmt_verify->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
    $stmt_verify->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
    $stmt_verify->execute();

    if ($stmt_verify->rowCount() === 0) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Acceso denegado: El lead no pertenece a su agencia o no existe.']);
        exit;
    }

    $stmt_update = $pdo->prepare("UPDATE valorador_leads SET notas = :notas, fecha_modificacion = NOW() WHERE id = :lead_id");
    $stmt_update->bindParam(':notas', $notas, PDO::PARAM_STR);
    $stmt_update->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);

    if ($stmt_update->execute()) {
        if ($stmt_update->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => 'Notas del lead actualizadas correctamente.']);
        } else {
            // Esto puede pasar si las notas son iguales a las existentes, lo cual no es un error.
            echo json_encode(['success' => true, 'message' => 'No se realizaron cambios en las notas (posiblemente eran iguales).']);
        }
    } else {
        http_response_code(500);
        error_log("Error al actualizar notas del lead " . $lead_id . " por user " . $user_id);
        echo json_encode(['success' => false, 'message' => 'Error al actualizar las notas en la base de datos.']);
    }

} catch (PDOException $e) {
    error_log("Error de BD en update_lead_notes.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB).']);
} catch (Exception $e) {
    error_log("Error general en update_lead_notes.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}

?> 