<?php
// api/procesar_notificacion_task.php
declare(strict_types=1);

require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\BrevoService;
use Api\lib\EmailVariablesService;
use Api\lib\Database;
use Api\lib\Logger;

// Habilitar la visualización de errores para depuración en Cloud Run
ini_set('display_errors', '1');
error_reporting(E_ALL);

// --- INICIO: DEPURACIÓN Y LECTURA DE PAYLOAD ---
error_log('[NOTIFICATION_TASK_DEBUG] ==> Script procesar_notificacion_task.php INVOCADO.');
error_log('[NOTIFICATION_TASK_DEBUG] Request Method: ' . $_SERVER['REQUEST_METHOD']);

$jsonPayload = file_get_contents('php://input');
error_log('[NOTIFICATION_TASK_DEBUG] Raw Payload: ' . $jsonPayload);

$data = json_decode($jsonPayload, true);
error_log('[NOTIFICATION_TASK_DEBUG] Decoded Payload: ' . print_r($data, true));

$leadId = $data['lead_id'] ?? null;
$clienteValoradorId = $data['cliente_valorador_id'] ?? null;
$isNewLead = $data['is_new_lead'] ?? true;

error_log("[NOTIFICATION_TASK_DEBUG] Parsed lead_id: {$leadId}, cliente_valorador_id: {$clienteValoradorId}, is_new_lead: " . ($isNewLead ? 'true' : 'false'));
// --- FIN: DEPURACIÓN Y LECTURA DE PAYLOAD ---

// --- Task Handler Security ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log('[NOTIFICATION_TASK_DEBUG] ==> SALIDA PREMATURA: Método no es POST.');
    http_response_code(405);
    echo 'Method Not Allowed';
    exit;
}

if (!$leadId || !$clienteValoradorId) {
    http_response_code(400);
    Logger::error('Notification Task Handler: Payload inválido.', ['payload' => $jsonPayload]);
    echo 'Payload inválido.';
    exit;
}

Logger::info("Notification Task Handler: Iniciando proceso para Lead ID: {$leadId}, Cliente Valorador ID: {$clienteValoradorId}, Nuevo Lead: " . ($isNewLead ? 'Sí' : 'No'));

try {
    $dbInstance = Database::getInstance();
    $db = $dbInstance->getConnection();

    // 1. Obtener email de notificaciones del cliente valorador
    $stmt = $db->prepare('SELECT email_notificaciones_leads, nombre_display, client_identifier FROM clientes_valorador WHERE id = ?');
    $stmt->execute([$clienteValoradorId]);
    $clienteData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$clienteData || empty($clienteData['email_notificaciones_leads'])) {
        Logger::warning("No se puede enviar notificación: email de notificaciones no configurado para cliente_valorador_id: {$clienteValoradorId}");
        http_response_code(200); // No es un error, simplemente no hay email configurado
        echo "Email de notificaciones no configurado";
        exit;
    }

    // 2. Obtener plantilla de notificación
    $templateName = $isNewLead ? 'notificacion_nuevo_lead' : 'notificacion_lead_existente';
    $stmt = $db->prepare('SELECT asunto_predeterminado, contenido_html FROM email_plantillas_html WHERE nombre_interno_plantilla = ? AND activa = 1');
    $stmt->execute([$templateName]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$template) {
        Logger::error("Plantilla de notificación no encontrada: {$templateName}");
        http_response_code(500);
        echo "Plantilla de notificación no encontrada: {$templateName}";
        exit;
    }

    // 3. Obtener variables del lead para la notificación
    $variables = EmailVariablesService::getVariablesForLead($leadId);

    // 4. Variables adicionales para la notificación
    $additionalVariables = [
        '{{tipo_evento}}' => $isNewLead ? 'Nuevo lead captado' : 'Lead existente - Nueva valoración',
        '{{dashboard_url}}' => rtrim(DASHBOARD_BASE_URL, '/') . '/leads/' . $leadId,
        '{{valorador_url}}' => 'https://' . $clienteData['client_identifier'] . '.inmoautomation.com/valora/',
        '{{agencia_nombre}}' => $clienteData['nombre_display'], // Para compatibilidad con plantillas
        '{{current_year}}' => date('Y'), // Variable faltante en la plantilla
    ];

    $allVariables = array_merge($variables, $additionalVariables);

    // 5. Procesar plantilla - Primero eliminar sintaxis Handlebars
    $cuerpoHtml = $template['contenido_html'];

    // Eliminar bloques condicionales Handlebars que no se pueden procesar con str_replace
    $cuerpoHtml = preg_replace('/\{\{#if [^}]+\}\}(.*?)\{\{\/if\}\}/s', '$1', $cuerpoHtml);

    // Reemplazar variables
    $asunto = str_replace(array_keys($allVariables), array_values($allVariables), $template['asunto_predeterminado']);
    $cuerpoHtml = str_replace(array_keys($allVariables), array_values($allVariables), $cuerpoHtml);

    Logger::info("Plantilla procesada para Lead ID: {$leadId}. Asunto: {$asunto}");

    // 6. Enviar email
    $brevoService = new BrevoService();
    $customFromEmail = $clienteData['client_identifier'] . '@inmoautomation.com';
    $customFromName = 'InmoAutomation - ' . $clienteData['nombre_display'];

    $result = $brevoService->sendEmail(
        $clienteData['email_notificaciones_leads'],
        $clienteData['nombre_display'],
        $asunto,
        $cuerpoHtml,
        '', // textBody
        $customFromEmail,
        $customFromName
    );

    if ($result['success']) {
        Logger::info("Notificación de nuevo lead enviada exitosamente para Lead ID: {$leadId}. Message ID: " . ($result['message_id'] ?? 'N/A'));
        http_response_code(200);
        echo "Notificación enviada exitosamente. Message ID: " . ($result['message_id'] ?? 'N/A');
    } else {
        Logger::error("Error enviando notificación de nuevo lead para Lead ID: {$leadId}. Error: " . ($result['error'] ?? 'Desconocido'));
        http_response_code(500);
        echo "Error enviando notificación: " . ($result['error'] ?? 'Desconocido');
    }

} catch (Exception $e) {
    Logger::error("Excepción en Notification Task Handler para Lead ID: {$leadId}. Error: " . $e->getMessage(), [
        'lead_id' => $leadId,
        'cliente_valorador_id' => $clienteValoradorId,
        'is_new_lead' => $isNewLead,
        'trace' => $e->getTraceAsString()
    ]);
    http_response_code(500);
    echo "Error interno: " . $e->getMessage();
}
?>
