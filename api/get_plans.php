<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en get_plans: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

try {
    $sql = "SELECT id, name, price_monthly FROM plans WHERE is_active = 1 ORDER BY display_order ASC, price_monthly ASC";
    $stmt = $pdo->query($sql);
    $plans = $stmt->fetchAll();

    echo json_encode(['success' => true, 'data' => $plans]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al obtener los planes: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error en la consulta a la base de datos.']);
    exit;
}
