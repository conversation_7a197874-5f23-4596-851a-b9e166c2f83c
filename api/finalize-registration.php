<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// api/finalize-registration.php
use Api\lib\Logger;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
Logger::info('[finalize-registration.php] Script iniciado.');
// Las cabeceras CORS y el manejo de OPTIONS ahora son gestionados por cors.php
header('Content-Type: application/json');
// Stripe y JWT secrets
$stripeSecretKey = STRIPE_SECRET_KEY;
$jwtSecretKey = JWT_SECRET;
if (!$stripeSecretKey || !$jwtSecretKey) {
    Logger::critical('[finalize-registration.php] Faltan STRIPE_SECRET_KEY o JWT_SECRET.');
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor: Configuración incompleta.']);
    exit();
}
\Stripe\Stripe::setApiKey($stripeSecretKey);
// Conexión DB
$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    Logger::critical('[finalize-registration.php] Error de conexión a la base de datos: ' . $conn->connect_error);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor: No se pudo conectar a la base de datos.']);
    exit();
}
$conn->set_charset(DB_CHARSET);
$input = json_decode(file_get_contents('php://input'), true);
Logger::debug('[finalize-registration.php] Raw input received:', $input);
// Campos esperados desde el frontend después de que signup.php devolvió 'requires_action'
// y el cliente manejó la acción del PaymentIntent de la factura.
$stripeSubscriptionId = $input['stripeSubscriptionId'] ?? null;
$stripeCustomerId = $input['stripeCustomerId'] ?? null; 
$paymentIntentId = $input['paymentIntentId'] ?? null; // Puede ser que el frontend envíe esto si lo tiene.
// Datos del formulario original (excepto contraseña, que el frontend debe enviar explícitamente)
$fullName = $input['originalFormData']['fullName'] ?? null;
$email = $input['originalFormData']['email'] ?? null;
$password = $input['originalFormData']['password'] ?? null; // El frontend DEBE enviar la contraseña aquí
$plan_slug = $input['originalFormData']['plan_slug'] ?? null;
$billing_cycle = $input['originalFormData']['billing_cycle'] ?? null;
$nombre_agencia = $input['originalFormData']['nombre_agencia'] ?? null; // CORRECCIÓN: Leer nombre de agencia
$requiredFields = compact('stripeSubscriptionId', 'stripeCustomerId', 'fullName', 'email', 'password', 'plan_slug', 'billing_cycle');
$missing_fields = array_keys(array_filter($requiredFields, fn($value) => empty($value)));
if (!empty($missing_fields)) {
    Logger::warning('[finalize-registration.php] Campos obligatorios faltantes.', ['missing_fields' => $missing_fields, 'input' => $input]);
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Faltan datos para finalizar el registro.', 'missing_fields' => $missing_fields]);
    exit();
}
$transaction_started = false;
try {
    Logger::info('[finalize-registration.php] Verificando suscripción y PaymentIntent.', ['subscription_id' => $stripeSubscriptionId, 'payment_intent_id_from_frontend' => $paymentIntentId]);
    // Recuperar la suscripción de Stripe para obtener la última factura y su payment_intent
    $subscription = \Stripe\Subscription::retrieve(
        $stripeSubscriptionId,
        ['expand' => ['latest_invoice']] // No necesitamos expandir el payment_intent aquí si usaremos el PI del frontend
    );
    if (!$subscription) {
        throw new Exception("No se pudo recuperar la suscripción de Stripe: {$stripeSubscriptionId}");
    }
    $agency_name_from_signup_metadata = $subscription->metadata['agency_name_on_signup'] ?? null;
    Logger::info('[finalize-registration.php] Nombre de agencia desde metadata de Stripe:', ['agency_name_on_signup' => $agency_name_from_signup_metadata]);
    $payment_intent_to_check = null;
    if (!empty($paymentIntentId)) {
        Logger::info('[finalize-registration.php] Se recibió paymentIntentId del frontend. Intentando recuperarlo.', ['pi_id' => $paymentIntentId]);
        try {
            $retrieved_pi_from_frontend = \Stripe\PaymentIntent::retrieve($paymentIntentId);
            // Verificar que este PI esté asociado a la factura de la suscripción
            if ($retrieved_pi_from_frontend && $retrieved_pi_from_frontend->invoice) {
                $invoice_of_pi = \Stripe\Invoice::retrieve($retrieved_pi_from_frontend->invoice);
                if ($invoice_of_pi && $invoice_of_pi->subscription === $subscription->id) {
                    $payment_intent_to_check = $retrieved_pi_from_frontend;
                    Logger::info('[finalize-registration.php] PaymentIntent del frontend recuperado y verificado contra la suscripción.', ['pi_id' => $payment_intent_to_check->id, 'status' => $payment_intent_to_check->status]);
                } else {
                    Logger::warning('[finalize-registration.php] PaymentIntent del frontend no coincide con la suscripción.', ['pi_id' => $paymentIntentId, 'pi_invoice_id' => $retrieved_pi_from_frontend->invoice, 'invoice_subscription' => $invoice_of_pi ? $invoice_of_pi->subscription : 'N/A', 'target_subscription_id' => $subscription->id]);
                }
            } else {
                 Logger::warning('[finalize-registration.php] PaymentIntent del frontend no tiene una factura asociada o no se pudo recuperar.', ['pi_id' => $paymentIntentId]);
            }
        } catch (Exception $e) {
            Logger::error('[finalize-registration.php] Error al recuperar el PaymentIntent enviado por el frontend: ' . $e->getMessage(), ['pi_id' => $paymentIntentId]);
            // No lanzamos excepción aquí, podríamos caer en la lógica de latest_invoice
        }
    }
    // Si no pudimos usar el PI del frontend, intentamos con latest_invoice.payment_intent
    if (!$payment_intent_to_check && isset($subscription->latest_invoice) && $subscription->latest_invoice->payment_intent) {
        Logger::info('[finalize-registration.php] Usando PaymentIntent de latest_invoice de la suscripción.');
        // Necesitamos recuperar el PI completo si latest_invoice->payment_intent es solo un ID
        if (is_string($subscription->latest_invoice->payment_intent)) {
            $payment_intent_to_check = \Stripe\PaymentIntent::retrieve($subscription->latest_invoice->payment_intent);
        } elseif ($subscription->latest_invoice->payment_intent instanceof \Stripe\PaymentIntent) {
            $payment_intent_to_check = $subscription->latest_invoice->payment_intent;
        }
    }
    if (!$payment_intent_to_check) {
         Logger::error('[finalize-registration.php] No se encontró un PaymentIntent válido para verificar.', ['subscription_id' => $stripeSubscriptionId, 'pi_from_frontend_id' => $paymentIntentId]);
         throw new Exception("No se encontró información de pago para la suscripción.");
    }
    Logger::info('[finalize-registration.php] PaymentIntent a verificar.', ['id' => $payment_intent_to_check->id, 'status' => $payment_intent_to_check->status]);
    // Verificando el estado del PaymentIntent
    if ($payment_intent_to_check->status !== 'succeeded') { 
        $errorMessage = 'El pago de la suscripción no se ha completado. Estado actual: ' . $payment_intent_to_check->status;
        if (isset($payment_intent_to_check->last_payment_error) && !empty($payment_intent_to_check->last_payment_error->message)) {
            $errorMessage = $payment_intent_to_check->last_payment_error->message;
        } else if (isset($payment_intent_to_check->last_payment_error) && is_string($payment_intent_to_check->last_payment_error)) {
            // A veces el error puede ser solo un string en lugar de un objeto
            $errorMessage = strval($payment_intent_to_check->last_payment_error);
        }
        Logger::warning('[finalize-registration.php] PaymentIntent no \'succeeded\'.', [
            'subscription_id' => $stripeSubscriptionId,
            'payment_intent_id' => $payment_intent_to_check->id,
            'status' => $payment_intent_to_check->status,
            'client_message' => $errorMessage
        ]);
        http_response_code(402); // Payment Required
        echo json_encode(['success' => false, 'message' => 'El pago de la suscripción no se ha completado: ' . $errorMessage]);
        exit();
    }
    // Si llegamos aquí, el PaymentIntent->status ES 'succeeded'
    Logger::info('[finalize-registration.php] PaymentIntent \'succeeded\'. Procediendo a crear usuario y suscripción en BD.', ['payment_intent_id' => $payment_intent_to_check->id]);
    // Verificar si el email ya existe en la BD
    $stmt_check_email = $conn->prepare("SELECT id FROM usuarios WHERE email = ?");
    if (!$stmt_check_email) { throw new Exception("Error al preparar consulta de email existente: " . $conn->error); }
    $stmt_check_email->bind_param("s", $email);
    $stmt_check_email->execute();
    $result_check_email = $stmt_check_email->get_result();
    if ($result_check_email->num_rows > 0) {
        $stmt_check_email->close();
        Logger::warning('[finalize-registration.php] Intento de finalizar registro para email ya existente.', ['email' => $email]);
        http_response_code(409); // Conflict
        echo json_encode(['success' => false, 'message' => 'El email ya está registrado. Por favor, inicia sesión.']);
        exit();
    }
    $stmt_check_email->close();
    // Consultar detalles del plan desde la BD (similar a signup.php)
    $stmt_plan = $conn->prepare("SELECT id, name FROM plans WHERE slug = ? AND is_active = 1");
    if (!$stmt_plan) { throw new Exception("Error al preparar consulta de plan: " . $conn->error); }
    $stmt_plan->bind_param("s", $plan_slug);
    $stmt_plan->execute();
    $result_plan = $stmt_plan->get_result();
    if ($result_plan->num_rows === 0) {
        $stmt_plan->close();
        throw new Exception("Plan '{$plan_slug}' no encontrado o no activo en la base de datos.");
    }
    $plan_details = $result_plan->fetch_assoc();
    $stmt_plan->close();
    $plan_db_id = $plan_details['id'];
    $plan_display_name = $plan_details['name'];
    // Iniciar transacción de BD
    $conn->begin_transaction();
    $transaction_started = true;
    // Hashear contraseña
    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
    // Generar UUIDs
    $userUuidResult = $conn->query("SELECT UUID() as uuid");
    if (!$userUuidResult) { throw new Exception("Error al generar UUID de usuario: " . $conn->error); }
    $userUuid = $userUuidResult->fetch_assoc()['uuid'];
    $userUuidResult->free();
    $subsUuidResult = $conn->query("SELECT UUID() as uuid");
    if (!$subsUuidResult) { throw new Exception("Error al generar UUID de suscripción: " . $conn->error); }
    $subsUuid = $subsUuidResult->fetch_assoc()['uuid'];
    $subsUuidResult->free();
    // Insertar usuario
    $stmt_user = $conn->prepare("INSERT INTO usuarios (uuid, nombre_completo, email, password_hash, stripe_customer_id, activo, fecha_creacion, fecha_modificacion) VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())");
    if (!$stmt_user) { throw new Exception("Error al preparar inserción de usuario: " . $conn->error); }
    $stmt_user->bind_param("sssss", $userUuid, $fullName, $email, $passwordHash, $stripeCustomerId);
    if (!$stmt_user->execute()) { throw new Exception("Error al crear usuario en BD: " . $stmt_user->error); }
    $userId = $stmt_user->insert_id;
    $stmt_user->close();
    // --- INICIO: Lógica de creación de Agencia y actualización de usuario ---
    $agencyUuidResult = $conn->query("SELECT UUID() as uuid");
    if (!$agencyUuidResult) { throw new Exception("Error al generar UUID de agencia: " . $conn->error); }
    $agencyUuid = $agencyUuidResult->fetch_assoc()['uuid'];
    $agencyUuidResult->free();
    $agencyName = !empty($nombre_agencia) ? $nombre_agencia : "Agencia de " . $fullName; // CORRECCIÓN: Usar la variable de originalFormData
    Logger::info('[finalize-registration.php] Nombre de agencia determinado para DB:', ['name' => $agencyName, 'from_original_form_data' => !empty($nombre_agencia)]);
    $stmt_agency = $conn->prepare("INSERT INTO agencies (uuid, name, owner_user_id, fecha_creacion, fecha_modificacion) VALUES (?, ?, ?, NOW(), NOW())");
    if (!$stmt_agency) { throw new Exception("Error al preparar inserción de agencia: " . $conn->error); }
    $stmt_agency->bind_param("ssi", $agencyUuid, $agencyName, $userId);
    if (!$stmt_agency->execute()) { throw new Exception("Error al crear agencia: " . $stmt_agency->error); }
    $new_agency_id = $stmt_agency->insert_id;
    $stmt_agency->close();
    // Actualizar usuario con el agency_id Y MARCAR COMO PROPIETARIO
    $stmt_update_user = $conn->prepare("UPDATE usuarios SET agency_id = ?, is_agency_owner = 1 WHERE id = ?"); // ASUMIENDO que existe la columna is_agency_owner
    if (!$stmt_update_user) { throw new Exception("Error al preparar actualización de usuario para agency_id y owner: " . $conn->error); }
    $stmt_update_user->bind_param("ii", $new_agency_id, $userId);
    if (!$stmt_update_user->execute()) { throw new Exception("Error al actualizar agency_id e is_agency_owner del usuario: " . $stmt_update_user->error); }
    $stmt_update_user->close();
    // --- FIN: Lógica de creación de Agencia y actualización de usuario ---
    // Insertar suscripción
    $trialStartTimestamp = $subscription->trial_start ? date('Y-m-d H:i:s', $subscription->trial_start) : null;
    $trialEndTimestamp = $subscription->trial_end ? date('Y-m-d H:i:s', $subscription->trial_end) : null;
    $currentPeriodEndTimestamp = date('Y-m-d H:i:s', $subscription->current_period_end);
    $currentPeriodStartTimestamp = date('Y-m-d H:i:s', $subscription->current_period_start);
    $stripeProductIdFromSub = 'default_product_id';
     if (!empty($subscription->items->data) && isset($subscription->items->data[0]->price->product)) {
        $stripeProductIdFromSub = $subscription->items->data[0]->price->product;
    }
    $stripePriceIdFromSub = $subscription->items->data[0]->price->id ?? ($payment_intent_to_check->metadata['stripe_price_id'] ?? 'unknown_price_id'); // Usar el precio de la sub o metadata PI
    $stripeMetadataJson = json_encode($subscription->metadata);
    $dbSubscriptionStatus = $subscription->status; // Usar el estado real de Stripe
    $stmt_subs = $conn->prepare("INSERT INTO suscripciones (uuid, user_id, plan_id, agency_id, stripe_subscription_id, stripe_product_id, stripe_price_id, nombre_plan_display, estado, fecha_inicio_prueba, fecha_fin_prueba, fecha_inicio_periodo_actual, fecha_fin_periodo_actual, billing_cycle, metadata_stripe, fecha_creacion, fecha_modificacion) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
    if (!$stmt_subs) { throw new Exception("Error al preparar inserción de suscripción: " . $conn->error); }
    $stmt_subs->bind_param("siiisssssssssss", $subsUuid, $userId, $plan_db_id, $new_agency_id, $subscription->id, $stripeProductIdFromSub, $stripePriceIdFromSub, $plan_display_name, $dbSubscriptionStatus, $trialStartTimestamp, $trialEndTimestamp, $currentPeriodStartTimestamp, $currentPeriodEndTimestamp, $billing_cycle, $stripeMetadataJson);
    if (!$stmt_subs->execute()) { throw new Exception("Error al crear suscripción en BD: " . $stmt_subs->error); }
    $stmt_subs->close();
    $conn->commit();
    Logger::info('[finalize-registration.php] Usuario y suscripción creados en BD exitosamente.', ['user_id' => $userId, 'subscription_id_db' => $subsUuid]);
    // Generar JWT
    $issuedAt = time();
    $jwtIssuer = defined('JWT_ISSUER') ? JWT_ISSUER : ($_ENV['JWT_ISSUER'] ?? 'inmoautomation.com');
    $jwtExpire = 60 * 60 * 24 * 7; // 7 días
    $jwtPayload = [
        'iat' => $issuedAt,
        'exp' => $issuedAt + $jwtExpire,
        'iss' => $jwtIssuer,
        'sub' => $userUuid,
        'user_id' => $userId,
        'agency_id' => $new_agency_id,
        'is_agency_owner' => true,
        'email' => $email,
        'nombre_completo' => $fullName,
        'roles' => ['user'],
        'agency_name' => $agencyName
    ];
    $jwt = JWT::encode($jwtPayload, $jwtSecretKey, 'HS256');
    http_response_code(201); // Created
    echo json_encode([
        'success' => true,
        'message' => 'Registro completado y suscripción activada correctamente.',
        'token' => $jwt,
        'user' => [
            'uuid' => $userUuid,
            'id' => $userId,
            'email' => $email,
            'nombre_completo' => $fullName,
            'roles' => ['user'],
            'agency_id' => $new_agency_id,
            'is_agency_owner' => true,
            'agency_name' => $agencyName
        ]
    ]);
} catch (\Stripe\Exception\ApiErrorException $e) {
    if ($transaction_started) { $conn->rollback(); }
    Logger::error('[finalize-registration.php] Error de API de Stripe: ' . $e->getMessage(), ['exception_details' => $e->getJsonBody()]);
    http_response_code($e->getHttpStatus() ?: 500);
    $stripeErrorMessage = $e->getStripeError() ? $e->getStripeError()->message : $e->getMessage();
    echo json_encode(['success' => false, 'message' => 'Error al finalizar con Stripe: ' . $stripeErrorMessage]);
} catch (Exception $e) {
    if ($transaction_started) { $conn->rollback(); }
    Logger::error('[finalize-registration.php] Error general: ' . $e->getMessage(), ['exception' => $e]);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al finalizar registro: ' . $e->getMessage()]);
} finally {
    // $stmt_user y $stmt_subs ya se cierran explícitamente. $stmt_check_email, $stmt_plan también.
    if ($conn && $conn instanceof mysqli && $conn->thread_id) {
        $conn->close();
    }
}
?> 