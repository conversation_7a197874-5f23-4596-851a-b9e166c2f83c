steps:
  # Paso 1: Construir la imagen de Docker.
  # Se usa el Dockerfile que está en la carpeta 'api' y se etiqueta con el hash del commit.
  - name: 'gcr.io/cloud-builders/docker'
    id: 'Build image'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/${PROJECT_ID}/cloud-run-source-deploy/dashboard-api:$SHORT_SHA'
      - '.'
    dir: 'api'

  # Paso 2: Subir la imagen a Artifact Registry.
  - name: 'gcr.io/cloud-builders/docker'
    id: 'Push image'
    args: ['push', 'us-central1-docker.pkg.dev/${PROJECT_ID}/cloud-run-source-deploy/dashboard-api:$SHORT_SHA']

  # Paso 3: Crear archivo de variables de entorno.
  # Este enfoque es el más robusto para evitar errores de parseo con caracteres especiales,
  # siguiendo la recomendación de `gcloud topic flags-file`.
  - name: 'bash'
    id: 'Create Env File'
    dir: 'api'
    args:
      - '-c'
      - |
        echo "APP_ENV: 'production'" > .env.yaml
        echo "API_URL: 'https://api.inmoautomation.com'" >> .env.yaml
        echo "LANDING_PAGE_URL: 'https://inmoautomation.com'" >> .env.yaml
        echo "DASHBOARD_URL: 'https://app.inmoautomation.com'" >> .env.yaml
        echo "APP_BASE_URL: 'https://app.inmoautomation.com'" >> .env.yaml
        echo "ALLOWED_ORIGINS: 'https://inmoautomation.com,https://app.inmoautomation.com,https://*.inmoautomation.com'" >> .env.yaml
        echo "DB_SOCKET: '/cloudsql/inmoautomation:us-central1:inmoautomation-db'" >> .env.yaml
        echo "DB_NAME: 's_valorador'" >> .env.yaml
        echo "DB_USER: 'inmoautomation-db'" >> .env.yaml
        echo "STRIPE_PRICE_ID_MONTHLY: 'price_1RQiFPHei6CusI6XdvKuYbqK'" >> .env.yaml
        echo "STRIPE_PRICE_ID_ANNUAL: 'price_1RQigsHei6CusI6XJsTYKVey'" >> .env.yaml
        echo "DEFAULT_SENDER_EMAIL: '<EMAIL>'" >> .env.yaml
        echo "DEFAULT_SENDER_NAME: 'InmoAutomation'" >> .env.yaml
        echo "GOOGLE_CLOUD_STORAGE_BUCKET: 'inmoautomation-logos'" >> .env.yaml
        echo "GOOGLE_CLOUD_PROJECT_ID: 'inmoautomation'" >> .env.yaml
        echo "LOGOS_BASE_URL: 'https://storage.googleapis.com/inmoautomation-logos'" >> .env.yaml
        echo "GOOGLE_CLOUD_REPORTS_BUCKET: 'inmoautomation-reports'" >> .env.yaml
        echo "REPORTS_BASE_URL: 'https://storage.googleapis.com/inmoautomation-reports'" >> .env.yaml
        echo "VALORADOR_API_ENDPOINT_URL: 'https://api-valorador.inmoautomation.com/valorar-vnext'" >> .env.yaml
        echo "CLOUD_RUN_INVOKER_SERVICE_ACCOUNT: '<EMAIL>'" >> .env.yaml
        # Las claves de API y otros secretos se inyectan directamente a través de --set-secrets
        # y no deben definirse aquí para evitar conflictos de tipo.
        # Las variables del valorador ahora usarán la misma conexión de socket
        echo "VALORADOR_DB_SOCKET: '/cloudsql/inmoautomation:us-central1:inmoautomation-db'" >> .env.yaml
        echo "VALORADOR_DB_NAME: 's_valorador'" >> .env.yaml
        echo "VALORADOR_DB_USER: 'inmoautomation-db'" >> .env.yaml

  # Paso 4: Desplegar en Cloud Run. Usa un archivo para las env-vars y la SA tiene permisos para actuar y acceder a secretos.
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'Deploy to Cloud Run'
    dir: 'api'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'dashboard-api'
      - '--image'
      - 'us-central1-docker.pkg.dev/${PROJECT_ID}/cloud-run-source-deploy/dashboard-api:$SHORT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--project'
      - '${PROJECT_ID}'
      - '--env-vars-file=.env.yaml'
      - '--add-cloudsql-instances=inmoautomation:us-central1:inmoautomation-db'
      - '--set-secrets=STRIPE_SECRET_KEY=dashboard-api-stripe-secret-key:latest,STRIPE_WEBHOOK_SECRET=dashboard-api-stripe-webhook-secret:latest,DB_PASS=dashboard-api-db-pass:latest,JWT_SECRET_KEY=dashboard-api-jwt-secret-key:latest,BREVO_API_KEY=dashboard-api-brevo-api-key:latest,GEMINI_API_KEY=dashboard-api-gemini-api-key:latest,OPENAI_API_KEY=dashboard-api-openai-api-key:latest,GOOGLE_PLACES_API_KEY=valorador-google-places-api-key:latest,VALORADOR_API_SECRET_KEY=valorador-api-secret-key:latest,VALORADOR_DB_PASS=valorador-db-pass:latest'

# Guardar la imagen construida en la caché de Cloud Build para acelerar futuros despliegues.
images:
  - 'us-central1-docker.pkg.dev/${PROJECT_ID}/cloud-run-source-deploy/dashboard-api:$SHORT_SHA'

# Opciones de la compilación para cumplir con las políticas de seguridad.
options:
  # Al usar una cuenta de servicio personalizada, se debe especificar un bucket de logs propiedad del usuario.
  # Esta opción le indica a Cloud Build que gestione automáticamente un bucket regional para este propósito.
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET

# Timeout para evitar builds demasiado largos.
timeout: '1200s' # 20 minutos
