<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
ob_start();
 // Usar config central de CORS
// ENCABEZADOS CORS Y MANEJO DE OPTIONS AHORA EN cors.php
header('Content-Type: application/json; charset=utf-8');
if (!function_exists('custom_log_profile_update')) {
    function custom_log_profile_update($message) {
        $logFile = __DIR__ . '/debug_profile.log'; // Log en el mismo directorio api/
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [update-profile-LOG] " . $message . PHP_EOL);
    }
}
custom_log_profile_update("--- [update-profile.php] INICIADO ---");
if (ob_get_length()) ob_clean();
// --- Autenticación por Token JWT ---
$user_id_from_token = null;
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
custom_log_profile_update("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_set('display_errors', '0'); // Mantener para el bloque try-catch de JWT
        try {
            custom_log_profile_update("Attempting to decode JWT...");
            if (empty(JWT_SECRET)) { 
                throw new Exception('JWT_SECRET no configurada (constante).');
            }
            $decoded = Firebase\JWT\JWT::decode($token, new Firebase\JWT\Key(JWT_SECRET, 'HS256'));
            if (isset($decoded->user_id)) {
                $user_id_from_token = $decoded->user_id;
                custom_log_profile_update("JWT decoded successfully. User ID from token: " . $user_id_from_token);
            } else {
                throw new Exception('Token JWT inválido: no contiene user_id.');
            }
        } catch (Firebase\JWT\ExpiredException $e) {
            custom_log_profile_update("JWT Decode Error: Token Expirado - " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token expirado. Por favor, inicia sesión de nuevo.']);
            exit();
        } catch (Exception $e) {
            custom_log_profile_update("JWT Decode Error: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o error de autenticación: ' . $e->getMessage()]);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors); // Restaurar
        }
    } else {
        custom_log_profile_update("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_profile_update("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
if (empty($user_id_from_token)) {
    custom_log_profile_update("CRITICAL: User ID from token is empty after auth block.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Autenticación fallida.']);
    exit();
}
$user_id = $user_id_from_token;
$response = ['success' => false, 'message' => 'Error desconocido al actualizar perfil.'];

try {
    custom_log_profile_update("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_profile_update("Conexión PDO a la BD establecida a través de la clase Database.");

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        custom_log_profile_update("[POST Request] Processing for user_id: " . $user_id);
        $input = json_decode(file_get_contents('php://input'), true);
        custom_log_profile_update("Input received: " . print_r($input, true));
        $newName = $input['name'] ?? null;
        $newEmail = $input['email'] ?? null;
        if (empty($newName) || empty($newEmail)) {
            http_response_code(400);
            $response['message'] = 'El nombre y el correo electrónico no pueden estar vacíos.';
            custom_log_profile_update("Validation Error: " . $response['message']);
            echo json_encode($response); 
            exit;
        }
        if (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
            http_response_code(400);
            $response['message'] = 'El formato del correo electrónico no es válido.';
            custom_log_profile_update("Validation Error: " . $response['message'] . " Email: " . $newEmail);
            echo json_encode($response);
            exit;
        }
        $stmt_check_email = $pdo->prepare("SELECT id FROM usuarios WHERE email = :email AND id != :user_id");
        $stmt_check_email->bindParam(':email', $newEmail, PDO::PARAM_STR);
        $stmt_check_email->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt_check_email->execute();
        if ($stmt_check_email->fetch()) {
            http_response_code(409); // Conflict
            $response['message'] = 'El correo electrónico ya está en uso por otra cuenta.';
            custom_log_profile_update("Conflict Error: " . $response['message'] . " Email: " . $newEmail);
            echo json_encode($response);
            exit;
        }
        $sql_update = "UPDATE usuarios SET nombre_completo = :nombre_completo, email = :email, fecha_modificacion = NOW() WHERE id = :user_id";
        $stmt_update = $pdo->prepare($sql_update);
        $stmt_update->bindParam(':nombre_completo', $newName, PDO::PARAM_STR);
        $stmt_update->bindParam(':email', $newEmail, PDO::PARAM_STR);
        $stmt_update->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt_update->execute();
        if ($stmt_update->rowCount() > 0) {
            custom_log_profile_update("Perfil actualizado para user_id: " . $user_id . ". Nombre: " . $newName . ", Email: " . $newEmail);
            $response['success'] = true;
            $response['message'] = 'Perfil actualizado correctamente.';
            $response['user'] = ['name' => $newName, 'email' => $newEmail]; 
        } else {
            custom_log_profile_update("No se realizaron cambios en el perfil para user_id: " . $user_id . " (quizás los datos eran iguales).");
            $response['success'] = true;
            $response['message'] = 'No se realizaron cambios (los datos pueden ser los mismos).';
            $response['user'] = ['name' => $newName, 'email' => $newEmail];
        }
        echo json_encode($response);
    } else {
        custom_log_profile_update("Error: Method not allowed. Method was: " . $_SERVER['REQUEST_METHOD']);
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Método no permitido. Solo se aceptan solicitudes POST.']);
    }
} catch (PDOException $e) {
    custom_log_profile_update("PDOException: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos al actualizar: ' . $e->getMessage()]);
} catch (Exception $e) {
    custom_log_profile_update("General Exception: " . $e->getMessage());
    http_response_code(500);
    $error_message = $e->getMessage();
    if (strpos($error_message, 'Token expirado') !== false || strpos($error_message, 'Token inválido') !== false) {
        http_response_code(401); 
    }
    echo json_encode(['success' => false, 'message' => 'Error en el servidor al actualizar: ' . $error_message]);
}
custom_log_profile_update("--- [update-profile.php] FINALIZADO ---");
ob_end_flush();
exit;
?>