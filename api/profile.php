<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// El resto del script continúa si no es OPTIONS
header('Content-Type: application/json; charset=utf-8');
if (!function_exists('custom_log_profile')) {
    function custom_log_profile($message) {
        $logFile = __DIR__ . '/debug_profile.log';
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [profile-LOG] " . $message . PHP_EOL, 3, $logFile);
    }
}
custom_log_profile("--- [profile.php] INICIADO ---");
if (ob_get_length()) ob_clean();
header('Content-Type: application/json; charset=utf-8');
custom_log_profile("CORS and Content-Type headers sent.");
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
// --- Autenticación por Token JWT ---
$user_id_from_token = null;
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
custom_log_profile("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_get('display_errors'); // Guardar estado actual
        ini_set('display_errors', '0'); // Desactivar temporalmente
        try {
            custom_log_profile("Attempting to decode JWT...");
            if (empty(JWT_SECRET)) {
                throw new Exception('JWT_SECRET_KEY no configurada en el entorno (constante).');
            }
            $decoded = Firebase\JWT\JWT::decode($token, new Firebase\JWT\Key(JWT_SECRET, 'HS256'));
            if (isset($decoded->user_id)) {
                $user_id_from_token = $decoded->user_id;
                custom_log_profile("JWT decoded successfully. User ID from token: " . $user_id_from_token);
            } else {
                throw new Exception('Token JWT inválido: no contiene user_id.');
            }
        } catch (Firebase\JWT\ExpiredException $e) {
            custom_log_profile("JWT Decode Error: Token Expirado - " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token expirado. Por favor, inicia sesión de nuevo.']);
            exit();
        } catch (Exception $e) {
            custom_log_profile("JWT Decode Error: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o error de autenticación: ' . $e->getMessage()]);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors); // Restaurar estado
        }
    } else {
        custom_log_profile("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_profile("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
if (empty($user_id_from_token)) {
    custom_log_profile("CRITICAL: User ID from token is empty after auth block.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Autenticación fallida.']);
    exit();
}
$user_id = $user_id_from_token;
$response = ['success' => false, 'message' => 'Error desconocido.'];

try {
    custom_log_profile("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_profile("Conexión PDO a la BD establecida a través de la clase Database.");

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        custom_log_profile("[GET Request] Processing for user_id: " . $user_id);
        $stmt = $pdo->prepare("SELECT nombre_completo, email FROM usuarios WHERE id = :user_id");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $user_profile = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($user_profile) {
            custom_log_profile("User profile found: " . print_r($user_profile, true));
            // Devolver directamente el objeto de perfil, ya que apiFetch espera UserProfile
            // Y la interfaz UserProfile en el frontend tiene 'name' y 'email'
            // Renombrar 'nombre_completo' a 'name' para coincidir con la interfaz UserProfile
            echo json_encode(['name' => $user_profile['nombre_completo'], 'email' => $user_profile['email']]);
        } else {
            custom_log_profile("User profile not found for user_id: " . $user_id);
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Perfil de usuario no encontrado.']);
        }
    } else {
        custom_log_profile("Error: Method not allowed. Method was: " . $_SERVER['REQUEST_METHOD']);
        http_response_code(405); // Method Not Allowed
        echo json_encode(['success' => false, 'message' => 'Método no permitido. Solo se aceptan solicitudes GET.']);
    }
} catch (PDOException $e) {
    custom_log_profile("PDOException: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    custom_log_profile("General Exception: " . $e->getMessage());
    http_response_code(500);
    // Asegurarnos que la respuesta siempre sea JSON
    $error_message = $e->getMessage();
    if (strpos($error_message, 'Token expirado') !== false || strpos($error_message, 'Token inválido') !== false) {
        http_response_code(401); // Re-confirmar el código de estado para errores de token
    }
    echo json_encode(['success' => false, 'message' => 'Error en el servidor: ' . $error_message]);
}
custom_log_profile("--- [profile.php] FINALIZADO ---");
ob_end_flush();
exit;
?>