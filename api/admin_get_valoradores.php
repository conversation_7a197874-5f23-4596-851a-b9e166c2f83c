<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// api/admin_get_valoradores.php
// error_reporting(E_ALL); // Manejado por bootstrap.php
// ini_set('display_errors', 0); // Manejado por bootstrap.php
 // <--- INTEGRAR BOOTSTRAP
require_once __DIR__ . '/utils/auth_check.php';
// Use Firebase\JWT\JWT; // auth_check.php o bootstrap.php manejan esto
// Use Firebase\JWT\Key;
header('Content-Type: application/json');
// --- Carga de .env y autoload manejada por bootstrap.php ---
// try { $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); $dotenv->load(); } catch ... (eliminado)
// --- Constantes de BD y JWT_SECRET vienen de bootstrap.php ---
// $dbHost = $_ENV['DB_HOST'] ?? null; (eliminado)
// ... (eliminación de otras asignaciones de variables de entorno)
// auth_check.php ahora usará JWT_SECRET de bootstrap.php
$authResult = verifyTokenAndAdminRole(); // <--- SIN ARGUMENTOS
if (!$authResult['success']) {
    http_response_code($authResult['status_code']);
    echo json_encode(['success' => false, 'message' => $authResult['message']]);
    exit();
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en admin_get_valoradores: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

try {
    // Paginación
    $page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT, ['options' => ['default' => 1, 'min_range' => 1]]);
    $limit = filter_input(INPUT_GET, 'limit', FILTER_VALIDATE_INT, ['options' => ['default' => 10, 'min_range' => 1]]);
    $offset = ($page - 1) * $limit;

    // Filtros y búsqueda
    $searchQuery = $_GET['search'] ?? '';
    $filterStatus = $_GET['status'] ?? ''; // '1' para activo, '0' para inactivo, '' para todos

    $baseSql = "FROM clientes_valorador cv LEFT JOIN usuarios u ON cv.user_id = u.id";
    $whereConditions = [];
    $params = [];

    if (!empty($searchQuery)) {
        $searchTerm = "%" . $searchQuery . "%";
        $whereConditions[] = "(cv.nombre_display LIKE ? OR cv.client_identifier LIKE ? OR u.nombre_completo LIKE ? OR u.email LIKE ?)";
        array_push($params, $searchTerm, $searchTerm, $searchTerm, $searchTerm);
    }
    if ($filterStatus !== '') {
        $whereConditions[] = "cv.activo = ?";
        $params[] = (int)$filterStatus;
    }

    $whereSql = "";
    if (!empty($whereConditions)) {
        $whereSql = " WHERE " . implode(" AND ", $whereConditions);
    }

    // Contar total de items para paginación
    $totalItemsSql = "SELECT COUNT(cv.id) " . $baseSql . $whereSql;
    $stmtTotal = $pdo->prepare($totalItemsSql);
    $stmtTotal->execute($params);
    $totalItems = (int)$stmtTotal->fetchColumn();
    
    $totalPages = $totalItems > 0 ? (int)ceil($totalItems / $limit) : 1;

    // Consulta principal de valoradores
    $valoradoresSql = "SELECT cv.id, cv.user_id, cv.client_identifier, cv.nombre_display, cv.logo_url, cv.color_primario, cv.color_secundario, cv.email_notificaciones_leads, cv.politica_privacidad_url, cv.terminos_uso_url, cv.cta_contacto_url, cv.config_valorador_json, cv.config_email_secuencia_json, cv.activo, cv.fecha_creacion, cv.fecha_modificacion, u.nombre_completo as nombre_usuario_asociado, u.email as email_usuario_asociado "
                    . $baseSql . $whereSql . " ORDER BY cv.fecha_creacion DESC LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;

    $stmtValoradores = $pdo->prepare($valoradoresSql);
    $stmtValoradores->execute($params);
    $valoradores = $stmtValoradores->fetchAll();

    echo json_encode([
        'success' => true,
        'data' => $valoradores,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalItems' => $totalItems,
            'limit' => $limit
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al obtener valoradores (admin_get_valoradores): " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al obtener los valoradores.']);
    exit();
}