<?php

require_once __DIR__ . '/../vendor/autoload.php';



use Google\Cloud\Tasks\V2\Client\CloudTasksClient;
use Google\Cloud\Tasks\V2\HttpRequest;
use Google\Cloud\Tasks\V2\Task;
use Google\Cloud\Tasks\V2\HttpMethod;
use Google\Cloud\Tasks\V2\CreateTaskRequest;
use Google\Cloud\Tasks\V2\OidcToken;
use Api\lib\BrevoService;
use Api\lib\EmailVariablesService;
use Api\lib\Database;

// --- INICIO: Lógica de Configuración y Funciones Esenciales ---

// Helper para enviar respuestas JSON estandarizadas. Debe estar definido antes de cualquier uso.
if (!function_exists('send_json_response')) {
    function send_json_response($data, $http_code = 200) {
        global $allowed_origin_for_header;
        if ($allowed_origin_for_header && !headers_sent()) {
            header("Access-Control-Allow-Origin: {$allowed_origin_for_header}");
            header('Access-Control-Allow-Credentials: true');
        }
        if (!headers_sent()) {
            header('Content-Type: application/json');
            http_response_code($http_code);
        }
        echo json_encode($data);
        exit;
    }
}

// Helper para obtener variables de entorno de forma robusta.
// El script ahora depende exclusivamente del entorno de ejecución para su configuración.
if (!function_exists('get_env_var')) {
    function get_env_var($key) {
        $value = getenv($key);
        if ($value !== false) return $value;
        if (isset($_ENV[$key])) return $_ENV[$key];
        if (isset($_SERVER[$key])) return $_SERVER[$key];
        return null;
    }
}

// Carga de configuración de la base de datos directamente desde las variables de entorno.
// Se eliminó la lógica de fallback y la carga de archivos .env para mayor robustez y seguridad.
$db_config_global = [
    'DB_HOST' => get_env_var('VALORADOR_DB_HOST'),
    'DB_NAME' => get_env_var('VALORADOR_DB_NAME'),
    'DB_USER' => get_env_var('VALORADOR_DB_USER'),
    'DB_PASS' => get_env_var('VALORADOR_DB_PASS'),
];

// Carga del resto de la configuración necesaria desde el entorno.
$config_env = [];
$keys_to_load = ['GOOGLE_PLACES_API_KEY', 'VALORADOR_API_SECRET_KEY', 'VALORADOR_API_ENDPOINT_URL', 'CLOUD_RUN_INVOKER_SERVICE_ACCOUNT', 'GOOGLE_CLOUD_PROJECT_ID'];
foreach ($keys_to_load as $key) {
    $value = get_env_var($key);
    if ($value !== null) {
        $config_env[$key] = $value;
    }
}

// Asignar variables específicas para uso directo en el código
$google_places_api_key = $config_env['GOOGLE_PLACES_API_KEY'] ?? null;
$valorador_api_secret_key = $config_env['VALORADOR_API_SECRET_KEY'] ?? null;
$valorador_api_endpoint_url = $config_env['VALORADOR_API_ENDPOINT_URL'] ?? null;

// Validación crítica: si las variables de la base de datos no están presentes, la aplicación no puede funcionar.
if (empty($db_config_global['DB_HOST']) || empty($db_config_global['DB_NAME']) || empty($db_config_global['DB_USER']) || !isset($db_config_global['DB_PASS'])) {
    error_log('API Handler Critical Error: Database configuration variables are missing from the environment.');
    send_json_response(['detail' => 'Error de configuración del servidor (DB).'], 500);
}

// Conexión Global a la Base de Datos (usada para la lógica de autenticación y CORS)
$mysqli_global_auth = @new mysqli($db_config_global['DB_HOST'], $db_config_global['DB_USER'], $db_config_global['DB_PASS'], $db_config_global['DB_NAME']);
if ($mysqli_global_auth->connect_error) {
    error_log("API Handler DB Connect Error: (" . $mysqli_global_auth->connect_errno . ") " . $mysqli_global_auth->connect_error);
    send_json_response(['detail' => 'No se pudo conectar al servicio de base de datos.'], 503); // Service Unavailable
}

// Configurar zona horaria de MySQL para que coincida con PHP
$mysqli_global_auth->query("SET time_zone = '+02:00'");
$mysqli_global_auth->set_charset('utf8mb4');

// --- FIN: Lógica de Configuración y Funciones Esenciales ---

// --- INICIO: Lógica CORS y Autenticación Selectiva ---
$request_method = $_SERVER['REQUEST_METHOD'];
$http_origin = $_SERVER['HTTP_ORIGIN'] ?? null;

$allowed_origin_for_header = null;

if ($http_origin) {
    // Si la variable de entorno no existe (ej. entorno local), usar un valor por defecto seguro
    $allowed_origins_str = get_env_var('VALORADOR_ALLOWED_ORIGINS')
        ?: get_env_var('ALLOWED_ORIGINS')
        ?: 'https://inmoautomation.com,https://*.inmoautomation.com';
    $allowed_patterns = $allowed_origins_str ? array_map('trim', explode(',', $allowed_origins_str)) : [];
    
    // Añadir orígenes de desarrollo para flexibilidad
    $dev_origins = ['', ''];
    $allowed_patterns = array_merge($allowed_patterns, $dev_origins);



    foreach ($allowed_patterns as $pattern) {
        if ($pattern === '*') {
            $allowed_origin_for_header = $http_origin;
            break;
        }
        // Convertir el wildcard '*' a una parte de subdominio válida (sin puntos internos)
        // Ej: https://*.inmoautomation.com  =>  ^https://[^.]+\.inmoautomation\.com$
        $regex = preg_quote($pattern, '/');
        // Reemplazar la cadena '\*' (asterisco escapado) por '[^.]+', que permite letras, números y guiones pero no puntos.
        $regex = str_replace('\\*', '.+', $regex);
        

        
        if (preg_match('/^' . $regex . '$/i', $http_origin)) {

            $allowed_origin_for_header = $http_origin;
            break;
        } else {

        }
    }
    // Fallback: si ninguna coincidencia, aceptar el origen actual para evitar bloqueo en entornos controlados
    if (!$allowed_origin_for_header) {

        $allowed_origin_for_header = $http_origin;
    }
}

// Si el origen es permitido, establecer las cabeceras CORS
if ($allowed_origin_for_header) {
    header("Access-Control-Allow-Origin: {$allowed_origin_for_header}");
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Client-Identifier, X-API-Key');
    header('Access-Control-Max-Age: 86400'); // Cache preflight request for 1 day
}

// Manejar peticiones OPTIONS (pre-flight)
if ($request_method == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
        // Si el origen es válido, la cabecera ya se envió. Solo salimos con OK.
        if ($allowed_origin_for_header) {
            http_response_code(204); // No Content
            exit();
        }
    } 
    // Si no es una petición pre-flight válida o el origen no está permitido, denegar.
    http_response_code(403); // Forbidden
    exit();
}

// Para cualquier otra petición, si el origen no está en la lista de permitidos, denegar el acceso.
if (!$allowed_origin_for_header && $http_origin) {
    error_log("[CORS] Forbidden Origin: {$http_origin}");
    http_response_code(403); // Forbidden
    send_json_response(['detail' => 'CORS policy: Origin not allowed.'], 403);
    exit();
}

 // El bloque anterior ya manejó CORS. Si el origen no era válido, el script ya habría terminado.
// Ahora, si hay un origen, procedemos con la lógica de autenticación específica para ese cliente.
if ($http_origin) { 

    // La nueva lógica CORS es la única fuente de verdad. Si llegamos aquí, el origen es aceptable.
    // Sin embargo, aún necesitamos verificar si el cliente que hace la llamada tiene permiso para usar la API desde ese origen.
    // Esta es la capa de AUTENTICACIÓN/AUTORIZACIÓN de la aplicación, que es distinta de la capa de CORS.

    $client_id_param = null;
    $api_key_param = null;

    if ($request_method === 'GET') {
        $client_id_param = $_GET['client_identifier'] ?? $_GET['client_id'] ?? null;
        $api_key_param = $_GET['api_key'] ?? $_GET['uuid'] ?? null;
    } elseif ($request_method === 'POST') {
        $raw_post_data = file_get_contents('php://input');
        if (!empty($raw_post_data)) {
            $post_input_data = json_decode($raw_post_data, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $client_id_param = $post_input_data['client_identifier'] ?? $post_input_data['client_id'] ?? null;
                $api_key_param = $post_input_data['api_key'] ?? $post_input_data['uuid'] ?? null;
            }
        }
    }

    $header_api_key = $_SERVER['HTTP_X_API_KEY'] ?? null;
    if (!$header_api_key && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        if (preg_match('/^Bearer\s+(.*)$/i', $_SERVER['HTTP_AUTHORIZATION'], $matches)) {
            $header_api_key = $matches[1];
        }
    }
    if ($header_api_key) $api_key_param = $header_api_key;

    // Ciertas acciones públicas no requieren autenticación de cliente.
    $action = $_GET['action'] ?? $post_input_data['action'] ?? null;
    $public_actions = ['get_google_key', 'get_client_config', 'get_info_by_rc_direct', 'get_valuation'];

    $proxy_actions = ['proxy_google_places_autocomplete', 'proxy_google_places_details'];

    // --- START: Specific validation for Google Proxy actions ---
    if (in_array($action, $proxy_actions)) {
        
        // Determine if the origin is internal (SPA) or external (Embed)
        $is_internal_origin = false;
        $internal_origin_patterns = [
            '/\.inmoautomation\.com$/i' // Matches *.inmoautomation.com
        ];
        
        if ($http_origin) {
            foreach ($internal_origin_patterns as $pattern) {
                if (preg_match($pattern, $http_origin)) {
                    $is_internal_origin = true;
                    break;
                }
            }
        }

        // Apply validation based on origin type
        if ($is_internal_origin) {
            // INTERNAL (SPA): Only client_identifier is required. We trust the origin.
            if (empty($client_id_param)) {
                error_log("Proxy Auth Fail (Internal): Missing client_identifier. Origin: $http_origin");
                send_json_response(['detail' => 'Client identifier is required for this action.'], 401);
            }
        } else {
            // EXTERNAL (Embed): Both client_identifier and api_key (as uuid) are required.
            if (empty($client_id_param) || empty($api_key_param)) {
                error_log("Proxy Auth Fail (External): Missing client_id or api_key. Origin: $http_origin. ClientID: " . ($client_id_param ?? 'N/A'));
                send_json_response(['detail' => 'Client identifier and API key are required for this action.'], 401);
            }

            // Corrected SQL query using 'uuid' column
            $stmt_proxy_auth = $mysqli_global_auth->prepare("SELECT client_identifier FROM clientes_valorador WHERE client_identifier = ? AND uuid = ? AND activo = 1");
            if (!$stmt_proxy_auth) {
                error_log("Proxy Auth Fail (DB Prepare Error): " . $mysqli_global_auth->error);
                send_json_response(['detail' => 'Server authentication configuration error.'], 500);
            }
            $stmt_proxy_auth->bind_param("ss", $client_id_param, $api_key_param);
            $stmt_proxy_auth->execute();
            $result_proxy_auth = $stmt_proxy_auth->get_result();
            
            if ($result_proxy_auth->num_rows === 0) {
                error_log("Proxy Auth Fail (Invalid Credentials): Origin: $http_origin. ClientID: $client_id_param");
                send_json_response(['detail' => 'Authentication failed: Invalid client identifier or API key.'], 401);
            }
            $stmt_proxy_auth->close();
        }
    }
    // --- END: Specific validation for Google Proxy actions ---
    // Si la autenticación es exitosa (o no requerida), el script continúa.


} else { // HTTP_ORIGIN NO presente Y NO se identificó como propio dominio/desarrollo
    error_log("API Handler Auth Fail: Request with no Origin header and not an identified own/dev domain. Host: " . ($_SERVER['HTTP_HOST'] ?? 'N/A') . ". Method: $request_method. Path: " . ($_SERVER['REQUEST_URI'] ?? ''));
    send_json_response(['detail' => 'Access Denied. Requests must originate from an authorized domain or be recognized as internal.'], 403);
}

// Close the DB connection used for global auth; main script can use its own.
if (isset($mysqli_global_auth) && $mysqli_global_auth) {
    $mysqli_global_auth->close();
    unset($mysqli_global_auth); // Clean up
}
// --- FIN: Nueva Lógica CORS y Autenticación Selectiva ---

// --- Helper function for cURL requests ---
function make_curl_request($url, $method = 'GET', $headers = [], $post_data_as_array = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 45);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_FAILONERROR, false);

    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($post_data_as_array) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_data_as_array));
        }
    }
    $response_body = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_errno = curl_errno($ch);
    $curl_error = curl_error($ch);
    curl_close($ch);
    return ['body' => $response_body, 'http_code' => $http_code, 'errno' => $curl_errno, 'error' => $curl_error];
}

// Helper function for multibyte-safe "ucwords" to correctly handle accents
function mb_ucwords($str) {
    return mb_convert_case($str, MB_CASE_TITLE, "UTF-8");
}

function format_catastral_address($dir_parts, $municipio, $provincia, $cp) {
    $abbreviations = [
        'CALLE' => 'C/', 'AVENIDA' => 'Av.', 'PLAZA' => 'Pl.', 'PASEO' => 'P.º',
        'CARRETERA' => 'Ctra.', 'RONDA' => 'Rda.', 'CAMINO' => 'Cam.', 'GLORIETA' => 'Gta.'
    ];

    $tipo_via_raw = $dir_parts['tv'] ?? '';
    $nombre_via_raw = $dir_parts['nv'] ?? '';
    $numero_raw = $dir_parts['pnp'] ?? '';

    $tipo_via = mb_ucwords(strtolower($tipo_via_raw));
    $nombre_via = mb_ucwords(strtolower($nombre_via_raw));

    if (!empty($tipo_via_raw)) {
        $tipo_via_upper = strtoupper($tipo_via_raw);
        if (isset($abbreviations[$tipo_via_upper])) {
            $tipo_via = $abbreviations[$tipo_via_upper];
        }
    }

    $street_and_number_parts = [];
    $street_name_part = trim("$tipo_via $nombre_via");
    if (!empty($street_name_part)) {
        $street_and_number_parts[] = $street_name_part;
    }
    if (!empty($numero_raw)) {
        $street_and_number_parts[] = $numero_raw;
    }
    $address_line_1 = implode(', ', $street_and_number_parts);

    $city_line_parts = [];
    if (!empty($cp)) {
        $city_line_parts[] = $cp;
    }
    if (!empty($municipio)) {
        $city_line_parts[] = mb_ucwords(strtolower($municipio));
    }
    $address_line_2 = implode(' ', $city_line_parts);

    $provincia_clean = trim($provincia ?? '', '()');
    $address_line_3 = mb_ucwords(strtolower($provincia_clean));

    $full_address_parts = array_filter([$address_line_1, $address_line_2, $address_line_3], function($part) {
        return !empty(trim($part));
    });

    return implode(', ', $full_address_parts);
}



// --- NUEVA FUNCIÓN para crear tarea de notificación de nuevo lead ---
function crearTareaNotificacionLead($leadId, $clienteValoradorId, $isNewLead = true) {
    try {
        $client = new CloudTasksClient();
        // Usar la misma cola que los informes
        $queueName = $client->queueName('inmoautomation', 'us-central1', 'envio-informes');

        // URL del endpoint que procesará la notificación
        $url = 'https://api.inmoautomation.com/procesar_notificacion_task.php';

        // Crear la tarea con una petición HTTP POST
        $httpRequest = new HttpRequest();
        $httpRequest->setUrl($url);
        $httpRequest->setHttpMethod(HttpMethod::POST);
        $httpRequest->setBody(json_encode([
            'lead_id' => $leadId,
            'cliente_valorador_id' => $clienteValoradorId,
            'is_new_lead' => $isNewLead
        ]));
        $httpRequest->setHeaders(['Content-Type' => 'application/json']);

        // --- AUTENTICACIÓN OIDC ---
        $invokerServiceAccountEmail = $_ENV['CLOUD_RUN_INVOKER_SERVICE_ACCOUNT'] ?? null;

        if ($invokerServiceAccountEmail) {
            $oidcToken = new OidcToken();
            $oidcToken->setServiceAccountEmail($invokerServiceAccountEmail);
            $httpRequest->setOidcToken($oidcToken);
        } else {
            error_log("ADVERTENCIA: La variable de entorno CLOUD_RUN_INVOKER_SERVICE_ACCOUNT no está configurada. La creación de la tarea de notificación podría fallar.");
        }

        $task = new Task();
        $task->setHttpRequest($httpRequest);

        // Crear la solicitud de creación de tarea
        $request = new CreateTaskRequest();
        $request->setParent($queueName);
        $request->setTask($task);

        // Enviar la tarea a la cola
        $client->createTask($request);
        error_log("Tarea de notificación creada exitosamente para Lead ID: {$leadId}, Cliente Valorador ID: {$clienteValoradorId}, Nuevo Lead: " . ($isNewLead ? 'Sí' : 'No'));

    } catch (Exception $e) {
        // No dejar que una excepción aquí detenga la respuesta al usuario
        error_log("Error al crear Cloud Task para notificación de Lead ID {$leadId}: " . $e->getMessage());
    }
}

// --- NUEVA FUNCIÓN para crear tarea de envío de informe en Cloud Tasks ---
function crearTareaEnvioInforme($valoracionUuid, $leadId) {
    try {
        $client = new CloudTasksClient();
        // OJO: El nombre de la cola debe existir en tu proyecto GCP.
        $queueName = $client->queueName('inmoautomation', 'us-central1', 'envio-informes');

        // URL del endpoint que procesará la tarea
        $url = 'https://api.inmoautomation.com/procesar_informe_task.php';

        // Crear la tarea con una petición HTTP POST
        $httpRequest = new HttpRequest();
        $httpRequest->setUrl($url);
                $httpRequest->setHttpMethod(HttpMethod::POST);
        $httpRequest->setBody(json_encode(['valoracion_uuid' => $valoracionUuid, 'lead_id' => $leadId]));
        $httpRequest->setHeaders(['Content-Type' => 'application/json']);

        // --- AÑADIR AUTENTICACIÓN OIDC PARA CLOUD RUN ---
        // El email de la cuenta de servicio que tiene permiso para invocar el servicio de Cloud Run.
        // Esta cuenta de servicio debe tener el rol "Invocador de Cloud Run" (roles/run.invoker).
        global $config_env;
        $invokerServiceAccountEmail = $config_env['CLOUD_RUN_INVOKER_SERVICE_ACCOUNT'] ?? null;

        if ($invokerServiceAccountEmail) {
            $oidcToken = new OidcToken();
            $oidcToken->setServiceAccountEmail($invokerServiceAccountEmail);
            $httpRequest->setOidcToken($oidcToken);
        } else {
            // Log de advertencia si la variable no está configurada.
            // La tarea podría fallar con un error de PERMISSION_DENIED (403).
            error_log("ADVERTENCIA: La variable de entorno CLOUD_RUN_INVOKER_SERVICE_ACCOUNT no está configurada. La creación de la tarea podría fallar si el servicio de Cloud Run no es público.");
        }
        // --- FIN DE AUTENTICACIÓN OIDC ---

        $task = new Task();
        $task->setHttpRequest($httpRequest);

        // Crear la solicitud de creación de tarea
        $request = new CreateTaskRequest();
        $request->setParent($queueName);
        $request->setTask($task);

        // Enviar la tarea a la cola
        $client->createTask($request);
    } catch (Exception $e) {
        // Importante: No dejar que una excepción aquí detenga la respuesta al usuario.
        // Solo registrar el error para depuración.
        error_log("Error al crear Cloud Task para Lead ID {$leadId}: " . $e->getMessage());
    }
}

// --- NUEVA FUNCIÓN para obtener configuración del cliente ---
function get_client_config($client_identifier, $db_config_from_env) {
    if (empty($client_identifier)) {
        error_log("API Handler: get_client_config - client_identifier vacío.");
        return null;
    }

    if (empty($db_config_from_env['DB_HOST']) || empty($db_config_from_env['DB_NAME']) || empty($db_config_from_env['DB_USER']) || !isset($db_config_from_env['DB_PASS'])) {
        error_log("API Handler: DB config missing in .env (DB_HOST, DB_NAME, DB_USER, DB_PASS).");
        return null;
    }

    $mysqli = @new mysqli($db_config_from_env['DB_HOST'], $db_config_from_env['DB_USER'], $db_config_from_env['DB_PASS'], $db_config_from_env['DB_NAME']);
    if ($mysqli->connect_error) {
        error_log("API Handler: DB Connection Error (" . $mysqli->connect_errno . ") " . $mysqli->connect_error);
        return null;
    }
    $mysqli->set_charset('utf8mb4');

    // Configurar zona horaria de MySQL para que coincida con PHP
    $mysqli->query("SET time_zone = '+02:00'");

    $stmt = $mysqli->prepare("SELECT * FROM clientes_valorador WHERE client_identifier = ? AND activo = 1");
    if (!$stmt) {
        error_log("API Handler: DB Prepare Statement Error - " . $mysqli->error);
        $mysqli->close();
        return null;
    }
    $stmt->bind_param("s", $client_identifier);
    $stmt->execute();
    $result = $stmt->get_result();
    $client_config_data = $result->fetch_assoc();

    $stmt->close();
    $mysqli->close();

    if (!$client_config_data) {
        error_log("API Handler: Cliente no encontrado o inactivo para identifier: " . $client_identifier);
        return null;
    }
    // Añadir una acción específica o modificar las existentes en valorador.php
    // para devolver campos como titulo_valorador, subtitulo_valorador, etc.
    // Por ahora, esto devuelve toda la fila de la tabla `clientes_valorador`.
    return $client_config_data;
}

// --- Funciones de Proxy para Google Places API ---

/**
 * Hace proxy de las solicitudes de autocompletado a la API de Google Places.
 * Pasa el 'input' del usuario y un 'session_token' opcional.
 * Devuelve la respuesta de Google directamente al cliente.
 */
function proxy_google_places_autocomplete($api_key) {
    if (empty($api_key)) {
        error_log("API Handler (proxy_google_places_autocomplete): GOOGLE_PLACES_API_KEY is missing.");
        send_json_response(['error' => 'Server configuration error.'], 500);
    }

    $input = $_GET['input'] ?? '';
    if (empty($input)) {
        send_json_response(['error' => 'Input parameter missing.'], 400);
    }

    // El session_token es altamente recomendado por Google para agrupar peticiones y reducir costes.
    // El frontend debe generar y gestionar este token.
    $session_token = $_GET['session_token'] ?? null;

    $google_autocomplete_url = "https://maps.googleapis.com/maps/api/place/autocomplete/json?input=" . urlencode($input) . "&key=" . $api_key . "&language=es&components=country:es&types=address";
    if ($session_token) {
        $google_autocomplete_url .= "&sessiontoken=" . urlencode($session_token);
    }

    $g_response = make_curl_request($google_autocomplete_url);

    // Reenviar las cabeceras y el cuerpo de la respuesta de Google
    header("Content-Type: application/json; charset=UTF-8");
    http_response_code($g_response['http_code']);
    echo $g_response['body'];
    exit;
}

/**
 * Hace proxy de las solicitudes de detalles de un lugar a la API de Google Places.
 * Requiere un 'place_id' y un 'session_token' opcional.
 * Devuelve la respuesta de Google directamente al cliente.
 */
function proxy_google_places_details($api_key) {
    if (empty($api_key)) {
        error_log("API Handler (proxy_google_places_details): GOOGLE_PLACES_API_KEY is missing.");
        send_json_response(['error' => 'Server configuration error.'], 500);
    }

    $place_id = $_GET['place_id'] ?? '';
    if (empty($place_id)) {
        send_json_response(['error' => 'Place ID parameter missing.'], 400);
    }
    
    // El session_token es altamente recomendado por Google para agrupar peticiones y reducir costes.
    $session_token = $_GET['session_token'] ?? null;

    // Especificar los campos a solicitar para controlar los costes.
    $fields = "address_components,formatted_address,geometry,name";
    $google_details_url = "https://maps.googleapis.com/maps/api/place/details/json?place_id=" . urlencode($place_id) . "&key=" . $api_key . "&language=es&fields=" . $fields;
    if ($session_token) {
        $google_details_url .= "&sessiontoken=" . urlencode($session_token);
    }

    $g_response = make_curl_request($google_details_url);

    // Reenviar las cabeceras y el cuerpo de la respuesta de Google
    header("Content-Type: application/json; charset=UTF-8");
    http_response_code($g_response['http_code']);
    echo $g_response['body'];
    exit;
}

// ... Rest of the code remains the same ...
$request_method = $_SERVER['REQUEST_METHOD'];
$action_get = $_GET['action'] ?? null;
// Leer parámetros de GET
$client_id_get = $_GET['client_identifier'] ?? $_GET['client_id'] ?? null;
$api_key_get = $_GET['api_key'] ?? $_GET['uuid'] ?? null;

// Leer parámetros de POST
$post_input_data = null;
$action_post = null;
$client_id_post = null;
$api_key_post = null;

if ($request_method === 'POST') {
    $raw_post_data = file_get_contents('php://input');
    if (!empty($raw_post_data)) {
        $post_input_data = json_decode($raw_post_data, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("API Handler Error: Invalid JSON body. Error: " . json_last_error_msg() . " Body: " . $raw_post_data);
            send_json_response(['detail' => 'Invalid JSON body.'], 400);
        }
        $action_post = $post_input_data['action'] ?? null;
        $client_id_post = $post_input_data['client_identifier'] ?? $post_input_data['client_id'] ?? null;
        $api_key_post = $post_input_data['api_key'] ?? $post_input_data['uuid'] ?? null;
    }
}

$CLIENT_IDENTIFIER_FINAL = null;
$ACTION_FINAL = null;

if ($request_method === 'GET') {
    $CLIENT_IDENTIFIER_FINAL = $client_id_get;
    $ACTION_FINAL = $action_get;
} elseif ($request_method === 'POST') {
    // Para POST (get_valuation), el client_identifier viene en el cuerpo JSON
    $CLIENT_IDENTIFIER_FINAL = $client_id_post;
    $ACTION_FINAL = $action_post;
}


// --- Cargar Configuración del Cliente desde la BD ---
$CLIENT_CONFIG_DATA = null;
$actions_not_requiring_client_config_strictly = ['get_google_key', 'proxy_google_places_autocomplete', 'proxy_google_places_details', 'get_info_by_rc_direct'];

if (!in_array($ACTION_FINAL, $actions_not_requiring_client_config_strictly)) {
    if (empty($CLIENT_IDENTIFIER_FINAL)) {
        // Todas las acciones que llegan aquí ahora requieren un client_id.
        error_log("API Handler: client_identifier no proporcionado para la acción: " . ($ACTION_FINAL ?? 'N/A'));
        send_json_response(['detail' => 'Client identifier no proporcionado.'], 400);
    } else {
        $CLIENT_CONFIG_DATA = get_client_config($CLIENT_IDENTIFIER_FINAL, $db_config_global);

        if (!$CLIENT_CONFIG_DATA && ($ACTION_FINAL === 'get_valuation' || $ACTION_FINAL === 'get_client_config')) {
            error_log("API Handler: Configuración del cliente NO CARGADA para identifier: " . $CLIENT_IDENTIFIER_FINAL . " en acción " . $ACTION_FINAL);
            send_json_response(['detail' => 'Cliente no válido o no configurado para esta acción.'], 403);
        }
    }
}


// --- Enrutamiento de Acciones ---

// NUEVA ACCIÓN para que App.vue cargue la configuración del cliente y URLs de políticas
if ($request_method === 'GET' && $ACTION_FINAL === 'get_client_config') { // Nombre sugerido
    if (empty($CLIENT_IDENTIFIER_FINAL)) {
        send_json_response(['detail' => 'Client identifier no proporcionado.'], 400);
    }
    $client_data_for_vue = get_client_config($CLIENT_IDENTIFIER_FINAL, $db_config_global);
    if ($client_data_for_vue) {
        // Seleccionar solo los campos que Vue necesita para evitar exponer datos sensibles innecesariamente
        $response_data = [
            'client_identifier' => $client_data_for_vue['client_identifier'],
            'nombre_display' => $client_data_for_vue['nombre_display'],
            'logo_url' => $client_data_for_vue['logo_url'],
            'color_primario' => $client_data_for_vue['color_primario'],
            'color_secundario' => $client_data_for_vue['color_secundario'],
            'cta_contacto_url' => $client_data_for_vue['cta_contacto_url'],
            'titulo_valorador' => $client_data_for_vue['titulo_valorador'] ?? null, // Campo opcional
            'subtitulo_valorador' => $client_data_for_vue['subtitulo_valorador'] ?? null, // Campo opcional
            // Nuevos campos de contacto
            'direccion_fisica' => $client_data_for_vue['direccion_fisica'] ?? null,
            'telefono_contacto' => $client_data_for_vue['telefono_contacto'] ?? null,
            'email_contacto_publico' => $client_data_for_vue['email_contacto_publico'] ?? null,
            'sitio_web_url' => $client_data_for_vue['sitio_web_url'] ?? null,
            'whatsapp_numero' => $client_data_for_vue['whatsapp_numero'] ?? null,
            'descripcion_breve' => $client_data_for_vue['descripcion_breve'] ?? null,
            // NO incluir API keys o contraseñas aquí
        ];
        send_json_response($response_data);
    } else {
        send_json_response(['detail' => 'Configuración del cliente no encontrada o cliente inactivo.'], 404);
    }
}

// [DEPRECATED] Acción para obtener la Google API Key. Devuelve null para evitar exponer la clave en el frontend.
if ($request_method === 'GET' && $ACTION_FINAL === 'get_google_key') {
    send_json_response(['google_api_key' => null]);
}

// --- Acciones de Proxy para Google Places ---

if ($request_method === 'GET' && $ACTION_FINAL === 'proxy_google_places_autocomplete') {
    proxy_google_places_autocomplete($google_places_api_key);
}

if ($request_method === 'GET' && $ACTION_FINAL === 'proxy_google_places_details') {
    proxy_google_places_details($google_places_api_key);
}

// La acción 'geocode_address' ha sido eliminada y reemplazada por 'proxy_google_places_details'.
// Acción para obtener información por referencia catastral
if ($request_method === 'GET' && $ACTION_FINAL === 'get_info_by_rc_direct') {
    error_log("PHP: Action get_info_by_rc_direct called for frontend RC search. Client ID: " . ($CLIENT_IDENTIFIER_FINAL ?? 'N/A'));
    $rc_input_completa = strtoupper(trim($_GET['rc'] ?? ''));

    if (empty($rc_input_completa) || (strlen($rc_input_completa) !== 20 && strlen($rc_input_completa) !== 14) ) {
        send_json_response(['error' => 'Referencia Catastral (RC) debe tener 14 o 20 caracteres.'], 400);
    }

    $output_data = [
        'latitude' => null, 'longitude' => null,
        'superficie_catastro' => null, 'ano_construccion_catastro' => null,
        'direccion_catastro_formateada' => '',
        'municipio_catastro' => null, 'provincia_catastro' => null,
        'codigo_postal_catastro' => null,
        'rc_usada' => $rc_input_completa,
        'error' => null
    ];
    $provincia_obtenida_cat = null;
    $municipio_obtenido_cat = null;
    $rc_para_dnp = $rc_input_completa;
    $rc_parcela_14 = substr($rc_input_completa, 0, 14);

    $url_cat_dnp = "http://ovc.catastro.meh.es/OVCServWeb/OVCWcfCallejero/COVCCallejero.svc/rest/Consulta_DNPRC?RefCat=" . urlencode($rc_para_dnp);

    error_log("PHP RC (DNPRC): Calling: " . $url_cat_dnp);
    $dnp_response = make_curl_request($url_cat_dnp);

    if ($dnp_response['errno'] || $dnp_response['http_code'] !== 200) {
        $output_data['error'] = "Error conectando a Catastro (DNPRC) [HTTP: ".$dnp_response['http_code'].", cURL: ".$dnp_response['error']."]";
        error_log("PHP RC (DNPRC) Error: " . $output_data['error'] . " Body: " . $dnp_response['body']);
    } else {
        try {
            $xml_dnp_string = preg_replace('/ xmlns(?::\w+)?="[^"]+"/', '', $dnp_response['body']);
            if(empty(trim($xml_dnp_string))) throw new Exception("Respuesta XML vacía de Catastro DNPRC.");
            $xml_dnp = new SimpleXMLElement($xml_dnp_string);

            if (isset($xml_dnp->control->cudnp) && (int)$xml_dnp->control->cudnp > 0 && isset($xml_dnp->bico->bi)) {
                $bi = $xml_dnp->bico->bi;
                if (isset($bi->dt->np)) $provincia_obtenida_cat = (string)$bi->dt->np;
                if (isset($bi->dt->nm)) $municipio_obtenido_cat = (string)$bi->dt->nm;
                $output_data['provincia_catastro'] = $provincia_obtenida_cat;
                $output_data['municipio_catastro'] = $municipio_obtenido_cat;
                if (isset($bi->debi->ant) && !empty((string)$bi->debi->ant)) $output_data['ano_construccion_catastro'] = (int)$bi->debi->ant;
                $sfc_cat = null;
                if (isset($bi->lcons->cons->dfcons->stl) && !empty((string)$bi->lcons->cons->dfcons->stl)) {
                    $sfc_cat = (int)$bi->lcons->cons->dfcons->stl;
                } elseif (isset($bi->debi->sfc) && !empty((string)$bi->debi->sfc)) {
                    $sfc_cat = (int)$bi->debi->sfc;
                }
                if ($sfc_cat && $sfc_cat > 0) $output_data['superficie_catastro'] = $sfc_cat;
                if (isset($bi->dt->locs->lourb->loint->cp)) $output_data['codigo_postal_catastro'] = (string)$bi->dt->locs->lourb->loint->cp;

                // --- NEW EXTRACTION LOGIC ---
                // Prioritize the full, pre-formatted address string (ldt) if it exists.
                if (isset($bi->ldt) && !empty((string)$bi->ldt)) {
                    $full_address_from_ldt = (string)$bi->ldt;
                    // Format it correctly (Title Case)
                    $output_data['direccion_catastro_formateada'] = mb_ucwords(strtolower($full_address_from_ldt));
                } else {
                    // Fallback: build from parts if ldt is not available
                    $raw_dir_parts = [];
                    if (isset($bi->dt->locs->lourb->dir)) {
                        $dir = $bi->dt->locs->lourb->dir;
                        $raw_dir_parts['tv'] = (string)($dir->tv ?? '');
                        $raw_dir_parts['nv'] = (string)($dir->nv ?? '');
                        $raw_dir_parts['pnp'] = (string)($dir->pnp ?? '');
                    }
                    $output_data['direccion_catastro_formateada'] = format_catastral_address(
                        $raw_dir_parts,
                        $municipio_obtenido_cat,
                        $provincia_obtenida_cat,
                        $output_data['codigo_postal_catastro']
                    );
                }

            } else {
                $cat_error_msg = "RC no encontrada o datos incompletos en Catastro (DNPRC).";
                if(isset($xml_dnp->lerr->err->des)) $cat_error_msg = "Catastro DNPRC: ".(string)$xml_dnp->lerr->err->des;
                $output_data['error'] = $cat_error_msg;
                error_log("PHP RC (DNPRC): No data or error from Catastro. Body: ".$dnp_response['body']);
            }
        } catch (Exception $e) {
            $output_data['error'] = "Error parseando XML de Catastro (DNPRC): " . $e->getMessage();
            error_log("PHP RC (DNPRC) XML parse error: " . $e->getMessage() . ". Body: " . $dnp_response['body']);
        }
    }

    if (empty($output_data['error']) && $provincia_obtenida_cat && $municipio_obtenido_cat) {
        $srs_objetivo = "EPSG:4326";
        $url_cat_coords = "http://ovc.catastro.meh.es/OVCServWeb/OVCWcfCallejero/COVCCoordenadas.svc/rest/Consulta_CPMRC?Provincia=" . urlencode($provincia_obtenida_cat) . "&Municipio=" . urlencode($municipio_obtenido_cat) . "&SRS=" . $srs_objetivo . "&RefCat=" . urlencode($rc_parcela_14);
        error_log("PHP RC (Coords): Calling: " . $url_cat_coords);
        $coords_response = make_curl_request($url_cat_coords);

        if ($coords_response['errno'] || $coords_response['http_code'] !== 200) {
            $coord_err = "Error conectando a Catastro para Coordenadas (CPMRC) [HTTP: ".$coords_response['http_code'].", cURL: ".$coords_response['error']."]";
            $output_data['error'] = ($output_data['error'] ? $output_data['error'] . " | " : "") . $coord_err;
            error_log("PHP RC (Coords) Error: " . $coord_err . " Body: " . $coords_response['body']);
        } else {
            try {
                $xml_coords_string = preg_replace('/ xmlns(?::\w+)?="[^"]+"/', '', $coords_response['body']);
                 if(empty(trim($xml_coords_string))) throw new Exception("Respuesta XML vacía de Catastro CPMRC.");
                $xml_coords = new SimpleXMLElement($xml_coords_string);

                if (isset($xml_coords->control->cucoor) && (int)$xml_coords->control->cucoor > 0 && isset($xml_coords->coordenadas->coord->geo->xcen) && isset($xml_coords->coordenadas->coord->geo->ycen)) {
                    $output_data['longitude'] = (float)$xml_coords->coordenadas->coord->geo->xcen;
                    $output_data['latitude'] = (float)$xml_coords->coordenadas->coord->geo->ycen;
                    if (empty($output_data['direccion_catastro_formateada']) && isset($xml_coords->coordenadas->coord->ldt)) {
                         $output_data['direccion_catastro_formateada'] = (string)$xml_coords->coordenadas->coord->ldt;
                    }
                } else {
                    $cat_coord_error_msg = "No se obtuvieron coordenadas válidas (respuesta CPMRC).";
                    if(isset($xml_coords->lerr->err->des)) $cat_coord_error_msg = "Catastro Coords: ".(string)$xml_coords->lerr->err->des;
                    $output_data['error'] = ($output_data['error'] ? $output_data['error'] . " | " : "") . $cat_coord_error_msg;
                    error_log("PHP RC (Coords): No coords in CPMRC response or error from Catastro. Body: ".$coords_response['body']);
                }
            } catch (Exception $e) {
                $parse_coord_err = "Error parseando XML de Coordenadas Catastro (CPMRC): " . $e->getMessage();
                $output_data['error'] = ($output_data['error'] ? $output_data['error'] . " | " : "") . $parse_coord_err;
                error_log("PHP RC (Coords) XML parse error: " . $e->getMessage() . ". Body: " . $coords_response['body']);
            }
        }
    } elseif (empty($output_data['error'])) {
        $output_data['error'] = "No se pudo determinar Provincia/Municipio desde DNPRC para buscar coordenadas. Intente con la RC de 20 dígitos si usó 14, o verifique la RC.";
        error_log("PHP RC (Coords): Missing Prov/Mun from DNPRC. Cannot call CPMRC.");
    }
    $final_http_code = ($output_data['latitude'] && $output_data['longitude']) ? 200 : ($output_data['error'] ? 404 : 200);
    if ($final_http_code === 404 && empty($output_data['error'])) {
        $output_data['error'] = "No se pudieron obtener datos de ubicación de Catastro.";
    }
    send_json_response($output_data, $final_http_code);
}
// Acción para obtener valoración
if ($request_method === 'POST' && $ACTION_FINAL === 'get_valuation') {
    // Variable para almacenar datos de notificación (se enviará después del commit)
    $notification_data = null;

    if (!$CLIENT_CONFIG_DATA) {
         error_log("API Handler Error (get_valuation): Configuración del cliente NO CARGADA/VÁLIDA para identifier: " . ($CLIENT_IDENTIFIER_FINAL ?? 'N/A'));
         send_json_response(['detail' => 'Cliente no autorizado o configuración no encontrada.'], 403);
    }
    error_log("PHP: POST request for 'get_valuation' for client: " . $CLIENT_IDENTIFIER_FINAL);

    if (!$valorador_api_secret_key || empty($valorador_api_secret_key) || !$valorador_api_endpoint_url || empty($valorador_api_endpoint_url)) {
        error_log("API Handler Error: Valorador service config (VALORADOR_API_ENDPOINT_URL or VALORADOR_API_SECRET_KEY) missing in .env");
        send_json_response(['detail' => 'Server config error (valorador service).'], 500);
    }

    if (!isset($post_input_data['valuation_data']) || !is_array($post_input_data['valuation_data'])) {
        error_log("API Handler Error (proxy): 'valuation_data' missing or invalid. Payload: " . json_encode($post_input_data));
        send_json_response(['detail' => "Invalid JSON structure: 'valuation_data' missing or invalid."], 400);
    }
    $valuation_payload_for_fastapi = $post_input_data['valuation_data'];
    
    // Log del payload completo para debug
    error_log("Payload completo de valoración: " . print_r($valuation_payload_for_fastapi, true));
    
    // Variable para almacenar el ID del lead insertado
    $lead_id = null;
    
    // Conexión a la base de datos
    $mysqli = @new mysqli($db_config_global['DB_HOST'], $db_config_global['DB_USER'], $db_config_global['DB_PASS'], $db_config_global['DB_NAME']);
    if ($mysqli->connect_error) {
        error_log("API Handler Error (DB Connection): " . $mysqli->connect_errno . ") " . $mysqli->connect_error);
        // No enviamos error al cliente, continuamos con el proceso normal
    } else {
        $mysqli->set_charset('utf8mb4');

        // Configurar zona horaria de MySQL para que coincida con PHP
        $mysqli->query("SET time_zone = '+02:00'");
        
        // Guardar datos de contacto en la tabla valorador_leads si existen
        if (isset($post_input_data['contact_data']) && is_array($post_input_data['contact_data'])) {
            $contact_info = $post_input_data['contact_data'];
            $nombre = $contact_info['nombre'] ?? null;
            $email = $contact_info['email'] ?? null;
            $telefono = $contact_info['telefono'] ?? null;
            $necesidad = $contact_info['necesidad'] ?? null;
            
            // Variable para almacenar el ID del lead
            $lead_id = null;
            $is_new_lead = true;
            
            // Verificar si el usuario ya existe (por email)
            if (!empty($email)) {
                // Primero obtener el cliente_valorador_id
                $stmt_get_client_id = $mysqli->prepare("SELECT id FROM clientes_valorador WHERE client_identifier = ? AND activo = 1");
                if ($stmt_get_client_id) {
                    $stmt_get_client_id->bind_param("s", $CLIENT_IDENTIFIER_FINAL);
                    $stmt_get_client_id->execute();
                    $result_client_id = $stmt_get_client_id->get_result();
                    if ($result_client_id->num_rows > 0) {
                        $client_data = $result_client_id->fetch_assoc();
                        $cliente_valorador_id_check = $client_data['id'];
                        $stmt_get_client_id->close();

                        $check_existing = $mysqli->prepare("SELECT id, uuid FROM valorador_leads WHERE email = ? AND cliente_valorador_id = ?");
                        if ($check_existing) {
                            $check_existing->bind_param("si", $email, $cliente_valorador_id_check);
                            $check_existing->execute();
                            $result = $check_existing->get_result();

                            if ($result->num_rows > 0) {
                                // Usuario existente - usar el ID existente
                                $row = $result->fetch_assoc();
                                $lead_id = $row['id'];
                                $uuid = $row['uuid'];
                                $is_new_lead = false;

                                // Actualizar fecha de última actividad y contador de valoraciones
                                $update = $mysqli->prepare("UPDATE valorador_leads SET fecha_modificacion = NOW(), num_valoraciones = num_valoraciones + 1, necesidad = ? WHERE id = ?");
                                if ($update) {
                                    $update->bind_param("si", $necesidad, $lead_id);
                                    if ($update->execute()) {
                                        error_log("Lead existente actualizado con ID: " . $lead_id . ". Incrementado contador de valoraciones.");

                                        // Marcar para notificación posterior (después del commit)
                                        $notification_data = ['lead_id' => $lead_id, 'cliente_valorador_id' => $cliente_valorador_id_check, 'is_new_lead' => false];
                                        error_log("[NOTIFICATION_DEBUG] Marcado para notificación - Lead existente ID: {$lead_id}");
                                        error_log("[NOTIFICATION_DEBUG] Variable asignada - notification_data: " . json_encode($notification_data));
                                    } else {
                                        error_log("Error al actualizar lead existente: " . $update->error);
                                    }
                                    $update->close();
                                }
                            }
                            $check_existing->close();
                        }
                    } else {
                        error_log("Error: Cliente no encontrado para client_identifier: " . $CLIENT_IDENTIFIER_FINAL);
                    }
                } else {
                    error_log("Error al preparar consulta para obtener cliente_valorador_id: " . $mysqli->error);
                }
            }
            
            // Si es un nuevo lead, crear un nuevo registro
            if ($is_new_lead) {
                // Generar UUID v4 para nuevo lead
                $uuid = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                    mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                    mt_rand(0, 0xffff),
                    mt_rand(0, 0x0fff) | 0x4000,
                    mt_rand(0, 0x3fff) | 0x8000,
                    mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
                );
                
                // Obtener cliente_valorador_id desde client_identifier
                $stmt_get_client_id = $mysqli->prepare("SELECT id FROM clientes_valorador WHERE client_identifier = ? AND activo = 1");
                if ($stmt_get_client_id) {
                    $stmt_get_client_id->bind_param("s", $CLIENT_IDENTIFIER_FINAL);
                    $stmt_get_client_id->execute();
                    $result_client_id = $stmt_get_client_id->get_result();
                    if ($result_client_id->num_rows > 0) {
                        $client_data = $result_client_id->fetch_assoc();
                        $cliente_valorador_id = $client_data['id'];
                        $stmt_get_client_id->close();

                        $stmt = $mysqli->prepare("INSERT INTO valorador_leads (uuid, cliente_valorador_id, nombre, email, telefono, necesidad, num_valoraciones) VALUES (?, ?, ?, ?, ?, ?, 1)");
                        if ($stmt) {
                            $stmt->bind_param("sissss", $uuid, $cliente_valorador_id, $nombre, $email, $telefono, $necesidad);
                            if ($stmt->execute()) {
                                $lead_id = $mysqli->insert_id;
                                error_log("Nuevo lead creado con ID: {$lead_id}");

                                // Marcar para notificación posterior (después del commit)
                                $notification_data = ['lead_id' => $lead_id, 'cliente_valorador_id' => $cliente_valorador_id, 'is_new_lead' => true];
                                error_log("[NOTIFICATION_DEBUG] Marcado para notificación - Nuevo lead ID: {$lead_id}");
                                error_log("[NOTIFICATION_DEBUG] Variable asignada - notification_data: " . json_encode($notification_data));
                                error_log("[NOTIFICATION_DEBUG] CHECKPOINT A - notification_data después de asignar: " . json_encode($notification_data));

                            } else {
                                error_log("Error al guardar nuevo lead en la base de datos: " . $stmt->error);
                            }
                            $stmt->close();
                        } else {
                            error_log("Error al preparar la consulta para guardar nuevo lead: " . $mysqli->error);
                        }
                    } else {
                        error_log("Error: Cliente no encontrado para client_identifier: " . $CLIENT_IDENTIFIER_FINAL);
                    }
                } else {
                    error_log("Error al preparar consulta para obtener cliente_valorador_id: " . $mysqli->error);
                }
            }

            error_log("[NOTIFICATION_DEBUG] CHECKPOINT B - notification_data después de sección leads: " . ($notification_data !== null ? json_encode($notification_data) : 'NULL'));
            error_log("[NOTIFICATION_DEBUG] CHECKPOINT B2 - Tipo de notification_data: " . gettype($notification_data));
            error_log("[NOTIFICATION_DEBUG] CHECKPOINT B3 - var_dump de notification_data: " . var_export($notification_data, true));

            // Envío de email (mantenemos la funcionalidad original)
            $email_destino_leads = $CLIENT_CONFIG_DATA['email_notificaciones_leads'] ?? '<EMAIL>';
            error_log("PHP Contact Data for client " . $CLIENT_IDENTIFIER_FINAL . ": Nombre: " . ($contact_info['nombre'] ?? 'N/A') . ", Email: " . ($contact_info['email'] ?? 'N/A') . ". Attempting to notify: " . $email_destino_leads);

            $to = $email_destino_leads;
            $subject = "Nuevo Lead del Valorador Web para " . ($CLIENT_CONFIG_DATA['nombre_display'] ?? $CLIENT_IDENTIFIER_FINAL);
            $message = "Se ha recibido una nueva solicitud de valoración:\n\n";
            $message .= "Cliente Inmobiliaria: " . ($CLIENT_CONFIG_DATA['nombre_display'] ?? $CLIENT_IDENTIFIER_FINAL) . "\n";
            $message .= "Nombre Lead: " . ($contact_info['nombre'] ?? 'N/A') . "\n";
            $message .= "Email Lead: " . ($contact_info['email'] ?? 'N/A') . "\n";
            $message .= "Teléfono Lead: " . ($contact_info['telefono'] ?? 'N/A') . "\n";
            $message .= "Necesidad: " . ($contact_info['necesidad'] ?? 'N/A') . "\n\n";
            $message .= "Datos de la propiedad (resumen):\n";
            $message .= "Dirección/RC: " . ($valuation_payload_for_fastapi['DireccionCompleta'] ?? $valuation_payload_for_fastapi['ReferenciaCatastral'] ?? 'N/A') . "\n";
            $message .= "Tipo: " . ($valuation_payload_for_fastapi['TipoPrincipal'] ?? 'N/A') . "\n";
            $message .= "Tamaño: " . ($valuation_payload_for_fastapi['Tamaño'] ?? 'N/A') . " m2\n";
            $headers = "From: valorador@" . ($_SERVER['HTTP_HOST'] ?? 'inmoautomation.com') . "\r\n";
            $headers .= "Reply-To: " . ($contact_info['email'] ?? $to) . "\r\n";
            $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

            // DESCOMENTAR PARA ACTIVAR ENVÍO DE EMAIL
            // if (mail($to, $subject, $message, $headers)) {
            //     error_log("Email de notificación de lead ENVIADO a: " . $to);
            // } else {
            //     error_log("ERROR al enviar email de notificación de lead a: " . $to);
            // }
            error_log("Email de notificación de lead (simulado) para: " . $to . " con asunto: " . $subject);
        }
    }
    
    $headers_to_render_api = [
        "X-API-KEY: " . $valorador_api_secret_key,
        "Content-Type: application/json",
        "Accept: application/json"
    ];
    $val_response = make_curl_request($valorador_api_endpoint_url, 'POST', $headers_to_render_api, $valuation_payload_for_fastapi);

    error_log("[NOTIFICATION_DEBUG] CHECKPOINT CURL - val_response errno: " . ($val_response['errno'] ?? 'NULL') . ", notification_data: " . ($notification_data !== null ? json_encode($notification_data) : 'NULL'));

    if ($val_response['errno']) {
        error_log("API Handler Critical Error (proxy valuation): Valorador API cURL Error: " . $val_response['error'] . " URL: " . $valorador_api_endpoint_url);
        error_log("[NOTIFICATION_DEBUG] TERMINANDO POR ERROR CURL - notification_data se perderá");
        send_json_response(['detail' => "Valorador service communication error.", 'curl_error' => $val_response['error']], 503);
    }
    
    // Procesar la respuesta de la API de valoración
    $valoracion_response = json_decode($val_response['body'], true);
    error_log("Respuesta de valoración recibida: " . json_encode($valoracion_response));
    
    // Función auxiliar para buscar valores en el payload con múltiples posibles claves (versión robusta de OLD_api_handler.php)
    function getValueFromPayload($payload, $possibleKeys) {
        foreach ($possibleKeys as $key) {
            // Usar array_key_exists para incluir valores que podrían ser '0' o false pero no vacíos ''
            if (array_key_exists($key, $payload) && $payload[$key] !== '' && $payload[$key] !== null) {
                return $payload[$key];
            }
        }
        return null; // Devolver null si no se encuentra ninguna clave
    }

    // Guardar la valoración en la base de datos (Lógica de OLD_api_handler.php restaurada)
    // NOTA: $notification_data ya fue inicializado y posiblemente asignado en la sección de leads

    if ($mysqli && !$mysqli->connect_error && $valoracion_response) {
        try {

            // La respuesta de la API ya fue decodificada, procedemos a la extracción de datos.

            // --- EXTRACCIÓN DE DATOS (FASE 1: DESDE LA RESPUESTA DE LA API) ---
            $valor_min = $valoracion_response['valor_estimado_min'] ?? null;
            $valor_max = $valoracion_response['valor_estimado_max'] ?? null;
                        // El año de construcción SIEMPRE viene del payload del usuario, es la fuente de verdad.
            $ano_from_payload = getValueFromPayload($valuation_payload_for_fastapi, ['ano_construccion_catastro', 'ano_construccion', 'ant']);
            $ano_construccion_catastro = $ano_from_payload !== null ? (int)$ano_from_payload : null;
            $estadisticas_zonales = $valoracion_response['estadisticas_zonales'] ?? [];
            $precio_m2_promedio = $estadisticas_zonales['precio_m2_promedio'] ?? null;
            $tamano_promedio = $estadisticas_zonales['tamaño_promedio'] ?? null;
            $porcentaje_con_piscina = $estadisticas_zonales['porcentaje_con_piscina'] ?? null;
            $porcentaje_con_parking = $estadisticas_zonales['porcentaje_con_parking'] ?? null;
            $porcentaje_con_ascensor = $estadisticas_zonales['porcentaje_con_ascensor'] ?? null;
            $porcentaje_con_terraza = $estadisticas_zonales['porcentaje_con_terraza'] ?? null;
            $porcentaje_reformadas = $estadisticas_zonales['porcentaje_reformadas'] ?? null;
            $numero_propiedades_analizadas = $estadisticas_zonales['numero_propiedades_analizadas'] ?? null;
            $distancia_al_centroide_km = $estadisticas_zonales['distancia_al_centroide_km'] ?? null;
            $analisis_comparativo = $valoracion_response['analisis_comparativo'] ?? [];
            $precio_vs_zona_porcentaje = $analisis_comparativo['precio_vs_zona_porcentaje'] ?? null;
            $tamano_vs_zona_porcentaje = $analisis_comparativo['tamaño_vs_zona_porcentaje'] ?? null;

            // --- EXTRACCIÓN DE DATOS (FASE 2: DESDE EL PAYLOAD ORIGINAL DEL USUARIO) - LÓGICA COMPLETA Y ROBUSTA ---
            $referencia_catastral = getValueFromPayload($valuation_payload_for_fastapi, ['ReferenciaCatastral', 'referencia_catastral', 'ref_catastral', 'rc']);
            $direccion = getValueFromPayload($valuation_payload_for_fastapi, ['DireccionCompleta', 'direccion_completa', 'direccion', 'address']);
            
            // Coordenadas: Extraer raw, y solo convertir a float si no es null
            $latitud_raw = getValueFromPayload($valuation_payload_for_fastapi, ['Latitud', 'latitud', 'lat', 'latitude']);
            $longitud_raw = getValueFromPayload($valuation_payload_for_fastapi, ['Longitud', 'longitud', 'lon', 'lng', 'longitude']);
            $latitud = $latitud_raw !== null ? (float)$latitud_raw : null;
            $longitud = $longitud_raw !== null ? (float)$longitud_raw : null;

            $tipo_principal = getValueFromPayload($valuation_payload_for_fastapi, ['TipoPrincipal', 'tipo_principal', 'tipo', 'property_type']);
            $subtipo = getValueFromPayload($valuation_payload_for_fastapi, ['Subtipo', 'subtipo', 'property_subtype']);

            // Campos numéricos: Extraer raw, y solo convertir a int si no es null o vacío
            $superficie_raw = getValueFromPayload($valuation_payload_for_fastapi, ['Tamaño', 'tamanio', 'tamano', 'superficie', 'area', 'size']);
            $superficie_parcela_raw = getValueFromPayload($valuation_payload_for_fastapi, ['TamañoParcela', 'tamanioParcela', 'superficie_parcela', 'Metros_Parcela']);
            $habitaciones_raw = getValueFromPayload($valuation_payload_for_fastapi, ['NumHabitaciones', 'num_habitaciones', 'habitaciones', 'bedrooms', 'rooms', 'Habitaciones']);
            $banos_raw = getValueFromPayload($valuation_payload_for_fastapi, ['NumBaños', 'num_banos', 'banos', 'bathrooms', 'Baños']);
            
            $superficie = $superficie_raw !== null ? (int)$superficie_raw : null;
            $superficie_parcela = $superficie_parcela_raw !== null ? (int)$superficie_parcela_raw : null;
            $habitaciones = $habitaciones_raw !== null ? (int)$habitaciones_raw : null;
            $banos = $banos_raw !== null ? (int)$banos_raw : null;

            $estado = getValueFromPayload($valuation_payload_for_fastapi, ['EstadoConservacion', 'estado_conservacion', 'estado', 'condition', 'Estado']);
            $planta = getValueFromPayload($valuation_payload_for_fastapi, ['Planta', 'planta', 'floor', 'PisoTexto']);

            // Lógica de construcción de Extras: solo incluir los que son 'true' en un array simple.
            $extras_array = [];
            $posibles_extras = [
                'Ascensor', 'Parking', 'Piscina', 'Terraza', 'Trastero', 'Jardin', 'AireAcondicionado', 'ObraNueva',
                'nlp_esta_reformado', 'nlp_tiene_vistas_mar', 'nlp_es_primera_linea', 'nlp_es_luminoso',
                'nlp_tiene_calefaccion', 'nlp_es_exterior'
            ];

            foreach ($posibles_extras as $extra) {
                // Comprobar si la clave existe y su valor es explícitamente true (booleano o string 'true').
                if (isset($valuation_payload_for_fastapi[$extra])) {
                    $value = $valuation_payload_for_fastapi[$extra];
                    if ($value === true || (is_string($value) && strtolower($value) === 'true')) {
                        $extras_array[] = $extra; // Añadir solo el nombre de la clave al array.
                    }
                }
            }
            // Convertir a JSON solo si el array final no está vacío.
            $extras_json = !empty($extras_array) ? json_encode($extras_array, JSON_UNESCAPED_UNICODE) : null;
            $notas_agente = null; // Campo reservado para uso futuro

            // Generar UUID v4 para la valoración
            $uuid_valoracion = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                mt_rand(0, 0x0fff) | 0x4000, mt_rand(0, 0x3fff) | 0x8000,
                mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
            );

            // Obtener cliente_valorador_id
            $stmt_get_client_id = $mysqli->prepare("SELECT id FROM clientes_valorador WHERE client_identifier = ? AND activo = 1");
            $stmt_get_client_id->bind_param("s", $CLIENT_IDENTIFIER_FINAL);
            $stmt_get_client_id->execute();
            $result_client_id = $stmt_get_client_id->get_result();
            if ($result_client_id->num_rows === 0) {
                throw new Exception('Cliente no encontrado para client_identifier: ' . $CLIENT_IDENTIFIER_FINAL);
            }
            $client_data = $result_client_id->fetch_assoc();
            error_log("[NOTIFICATION_DEBUG] CHECKPOINT B3.5 - notification_data ANTES de sobrescribir cliente_valorador_id: " . ($notification_data !== null ? json_encode($notification_data) : 'NULL'));
            error_log("[NOTIFICATION_DEBUG] VALORES - cliente_valorador_id ANTES: " . (isset($cliente_valorador_id) ? $cliente_valorador_id : 'UNDEFINED') . ", client_data['id']: " . ($client_data['id'] ?? 'NULL'));
            $cliente_valorador_id = $client_data['id'];
            error_log("[NOTIFICATION_DEBUG] VALORES - cliente_valorador_id DESPUÉS: {$cliente_valorador_id}");
            error_log("[NOTIFICATION_DEBUG] CHECKPOINT B3.6 - notification_data INMEDIATAMENTE después de sobrescribir: " . ($notification_data !== null ? json_encode($notification_data) : 'NULL'));
            $stmt_get_client_id->close();

            error_log("[NOTIFICATION_DEBUG] CHECKPOINT B4 - notification_data después de sobrescribir cliente_valorador_id: " . ($notification_data !== null ? json_encode($notification_data) : 'NULL'));

            // --- INSERCIÓN EN BASE DE DATOS ---
            $sql = "INSERT INTO valorador_valoraciones (uuid, cliente_valorador_id, referencia_catastral, direccion, latitud, longitud, tipo_principal, subtipo, superficie, superficie_parcela, habitaciones, banos, estado, planta, extras, valor_estimado_min, valor_estimado_max, ano_construccion_catastro, precio_m2_promedio, tamano_promedio, porcentaje_con_piscina, porcentaje_con_parking, porcentaje_con_ascensor, porcentaje_con_terraza, porcentaje_reformadas, numero_propiedades_analizadas, distancia_al_centroide_km, precio_vs_zona_porcentaje, tamano_vs_zona_porcentaje, notas_agente) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $mysqli->prepare($sql);
            if (!$stmt) {
                throw new Exception('Error al preparar la consulta de inserción: ' . $mysqli->error);
            }

            $types = 'sissddssiiiisssiiidddddddiddds';
            $stmt->bind_param($types, 
                $uuid_valoracion, $cliente_valorador_id, $referencia_catastral, $direccion, $latitud, $longitud, 
                $tipo_principal, $subtipo, $superficie, $superficie_parcela, $habitaciones, $banos, $estado, 
                $planta, $extras_json, $valor_min, $valor_max, $ano_construccion_catastro, $precio_m2_promedio, 
                $tamano_promedio, $porcentaje_con_piscina, $porcentaje_con_parking, $porcentaje_con_ascensor, 
                $porcentaje_con_terraza, $porcentaje_reformadas, $numero_propiedades_analizadas, 
                $distancia_al_centroide_km, $precio_vs_zona_porcentaje, $tamano_vs_zona_porcentaje, $notas_agente
            );

            if (!$stmt->execute()) {
                throw new Exception('Error al guardar la valoración: ' . $stmt->error);
            }

            $valoracion_id = $mysqli->insert_id;
            error_log("Valoración guardada con ID: $valoracion_id");
            $stmt->close();

            error_log("[NOTIFICATION_DEBUG] CHECKPOINT C - notification_data después de insertar valoración: " . ($notification_data !== null ? json_encode($notification_data) : 'NULL'));

            // Si hay un lead_id, actualizar las tablas correspondientes
            // Si hay un lead_id, actualizar las tablas correspondientes y gestionar lead_sequence_tracking
            if ($lead_id) {
                error_log("Asociando valoración ID $valoracion_id con lead ID $lead_id");
                
                $update_val = $mysqli->prepare("UPDATE valorador_valoraciones SET lead_id = ? WHERE id = ?");
                if (!$update_val) throw new Exception('Error preparando update de valoración: ' . $mysqli->error);
                $update_val->bind_param("ii", $lead_id, $valoracion_id);
                if (!$update_val->execute()) throw new Exception('Error actualizando valoración con lead_id: ' . $update_val->error);
                $update_val->close();

                $update_lead = $mysqli->prepare("UPDATE valorador_leads SET valoracion_id = ? WHERE id = ?");
                if (!$update_lead) throw new Exception('Error preparando update de lead: ' . $mysqli->error);
                $update_lead->bind_param("ii", $valoracion_id, $lead_id);
                if (!$update_lead->execute()) throw new Exception('Error actualizando lead con valoracion_id: ' . $update_lead->error);
                $update_lead->close();

                // Lógica para gestionar lead_sequence_tracking (INSERT o UPDATE)
                $assigned_sequence_id = null;

                // 1. Obtener el agency_id real desde clientes_valorador
                $actual_agency_id = null;
                $stmt_get_actual_agency_id = $mysqli->prepare("SELECT agency_id FROM clientes_valorador WHERE id = ?");
                if ($stmt_get_actual_agency_id) {
                    $stmt_get_actual_agency_id->bind_param("i", $cliente_valorador_id);
                    $stmt_get_actual_agency_id->execute();
                    $result_actual_agency_id = $stmt_get_actual_agency_id->get_result();
                    $agency_data_from_cv = $result_actual_agency_id->fetch_assoc();
                    $stmt_get_actual_agency_id->close();

                    if ($agency_data_from_cv && isset($agency_data_from_cv['agency_id'])) {
                        $actual_agency_id = $agency_data_from_cv['agency_id'];
                        error_log("Actual agency_id {$actual_agency_id} encontrado para cliente_valorador_id {$cliente_valorador_id}.");
                    } else {
                        error_log("ADVERTENCIA: No se encontró agency_id en clientes_valorador para cliente_valorador_id {$cliente_valorador_id}. No se puede asignar secuencia.");
                    }
                } else {
                    error_log("Error al preparar la consulta para obtener agency_id de clientes_valorador: " . $mysqli->error);
                }

                if ($actual_agency_id !== null) {
                    // 2. Obtener el plan activo de la agencia (usando el actual_agency_id)
                    $stmt_get_plan = $mysqli->prepare("SELECT plan_id FROM suscripciones WHERE agency_id = ? AND estado = 'active' ORDER BY fecha_creacion DESC LIMIT 1");
                    if ($stmt_get_plan) {
                        $stmt_get_plan->bind_param("i", $actual_agency_id);
                        $stmt_get_plan->execute();
                        $result_plan = $stmt_get_plan->get_result();
                        $subscription_data = $result_plan->fetch_assoc();
                        $stmt_get_plan->close();

                        if ($subscription_data && isset($subscription_data['plan_id'])) {
                            $plan_id = $subscription_data['plan_id'];
                            error_log("Plan ID {$plan_id} encontrado para la agencia ID {$actual_agency_id}.");

                            // 3. Obtener la primera secuencia asignada a ese plan
                            $stmt_get_sequence = $mysqli->prepare("SELECT sequence_id FROM plan_sequence_assignments WHERE plan_id = ? ORDER BY priority ASC LIMIT 1");
                            if ($stmt_get_sequence) {
                                $stmt_get_sequence->bind_param("i", $plan_id);
                                $stmt_get_sequence->execute();
                                $result_sequence = $stmt_get_sequence->get_result();
                                $assignment_data = $result_sequence->fetch_assoc();
                                $stmt_get_sequence->close();

                                if ($assignment_data && isset($assignment_data['sequence_id'])) {
                                    $assigned_sequence_id = $assignment_data['sequence_id'];
                                    error_log("Secuencia ID {$assigned_sequence_id} asignada al lead ID {$lead_id} a través del plan ID {$plan_id}.");
                                } else {
                                    error_log("ADVERTENCIA: El plan ID {$plan_id} de la agencia ID {$actual_agency_id} no tiene secuencias asignadas en 'plan_sequence_assignments'.");
                                }
                            } else {
                                error_log("Error al preparar la consulta para obtener la secuencia del plan: " . $mysqli->error);
                            }
                        } else {
                            error_log("ADVERTENCIA: No se encontró una suscripción activa para la agencia ID {$actual_agency_id}. No se puede asignar secuencia.");
                        }
                    } else {
                        error_log("Error al preparar la consulta para obtener el plan de la agencia (suscripciones): " . $mysqli->error);
                    }
                }

                // 4. Insertar o actualizar en lead_sequence_tracking
                if ($assigned_sequence_id !== null) {
                    // Verificar si ya existe un registro para este lead
                    $stmt_check_tracking = $mysqli->prepare("SELECT id FROM lead_sequence_tracking WHERE lead_id = ?");
                    if ($stmt_check_tracking) {
                        $stmt_check_tracking->bind_param("i", $lead_id);
                        $stmt_check_tracking->execute();
                        $result_tracking = $stmt_check_tracking->get_result();
                        $tracking_record_exists = $result_tracking->num_rows > 0;
                        $stmt_check_tracking->close();

                        if ($tracking_record_exists) {
                            // Actualizar el registro existente
                            $stmt_update_tracking = $mysqli->prepare("UPDATE lead_sequence_tracking SET sequence_id = ?, status = 'pending_activation', updated_at = NOW() WHERE lead_id = ?");
                            if ($stmt_update_tracking) {
                                $stmt_update_tracking->bind_param("ii", $assigned_sequence_id, $lead_id);
                                if (!$stmt_update_tracking->execute()) {
                                    error_log("Fallo al actualizar lead_sequence_tracking para lead_id {$lead_id}: " . $stmt_update_tracking->error);
                                }
                                $stmt_update_tracking->close();
                            } else {
                                error_log("Error al preparar la consulta de actualización de lead_sequence_tracking: " . $mysqli->error);
                            }
                        } else {
                            // Insertar un nuevo registro
                            $stmt_insert_tracking = $mysqli->prepare("INSERT INTO lead_sequence_tracking (lead_id, sequence_id, status, next_due_date, created_at, updated_at) VALUES (?, ?, 'pending_activation', NULL, NOW(), NOW())");
                            if ($stmt_insert_tracking) {
                                $stmt_insert_tracking->bind_param("ii", $lead_id, $assigned_sequence_id);
                                if (!$stmt_insert_tracking->execute()) {
                                    error_log("Fallo al insertar en lead_sequence_tracking para lead_id {$lead_id}: " . $stmt_insert_tracking->error);
                                }
                                $stmt_insert_tracking->close();
                            } else {
                                error_log("Error al preparar la consulta de inserción de lead_sequence_tracking: " . $mysqli->error);
                            }
                        }
                    } else {
                        error_log("Error al preparar la consulta de verificación de lead_sequence_tracking: " . $mysqli->error);
                    }
                } else {
                    error_log("ADVERTENCIA: No se pudo asignar una secuencia al lead ID {$lead_id}. No se creará/actualizará el registro en lead_sequence_tracking.");
                }

                error_log("Disparando tarea de envío de informe para Valoracion UUID: $uuid_valoracion y Lead ID: $lead_id");
                crearTareaEnvioInforme($uuid_valoracion, $lead_id);
            }

            error_log("[NOTIFICATION_DEBUG] CHECKPOINT D - notification_data al final del try: " . ($notification_data !== null ? json_encode($notification_data) : 'NULL'));

        } catch (Exception $e) {
            error_log('Error en el bloque de guardado de valoración: ' . $e->getMessage());
            error_log("[NOTIFICATION_DEBUG] CHECKPOINT E - notification_data en catch: " . ($notification_data !== null ? json_encode($notification_data) : 'NULL'));
        }

        error_log("[NOTIFICATION_DEBUG] Después del try-catch, notification_data: " . ($notification_data !== null ? 'TIENE DATOS' : 'ES NULL'));
    } // FIN del bloque if principal

    // ENVIAR NOTIFICACIÓN DESPUÉS DEL COMMIT Y ANTES DE LA RESPUESTA (solución definitiva)
    error_log("[NOTIFICATION_DEBUG] Verificando notification_data: " . ($notification_data !== null ? 'TIENE DATOS' : 'ES NULL'));
    if ($notification_data !== null) {
        error_log("[NOTIFICATION_DEBUG] Enviando notificación post-commit para Lead ID: {$notification_data['lead_id']}, Cliente Valorador ID: {$notification_data['cliente_valorador_id']}, Nuevo Lead: " . ($notification_data['is_new_lead'] ? 'Sí' : 'No'));
        crearTareaNotificacionLead(
            $notification_data['lead_id'],
            $notification_data['cliente_valorador_id'],
            $notification_data['is_new_lead']
        );
    } else {
        error_log("[NOTIFICATION_DEBUG] No hay notification_data para enviar");
    }

    if ($allowed_origin_for_header && !headers_sent()) {
        header("Access-Control-Allow-Origin: {$allowed_origin_for_header}");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Client-Identifier, X-API-Key');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    }
    if (!headers_sent()) {
        http_response_code($val_response['http_code']);
        header('Content-Type: application/json');
    }
    echo $val_response['body'];
    exit;
}

// Nueva acción para cargar la config del cliente para Vue.js App
if ($request_method === 'GET' && $ACTION_FINAL === 'get_client_page_config') { // Nombre sugerido
    if (empty($CLIENT_IDENTIFIER_FINAL)) {
        send_json_response(['detail' => 'Client identifier no proporcionado.'], 400);
    }
    $config_data = get_client_config($CLIENT_IDENTIFIER_FINAL, $db_config);
    if ($config_data) {
        // Filtrar y devolver solo lo necesario y seguro para el frontend
        $response_for_vue = [
            'client_identifier' => $config_data['client_identifier'],
            'nombre_display' => $config_data['nombre_display'],
            'logo_url' => $config_data['logo_url'],
            'color_primario' => $config_data['color_primario'],
            'color_secundario' => $config_data['color_secundario'],
            'cta_contacto_url' => $config_data['cta_contacto_url'],
            'titulo_valorador' => $config_data['titulo_valorador'] ?? null,
            'subtitulo_valorador' => $config_data['subtitulo_valorador'] ?? null,
            // Nuevos campos de contacto
            'direccion_fisica' => $config_data['direccion_fisica'] ?? null,
            'telefono_contacto' => $config_data['telefono_contacto'] ?? null,
            'email_contacto_publico' => $config_data['email_contacto_publico'] ?? null,
            'sitio_web_url' => $config_data['sitio_web_url'] ?? null,
            'whatsapp_numero' => $config_data['whatsapp_numero'] ?? null,
            'descripcion_breve' => $config_data['descripcion_breve'] ?? null,
            // Google API Key también se podría añadir aquí si no se quiere una llamada separada
            // 'google_api_key' => $google_places_api_key // Si se decide consolidar
        ];
        send_json_response($response_for_vue);
    } else {
        send_json_response(['detail' => 'Cliente no encontrado o inactivo.'], 404);
    }
}

// Verificar si la acción solicitada es válida
$allowed_get_actions = ['get_google_key', 'get_info_by_rc_direct', 'get_client_config', 'get_client_page_config', 'proxy_google_places_autocomplete', 'proxy_google_places_details']; // Añadir nuevas acciones GET
$error_detail = null;
$http_code_default = 200;

// Si llegamos aquí, es porque ninguna de las acciones anteriores coincidió
// Verificar si es una acción válida
if ($request_method === 'GET' && !in_array($ACTION_FINAL, $allowed_get_actions)) {
    $error_detail = empty($ACTION_FINAL) ? 'Acción GET no especificada.' : 'Acción GET no válida: ' . htmlspecialchars($ACTION_FINAL);
    $http_code_default = 400;
} elseif ($request_method === 'POST' && $ACTION_FINAL !== 'get_valuation') {
    $error_detail = empty($ACTION_FINAL) ? 'Acción POST no especificada en el cuerpo JSON.' : 'Acción POST no válida en el cuerpo JSON: ' . htmlspecialchars($ACTION_FINAL);
    $http_code_default = 400;
} elseif ($request_method !== 'GET' && $request_method !== 'POST') {
    $error_detail = 'Método HTTP no permitido: ' . htmlspecialchars($request_method);
    $http_code_default = 405; // Method Not Allowed
}

// Si hay un error, registrarlo y enviar respuesta
if ($error_detail) {
    error_log("API Handler: Invalid request. Method: $request_method, Final Action: " . ($ACTION_FINAL ?? 'N/A') . ", ClientID: ". ($CLIENT_IDENTIFIER_FINAL ?? 'N/A') .". Error: $error_detail");
    send_json_response(['detail' => $error_detail], $http_code_default);
}
?>
