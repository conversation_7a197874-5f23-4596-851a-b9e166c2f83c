<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// bootstrap.php se encargará de error_reporting e ini_set para no CLI.
// ini_set('display_errors', 1); // ELIMINADO
// error_reporting(E_ALL); // ELIMINADO
 // Incluir bootstrap.php
 // Usar config central de CORS
// Cabeceras CORS y manejo de OPTIONS ahora en cors.php
header('Content-Type: application/json');
// Composer autoload y Dotenv son manejados por bootstrap.php
// require_once __DIR__ . '/vendor/autoload.php'; // ELIMINADO
// try { $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); $dotenv->load(); } catch ... // ELIMINADO
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
// Función de log (puede permanecer o usar una global de bootstrap si existe)
function custom_log_valoraciones_lead($message) {
    $logFile = __DIR__ . '/debug_valoraciones_lead.log';
    $timestamp = date('Y-m-d H:i:s');
    @file_put_contents($logFile, "[$timestamp] " . $message . "\n", FILE_APPEND);
}
// Verificar Token JWT
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if (!$authHeader) {
    http_response_code(401);
    custom_log_valoraciones_lead("Intento de acceso sin token.");
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Token no proporcionado.']);
    exit();
}
$tokenParts = explode(' ', $authHeader);
if (count($tokenParts) !== 2 || strcasecmp($tokenParts[0], 'Bearer') !== 0 || empty($tokenParts[1])) {
    http_response_code(401);
    custom_log_valoraciones_lead("Token malformado: " . $authHeader);
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Token malformado.']);
    exit();
}
$jwt = $tokenParts[1];
$user_id_from_jwt = null;
$agency_id_from_jwt = null; // Variable para agency_id
$previous_display_errors = ini_get('display_errors'); // Guardar estado actual
ini_set('display_errors', '0'); // Desactivar temporalmente
try {
    JWT::$leeway = 60; // Permitir 60 segundos de desviación para exp, nbf, iat
    $decoded = JWT::decode($jwt, new Key(JWT_SECRET, 'HS256'));
    $user_id_from_jwt = $decoded->user_id ?? null; // Extraer user_id
    $agency_id_from_jwt = $decoded->agency_id ?? null; // Extraer agency_id
    custom_log_valoraciones_lead("Token decodificado para user_id: {$user_id_from_jwt}, agency_id: {$agency_id_from_jwt}");
} catch (Exception $e) {
    http_response_code(401);
    custom_log_valoraciones_lead("Error de decodificación de token: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Token inválido o expirado. (' . $e->getMessage() . ')']);
    exit();
} finally {
    ini_set('display_errors', $previous_display_errors); // Restaurar estado
}
if (!$user_id_from_jwt) {
    http_response_code(401);
    custom_log_valoraciones_lead("user_id no encontrado en el token después de decodificar.");
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Información de usuario no encontrada en el token.']);
    exit();
}
// Verificar si se proporcionó el ID del lead
$lead_id = isset($_GET['lead_id']) ? intval($_GET['lead_id']) : null;
if (!$lead_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de lead no proporcionado.']);
    exit();
}

try {
    custom_log_valoraciones_lead("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_valoraciones_lead("Conexión PDO establecida a través de la clase Database.");
} catch (Exception $e) {
    http_response_code(500);
    custom_log_valoraciones_lead("Error de conexión a BD: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

// 1. Obtener cliente_valorador_id usando agency_id del token
if (!$agency_id_from_jwt) {
    http_response_code(403);
    custom_log_valoraciones_lead("agency_id no encontrado en el token para user_id: {$user_id_from_jwt}");
    echo json_encode(['success' => false, 'message' => 'Agencia no identificada en el token.']);
    exit();
}

$stmt_client = $pdo->prepare("SELECT id, client_identifier FROM clientes_valorador WHERE agency_id = ? AND activo = 1 LIMIT 1");
$stmt_client->execute([$agency_id_from_jwt]);
$client_data = $stmt_client->fetch();

if ($client_data) {
    $cliente_valorador_id = $client_data['id'];
    $client_identifier = $client_data['client_identifier'];
    custom_log_valoraciones_lead("Cliente valorador encontrado: ID {$cliente_valorador_id}, identifier {$client_identifier} para agency_id: {$agency_id_from_jwt}");
} else {
    http_response_code(404);
    custom_log_valoraciones_lead("No se encontró cliente valorador para agency_id: {$agency_id_from_jwt} (Usuario actual user_id: {$user_id_from_jwt})");
    echo json_encode(['success' => false, 'message' => 'No se encontró configuración de valorador para la agencia.']);
    exit();
}

// 2. Verificar que el lead pertenece al cliente
$stmt_verify = $pdo->prepare("SELECT id FROM valorador_leads WHERE id = ? AND cliente_valorador_id = ? LIMIT 1");
$stmt_verify->execute([$lead_id, $cliente_valorador_id]);

if (!$stmt_verify->fetch()) {
    http_response_code(404);
    custom_log_valoraciones_lead("Lead no encontrado o no pertenece al cliente: " . $lead_id);
    echo json_encode(['success' => false, 'message' => 'Lead no encontrado o no pertenece a este cliente.']);
    exit();
}

// 3. Obtener valoraciones relacionadas con el lead
$stmt_valoraciones = $pdo->prepare("SELECT id, uuid, direccion, tipo_principal, subtipo, superficie, valor_estimado_min, valor_estimado_max, fecha_creacion FROM valorador_valoraciones WHERE lead_id = ? AND cliente_valorador_id = ? ORDER BY fecha_creacion DESC");
$stmt_valoraciones->execute([$lead_id, $cliente_valorador_id]);
$valoraciones = $stmt_valoraciones->fetchAll();

custom_log_valoraciones_lead(count($valoraciones) . " valoraciones encontradas para lead_id: " . $lead_id);

// Respuesta
echo json_encode(['success' => true, 'valoraciones' => $valoraciones]);
?>
