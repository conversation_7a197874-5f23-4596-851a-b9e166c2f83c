<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

use App\Services\OpenAIService;

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (json_last_error() !== JSON_ERROR_NONE || empty($input['prompt_template']) || empty($input['lead_uuid'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid input. Missing prompt_template or lead_uuid.']);
    exit;
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit;
}
$conn->set_charset(DB_CHARSET);

// Fetch lead and valuation data
$sql = "SELECT l.*, v.* FROM valorador_leads l LEFT JOIN valorador_valoraciones v ON l.valoracion_id = v.id WHERE l.uuid = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('s', $input['lead_uuid']);
$stmt->execute();
$result = $stmt->get_result();
$context_data = $result->fetch_assoc();

if (!$context_data) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'Lead not found.']);
    $stmt->close();
    $conn->close();
    exit;
}

// Replace placeholders in prompt
$prompt = $input['prompt_template'];
$placeholders = [
    '{{lead_name}}' => $context_data['nombre'] ?? '',
    '{{lead_email}}' => $context_data['email'] ?? '',
    '{{lead_phone}}' => $context_data['telefono'] ?? '',
    '{{property_address}}' => $context_data['direccion'] ?? '',
    '{{property_type}}' => $context_data['tipo_principal'] ?? '',
    '{{property_subtype}}' => $context_data['subtipo'] ?? '',
    '{{property_surface}}' => $context_data['superficie'] ?? '',
    '{{property_year}}' => $context_data['ano_construccion_catastro'] ?? '',
    '{{valuation_min}}' => number_format($context_data['valor_estimado_min'] ?? 0, 0, ',', '.'),
    '{{valuation_max}}' => number_format($context_data['valor_estimado_max'] ?? 0, 0, ',', '.'),
];

foreach ($placeholders as $key => $value) {
    $prompt = str_replace($key, (string)$value, $prompt);
}

// Call OpenAI Service
try {
    $openai_service = new OpenAIService(OPENAI_API_KEY);
    $generated_content = $openai_service->generateEmailContent($prompt);

    if ($generated_content['success']) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => [
                'subject' => $generated_content['subject'],
                'body' => $generated_content['body']
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => $generated_content['error'] ?? 'Failed to generate content from AI service.']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'An exception occurred with the AI service: ' . $e->getMessage()]);
}

$stmt->close();
$conn->close();
