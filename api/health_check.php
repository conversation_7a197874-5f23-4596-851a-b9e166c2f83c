<?php
declare(strict_types=1);

// =================================================================
// SCRIPT DE DIAGNÓSTICO INTEGRAL - INMOAUTOMATION API
// =================================================================
// Este script verifica la configuración del entorno, la conexión a la
// base de datos, la autenticación y el estado de los endpoints clave.
//
// USO:
// 1. (Opcional) Ejecuta desde el navegador: https://api.inmoautomation.com/health_check.php
// 2. (Recomendado) Ejecuta desde la terminal para una salida más limpia:
//    php -f /ruta/a/tu/proyecto/api/health_check.php
//
// IMPORTANTE:
// Configura las credenciales de un usuario de prueba en la sección
// de configuración para que las pruebas de autenticación funcionen.
// =================================================================

// --- CONFIGURACIÓN DE PRUEBA ---
// REEMPLAZA ESTO CON LAS CREDENCIALES DE UN USUARIO DE PRUEBA REAL
define('TEST_USER_EMAIL', '<EMAIL>');
define('TEST_USER_PASSWORD', 'k8wpMVnL3B5ZJAm');
// -----------------------------

// --- INICIALIZACIÓN ---
header('Content-Type: text/plain; charset=utf-8');
ini_set('display_errors', '1');
ini_set('display_startup_errors', '1');
error_reporting(E_ALL);

$isCli = (PHP_SAPI === 'cli');
$totalStartTime = microtime(true);

// --- FUNCIONES AUXILIARES ---
function print_header(string $title): void {
    echo "\n" . str_repeat('=', 60) . "\n";
    echo "  " . strtoupper($title) . "\n";
    echo str_repeat('=', 60) . "\n";
}

function print_status(string $check, bool $success, string $details = ''): void {
    $status = $success ? "✅ OK" : "❌ ERROR";
    echo sprintf("%-45s [%s]\n", $check, $status);
    if (!empty($details)) {
        echo "   └─ " . str_replace("\n", "\n      ", $details) . "\n";
    }
}

function get_execution_time(float $startTime): string {
    return number_format((microtime(true) - $startTime) * 1000, 2) . ' ms';
}

// =================================================================
// FASE 1: VERIFICACIÓN DEL ENTORNO
// =================================================================
print_header('Fase 1: Verificación del Entorno');

$phpVersion = phpversion();
$isPhpVersionOk = version_compare($phpVersion, '8.1', '>=');
print_status('Versión de PHP >= 8.1', $isPhpVersionOk, "Versión actual: $phpVersion");

$requiredExtensions = ['pdo_mysql', 'json', 'curl', 'openssl', 'mbstring'];
foreach ($requiredExtensions as $ext) {
    $isLoaded = extension_loaded($ext);
    print_status("Extensión PHP: $ext", $isLoaded);
}

// =================================================================
// FASE 2: CARGA DE CONFIGURACIÓN
// =================================================================
print_header('Fase 2: Carga de Configuración');
$bootstrapPath = __DIR__ . '/config/bootstrap.php';
$bootstrapLoaded = @include_once $bootstrapPath;
print_status('Carga de config/bootstrap.php', (bool)$bootstrapLoaded);

if (!$bootstrapLoaded) {
    echo "\nCRÍTICO: No se pudo cargar bootstrap.php. Las pruebas no pueden continuar.\n";
    exit(1);
}

$constantsToCheck = ['DB_SOCKET', 'DB_HOST', 'DB_DATABASE', 'DB_USERNAME', 'JWT_SECRET'];
foreach ($constantsToCheck as $const) {
    $isDefined = defined($const);
    $value = $isDefined ? (strpos($const, 'SECRET') !== false ? '***' : constant($const)) : 'No definida';
    print_status("Constante definida: $const", $isDefined, "Valor: $value");
}

// =================================================================
// FASE 3: CONEXIÓN A LA BASE DE DATOS
// =================================================================
print_header('Fase 3: Conexión a la Base de Datos');
$dbStartTime = microtime(true);
$pdo = null;
$dbError = '';
try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
    $dbSuccess = ($pdo instanceof PDO);
} catch (Exception $e) {
    $dbSuccess = false;
    $dbError = get_class($e) . ": " . $e->getMessage();
}
print_status('Conexión vía Api\\lib\\Database', $dbSuccess, $dbError);

if ($dbSuccess) {
    print_status('Tiempo de conexión', true, get_execution_time($dbStartTime));
    $serverInfo = $pdo->getAttribute(PDO::ATTR_SERVER_VERSION);
    $connectionStatus = $pdo->getAttribute(PDO::ATTR_CONNECTION_STATUS);
    print_status('Info del servidor MySQL', true, "Versión: $serverInfo");
    print_status('Estado de la conexión', true, $connectionStatus);
}

// =================================================================
// FASE 4: PRUEBA DE AUTENTICACIÓN
// =================================================================
print_header('Fase 4: Prueba de Autenticación (Login)');
$jwtToken = null;
if (TEST_USER_EMAIL === '<EMAIL>') {
    print_status('Prueba de Login', false, 'Las credenciales de prueba no han sido configuradas en este script.');
} else {
    $authStartTime = microtime(true);
    $loginUrl = 'https://api.inmoautomation.com/login.php';
    $postData = json_encode(['email' => TEST_USER_EMAIL, 'password' => TEST_USER_PASSWORD]);

    $ch = curl_init($loginUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    $loginSuccess = ($httpCode === 200);
    print_status('Llamada a /login.php', $loginSuccess, "Código HTTP: $httpCode, Tiempo: " . get_execution_time($authStartTime));

    if ($loginSuccess) {
        $responseData = json_decode($response, true);
        if (isset($responseData['token'])) {
            $jwtToken = $responseData['token'];
            print_status('Obtención de token JWT', true, 'Token recibido correctamente.');
        } else {
            print_status('Obtención de token JWT', false, 'La respuesta no contiene un token.');
        }
    } else {
        print_status('Respuesta de Login', false, "Respuesta: $response");
    }
}

// =================================================================
// FASE 5: PRUEBA DE ENDPOINTS PROTEGIDOS
// =================================================================
print_header('Fase 5: Prueba de Endpoints Protegidos');
if (!$jwtToken) {
    print_status('Verificación de Endpoints', false, 'No se puede continuar sin un token JWT válido.');
} else {
    $endpointsToTest = [
        'Obtener perfil de usuario' => '/profile.php',
        'Obtener datos de suscripción' => '/subscription.php',
        'Obtener valoraciones' => '/admin_get_valoraciones.php',
        'Obtener leads' => '/admin_get_leads.php'
    ];

    foreach ($endpointsToTest as $description => $endpoint) {
        $endpointStartTime = microtime(true);
        $url = 'https://api.inmoautomation.com' . $endpoint;
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $jwtToken,
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $endpointSuccess = ($httpCode === 200);
        print_status($description . " ($endpoint)", $endpointSuccess, "Código HTTP: $httpCode, Tiempo: " . get_execution_time($endpointStartTime));
        if (!$endpointSuccess) {
            print_status('Respuesta de Endpoint', false, "Respuesta: $response");
        }
    }
}

// --- RESUMEN FINAL ---
print_header('Resumen Final');
echo "Diagnóstico completado en " . get_execution_time($totalStartTime) . "\n";
echo "Revise los resultados anteriores para detectar cualquier error (❌).\n";
