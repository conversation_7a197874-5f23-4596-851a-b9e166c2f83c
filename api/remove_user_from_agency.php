<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
header('Content-Type: application/json');
// --- Logger específico para este endpoint ---
if (!function_exists('custom_log_remove_agency_user')) {
    function custom_log_remove_agency_user($message) {
        $logFile = __DIR__ . '/debug_remove_agency_user.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_remove_agency_user("--- [remove_user_from_agency.php] INICIADO ---");
// --- Verificación de autenticación y contexto de agencia ---
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
$current_user_id_jwt = null; // ID del admin que hace la petición
$current_agency_id = null;
$is_agency_owner = false;
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            JWT::$leeway = 60;
            $decodedToken = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
            $current_user_id_jwt = $decodedToken->user_id ?? null;
            $current_agency_id = $decodedToken->agency_id ?? null;
            $is_agency_owner = isset($decodedToken->is_agency_owner) ? (bool)$decodedToken->is_agency_owner : false;
            if (!$current_user_id_jwt || !$current_agency_id) {
                throw new Exception('User ID o Agency ID no encontrados en el token.');
            }
            custom_log_remove_agency_user("Token decodificado: user_id_jwt={$current_user_id_jwt}, agency_id={$current_agency_id}, is_owner=" . ($is_agency_owner ? 'Sí' : 'No'));
        } catch (Exception $e) {
            custom_log_remove_agency_user("Error de Token: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        }
    } else {
        custom_log_remove_agency_user("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_remove_agency_user("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Verificación de Permiso de Dueño de Agencia ---
if (!$is_agency_owner) {
    custom_log_remove_agency_user("Acceso denegado: Usuario user_id={$current_user_id_jwt} no es dueño de la agencia agency_id={$current_agency_id}.");
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo el propietario de la agencia puede eliminar usuarios.']);
    exit();
}
// --- Conexión a la base de datos ---
$pdo = null; 
try {
    custom_log_remove_agency_user("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_remove_agency_user("Conexión PDO establecida a través de la clase Database.");
} catch (Exception $e) {
    custom_log_remove_agency_user("Error de BD: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB_CONNECT).']);
    exit();
}
// --- Lógica Principal: Eliminar usuario de la agencia ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $user_id_to_remove = filter_var($input['user_id_to_remove'] ?? null, FILTER_VALIDATE_INT);
    if (!$user_id_to_remove) {
        custom_log_remove_agency_user("ID de usuario a eliminar no proporcionado o inválido.");
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID de usuario a eliminar es obligatorio.']);
        exit();
    }
    custom_log_remove_agency_user("Intento de eliminar user_id: {$user_id_to_remove} de la agencia {$current_agency_id} por el owner {$current_user_id_jwt}.");
    try {
        // 1. Obtener el owner_user_id de la agencia actual para verificar que no se elimine a sí mismo.
        $stmt_owner_check = $pdo->prepare("SELECT owner_user_id FROM agencies WHERE id = ?");
        $stmt_owner_check->execute([$current_agency_id]);
        $agency_details = $stmt_owner_check->fetch();
        if (!$agency_details || !isset($agency_details['owner_user_id'])) {
            throw new Exception("No se pudo encontrar la agencia actual. Agency ID: " . $current_agency_id);
        }
        $actual_owner_user_id = (int)$agency_details['owner_user_id'];
        if ($user_id_to_remove === $actual_owner_user_id) {
            custom_log_remove_agency_user("Intento de eliminar al propietario (user_id: {$user_id_to_remove}) de su propia agencia ({$current_agency_id}). Acción no permitida.");
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'No puedes eliminar al propietario de la agencia.']);
            exit();
        }
        // 2. Verificar que el usuario a eliminar pertenezca a la agencia del owner.
        $stmt_user_check = $conn_mysqli->prepare("SELECT id, agency_id FROM usuarios WHERE id = ?");
        if (!$stmt_user_check) throw new Exception ("Error preparando verificación de usuario: " . $conn_mysqli->error);
        $stmt_user_check->bind_param("i", $user_id_to_remove);
        $stmt_user_check->execute();
        $result_user_check = $stmt_user_check->get_result();
        $user_to_remove_data = $result_user_check->fetch_assoc();
        $stmt_user_check->close();
        if (!$user_to_remove_data) {
            custom_log_remove_agency_user("Usuario a eliminar (ID: {$user_id_to_remove}) no encontrado.");
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Usuario a eliminar no encontrado.']);
            exit();
        }
        if ((int)$user_to_remove_data['agency_id'] !== $current_agency_id) {
            custom_log_remove_agency_user("Usuario (ID: {$user_id_to_remove}) no pertenece a la agencia {$current_agency_id}. Pertenece a: " . $user_to_remove_data['agency_id']);
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Este usuario no pertenece a tu agencia.']);
            exit();
        }
        // 3. Desvincular y desactivar al usuario (o solo desvincular)
        // Optamos por desvincular (agency_id=NULL) y desactivar (activo=0).
        // También se podrían limpiar los roles: roles = NULL o roles = '[]'
        $stmt_update = $conn_mysqli->prepare("UPDATE usuarios SET agency_id = NULL, activo = 0, roles = '[]' WHERE id = ? AND agency_id = ?");
        if (!$stmt_update) throw new Exception ("Error preparando actualización de usuario: " . $conn_mysqli->error);
        $stmt_update->bind_param("ii", $user_id_to_remove, $current_agency_id);
        if ($stmt_update->execute()) {
            if ($stmt_update->affected_rows > 0) {
                custom_log_remove_agency_user("Usuario {$user_id_to_remove} desvinculado y desactivado de la agencia {$current_agency_id}.");
                echo json_encode(['success' => true, 'message' => 'Usuario eliminado correctamente de la agencia.']);
            } else {
                custom_log_remove_agency_user("Ninguna fila afectada al intentar eliminar usuario {$user_id_to_remove}. Puede que ya estuviera desvinculado o no existiera con esa agency_id.");
                // Esto podría pasar si hay una race condition o datos inconsistentes, pero la verificación anterior debería prevenirlo.
                echo json_encode(['success' => false, 'message' => 'No se pudo eliminar el usuario o ya no estaba asociado.']);
            }
        } else {
            custom_log_remove_agency_user("Error al ejecutar la actualización para eliminar usuario: " . $stmt_update->error);
            throw new Exception("No se pudo eliminar el usuario de la agencia.");
        }
        $stmt_update->close();
        $conn_mysqli->close();
        if ($pdo) $pdo = null;
        exit();
    } catch (Exception $e) {
        custom_log_remove_agency_user("Excepción al eliminar usuario: " . $e->getMessage());
        if ($conn_mysqli && $conn_mysqli->ping()) $conn_mysqli->close();
        if ($pdo) $pdo = null;
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error en el servidor al eliminar usuario: ' . $e->getMessage()]);
        exit();
    }
} else {
    custom_log_remove_agency_user("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    if ($conn_mysqli && $conn_mysqli->ping()) $conn_mysqli->close();
    if ($pdo) $pdo = null;
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}
?>