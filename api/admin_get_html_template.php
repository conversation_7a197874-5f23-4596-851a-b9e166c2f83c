<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$template_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($template_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de plantilla requerido']);
    exit;
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}
$conn->set_charset(DB_CHARSET);

try {
    // Obtener plantilla completa
    $sql = "
        SELECT 
            id,
            nombre_interno_plantilla,
            descripcion,
            asunto_predeterminado,
            contenido_html,
            tipo_plantilla,
            activa,
            fecha_creacion,
            fecha_modificacion,
            CHAR_LENGTH(contenido_html) as html_size
        FROM email_plantillas_html 
        WHERE id = ?
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $template_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Plantilla no encontrada']);
        exit;
    }
    
    $template = $result->fetch_assoc();
    
    // Obtener estadísticas de uso
    $usage_sql = "
        SELECT 
            COUNT(*) as total_usage,
            COUNT(DISTINCT ss.sequence_id) as sequences_using,
            GROUP_CONCAT(DISTINCT s.name SEPARATOR ', ') as sequence_names
        FROM sequence_steps ss
        LEFT JOIN sequences s ON ss.sequence_id = s.id
        WHERE ss.email_template_id = ?
    ";
    
    $usage_stmt = $conn->prepare($usage_sql);
    $usage_stmt->bind_param('i', $template_id);
    $usage_stmt->execute();
    $usage_stats = $usage_stmt->get_result()->fetch_assoc();
    
    // Obtener variables utilizadas en la plantilla
    $variables_found = [];
    if (preg_match_all('/\{\{([^}]+)\}\}/', $template['contenido_html'], $matches)) {
        $variables_found = array_unique($matches[1]);
        sort($variables_found);
    }
    
    // Procesar plantilla
    $processed_template = [
        'id' => (int)$template['id'],
        'nombre_interno_plantilla' => $template['nombre_interno_plantilla'],
        'descripcion' => $template['descripcion'],
        'asunto_predeterminado' => $template['asunto_predeterminado'],
        'html_content' => $template['contenido_html'],
        'tipo_plantilla' => $template['tipo_plantilla'],
        'activa' => (bool)$template['activa'],
        'fecha_creacion' => $template['fecha_creacion'],
        'fecha_modificacion' => $template['fecha_modificacion'],
        'html_size' => (int)$template['html_size'],
        'variables_found' => $variables_found,
        'usage_stats' => [
            'total_usage' => (int)$usage_stats['total_usage'],
            'sequences_using' => (int)$usage_stats['sequences_using'],
            'sequence_names' => $usage_stats['sequence_names'] ? explode(', ', $usage_stats['sequence_names']) : [],
            'can_delete' => (int)$usage_stats['total_usage'] === 0
        ]
    ];
    
    // Agregar información del tipo
    switch($processed_template['tipo_plantilla']) {
        case 'secuencia_email':
            $processed_template['type_info'] = [
                'display_name' => 'Secuencia de Email',
                'icon' => '📧',
                'description' => 'Plantilla para emails de secuencias automatizadas',
                'recommended_variables' => [
                    'lead_name', 'lead_email', 'property_address', 'property_type',
                    'agency_name', 'current_date', 'email_content'
                ]
            ];
            break;
        case 'informe_valoracion':
            $processed_template['type_info'] = [
                'display_name' => 'Informe de Valoración',
                'icon' => '📊',
                'description' => 'Plantilla para informes de valoración de propiedades',
                'recommended_variables' => [
                    'lead_name', 'property_address', 'property_value_range',
                    'agency_name', 'current_date', 'valoracion_details'
                ]
            ];
            break;
        case 'general':
            $processed_template['type_info'] = [
                'display_name' => 'General',
                'icon' => '📄',
                'description' => 'Plantilla de uso general',
                'recommended_variables' => [
                    'lead_name', 'agency_name', 'current_date'
                ]
            ];
            break;
        default:
            $processed_template['type_info'] = [
                'display_name' => ucfirst(str_replace('_', ' ', $processed_template['tipo_plantilla'])),
                'icon' => '📝',
                'description' => 'Plantilla personalizada',
                'recommended_variables' => []
            ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $processed_template
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error obteniendo plantilla: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
