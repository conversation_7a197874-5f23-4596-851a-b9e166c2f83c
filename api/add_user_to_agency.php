<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
error_log("ADD_USER_TO_AGENCY_SCRIPT_STARTED"); // LOG DE PRUEBA INMEDIATO
 // Usar config central de CORS
use Api\lib\BrevoService;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Api\utils\PlanFeatures; // CAMBIADO Utils a utils (minúscula)
// Las cabeceras CORS y el manejo de OPTIONS ahora son gestionados por cors.php
header('Content-Type: application/json');
// --- Logger específico para este endpoint ---
if (!function_exists('custom_log_add_agency_user')) {
    function custom_log_add_agency_user($message) {
        $logFile = __DIR__ . '/debug_add_agency_user.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_add_agency_user("--- [add_user_to_agency.php] INICIADO ---");
// --- Verificación de autenticación y contexto de agencia ---
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
$current_user_id_jwt = null; // ID del usuario que hace la petición
$current_agency_id = null;
$is_agency_owner = false;
$current_owner_name = 'El Propietario';
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            JWT::$leeway = 60;
            $decodedToken = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
            $current_user_id_jwt = $decodedToken->user_id ?? null;
            $current_agency_id = $decodedToken->agency_id ?? null;
            $is_agency_owner = isset($decodedToken->is_agency_owner) ? (bool)$decodedToken->is_agency_owner : false;
            $current_owner_name = $decodedToken->nombre_completo ?? 'El Propietario';
            if (!$current_user_id_jwt || !$current_agency_id) {
                throw new Exception('User ID o Agency ID no encontrados en el token.');
            }
            custom_log_add_agency_user("Token decodificado: user_id_jwt={$current_user_id_jwt}, agency_id={$current_agency_id}, is_owner=" . ($is_agency_owner ? 'Sí' : 'No'));
        } catch (Exception $e) {
            custom_log_add_agency_user("Error de Token: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        }
    } else {
        custom_log_add_agency_user("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_add_agency_user("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Verificación de Permiso de Dueño de Agencia ---
if (!$is_agency_owner) {
    custom_log_add_agency_user("Acceso denegado: Usuario user_id={$current_user_id_jwt} no es dueño de la agencia agency_id={$current_agency_id}.");
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo el propietario de la agencia puede añadir usuarios.']);
    exit();
}
// --- Conexión a la base de datos ---
$pdo = null;
try {
    custom_log_add_agency_user("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_add_agency_user("Conexión PDO establecida a través de la clase Database.");
} catch (Exception $e) {
    custom_log_add_agency_user("Error de BD: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB_CONNECT).']);
    exit();
}
// --- Lógica Principal: Añadir usuario a la agencia ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $nombre_completo_invitado = trim($input['nombre_completo'] ?? '');
    $email_invitado = trim(strtolower($input['email'] ?? ''));
    // Validaciones básicas
    if (empty($nombre_completo_invitado) || empty($email_invitado)) {
        custom_log_add_agency_user("Datos incompletos: nombre o email faltantes.");
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Nombre completo y email son obligatorios.']);
        exit();
    }
    if (!filter_var($email_invitado, FILTER_VALIDATE_EMAIL)) {
        custom_log_add_agency_user("Email inválido: " . $email_invitado);
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'El formato del email no es válido.']);
        exit();
    }
    try {
        // 1. Obtener owner_user_id y nombre de la agencia actual
        $stmt_agency_details = $pdo->prepare("SELECT owner_user_id, name FROM agencies WHERE id = ?");
        $stmt_agency_details->execute([$current_agency_id]);
        $agency_details = $stmt_agency_details->fetch();
        if (!$agency_details || !isset($agency_details['owner_user_id']) || !isset($agency_details['name'])) {
            throw new Exception("No se pudo encontrar la agencia, su propietario o su nombre. Agency ID: " . $current_agency_id);
        }
        $owner_user_id = $agency_details['owner_user_id'];
        $agency_name = $agency_details['name'];
        custom_log_add_agency_user("Owner ID de la agencia {$current_agency_id} es {$owner_user_id}. Nombre agencia: {$agency_name}.");
        // 2. Obtener max_dashboard_users del plan de la agencia
        $planDetails = PlanFeatures::getUserActivePlanDetails((int)$owner_user_id, $pdo);
        if (!$planDetails || !isset($planDetails['max_dashboard_users'])) {
            custom_log_add_agency_user("No se pudieron obtener los detalles del plan o max_dashboard_users para owner_id: " . $owner_user_id);
            throw new Exception("No se pudo determinar el límite de usuarios del plan.");
        }
        $max_dashboard_users = (int)$planDetails['max_dashboard_users'];
        custom_log_add_agency_user("Límite de usuarios del plan (max_dashboard_users): " . $max_dashboard_users);
        // 3. Contar usuarios actuales en la agencia
        $stmt_count = $conn_mysqli->prepare("SELECT COUNT(*) as user_count FROM usuarios WHERE agency_id = ?");
        if (!$stmt_count) throw new Exception("Error preparando conteo de usuarios: " . $conn_mysqli->error);
        $stmt_count->bind_param("i", $current_agency_id);
        $stmt_count->execute();
        $result_count = $stmt_count->get_result();
        $row_count = $result_count->fetch_assoc();
        $current_user_count = (int)$row_count['user_count'];
        $stmt_count->close();
        custom_log_add_agency_user("Usuarios actuales en la agencia {$current_agency_id}: " . $current_user_count);
        // 4. Verificar límite
        if ($current_user_count >= $max_dashboard_users && $max_dashboard_users > 0) {
            custom_log_add_agency_user("Límite de usuarios ({$max_dashboard_users}) alcanzado para la agencia {$current_agency_id}.");
            http_response_code(403); // Forbidden o 400 Bad Request
            echo json_encode(['success' => false, 'message' => "Has alcanzado el límite de {$max_dashboard_users} usuarios para tu plan actual."]);
            exit();
        }
        // 5. Verificar si el email ya existe
        $stmt_email_check = $conn_mysqli->prepare("SELECT id FROM usuarios WHERE email = ?");
        if (!$stmt_email_check) throw new Exception("Error preparando verificación de email: " . $conn_mysqli->error);
        $stmt_email_check->bind_param("s", $email_invitado);
        $stmt_email_check->execute();
        $result_email_check = $stmt_email_check->get_result();
        if ($result_email_check->num_rows > 0) {
            custom_log_add_agency_user("Intento de añadir usuario con email existente: " . $email_invitado);
            http_response_code(409); // Conflict
            echo json_encode(['success' => false, 'message' => 'Este email ya está registrado en el sistema.']);
            $stmt_email_check->close();
            exit();
        }
        $stmt_email_check->close();
        // 6. Generar contraseña aleatoria segura
        $generated_password = bin2hex(random_bytes(8)); // 16 caracteres hexadecimales
        $password_hash = password_hash($generated_password, PASSWORD_DEFAULT);
        custom_log_add_agency_user("Contraseña generada para {$email_invitado}.");
        // 7. Insertar nuevo usuario
        $default_roles = json_encode(['user']); // Rol por defecto
        $activo = 1; // Usuario activo directamente
        $stmt_insert = $conn_mysqli->prepare("INSERT INTO usuarios (uuid, nombre_completo, email, password_hash, agency_id, roles, activo, email_verificado_at, fecha_creacion, fecha_modificacion) VALUES (UUID(), ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())");
        if (!$stmt_insert) throw new Exception("Error preparando inserción de usuario: " . $conn_mysqli->error);
        $stmt_insert->bind_param("sssisi", $nombre_completo_invitado, $email_invitado, $password_hash, $current_agency_id, $default_roles, $activo);
        if ($stmt_insert->execute()) {
            $new_user_id = $stmt_insert->insert_id;
            custom_log_add_agency_user("Usuario {$new_user_id} ({$email_invitado}) añadido exitosamente a la agencia {$current_agency_id}.");
            // 8. Enviar email de bienvenida/invitación
            $brevoService = new BrevoService();
            // Obtener la plantilla de la base de datos
            $stmt_template = $conn_mysqli->prepare("SELECT asunto_predeterminado, contenido_html FROM email_plantillas_html WHERE nombre_interno_plantilla = ?");
            if (!$stmt_template) { throw new Exception("Error al preparar la consulta de la plantilla de email: " . $conn_mysqli->error); }
            $template_name = 'bienvenida_nuevo_miembro_agencia';
            $stmt_template->bind_param("s", $template_name);
            $stmt_template->execute();
            $result_template = $stmt_template->get_result();
            if ($result_template->num_rows === 0) { throw new Exception("No se encontró la plantilla de email '{$template_name}'."); }
            $template = $result_template->fetch_assoc();
            $stmt_template->close();
            // URL de login debe apuntar a la app, no a la landing
            $login_url = rtrim(DASHBOARD_BASE_URL, '/'); 
            // Reemplazar placeholders en el cuerpo y asunto del email
            $asunto_final = str_replace(['{{nombre_agencia}}'], [$agency_name], $template['asunto_predeterminado']);
            $email_html_body_final = str_replace(['{{nombre_agencia}}'], [$agency_name], $template['contenido_html']);
            $email_enviado_con_plantilla = true;
            // Datos para reemplazar placeholders
            $placeholders = [
                '{{nombre_completo_invitado}}' => $nombre_completo_invitado,
                '{{nombre_del_propietario}}' => $current_owner_name,
                '{{nombre_de_la_agencia}}' => $agency_name,
                '{{email_invitado}}' => $email_invitado,
                '{{contrasena_generada}}' => $generated_password,
                '{{url_login}}' => $login_url, // $login_url ya tiene el valor correcto
                '{{current_year}}' => date('Y')
            ];
            // Reemplazar placeholders
            $asunto_final = str_replace(array_keys($placeholders), array_values($placeholders), $asunto_final);
            $email_html_body_final = str_replace(array_keys($placeholders), array_values($placeholders), $email_html_body_final);
            // DEBUG: Log para $email_html_body_final (login_url ya se logueó antes)
            custom_log_add_agency_user("[DEBUG add_user_to_agency.php] email_html_body_final (después de plantilla): " . substr($email_html_body_final, 0, 500) . "...");
            $emailResult = $brevoService->sendEmail(
                $email_invitado,
                $nombre_completo_invitado,
                $asunto_final,
                $email_html_body_final
            );
            if ($emailResult['success']) {
                $log_msg_email = $email_enviado_con_plantilla ? "Email de bienvenida (plantilla: {$template_name}) enviado" : "Email de bienvenida (fallback) enviado";
                custom_log_add_agency_user($log_msg_email . " a {$email_invitado}. Message ID: " . ($emailResult['message_id'] ?? 'N/A'));
                echo json_encode(['success' => true, 'message' => 'Usuario añadido correctamente a la agencia y email de bienvenida enviado.', 'user_id' => $new_user_id]);
            } else {
                custom_log_add_agency_user("Usuario {$new_user_id} añadido, pero falló el envío del email de bienvenida a {$email_invitado}. Error: " . ($emailResult['error'] ?? 'Desconocido'));
                // A pesar del fallo del email, el usuario fue creado. Considerar la mejor respuesta aquí.
                // Por ahora, informamos el éxito de creación pero advertimos sobre el email.
                echo json_encode(['success' => true, 'message' => 'Usuario añadido correctamente, pero hubo un problema al enviar el email de bienvenida. Por favor, contacta al nuevo miembro con sus credenciales.', 'user_id' => $new_user_id, 'email_error' => $emailResult['error']]);
            }
        } else {
            custom_log_add_agency_user("Error al ejecutar inserción de usuario: " . $stmt_insert->error);
            throw new Exception("No se pudo añadir el usuario a la agencia.");
        }
        $stmt_insert->close();
        $conn_mysqli->close();
        if ($pdo) $pdo = null; // Cerrar conexión PDO
        exit();
    } catch (Exception $e) {
        custom_log_add_agency_user("Excepción al añadir usuario: " . $e->getMessage());
        if ($conn_mysqli && $conn_mysqli->ping()) $conn_mysqli->close();
        if ($pdo) $pdo = null;
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error en el servidor al añadir usuario: ' . $e->getMessage()]);
        exit();
    }
} else {
    custom_log_add_agency_user("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    if ($conn_mysqli && $conn_mysqli->ping()) $conn_mysqli->close();
    if ($pdo) $pdo = null;
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}
?>