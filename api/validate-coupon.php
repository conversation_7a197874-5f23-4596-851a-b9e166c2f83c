<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Logger;

header('Content-Type: application/json');

function custom_log_coupon($message) {
    Logger::info("[validate-coupon.php] " . $message);
}

custom_log_coupon("--- [validate-coupon.php] INICIADO ---");

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);

if (json_last_error() !== JSON_ERROR_NONE) {
    custom_log_coupon("Error al decodificar JSON: " . json_last_error_msg());
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Error al decodificar JSON de entrada: ' . json_last_error_msg()]);
    exit();
}

$couponCode = trim($input['coupon_code'] ?? '');
$customerId = trim($input['customer_id'] ?? '');

if (empty($couponCode)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Código de cupón requerido.']);
    exit();
}

custom_log_coupon("Validando cupón: $couponCode para customer: $customerId");

try {
    // Inicializar Stripe
    \Stripe\Stripe::setApiKey(env('STRIPE_SECRET_KEY'));
    
    // Buscar el cupón en Stripe
    try {
        $coupon = \Stripe\Coupon::retrieve($couponCode);
        custom_log_coupon("Cupón encontrado: " . $coupon->id);
    } catch (\Stripe\Exception\InvalidRequestException $e) {
        if (strpos($e->getMessage(), 'No such coupon') !== false) {
            custom_log_coupon("Cupón no encontrado: $couponCode");
            echo json_encode([
                'success' => false,
                'message' => 'Código de cupón no válido.',
                'error_type' => 'invalid_coupon'
            ]);
            exit();
        }
        throw $e;
    }
    
    // Verificar si el cupón está activo
    if (!$coupon->valid) {
        custom_log_coupon("Cupón no válido: $couponCode");
        echo json_encode([
            'success' => false,
            'message' => 'Este cupón ya no es válido.',
            'error_type' => 'invalid_coupon'
        ]);
        exit();
    }
    
    // Verificar si el cupón ha expirado
    if ($coupon->redeem_by && $coupon->redeem_by < time()) {
        custom_log_coupon("Cupón expirado: $couponCode");
        echo json_encode([
            'success' => false,
            'message' => 'Este cupón ha expirado.',
            'error_type' => 'expired_coupon'
        ]);
        exit();
    }
    
    // Verificar límites de uso
    if ($coupon->max_redemptions && $coupon->times_redeemed >= $coupon->max_redemptions) {
        custom_log_coupon("Cupón agotado: $couponCode");
        echo json_encode([
            'success' => false,
            'message' => 'Este cupón ya ha sido utilizado el máximo número de veces.',
            'error_type' => 'exhausted_coupon'
        ]);
        exit();
    }
    
    // Si se proporciona customer_id, verificar si ya lo ha usado
    if (!empty($customerId)) {
        try {
            $customer = \Stripe\Customer::retrieve($customerId);
            
            // Buscar suscripciones del customer que tengan este cupón
            $subscriptions = \Stripe\Subscription::all([
                'customer' => $customerId,
                'limit' => 100
            ]);
            
            foreach ($subscriptions->data as $subscription) {
                if ($subscription->discount && 
                    $subscription->discount->coupon && 
                    $subscription->discount->coupon->id === $couponCode) {
                    custom_log_coupon("Customer ya usó el cupón: $couponCode");
                    echo json_encode([
                        'success' => false,
                        'message' => 'Ya has utilizado este cupón anteriormente.',
                        'error_type' => 'already_used'
                    ]);
                    exit();
                }
            }
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            // Customer no existe, continuar
            custom_log_coupon("Customer no encontrado: $customerId");
        }
    }
    
    // Preparar información del cupón
    $couponInfo = [
        'id' => $coupon->id,
        'name' => $coupon->name,
        'valid' => $coupon->valid,
        'duration' => $coupon->duration,
        'duration_in_months' => $coupon->duration_in_months,
        'redeem_by' => $coupon->redeem_by,
        'max_redemptions' => $coupon->max_redemptions,
        'times_redeemed' => $coupon->times_redeemed
    ];
    
    // Calcular descuento
    if ($coupon->percent_off) {
        $couponInfo['type'] = 'percentage';
        $couponInfo['percent_off'] = $coupon->percent_off;
        $couponInfo['discount_text'] = $coupon->percent_off . '% de descuento';
    } elseif ($coupon->amount_off) {
        $couponInfo['type'] = 'fixed';
        $couponInfo['amount_off'] = $coupon->amount_off;
        $couponInfo['currency'] = $coupon->currency;
        $couponInfo['discount_text'] = ($coupon->amount_off / 100) . ' ' . strtoupper($coupon->currency) . ' de descuento';
    }
    
    // Información de duración
    switch ($coupon->duration) {
        case 'once':
            $couponInfo['duration_text'] = 'Aplicable una sola vez';
            break;
        case 'repeating':
            $couponInfo['duration_text'] = "Aplicable durante {$coupon->duration_in_months} meses";
            break;
        case 'forever':
            $couponInfo['duration_text'] = 'Aplicable para siempre';
            break;
    }
    
    custom_log_coupon("Cupón válido: $couponCode - " . $couponInfo['discount_text']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Cupón válido aplicado.',
        'coupon' => $couponInfo
    ]);
    
} catch (\Stripe\Exception\ApiErrorException $e) {
    custom_log_coupon("Error de Stripe API: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Error al validar el cupón: ' . $e->getMessage(),
        'error_type' => 'stripe_error'
    ]);
    
} catch (Exception $e) {
    custom_log_coupon("Error general: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor.',
        'error_type' => 'server_error'
    ]);
}
?>
