<?php

declare(strict_types=1);


/**
 * Script para verificar el estado actual después de los fixes
 */

require_once __DIR__ . '/config/bootstrap.php';

echo "=== VERIFICANDO ESTADO ACTUAL DESPUÉS DE LOS FIXES ===\n\n";

// Conectar a la base de datos
$db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
$db->set_charset('utf8mb4');

// Obtener datos del usuario
$user_query = "SELECT u.id, u.email, u.agency_id, u.stripe_customer_id FROM usuarios u WHERE u.email = '<EMAIL>'";
$user_result = $db->query($user_query);
$user_data = $user_result->fetch_assoc();

$userId = $user_data['id'];
$agencyId = $user_data['agency_id'];
$stripeCustomerId = $user_data['stripe_customer_id'];

echo "Usuario: ID = $userId, Agency = $agencyId\n\n";

// 1. Estado del valorador
echo "1. ESTADO DEL VALORADOR:\n";
echo "=" . str_repeat("=", 40) . "\n";

$valorador_query = "SELECT id, client_identifier, nombre_display, activo, fecha_creacion FROM clientes_valorador WHERE agency_id = ?";
$stmt = $db->prepare($valorador_query);
$stmt->bind_param("i", $agencyId);
$stmt->execute();
$valorador_result = $stmt->get_result();
$valorador = $valorador_result->fetch_assoc();
$stmt->close();

if ($valorador) {
    echo "✅ Valorador encontrado:\n";
    echo "  - ID: {$valorador['id']}\n";
    echo "  - Identifier: {$valorador['client_identifier']}\n";
    echo "  - Nombre: {$valorador['nombre_display']}\n";
    echo "  - Activo: " . ($valorador['activo'] ? '✅ SÍ' : '❌ NO') . "\n";
    echo "  - Creado: {$valorador['fecha_creacion']}\n";
} else {
    echo "❌ No se encontró valorador\n";
}

// 2. Estado de la suscripción en BD
echo "\n2. ESTADO DE LA SUSCRIPCIÓN EN BD:\n";
echo "=" . str_repeat("=", 40) . "\n";

$sub_query = "SELECT id, stripe_subscription_id, estado, nombre_plan_display, fecha_creacion, fecha_modificacion
              FROM suscripciones WHERE user_id = ? ORDER BY fecha_creacion DESC LIMIT 1";
$stmt = $db->prepare($sub_query);
$stmt->bind_param("i", $userId);
$stmt->execute();
$sub_result = $stmt->get_result();
$subscription = $sub_result->fetch_assoc();
$stmt->close();

if ($subscription) {
    echo "✅ Suscripción encontrada:\n";
    echo "  - ID: {$subscription['id']}\n";
    echo "  - Stripe ID: {$subscription['stripe_subscription_id']}\n";
    echo "  - Estado: {$subscription['estado']}\n";
    echo "  - Plan: {$subscription['nombre_plan_display']}\n";
    echo "  - Creada: {$subscription['fecha_creacion']}\n";
    echo "  - Modificada: {$subscription['fecha_modificacion']}\n";
} else {
    echo "❌ No se encontró suscripción\n";
}

// 3. Estado en Stripe
echo "\n3. ESTADO EN STRIPE:\n";
echo "=" . str_repeat("=", 40) . "\n";

try {
    \Stripe\Stripe::setApiKey(env('STRIPE_SECRET_KEY'));
    
    // Customer
    $customer = \Stripe\Customer::retrieve($stripeCustomerId);
    echo "Customer:\n";
    echo "  - ID: {$customer->id}\n";
    echo "  - Email: {$customer->email}\n";
    echo "  - Balance: " . ($customer->balance / 100) . " EUR\n";
    
    // Suscripciones
    $subscriptions = \Stripe\Subscription::all([
        'customer' => $stripeCustomerId,
        'limit' => 3
    ]);
    
    echo "\nSuscripciones en Stripe:\n";
    foreach ($subscriptions->data as $sub) {
        echo "  - ID: {$sub->id}\n";
        echo "    Estado: {$sub->status}\n";
        echo "    Cancelar al final: " . ($sub->cancel_at_period_end ? 'SÍ' : 'NO') . "\n";
        echo "    Periodo actual: " . date('Y-m-d H:i:s', $sub->current_period_start) . " - " . date('Y-m-d H:i:s', $sub->current_period_end) . "\n";
        
        foreach ($sub->items->data as $item) {
            echo "    Plan: " . ($item->price->unit_amount / 100) . " EUR/{$item->price->recurring->interval}\n";
        }
        echo "    ---\n";
    }
    
    // Facturas recientes
    $invoices = \Stripe\Invoice::all([
        'customer' => $stripeCustomerId,
        'limit' => 5
    ]);
    
    echo "\nFacturas recientes:\n";
    foreach ($invoices->data as $invoice) {
        echo "  - ID: {$invoice->id}\n";
        echo "    Estado: {$invoice->status}\n";
        echo "    Total: " . ($invoice->total / 100) . " EUR\n";
        echo "    Fecha: " . date('Y-m-d H:i:s', $invoice->created) . "\n";
        echo "    ---\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error consultando Stripe: " . $e->getMessage() . "\n";
}

// 4. Resumen de lo que hicieron los fixes
echo "\n4. RESUMEN DE LOS FIXES APLICADOS:\n";
echo "=" . str_repeat("=", 40) . "\n";

echo "FIX 1 - VALORADOR:\n";
echo "  ✅ Cambió clientes_valorador.activo de 0 a 1\n";
echo "  ✅ Actualizó suscripciones.estado de 'past_due' a 'active'\n";
echo "  ✅ Resultado: Banner de 'Valorador inactivo' desapareció\n\n";

echo "FIX 2 - FACTURACIÓN:\n";
echo "  ✅ Anuló factura duplicada: in_1RkT2nHei6CusI6XGCoxHGNq (49 EUR)\n";
echo "  ✅ Aplicó crédito de 9.09 EUR por sobrecobro\n";
echo "  ✅ Resultado: Eliminó factura pendiente y compensó sobrecobro\n\n";

echo "ESTADO FINAL:\n";
if ($valorador && $valorador['activo'] == 1) {
    echo "  ✅ Valorador ACTIVO\n";
} else {
    echo "  ❌ Valorador INACTIVO\n";
}

if ($subscription && $subscription['estado'] == 'active') {
    echo "  ✅ Suscripción ACTIVA\n";
} else {
    echo "  ❌ Suscripción INACTIVA\n";
}

$db->close();

echo "\n=== VERIFICACIÓN COMPLETADA ===\n";
?>
