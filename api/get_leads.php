<?php
declare(strict_types=1);
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;
// Logger específico para este endpoint
if (!function_exists('custom_log_get_leads')) {
    function custom_log_get_leads($message) {
        $logFile = __DIR__ . '/logs/debug_get_leads.log';
        if (!is_dir(__DIR__ . '/logs')) {
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                error_log("Advertencia [get_leads]: No se pudo crear el directorio de logs: " . __DIR__ . '/logs');
                return;
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [get_leads] {$message}\n", 3, $logFile);
    }
}
// Las cabeceras y el manejo de OPTIONS se han movido a cors.php
// --- Función para obtener y decodificar el token JWT ---
function get_decoded_jwt_payload_leads_corrected() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded;
            } catch (Exception $e) { 
                custom_log_get_leads("Error decodificando token: " . $e->getMessage());
            }
            if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                ini_set('display_errors', $previous_display_errors);
            }
        } else {
            custom_log_get_leads("JWT_SECRET no está configurado.");
        }
    }
    return null;
}
$decoded_payload = get_decoded_jwt_payload_leads_corrected();
if (!$decoded_payload) {
    http_response_code(401);
    custom_log_get_leads("Acceso no autorizado: Token no válido o ausente.");
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado. Token no válido o ausente.']);
    exit;
}
$user_id = $decoded_payload->user_id ?? null;
$agency_id_from_jwt = $decoded_payload->agency_id ?? null;
$roles = $decoded_payload->roles ?? [];
$is_super_admin = in_array('admin', $roles);
if (!$user_id) {
    http_response_code(401);
    custom_log_get_leads("Acceso no autorizado: ID de usuario ausente en token.");
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado (ID de usuario ausente en token).']);
    exit;
}
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 15;
$offset = ($page - 1) * $limit;
$searchTerm = $_GET['search'] ?? ''; // Usar '' como default para evitar errores si es null
$orderBy = $_GET['orderBy'] ?? 'vl.fecha_creacion';
$orderDir = $_GET['orderDir'] ?? 'DESC';

// Nuevos parámetros para filtros
$status = $_GET['status'] ?? null;
$period = $_GET['period'] ?? null;

$allowedOrderBy = [
    'vl.fecha_creacion', 
    'vl.nombre', 
    'vv.valor_estimado_min', // Asegúrate que esto se quiera ordenar así, puede ser confuso sin el max
    'ia_interest_score', 
    'ia_last_interaction_date',
    'vv.direccion' // Para ordenar por dirección de propiedad
];
$allowedOrderDir = ['ASC', 'DESC'];
if (!in_array($orderBy, $allowedOrderBy)) {
    custom_log_get_leads("Advertencia: Campo de ordenamiento no permitido '{$orderBy}'. Usando default 'vl.fecha_creacion'.");
    $orderBy = 'vl.fecha_creacion';
}
if (!in_array(strtoupper($orderDir), $allowedOrderDir)) {
    custom_log_get_leads("Advertencia: Dirección de ordenamiento no permitida '{$orderDir}'. Usando default 'DESC'.");
    $orderDir = 'DESC';
}
$stmt_leads_debug = null; // Para logging
try {
    $pdo = Database::getInstance()->getConnection();
    $base_sql_select_fields = "
        vl.id as lead_id,
        vl.cliente_valorador_id,
        cv.id as cliente_valorador_id_full,
        vl.nombre,
        vl.email,
        vl.telefono,
        vl.uuid,
        vl.necesidad,
        vl.estado,
        vl.num_valoraciones,
        vl.notas,
        vl.valoracion_id,
        vl.fecha_modificacion,
        vv.direccion as direccion_propiedad,
        vv.tipo_principal as tipo_propiedad,
        vv.valor_estimado_min,
        vv.valor_estimado_max,
        vl.fecha_creacion as fecha_captura,
        cv.nombre_display as valorador_nombre,
        cv.client_identifier,
        lst.status AS lead_estado_secuencia,
        lst.sequence_id AS lead_id_secuencia_asignada,
        lst.next_due_date AS ia_next_email_date,
        lst.last_processed_at AS ia_last_processed_date,
        s.name AS ia_sequence_name,
        ss.name AS ia_current_step_name,
        ss.step_order AS ia_current_step_order
    ";
    $base_sql_from_joins = "
        FROM valorador_leads vl
        LEFT JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
        LEFT JOIN valorador_valoraciones vv ON vl.valoracion_id = vv.id
        LEFT JOIN lead_sequence_tracking lst ON vl.id = lst.lead_id
        LEFT JOIN sequences s ON lst.sequence_id = s.id
        LEFT JOIN sequence_steps ss ON lst.current_step_id = ss.id
    ";
    $base_sql_count = "SELECT COUNT(vl.id) " . $base_sql_from_joins;
    $base_sql_select = "SELECT " . $base_sql_select_fields . $base_sql_from_joins;
    $where_clauses = [];
    $params = []; // Este será un array asociativo para parámetros nombrados
    if (!$is_super_admin) {
        if (!$agency_id_from_jwt) {
            http_response_code(403);
            custom_log_get_leads("Agencia no identificada para el usuario no admin: {$user_id}");
            echo json_encode(['success' => false, 'message' => 'Agencia no identificada para el usuario.']);
            exit;
        }
        // MODIFICADO: Permitir acceso a datos históricos incluso con valoradores inactivos
        $stmt_clients = $pdo->prepare("SELECT client_identifier FROM clientes_valorador WHERE agency_id = :agency_id");
        $stmt_clients->bindParam(':agency_id', $agency_id_from_jwt, PDO::PARAM_INT);
        $stmt_clients->execute();
        $client_identifiers_for_agency = $stmt_clients->fetchAll(PDO::FETCH_COLUMN);
        if (empty($client_identifiers_for_agency)) {
            custom_log_get_leads("Usuario {$user_id} (agencia {$agency_id_from_jwt}) no tiene valoradores configurados. Devolviendo 0 leads.");
            echo json_encode(['success' => true, 'leads' => [], 'pagination' => ['total' => 0, 'page' => $page, 'limit' => $limit, 'totalPages' => 0]]);
            exit;
        }
        $client_id_placeholders = [];
        foreach ($client_identifiers_for_agency as $key => $cid) {
            $placeholder = ":client_id_" . $key;
            $client_id_placeholders[] = $placeholder;
            $params[$placeholder] = $cid;
        }
        $where_clauses[] = "cv.client_identifier IN (" . implode(',', $client_id_placeholders) . ")";
    }
    if (!empty($searchTerm)) {
        $search_term_param = '%' . $searchTerm . '%';
        $where_clauses[] = "(vl.nombre LIKE :search_term_nombre OR vl.email LIKE :search_term_email OR vv.direccion LIKE :search_term_direccion)";
        $params[':search_term_nombre'] = $search_term_param;
        $params[':search_term_email'] = $search_term_param;
        $params[':search_term_direccion'] = $search_term_param;
    }

    // Filtro por estado de la secuencia (status)
    if ($status && $status !== 'all') {
        $allowed_statuses = ['active', 'paused', 'completed', 'lost', 'unsubscribed'];
        if (in_array($status, $allowed_statuses)) {
            $where_clauses[] = "lst.status = :status";
            $params[':status'] = $status;
        }
    }

    // Filtro por período de tiempo (period)
    if ($period && $period !== 'all') {
        $date_condition = "";
        switch ($period) {
            case 'today':
                $date_condition = "DATE(vl.fecha_creacion) = CURDATE()";
                break;
            case 'last_week':
                $date_condition = "vl.fecha_creacion >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                break;
            case 'last_month':
                $date_condition = "vl.fecha_creacion >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                break;
            case 'last_3_months':
                $date_condition = "vl.fecha_creacion >= DATE_SUB(NOW(), INTERVAL 3 MONTH)";
                break;
        }
        if ($date_condition) {
            $where_clauses[] = $date_condition;
        }
    }

    $sql_where = "";
    if (!empty($where_clauses)) {
        $sql_where = " WHERE " . implode(" AND ", $where_clauses);
    }
    // Obtener total para paginación
    $stmt_total = $pdo->prepare($base_sql_count . $sql_where);
    $stmt_total->execute($params); // $params ya contiene todos los necesarios para el WHERE
    $total_records = (int)$stmt_total->fetchColumn();
    $total_pages = ceil($total_records / $limit);
    // Obtener leads para la página actual
    $sql_leads = $base_sql_select . $sql_where . " ORDER BY {$orderBy} {$orderDir} LIMIT :limit OFFSET :offset";
    $stmt_leads_debug = $pdo->prepare($sql_leads);
    // Añadir limit y offset a los parámetros para la consulta de selección
    $params[':limit'] = $limit;
    $params[':offset'] = $offset;
    custom_log_get_leads("SQL Ejecutado: " . $sql_leads . " con Params: " . json_encode($params));
    $stmt_leads_debug->execute($params);
    $leads = $stmt_leads_debug->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode([
        'success' => true, 
        'leads' => $leads,
        'pagination' => [
            'total' => $total_records,
            'page' => $page,
            'limit' => $limit,
            'totalPages' => $total_pages
        ]
    ]);
} catch (PDOException $e) {
    custom_log_get_leads("Error de BD en get_leads: " . $e->getMessage());
    $debug_sql = $stmt_leads_debug ? $stmt_leads_debug->queryString : "No disponible";
    error_log("[get_leads.php] PDOException: " . $e->getMessage() . " - SQL: " . $debug_sql . " - Params: " . json_encode($params ?? [])); // Usar $params ?? [] por si acaso
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB).']);
} catch (Exception $e) {
    custom_log_get_leads("Error general: " . $e->getMessage());
    error_log("[get_leads.php] Exception: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}
?>