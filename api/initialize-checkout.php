<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Api\lib\Logger;
// --- INICIO DEBUG LOGGING ---
$logFileDir = __DIR__ . '/logs';
if (!is_dir($logFileDir)) {
    mkdir($logFileDir, 0777, true); // Intentar crear el directorio logs si no existe
}
$debugLogFile = $logFileDir . '/initialize-checkout-debug.log';
$logSeparator = "-------------------------------------\n";
// Función helper para logging
function writeToCheckoutLog($message, $logFilePath) {
    $timestamp = date("Y-m-d H:i:s");
    $logEntry = "[{$timestamp}] {$message}\n";
    // FILE_APPEND para no sobrescribir, LOCK_EX para escritura segura (aunque menos performante para logs muy activos)
    file_put_contents($logFilePath, $logEntry, FILE_APPEND | LOCK_EX);
}
Logger::info('[Initialize-checkout.php] Script iniciado.');
writeToCheckoutLog("REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'N/A'), $debugLogFile);
// --- FIN DEBUG LOGGING ---
// Las cabeceras CORS y el manejo de OPTIONS ahora son gestionados por cors.php
header('Content-Type: application/json');
$stripeSecretKey = defined('STRIPE_SECRET_KEY') ? STRIPE_SECRET_KEY : ($_ENV['STRIPE_SECRET_KEY'] ?? null);
if (!$stripeSecretKey) {
    $errorMsg = 'Falta la variable de entorno STRIPE_SECRET_KEY';
    writeToCheckoutLog("ERROR: " . $errorMsg, $debugLogFile);
    error_log('initialize-checkout.php: ' . $errorMsg); // También al log general de PHP
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor: Configuración de Stripe incompleta.']);
    exit();
}
writeToCheckoutLog("STRIPE_SECRET_KEY obtenida.", $debugLogFile);
\Stripe\Stripe::setApiKey($stripeSecretKey);
writeToCheckoutLog("Stripe API Key configurada.", $debugLogFile);
$rawInput = file_get_contents('php://input');
writeToCheckoutLog("Raw input: " . $rawInput, $debugLogFile);
$input = json_decode($rawInput, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    $errorMsg = 'Error al decodificar JSON de entrada: ' . json_last_error_msg();
    writeToCheckoutLog("ERROR: " . $errorMsg, $debugLogFile);
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $errorMsg]);
    exit();
}
writeToCheckoutLog("Input decodificado: " . print_r($input, true), $debugLogFile);
$email = $input['email'] ?? null;
$fullName = $input['fullName'] ?? null;
$couponCode = trim($input['coupon_code'] ?? '');
if (empty($email)) {
    $errorMsg = 'El email es obligatorio para inicializar el checkout.';
    writeToCheckoutLog("VALIDATION ERROR: " . $errorMsg, $debugLogFile);
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $errorMsg]);
    exit();
}
writeToCheckoutLog("Email validado (no vacío): " . $email, $debugLogFile);
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errorMsg = 'Formato de email inválido para inicializar el checkout.';
    writeToCheckoutLog("VALIDATION ERROR: " . $errorMsg, $debugLogFile);
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $errorMsg]);
    exit();
}
writeToCheckoutLog("Email validado (formato): " . $email, $debugLogFile);
if (empty($fullName) || !is_string($fullName) || trim($fullName) === '') {
    $errorMsg = 'El nombre completo es obligatorio para inicializar el checkout.';
    writeToCheckoutLog("VALIDATION ERROR: " . $errorMsg, $debugLogFile);
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $errorMsg]);
    exit();
}
writeToCheckoutLog("Nombre completo validado: " . $fullName, $debugLogFile);
// Conexión a la base de datos para verificar si el cliente ya existe
// Esto es opcional aquí, ya que signup.php lo verificará de todas formas.
// Si un cliente con ese email ya existe en Stripe, podrías recuperarlo
// en lugar de crear uno nuevo, pero para el flujo de "nuevo signup" con
// datos frescos del formulario, crear uno nuevo o actualizarlo después es común.
// Por simplicidad aquí, vamos a crear un nuevo cliente de Stripe.
// Si quisieras reusar, tendrías que buscar en tu tabla `usuarios` por email
// y obtener el `stripe_customer_id`.
try {
    writeToCheckoutLog("Intentando crear Customer en Stripe...", $debugLogFile);
    $existing_customers = \Stripe\Customer::all(["email" => $email, "limit" => 1]);
    if ($existing_customers->data) {
        $customer = $existing_customers->data[0];
        writeToCheckoutLog("Cliente de Stripe existente encontrado: ID=" . $customer->id, $debugLogFile);
        Logger::info("[Initialize-checkout.php] Cliente de Stripe existente encontrado.", ['customerId' => $customer->id]);
    } else {
        writeToCheckoutLog("No se encontró cliente de Stripe existente. Creando uno nuevo.", $debugLogFile);
        $customer = \Stripe\Customer::create([
            'email' => $email,
            'name' => trim($fullName),
            // Puedes añadir más metadatos si es necesario
        ]);
        writeToCheckoutLog("Cliente de Stripe creado: ID=" . $customer->id, $debugLogFile);
        Logger::info("[Initialize-checkout.php] Cliente de Stripe creado.", ['customerId' => $customer->id]);
    }
    writeToCheckoutLog("Intentando crear SetupIntent en Stripe...", $debugLogFile);
    $setupIntent = \Stripe\SetupIntent::create([
        'customer' => $customer->id,
        'usage' => 'on_session',
        'automatic_payment_methods' => [
            'enabled' => true,
            'allow_redirects' => 'always'
        ]
    ]);
    writeToCheckoutLog("SetupIntent de Stripe creado: ID=" . $setupIntent->id . ", ClientSecret=" . $setupIntent->client_secret, $debugLogFile);
    Logger::info("[Initialize-checkout.php] SetupIntent creado.", ['setupIntentId' => $setupIntent->id, 'clientSecretProvided' => !empty($setupIntent->client_secret)]);

    // Validar cupón si se proporciona
    $couponInfo = null;
    if (!empty($couponCode)) {
        try {
            $coupon = \Stripe\Coupon::retrieve($couponCode);
            if ($coupon->valid && (!$coupon->redeem_by || $coupon->redeem_by >= time())) {
                $couponInfo = [
                    'id' => $coupon->id,
                    'name' => $coupon->name,
                    'percent_off' => $coupon->percent_off,
                    'amount_off' => $coupon->amount_off,
                    'currency' => $coupon->currency,
                    'duration' => $coupon->duration,
                    'duration_in_months' => $coupon->duration_in_months
                ];
                writeToCheckoutLog("Cupón válido aplicado: " . $couponCode, $debugLogFile);
            } else {
                writeToCheckoutLog("Cupón no válido o expirado: " . $couponCode, $debugLogFile);
            }
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            writeToCheckoutLog("Cupón no encontrado: " . $couponCode, $debugLogFile);
        }
    }

    $responseData = [
        'success' => true,
        'stripeCustomerId' => $customer->id,
        'setupIntentClientSecret' => $setupIntent->client_secret,
        'coupon' => $couponInfo,
        'message' => 'Checkout inicializado correctamente.'
    ];
    writeToCheckoutLog("Respuesta exitosa preparada: " . json_encode($responseData), $debugLogFile);
    http_response_code(200);
    echo json_encode($responseData);
    exit();
} catch (\Stripe\Exception\ApiErrorException $e) {
    // Log detallado del error de Stripe
    $errorType = get_class($e);
    $errorMessageForLog = $e->getMessage();
    $stripeErrorObject = method_exists($e, 'getError') ? $e->getError() : null;
    $logDetails = "Stripe API Error: Type={$errorType} Status=" . $e->getHttpStatus() .
                  " StripeErrorCode=" . ($stripeErrorObject ? $stripeErrorObject->code : 'N/A') .
                  " StripeErrorParam=" . ($stripeErrorObject ? $stripeErrorObject->param : 'N/A') .
                  " Message={$errorMessageForLog}";
    writeToCheckoutLog("STRIPE API ERROR: " . $logDetails, $debugLogFile);
    error_log('initialize-checkout.php: ' . $logDetails); // También al log general de PHP
    // Obtener el mensaje para el cliente
    $clientMessage = $errorMessageForLog; // Usar el mensaje principal de la excepción por defecto
    if ($stripeErrorObject && !empty($stripeErrorObject->message)) {
        $clientMessage = $stripeErrorObject->message; // Preferir el mensaje específico del ErrorObject si está disponible
    }
    if (empty($clientMessage)) { // Fallback si el mensaje sigue vacío
        $clientMessage = "Ocurrió un error con el procesador de pagos. Código HTTP: " . $e->getHttpStatus();
    }
    http_response_code($e->getHttpStatus() ?: 500);
    $errorResponse = ['success' => false, 'message' => 'Error al inicializar checkout con Stripe: ' . $clientMessage];
    writeToCheckoutLog("Enviando respuesta de error Stripe: " . json_encode($errorResponse), $debugLogFile);
    echo json_encode($errorResponse);
    exit();
} catch (Throwable $t) { // Throwable captura tanto Error como Exception
    $errorDetails = "General Error/Exception: Type=" . get_class($t) . " Message=" . $t->getMessage() . " File=" . $t->getFile() . " Line=" . $t->getLine();
    writeToCheckoutLog("GENERAL ERROR/EXCEPTION: " . $errorDetails, $debugLogFile);
    error_log('initialize-checkout.php: ' . $errorDetails); // También al log general de PHP
    http_response_code(500);
    $errorResponse = ['success' => false, 'message' => 'Error interno del servidor. Por favor, contacta a soporte.']; // Mensaje genérico para el cliente
    writeToCheckoutLog("Enviando respuesta de error general: " . json_encode($errorResponse), $debugLogFile);
    echo json_encode($errorResponse);
    exit();
}
// Este final de script no debería alcanzarse si todo tiene un exit()
writeToCheckoutLog("ALERTA: Fin de script alcanzado inesperadamente.", $debugLogFile);
?> 