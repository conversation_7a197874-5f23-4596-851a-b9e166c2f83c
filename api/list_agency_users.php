<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
header('Content-Type: application/json');
// --- Logger específico para este endpoint ---
if (!function_exists('custom_log_list_agency_users')) {
    function custom_log_list_agency_users($message) {
        $logFile = __DIR__ . '/debug_list_agency_users.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_list_agency_users("--- [list_agency_users.php] INICIADO ---");
// --- Verificación de autenticación y contexto de agencia ---
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
$decodedToken = null;
$current_user_id = null;
$current_agency_id = null;
$is_agency_owner = false;
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            JWT::$leeway = 60; // Permitir 60 segundos de desviación
            $decodedToken = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
            $current_user_id = $decodedToken->user_id ?? null;
            $current_agency_id = $decodedToken->agency_id ?? null;
            $is_agency_owner = isset($decodedToken->is_agency_owner) ? (bool)$decodedToken->is_agency_owner : false;
            if (!$current_user_id || !$current_agency_id) {
                throw new Exception('User ID o Agency ID no encontrados en el token.');
            }
            custom_log_list_agency_users("Token decodificado: user_id={$current_user_id}, agency_id={$current_agency_id}, is_owner=" . ($is_agency_owner ? 'Sí' : 'No'));
        } catch (Exception $e) {
            custom_log_list_agency_users("Error de Token: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        }
    } else {
        custom_log_list_agency_users("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_list_agency_users("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Verificación de Permiso de Dueño de Agencia (Opcional, pero implementado) ---
if (!$is_agency_owner) {
    custom_log_list_agency_users("Acceso denegado: Usuario user_id={$current_user_id} no es dueño de la agencia agency_id={$current_agency_id}.");
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo el propietario de la agencia puede listar usuarios.']);
    exit();
}
// --- Conexión a la base de datos ---
$conn = null;
try {
    $conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
    if ($conn->connect_error) {
        throw new Exception("Error de conexión a la base de datos: " . $conn->connect_error);
    }
    $conn->set_charset(DB_CHARSET);
    custom_log_list_agency_users("Conexión a BD establecida.");
} catch (Exception $e) {
    custom_log_list_agency_users("Error de BD: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB_CONNECT).']);
    exit();
}
// --- Lógica Principal: Obtener usuarios de la agencia ---
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        $stmt = $conn->prepare("SELECT id, uuid, nombre_completo, email, activo, roles FROM usuarios WHERE agency_id = ?");
        if (!$stmt) {
            throw new Exception("Error al preparar la consulta: " . $conn->error);
        }
        $stmt->bind_param("i", $current_agency_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $agency_users = [];
        while ($row = $result->fetch_assoc()) {
            // Decodificar roles si es una cadena JSON
            if (isset($row['roles']) && is_string($row['roles'])) {
                $roles_decoded = json_decode($row['roles'], true);
                $row['roles'] = (json_last_error() === JSON_ERROR_NONE) ? $roles_decoded : [];
            } else {
                $row['roles'] = []; // Default a array vacío si no está o no es string
            }
            $agency_users[] = $row;
        }
        $stmt->close();
        $conn->close();
        custom_log_list_agency_users("Encontrados " . count($agency_users) . " usuarios para agency_id: " . $current_agency_id);
        echo json_encode(['success' => true, 'users' => $agency_users]);
        exit();
    } catch (Exception $e) {
        custom_log_list_agency_users("Excepción al obtener usuarios: " . $e->getMessage());
        if ($conn && $conn->ping()) $conn->close(); // Asegurar cierre de conexión
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error en el servidor al obtener usuarios: ' . $e->getMessage()]);
        exit();
    }
} else {
    custom_log_list_agency_users("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    if ($conn && $conn->ping()) $conn->close();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}
?> 