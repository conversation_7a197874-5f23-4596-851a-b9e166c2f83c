<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// /api/create-billing-portal-session.php - Crea una sesión para el portal de facturación de Stripe
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
// --- Logger Setup ---
if (!function_exists('custom_log_billing_portal')) {
    function custom_log_billing_portal($message) {
        $logFile = __DIR__ . '/debug_billing_portal.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_billing_portal("--- [create-billing-portal-session.php] INICIADO ---");
// --- Verificación de autenticación ---
$userId = null;
// Código para permitir pasar el token por URL (solo para desarrollo)
if (isset($_GET['token'])) {
    custom_log_billing_portal("Token recibido por URL: " . substr($_GET['token'], 0, 10) . '...');
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer ' . $_GET['token'];
}
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
custom_log_billing_portal("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            custom_log_billing_portal("Intentando decodificar JWT...");
            $key = JWT_SECRET;
            // Versión actualizada de JWT::decode compatible con versiones recientes
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            $userId = $decoded->user_id;
            custom_log_billing_portal("JWT decodificado correctamente. User ID: " . $userId);
        } catch (Exception $e) {
            custom_log_billing_portal("Error al decodificar JWT: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.']);
            exit();
        }
    } else {
        custom_log_billing_portal("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_billing_portal("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Conexión a la base de datos ---
$db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($db->connect_error) {
    custom_log_billing_portal("Error de conexión a la base de datos: " . $db->connect_error);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos.']);
    exit();
}
custom_log_billing_portal("Conexión a la base de datos establecida correctamente.");
// --- Crear sesión del portal de facturación ---
// Para pruebas, aceptamos tanto GET como POST
if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'GET') {
    custom_log_billing_portal("[POST Request] Procesando para user_id: " . $userId);
    // Consulta a la base de datos para obtener el stripe_customer_id
    $query = "SELECT stripe_customer_id FROM usuarios WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $stripeCustomerId = $row['stripe_customer_id'];
        if (!$stripeCustomerId) {
            custom_log_billing_portal("El usuario no tiene un Stripe Customer ID asociado.");
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No hay información de facturación disponible.']);
            exit();
        }
        custom_log_billing_portal("Stripe Customer ID encontrado: " . $stripeCustomerId);
        // Verificar si tenemos la clave de API de Stripe configurada
        if (defined('STRIPE_SECRET_KEY') && !empty(STRIPE_SECRET_KEY)) {
            try {
                custom_log_billing_portal("Intentando crear sesión de portal de facturación con Stripe...");
                // Inicializar Stripe con la clave secreta
                \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
                // Crear la sesión del portal de facturación
                $session = \Stripe\BillingPortal\Session::create([
                    'customer' => $stripeCustomerId,
                    'return_url' => defined('DASHBOARD_BASE_URL') ? DASHBOARD_BASE_URL . '/suscripcion' : 'http://localhost:3000/suscripcion',
                ]);
                $url = $session->url;
                custom_log_billing_portal("Sesión de portal de facturación creada correctamente: " . substr($url, 0, 50) . '...');
            } catch (\Exception $e) {
                custom_log_billing_portal("Error al crear sesión de portal de facturación: " . $e->getMessage());
                // Si hay un error, usamos la URL de ejemplo como fallback
                $url = "https://billing.stripe.com/p/example/" . $stripeCustomerId;
                custom_log_billing_portal("Usando URL de ejemplo como fallback: " . $url);
            }
        } else {
            custom_log_billing_portal("No se encontró la clave de API de Stripe, usando URL de ejemplo.");
            // URL de ejemplo para desarrollo
            $url = "https://billing.stripe.com/p/example/" . $stripeCustomerId;
        }
        echo json_encode(['success' => true, 'url' => $url]);
        exit();
    } else {
        custom_log_billing_portal("No se encontró información del cliente para el usuario ID: " . $userId);
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'No se encontró información del cliente.']);
        exit();
    }
} else {
    custom_log_billing_portal("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}
