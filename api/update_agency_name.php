<?php
declare(strict_types=1);
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Api\lib\Database;
use Api\lib\Logger;
header('Content-Type: application/json');
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
$current_agency_id = null;
$is_agency_owner = false;
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            JWT::$leeway = 60;
            $decodedToken = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
            $current_agency_id = $decodedToken->agency_id ?? null;
            $is_agency_owner = isset($decodedToken->is_agency_owner) ? (bool)$decodedToken->is_agency_owner : false;
            if (!$current_agency_id || !$decodedToken->user_id) { // Also check user_id for completeness
                Logger::warning('[update_agency_name.php] Agency ID o User ID no encontrado en el token.');
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Token inválido: Información de usuario/agencia incompleta.']);
                exit();
            }
        } catch (Exception $e) {
            Logger::error('[update_agency_name.php] Error de Token: ' . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        }
    } else {
        Logger::warning('[update_agency_name.php] Formato de Authorization header incorrecto.');
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    Logger::warning('[update_agency_name.php] No se proporcionó token de autorización.');
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!$is_agency_owner) {
        Logger::warning('[update_agency_name.php] Intento de actualizar nombre por no-propietario.', ['agency_id' => $current_agency_id, 'user_id' => $decodedToken->user_id]);
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo el propietario puede cambiar el nombre de la agencia.']);
        exit();
    }
    $input = json_decode(file_get_contents('php://input'), true);
    $new_agency_name = trim($input['new_agency_name'] ?? '');
    if (empty($new_agency_name)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'El nombre de la agencia no puede estar vacío.']);
        exit();
    }
    if (mb_strlen($new_agency_name) > 255) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'El nombre de la agencia es demasiado largo. Máximo 255 caracteres.']);
        exit();
    }
    $pdo = null;
    try {
        $pdo = Database::getInstance()->getConnection();
        $stmt = $pdo->prepare("UPDATE agencies SET name = ?, fecha_modificacion = NOW() WHERE id = ?");
        $stmt->execute([$new_agency_name, $current_agency_id]);
        if ($stmt->rowCount() > 0) {
            Logger::info('[update_agency_name.php] Nombre de agencia actualizado.', ['agency_id' => $current_agency_id, 'new_name' => $new_agency_name]);
            http_response_code(200);
            echo json_encode(['success' => true, 'message' => 'Nombre de la agencia actualizado correctamente.', 'new_agency_name' => $new_agency_name]);
        } else {
            Logger::info('[update_agency_name.php] No se realizaron cambios en el nombre de la agencia (posiblemente ya era el mismo o agency_id no encontrado). ', ['agency_id' => $current_agency_id, 'new_name' => $new_agency_name]);
            http_response_code(200); // Consider 304 if frontend can handle it and we verify old name
            echo json_encode(['success' => true, 'message' => 'No se realizaron cambios en el nombre de la agencia.', 'new_agency_name' => $new_agency_name]);
        }
    } catch (Exception $e) {
        Logger::error('[update_agency_name.php] Error al actualizar nombre de agencia: ' . $e->getMessage(), ['agency_id' => $current_agency_id]);
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error interno al actualizar el nombre de la agencia.']);
    }
} else {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
}
?> 