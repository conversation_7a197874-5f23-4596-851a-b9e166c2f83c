<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

// Carga las variables de entorno
// $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
// $dotenv->load();

// Configuración de la base de datos
// $db_host = $_ENV['DB_HOST'];
// $db_name = $_ENV['DB_NAME'];
// $db_user = $_ENV['DB_USER'];
// $db_pass = $_ENV['DB_PASS'];

// Inicializar Stripe
if (!defined('STRIPE_SECRET_KEY') || empty(STRIPE_SECRET_KEY)) {
    http_response_code(500);
    // Considerar un log aquí si se implementa un logger centralizado en bootstrap
    echo json_encode(['success' => false, 'message' => 'Error de configuración interna del servidor (Stripe).']);
    exit;
}
\Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

// Verificar token y rol de administrador
$auth_result = verifyTokenAndAdminRole();
if (!$auth_result['success']) {
    http_response_code($auth_result['status_code'] ?? 401);
    echo json_encode(['success' => false, 'message' => $auth_result['message']]);
    exit;
}

header('Content-Type: application/json');

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en admin_get_facturacion: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos.']);
    exit;
}

try {
    // Parámetros de paginación y filtros
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 15;
    $offset = ($page - 1) * $limit;

    $search_query = isset($_GET['search']) ? $_GET['search'] : '';
    $plan_id_filter = isset($_GET['plan_id']) ? $_GET['plan_id'] : ''; // Corresponde a nombre_plan_display
    $status_filter = isset($_GET['estado']) ? $_GET['estado'] : ''; // Estado de la suscripción

    $base_query = "FROM subscriptions s JOIN usuarios u ON s.user_id = u.id";
    $where_clauses = [];
    $params = [];

    if (!empty($search_query)) {
        $where_clauses[] = "(u.nombre_completo LIKE ? OR u.email LIKE ? OR s.stripe_subscription_id LIKE ?)";
        $search_like = "%{$search_query}%";
        array_push($params, $search_like, $search_like, $search_like);
    }

    if (!empty($plan_id_filter)) {
        $where_clauses[] = "s.plan_id = ?";
        array_push($params, $plan_id_filter);
    }

    if (!empty($status_filter)) {
        $where_clauses[] = "s.status = ?";
        array_push($params, $status_filter);
    }

    $where_sql = "";
    if (count($where_clauses) > 0) {
        $where_sql = " WHERE " . implode(" AND ", $where_clauses);
    }

    // Consulta para total de items (para paginación)
    $total_items_query = "SELECT COUNT(s.id) " . $base_query . $where_sql;
    $stmt_total = $pdo->prepare($total_items_query);
    $stmt_total->execute($params);
    $total_items = (int)$stmt_total->fetchColumn();
    $total_pages = (int)ceil($total_items / $limit);

    // Consulta principal
    $data_query = "SELECT s.id, s.user_id, u.nombre_completo, u.email, s.stripe_subscription_id, s.plan_id, s.status, s.current_period_start, s.current_period_end, s.created_at "
                . $base_query . $where_sql 
                . " ORDER BY s.created_at DESC LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;

    $stmt_data = $pdo->prepare($data_query);
    $stmt_data->execute($params);
    $subscriptions = $stmt_data->fetchAll();

    echo json_encode([
        'success' => true,
        'data' => $subscriptions,
        'pagination' => [
            'totalItems' => $total_items,
            'totalPages' => $total_pages,
            'currentPage' => $page,
            'limit' => $limit
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al obtener datos de facturación: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error al obtener los datos de facturación.']);
    exit;
}