<?php
declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
// --- Importar clases de JWT ---
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;

// --- Función para obtener y decodificar el token JWT localmente ---
function get_decoded_jwt_payload_stats() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                // Desactivar display_errors temporalmente para evitar que las advertencias de JWT (deprecated) rompan el JSON
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                // Restaurar el valor original de display_errors
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded; // Devuelve el payload del objeto
            } catch (ExpiredException $e) {
                // custom_log_dashboard_stats("Token expirado: " . $e->getMessage());
            } catch (SignatureInvalidException $e) {
                // custom_log_dashboard_stats("Firma de token inválida: " . $e->getMessage());
            } catch (BeforeValidException $e) {
                // custom_log_dashboard_stats("Token no válido todavía: " . $e->getMessage());
            } catch (\Throwable $e) {
                // custom_log_dashboard_stats("Error decodificando token: " . $e->getMessage());
            }
            // Asegurar que display_errors se restaura si hubo una excepción y quedó en '0'
            if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                ini_set('display_errors', $previous_display_errors);
            }
        } else {
            // custom_log_dashboard_stats("JWT_SECRET no está configurado.");
        }
    }
    return null;
}

$decoded_payload = get_decoded_jwt_payload_stats();

// Obtener datos del payload decodificado localmente
$user_id = $decoded_payload->user_id ?? null;
$agency_id = $decoded_payload->agency_id ?? null;
$roles = $decoded_payload->roles ?? []; // Asumir que roles es un array en el token

$is_super_admin = in_array('admin', $roles);

// FUNCIÓN DE LOG ACTUALIZADA (opcional, pero útil si se quiere mantener):
// Se recomienda integrarla con el Logger centralizado si bootstrap.php lo configura.
if (!function_exists('custom_log_dashboard_stats')) {
    function custom_log_dashboard_stats($message) {
        $logFile = __DIR__ . '/logs/debug_dashboard_stats.log'; // Cambiado a subdirectorio logs
        // Asegurarse que el directorio logs exista y tenga permisos de escritura.
        if (!is_dir(__DIR__ . '/logs')) {
            // Intentar crear el directorio de logs
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                // Si falla la creación, no intentar escribir el log para evitar errores fatales.
                // Podrías usar error_log() global aquí para notificar este problema específico.
                error_log("Advertencia: No se pudo crear el directorio de logs: " . __DIR__ . '/logs');
                return; 
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp] {$message}\n";
        // Usar error_log es más seguro y estándar si la escritura directa falla o no está configurada.
        error_log($logEntry, 3, $logFile);
    }
}

if (!$user_id) {
    http_response_code(401);
    custom_log_dashboard_stats("Acceso no autorizado: Usuario no identificado en get_dashboard_stats.");
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Usuario no identificado.']);
    exit;
}

// --- Función auxiliar para calcular el porcentaje de cambio ---
if (!function_exists('calculate_change_percent_dashboard')) {
    function calculate_change_percent_dashboard($current_value, $previous_value) {
        if ($previous_value == 0) {
            return ($current_value > 0) ? 100.0 : 0.0; // Si antes era 0 y ahora es >0, es 100% de aumento. Si ambos son 0, es 0%
        }
        return round((($current_value - $previous_value) / $previous_value) * 100, 1);
    }
}

// BLOQUE TRY-CATCH PRINCIPAL CON LÓGICA DE NEGOCIO:
try {
    // NUEVA CONEXIÓN A BD (PDO):
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    $client_identifiers_for_query = [];
    $in_placeholders = ''; // Para evitar error si no hay client_identifiers

    if (!$is_super_admin) {
        if (!$agency_id) {
            http_response_code(403);
            custom_log_dashboard_stats("Acceso denegado: No se encontró información de agencia para el usuario id: {$user_id} en get_dashboard_stats.");
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: No se encontró información de agencia para el usuario.']);
            exit;
        }

        // MODIFICADO: Permitir acceso a datos históricos incluso con valoradores inactivos
        $stmt_clients = $pdo->prepare("SELECT client_identifier FROM clientes_valorador WHERE agency_id = :agency_id");
        $stmt_clients->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
        $stmt_clients->execute();
        $client_identifiers_for_query = $stmt_clients->fetchAll(PDO::FETCH_COLUMN);

        if (empty($client_identifiers_for_query)) {
            custom_log_dashboard_stats("Agencia id: {$agency_id} no tiene valoradores activos. Devolviendo stats a cero.");
            // Devolver estructura completa con ceros y null para changePercent
            echo json_encode(['success' => true, 'stats' => [
                'valoraciones_count' => 0, 
                'leads_count' => 0, 
                'emails_enviados_ultimos_7_dias' => 0,
                'valoraciones_ultimos_7_dias' => 0,
                'leads_ultimos_7_dias' => 0,
                'leads_en_nutricion_activa' => 0,
                'emails_enviados_ultimos_30_dias' => ['value' => 0, 'changePercent' => null],
                'leads_ultimos_30_dias' => ['value' => 0, 'changePercent' => null],
                'valoraciones_ultimos_30_dias' => ['value' => 0, 'changePercent' => null]
            ]]);
            exit;
        }
        $in_placeholders = implode(',', array_fill(0, count($client_identifiers_for_query), '?'));
    }

    $stats = [
        'valoraciones_count' => 0, // Total histórico
        'leads_count' => 0, // Total histórico
        'emails_enviados_ultimos_7_dias' => 0,
        'valoraciones_ultimos_7_dias' => 0,
        'leads_ultimos_7_dias' => 0,
        'leads_en_nutricion_activa' => 0,
        'emails_enviados_ultimos_30_dias' => ['value' => 0, 'changePercent' => null],
        'leads_ultimos_30_dias' => ['value' => 0, 'changePercent' => null],
        'valoraciones_ultimos_30_dias' => ['value' => 0, 'changePercent' => null]
    ];

    // --- Helper para fechas ---
    $current_period_start_date = date('Y-m-d H:i:s', strtotime('-30 days'));
    $current_period_end_date = date('Y-m-d H:i:s'); // Hasta ahora
    
    $previous_period_start_date = date('Y-m-d H:i:s', strtotime('-60 days'));
    $previous_period_end_date = date('Y-m-d H:i:s', strtotime('-31 days')); // Excluye el día -30

    $date_7_days_ago = date('Y-m-d H:i:s', strtotime('-7 days'));

    // Contar valoraciones (TOTALES)
    $sql_valoraciones_total = "SELECT COUNT(*) as total FROM valorador_valoraciones vv LEFT JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id";
    $params_valoraciones_total = [];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_valoraciones_total .= " WHERE cv.client_identifier IN (" . $in_placeholders . ")";
        $params_valoraciones_total = $client_identifiers_for_query;
    }
    $stmt_valoraciones_total = $pdo->prepare($sql_valoraciones_total);
    $stmt_valoraciones_total->execute($params_valoraciones_total);
    $stats['valoraciones_count'] = (int)$stmt_valoraciones_total->fetchColumn();

    // Contar leads (TOTALES)
    $sql_leads_total = "SELECT COUNT(*) as total FROM valorador_leads vl LEFT JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id";
    $params_leads_total = [];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_leads_total .= " WHERE cv.client_identifier IN (" . $in_placeholders . ")";
        $params_leads_total = $client_identifiers_for_query;
    }
    $stmt_leads_total = $pdo->prepare($sql_leads_total);
    $stmt_leads_total->execute($params_leads_total);
    $stats['leads_count'] = (int)$stmt_leads_total->fetchColumn();

    // --- VALORACIONES ---
    // Últimos 30 días
    $sql_valoraciones_current = "SELECT COUNT(*) as total FROM valorador_valoraciones vv LEFT JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id WHERE vv.fecha_creacion >= ? AND vv.fecha_creacion <= ?";
    $params_valoraciones_current = [$current_period_start_date, $current_period_end_date];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_valoraciones_current .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_valoraciones_current = array_merge($params_valoraciones_current, $client_identifiers_for_query);
    }
    $stmt_valoraciones_current = $pdo->prepare($sql_valoraciones_current);
    $stmt_valoraciones_current->execute($params_valoraciones_current);
    $current_valoraciones = (int)$stmt_valoraciones_current->fetchColumn();
    $stats['valoraciones_ultimos_30_dias']['value'] = $current_valoraciones;

    // 30 días previos
    $sql_valoraciones_previous = "SELECT COUNT(*) as total FROM valorador_valoraciones vv LEFT JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id WHERE vv.fecha_creacion >= ? AND vv.fecha_creacion <= ?";
    $params_valoraciones_previous = [$previous_period_start_date, $previous_period_end_date];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_valoraciones_previous .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_valoraciones_previous = array_merge($params_valoraciones_previous, $client_identifiers_for_query);
    }
    $stmt_valoraciones_previous = $pdo->prepare($sql_valoraciones_previous);
    $stmt_valoraciones_previous->execute($params_valoraciones_previous);
    $previous_valoraciones = (int)$stmt_valoraciones_previous->fetchColumn();
    $stats['valoraciones_ultimos_30_dias']['changePercent'] = calculate_change_percent_dashboard($current_valoraciones, $previous_valoraciones);

    // Valoraciones últimos 7 días (ya estaba, solo ajusto params si es necesario)
    $sql_valoraciones_7_dias = "SELECT COUNT(*) as total FROM valorador_valoraciones vv LEFT JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id WHERE vv.fecha_creacion >= ?";
    $params_valoraciones_7_dias = [$date_7_days_ago];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_valoraciones_7_dias .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_valoraciones_7_dias = array_merge($params_valoraciones_7_dias, $client_identifiers_for_query);
    }
    $stmt_valoraciones_7_dias = $pdo->prepare($sql_valoraciones_7_dias);
    $stmt_valoraciones_7_dias->execute($params_valoraciones_7_dias);
    $stats['valoraciones_ultimos_7_dias'] = (int)$stmt_valoraciones_7_dias->fetchColumn();


    // --- LEADS ---
    // Últimos 30 días
    $sql_leads_current = "SELECT COUNT(*) as total FROM valorador_leads vl LEFT JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id WHERE vl.fecha_creacion >= ? AND vl.fecha_creacion <= ?";
    $params_leads_current = [$current_period_start_date, $current_period_end_date];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_leads_current .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_leads_current = array_merge($params_leads_current, $client_identifiers_for_query);
    }
    $stmt_leads_current = $pdo->prepare($sql_leads_current);
    $stmt_leads_current->execute($params_leads_current);
    $current_leads = (int)$stmt_leads_current->fetchColumn();
    $stats['leads_ultimos_30_dias']['value'] = $current_leads;

    // 30 días previos
    $sql_leads_previous = "SELECT COUNT(*) as total FROM valorador_leads vl LEFT JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id WHERE vl.fecha_creacion >= ? AND vl.fecha_creacion <= ?";
    $params_leads_previous = [$previous_period_start_date, $previous_period_end_date];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_leads_previous .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_leads_previous = array_merge($params_leads_previous, $client_identifiers_for_query);
    }
    $stmt_leads_previous = $pdo->prepare($sql_leads_previous);
    $stmt_leads_previous->execute($params_leads_previous);
    $previous_leads = (int)$stmt_leads_previous->fetchColumn();
    $stats['leads_ultimos_30_dias']['changePercent'] = calculate_change_percent_dashboard($current_leads, $previous_leads);
    
    // Leads últimos 7 días (ya estaba)
    $sql_leads_7_dias = "SELECT COUNT(*) as total FROM valorador_leads vl LEFT JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id WHERE vl.fecha_creacion >= ?";
    $params_leads_7_dias = [$date_7_days_ago];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_leads_7_dias .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_leads_7_dias = array_merge($params_leads_7_dias, $client_identifiers_for_query);
    }
    $stmt_leads_7_dias = $pdo->prepare($sql_leads_7_dias);
    $stmt_leads_7_dias->execute($params_leads_7_dias);
    $stats['leads_ultimos_7_dias'] = (int)$stmt_leads_7_dias->fetchColumn();


    // --- EMAILS ENVIADOS ---
    // Últimos 30 días
    $sql_emails_current = "SELECT COUNT(leh.id)
                           FROM lead_emails_historial leh
                           JOIN valorador_leads vl ON leh.lead_id = vl.id
                           JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                           WHERE leh.estado_envio = 'sent'
                             AND leh.fecha_envio_real >= ? AND leh.fecha_envio_real <= ?";
    $params_emails_current = [$current_period_start_date, $current_period_end_date];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_emails_current .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_emails_current = array_merge($params_emails_current, $client_identifiers_for_query);
    }
    $stmt_emails_current = $pdo->prepare($sql_emails_current);
    $stmt_emails_current->execute($params_emails_current);
    $current_emails = (int)$stmt_emails_current->fetchColumn();
    $stats['emails_enviados_ultimos_30_dias']['value'] = $current_emails;

    // 30 días previos
    $sql_emails_previous = "SELECT COUNT(leh.id)
                           FROM lead_emails_historial leh
                           JOIN valorador_leads vl ON leh.lead_id = vl.id
                           JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                           WHERE leh.estado_envio = 'sent'
                             AND leh.fecha_envio_real >= ? AND leh.fecha_envio_real <= ?";
    $params_emails_previous = [$previous_period_start_date, $previous_period_end_date];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_emails_previous .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_emails_previous = array_merge($params_emails_previous, $client_identifiers_for_query);
    }
    $stmt_emails_previous = $pdo->prepare($sql_emails_previous);
    $stmt_emails_previous->execute($params_emails_previous);
    $previous_emails = (int)$stmt_emails_previous->fetchColumn();
    $stats['emails_enviados_ultimos_30_dias']['changePercent'] = calculate_change_percent_dashboard($current_emails, $previous_emails);

    // Emails enviados últimos 7 días (ya estaba)
    $sql_emails_7_dias = "SELECT COUNT(leh.id)
                           FROM lead_emails_historial leh
                           JOIN valorador_leads vl ON leh.lead_id = vl.id
                           JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                           WHERE leh.estado_envio = 'sent'
                             AND leh.fecha_envio_real >= ?";
    $params_emails_7_dias = [$date_7_days_ago];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_emails_7_dias .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_emails_7_dias = array_merge($params_emails_7_dias, $client_identifiers_for_query);
    }
    $stmt_emails_7_dias = $pdo->prepare($sql_emails_7_dias);
    $stmt_emails_7_dias->execute($params_emails_7_dias);
    $stats['emails_enviados_ultimos_7_dias'] = (int)$stmt_emails_7_dias->fetchColumn();
    
    // Contar leads en nutrición activa (ya estaba)
    $sql_nurturing = "SELECT COUNT(DISTINCT lst.lead_id)
                      FROM lead_sequence_tracking lst
                      LEFT JOIN valorador_leads vl ON lst.lead_id = vl.id
                      LEFT JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                      WHERE lst.status = 'active'";
    $params_nurturing = [];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $sql_nurturing .= " AND cv.client_identifier IN (" . $in_placeholders . ")";
        $params_nurturing = $client_identifiers_for_query;
    }
    $stmt_nurturing = $pdo->prepare($sql_nurturing);
    $stmt_nurturing->execute($params_nurturing);
    $stats['leads_en_nutricion_activa'] = (int)$stmt_nurturing->fetchColumn();

    custom_log_dashboard_stats("Stats calculadas para user_id: {$user_id}, agency_id: {$agency_id} (...);"); // Log abreviado
    echo json_encode(['success' => true, 'stats' => $stats]);

} catch (PDOException $e) {
    custom_log_dashboard_stats("Error de BD en get_dashboard_stats: " . $e->getMessage());
    error_log("[get_dashboard_stats.php] PDOException: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB).']);
} catch (Exception $e) {
    custom_log_dashboard_stats("Error general en get_dashboard_stats: " . $e->getMessage());
    error_log("[get_dashboard_stats.php] Exception: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}

?>
