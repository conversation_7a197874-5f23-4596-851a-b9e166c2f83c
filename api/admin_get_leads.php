<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// error_reporting(E_ALL); // Manejado por bootstrap.php
// ini_set('display_errors', 0); // Manejado por bootstrap.php
 // <--- INTEGRAR BOOTSTRAP
require_once __DIR__ . '/utils/auth_check.php'; // Contiene verifyTokenAndAdminRole
// Use Firebase\JWT\JWT; // auth_check.php o bootstrap.php manejan esto
// Use Firebase\JWT\Key;
header('Content-Type: application/json');
// --- Carga de .env y autoload manejada por bootstrap.php ---
// try { $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); $dotenv->load(); } catch ... (eliminado)
// --- Constantes de BD y JWT_SECRET vienen de bootstrap.php ---
// $dbHost = $_ENV['DB_HOST'] ?? null; (eliminado)
// ... (eliminación de otras asignaciones de variables de entorno)
// Verificar token y rol de administrador
// auth_check.php ahora usará JWT_SECRET de bootstrap.php
$auth_response = verifyTokenAndAdminRole(); // <--- SIN ARGUMENTOS
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en admin_get_leads: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

try {
    // Parámetros de paginación y filtro
    $page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT, ['options' => ['default' => 1, 'min_range' => 1]]);
    $limit = filter_input(INPUT_GET, 'limit', FILTER_VALIDATE_INT, ['options' => ['default' => 15, 'min_range' => 1]]);
    $offset = ($page - 1) * $limit;
    $search_query = $_GET['search'] ?? '';
    $filter_estado = $_GET['estado'] ?? '';
    $filter_client_id = $_GET['client_id'] ?? ''; // client_identifier del valorador

    // Construcción de la consulta base
    $sql_base = "FROM valorador_leads vl
                LEFT JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                LEFT JOIN lead_sequence_tracking lst ON vl.id = lst.lead_id";
    $where_clauses = [];
    $params = [];

    // Filtro de búsqueda
    if (!empty($search_query)) {
        $where_clauses[] = "(vl.nombre LIKE ? OR vl.email LIKE ? OR vl.telefono LIKE ?)";
        $search_param = "%" . $search_query . "%";
        array_push($params, $search_param, $search_param, $search_param);
    }
    // Filtro por estado
    if (!empty($filter_estado)) {
        $where_clauses[] = "vl.estado = ?";
        $params[] = $filter_estado;
    }
    // Filtro por client_id (client_identifier del valorador)
    if (!empty($filter_client_id)) {
        $where_clauses[] = "vl.client_id = ?";
        $params[] = $filter_client_id;
    }

    $sql_where = "";
    if (!empty($where_clauses)) {
        $sql_where = " WHERE " . implode(" AND ", $where_clauses);
    }

    // Obtener el total de items para la paginación
    $sql_total = "SELECT COUNT(vl.id) " . $sql_base . $sql_where;
    $stmt_total = $pdo->prepare($sql_total);
    $stmt_total->execute($params);
    $totalItems = (int)$stmt_total->fetchColumn();
    $total_pages = $totalItems > 0 ? (int)ceil($totalItems / $limit) : 1;

    // Obtener los datos de los leads
    $sql_data = "SELECT vl.*, cv.nombre_display AS nombre_valorador_asociado,
                lst.status AS lead_estado_secuencia,
                lst.sequence_id AS lead_id_secuencia_asignada,
                (SELECT COUNT(*) FROM valorador_valoraciones vv WHERE vv.lead_id = vl.id) as num_valoraciones 
                " . $sql_base . $sql_where . " ORDER BY vl.fecha_creacion DESC LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;

    $stmt_data = $pdo->prepare($sql_data);
    $stmt_data->execute($params);
    $leads = $stmt_data->fetchAll();

    echo json_encode([
        'success' => true,
        'data' => $leads,
        'pagination' => [
            'totalItems' => $totalItems,
            'totalPages' => $total_pages,
            'currentPage' => $page,
            'limit' => $limit
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al obtener leads (admin_get_leads): " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al obtener los leads.']);
    exit();
}