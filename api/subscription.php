<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Api\Utils\PlanFeatures;
// /api/subscription.php - Obtiene la información de suscripción del usuario
// IMPORTANTE: Evitar cualquier salida antes de establecer los headers
// Desactivar la salida de errores directa, los capturaremos y devolveremos como JSON
// ini_set('display_errors', 0); // Gestionado por bootstrap.php
// ini_set('display_startup_errors', 0); // Gestionado por bootstrap.php
// error_reporting(E_ALL); // Gestionado por bootstrap.php
// Iniciar buffer de salida para evitar cualquier salida accidental
// ob_start(); // Considerar si es necesario globalmente o por endpoint
// --- Logger Setup ---
if (!function_exists('custom_log_subscription')) {
    function custom_log_subscription($message) {
        $logFile = __DIR__ . '/debug_subscription.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_subscription("--- [subscription.php] INICIADO ---");
// --- Autoload y Carga de .env ---
// $autoloadPath = __DIR__ . '/vendor/autoload.php'; // Gestionado por bootstrap.php
// ... (eliminar require_once $autoloadPath y carga de Dotenv) ...
// Incluir PlanFeatures.php (Asumiendo que no está autocargado por Composer y es específico)
$planFeaturesPath = __DIR__ . '/utils/PlanFeatures.php';
custom_log_subscription("Intentando cargar PlanFeatures desde: " . $planFeaturesPath);
if (!file_exists($planFeaturesPath)) {
    custom_log_subscription("CRÍTICO: utils/PlanFeatures.php NO ENCONTRADO en " . $planFeaturesPath);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de configuración del servidor (PlanFeatures).']);
    exit();
}
require_once $planFeaturesPath;
custom_log_subscription("PlanFeatures.php cargado correctamente.");

// Incluir el servicio de estados de suscripción
$subscriptionStatusServicePath = __DIR__ . '/lib/SubscriptionStatusService.php';
if (!file_exists($subscriptionStatusServicePath)) {
    custom_log_subscription("CRÍTICO: lib/SubscriptionStatusService.php NO ENCONTRADO en " . $subscriptionStatusServicePath);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de configuración del servidor (SubscriptionStatusService).']);
    exit();
}
require_once $subscriptionStatusServicePath;
custom_log_subscription("SubscriptionStatusService.php cargado correctamente.");
// --- Verificación de autenticación ---
$userId = null;
// Código para permitir pasar el token por URL (solo para desarrollo)
if (isset($_GET['token'])) {
    custom_log_subscription("Token recibido por URL: " . substr($_GET['token'], 0, 10) . '...');
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer ' . $_GET['token'];
}
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
custom_log_subscription("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_get('display_errors'); // Guardar estado actual
        ini_set('display_errors', '0'); // Desactivar temporalmente
        try {
            custom_log_subscription("Intentando decodificar JWT...");
            $key = JWT_SECRET; // USAR CONSTANTE DE BOOTSTRAP
            custom_log_subscription("Longitud de la clave JWT_SECRET_KEY: " . strlen($key));
            custom_log_subscription("Primeros 10 caracteres de la clave: " . substr($key, 0, 10));
            custom_log_subscription("Últimos 10 caracteres de la clave: " . substr($key, -10));
            // Verificar si los headers del token son válidos
            list($headb64, $bodyb64, $cryptob64) = explode('.', $token);
            custom_log_subscription("Token recibido: Encabezado.Cuerpo.Firma");
            $header = json_decode(base64_decode($headb64));
            custom_log_subscription("Headers del token: " . json_encode($header));
            // Versión actualizada de JWT::decode compatible con versiones recientes
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            // Registrar la estructura completa del token decodificado
            custom_log_subscription("Estructura del token: " . json_encode($decoded));
            $userId = null; // Este será el owner_user_id de la agencia
            $agencyId = null;
            $isAgencyOwner = false; // No se usa directamente aquí pero es bueno extraerlo
            if (isset($decoded->user_id)) {
                // $userId = $decoded->user_id; // No usamos el user_id directamente del token para la consulta principal
            } else {
                custom_log_subscription("Error: No se encontró user_id en el token JWT");
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Token inválido: no contiene user_id.']);
                exit();
            }
            if (isset($decoded->agency_id) && $decoded->agency_id !== null) {
                $agencyId = $decoded->agency_id;
                custom_log_subscription("agency_id del token: " . $agencyId);
            } else {
                custom_log_subscription("Error: No se encontró agency_id en el token JWT o es nulo.");
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Token inválido: no contiene agency_id.']);
                exit();
            }
            if (isset($decoded->is_agency_owner)) {
                $isAgencyOwner = (bool)$decoded->is_agency_owner;
                custom_log_subscription("is_agency_owner del token: " . ($isAgencyOwner ? 'Sí' : 'No'));
            }
        } catch (Exception $e) {
            custom_log_subscription("Error al decodificar JWT: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.']);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors); // Restaurar estado
        }
    } else {
        custom_log_subscription("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_subscription("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Conexión a la base de datos ---
try {
    custom_log_subscription("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_subscription("Conexión PDO a la base de datos establecida correctamente a través de la clase Database.");

    // Obtener el owner_user_id de la agencia
    $stmt_owner = $pdo->prepare("SELECT owner_user_id FROM agencies WHERE id = ?");
    $stmt_owner->execute([$agencyId]);
    $agency_data = $stmt_owner->fetch();
    if (!$agency_data || !isset($agency_data['owner_user_id'])) {
        custom_log_subscription("CRÍTICO: No se pudo encontrar la agencia o el owner_user_id para agency_id: " . $agencyId);
        http_response_code(404); // O 500 si esto se considera un error interno grave
        echo json_encode(['success' => false, 'message' => 'Información de agencia no encontrada.']);
        exit();
    }
    $userId = $agency_data['owner_user_id']; // Ahora $userId es el owner_user_id
    custom_log_subscription("owner_user_id obtenido para agency_id {" . $agencyId . "}: " . $userId);
    // Obtener el nombre de la agencia
    $agency_name_from_db = null;
    $stmt_agency_name = $pdo->prepare("SELECT name FROM agencies WHERE id = ?");
    $stmt_agency_name->execute([$agencyId]);
    $agency_name_data = $stmt_agency_name->fetch();
    if ($agency_name_data && isset($agency_name_data['name'])) {
        $agency_name_from_db = $agency_name_data['name'];
        custom_log_subscription("Nombre de agencia obtenido para agency_id {" . $agencyId . "}: " . $agency_name_from_db);
    } else {
        custom_log_subscription("ADVERTENCIA: No se pudo obtener el nombre de la agencia para agency_id {" . $agencyId . "}");
    }
} catch (Exception $e) {
    custom_log_subscription("EXCEPCIÓN al conectar a la base de datos o al obtener datos de la agencia: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos: ' . $e->getMessage()]);
    exit();
}
// --- Obtener información de la suscripción ---
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    custom_log_subscription("[GET Request] Procesando para agency_id: " . $agencyId . " (owner_user_id: " . $userId . ")");
    try {
        // Obtener detalles del plan desde PlanFeatures.php usando el owner_user_id
        $planDetailsFromDB = PlanFeatures::getUserActivePlanDetails($userId, $pdo);
        // LOG DETALLADO de planDetailsFromDB
        if ($planDetailsFromDB) {
            custom_log_subscription("Contenido de \$planDetailsFromDB: " . json_encode($planDetailsFromDB));
        } else {
            custom_log_subscription("\$planDetailsFromDB es null o false.");
        }
        if (!$planDetailsFromDB) {
            custom_log_subscription("No se encontraron detalles del plan activo para el usuario ID: " . $userId . " desde PlanFeatures.");
            // Aún podríamos tener una suscripción en Stripe, pero sin plan local. ¿Manejar este caso?
            // Por ahora, si no hay plan local, podríamos devolver un error o datos limitados.
        }
        // Consulta a la base de datos para obtener el stripe_customer_id del usuario (se mantiene por si PlanFeatures no lo devuelve)
        // Esta consulta ahora debe ser sobre el owner_user_id
        $query_user = "SELECT stripe_customer_id FROM usuarios WHERE id = ?"; 
        custom_log_subscription("Ejecutando consulta: {$query_user} con owner_user_id={$userId}");
        $stmt_user = $pdo->prepare($query_user);
        if (!$stmt_user) {
            throw new Exception("Error en prepare() para usuario: " . implode(" ", $pdo->errorInfo()));
        }
        $stmt_user->execute([$userId]);
        $stripeCustomerId = null;
        if ($row_user = $stmt_user->fetch()) {
            $stripeCustomerId = $row_user['stripe_customer_id'];
            custom_log_subscription("stripe_customer_id encontrado para owner_user_id {" . $userId . "}: {$stripeCustomerId}");
        } else {
            custom_log_subscription("No se encontró el owner_user_id con ID: " . $userId . " en la tabla usuarios.");
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Usuario propietario de la agencia no encontrado.']);
            exit();
        }
        $stmt_user = null; // Liberar el statement
        // Obtener stripe_subscription_id. Puede venir de PlanFeatures o necesitar una consulta separada si PlanFeatures falló o no devolvió suscripción.
        $stripeSubscriptionId = $planDetailsFromDB['stripe_subscription_id'] ?? null;
        if (!$stripeSubscriptionId && $stripeCustomerId) {
             // Intentar obtener la suscripción más reciente de Stripe para este customer si no la tenemos de la BD local
            custom_log_subscription("No se obtuvo stripe_subscription_id de PlanFeatures, intentando buscar en Stripe para customer: {$stripeCustomerId}");
            // Este bloque es opcional y depende de si quieres rescatar suscripciones no sincronizadas.
            // Por ahora, si PlanFeatures no devuelve una suscripción, asumimos que no hay una activa relevante.
        }
        if (!$stripeSubscriptionId) {
            custom_log_subscription("No se encontró información de suscripción activa para el usuario ID: " . $userId);
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'No se encontró información de suscripción activa.']);
            exit();
        }
        custom_log_subscription("Datos de Stripe a obtener: Customer ID: " . $stripeCustomerId . ", Subscription ID: " . $stripeSubscriptionId);
        // Inicializar la API de Stripe
        \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
        // Obtener datos reales de la suscripción desde Stripe
        custom_log_subscription("Consultando a Stripe para obtener datos de la suscripción: {$stripeSubscriptionId}");
        $stripeSubscription = \Stripe\Subscription::retrieve($stripeSubscriptionId);
        // Obtener datos del cliente si es necesario (aunque ya deberíamos tener el ID)
        // $stripeCustomer = \Stripe\Customer::retrieve($stripeCustomerId);
        // Obtener el balance del cliente de Stripe - ESTA ES LA LÍNEA QUE DEVOLVÍA 0
        // $customer_balance_cents = 0;
        // if ($stripeCustomerId) {
        //     try {
        //         $stripeCustomer = \Stripe\Customer::retrieve($stripeCustomerId);
        //         $customer_balance_cents = $stripeCustomer->balance; // Es negativo si el cliente tiene crédito
        //         custom_log_subscription("Saldo del cliente {$stripeCustomerId} recuperado (directo del customer): {$customer_balance_cents} centavos.");
        //     } catch (Exception $e) {
        //         custom_log_subscription("Error al recuperar el saldo (directo) del cliente {$stripeCustomerId}: " . $e->getMessage() . ". Se asumirá saldo cero.");
        //     }
        // }
        // Obtener datos del plan/precio de Stripe (actual)
        $currentStripePrice = $stripeSubscription->items->data[0]->price;
        $currentStripeProduct = \Stripe\Product::retrieve($currentStripePrice->product);
        $rawPlanAmountCents = $currentStripePrice->unit_amount; // Precio bruto del plan actual en centavos
        // --- NUEVA LÓGICA: Obtener la próxima factura (upcoming invoice) para determinar el próximo cobro y el crédito aplicado ---
        $upcomingInvoice = null;
        $nextBillingAmountForResponse = $rawPlanAmountCents / 100; // Fallback al precio bruto
        $calculatedCustomerBalanceCents = 0; // Por defecto, si no podemos obtener la factura previa o no hay crédito.
        try {
            custom_log_subscription("Intentando obtener la factura previa (upcoming invoice) para la suscripción: {$stripeSubscriptionId} y cliente: {$stripeCustomerId}");
            $upcomingInvoiceParams = ['customer' => $stripeCustomerId, 'subscription' => $stripeSubscriptionId];
            // Si hay cambios pendientes en la suscripción que aún no se han facturado (ej. un downgrade reciente que generará crédito en la próxima factura)
            // Stripe puede necesitar que se especifiquen esos cambios para una vista previa precisa.
            // Para una suscripción estable, solo customer y subscription suelen ser suficientes.
            // Considerar pasar 'subscription_proration_behavior' => 'none' si solo queremos ver el próximo ciclo normal sin recalcular prorrateos
            $upcomingInvoice = \Stripe\Invoice::upcoming($upcomingInvoiceParams);
            if ($upcomingInvoice && isset($upcomingInvoice->total)) {
                custom_log_subscription("Factura previa (upcoming) obtenida. Total: {$upcomingInvoice->total}, Subtotal: {$upcomingInvoice->subtotal}, Starting Balance en Invoice: " . ($upcomingInvoice->starting_balance ?? 'N/A'));
                $nextBillingAmountForResponse = $upcomingInvoice->total / 100; // Este es el monto que Stripe realmente cobrará.
                // Calcular el crédito efectivo aplicado a esta factura previa
                // Si el total de la factura previa es menor que el precio bruto del plan, la diferencia es el crédito aplicado.
                if ($upcomingInvoice->total < $rawPlanAmountCents) {
                    $effectiveCreditAppliedCents = $rawPlanAmountCents - $upcomingInvoice->total;
                    $calculatedCustomerBalanceCents = -$effectiveCreditAppliedCents; // Negativo para indicar crédito
                    custom_log_subscription("Crédito efectivo calculado a partir de la factura previa: {$calculatedCustomerBalanceCents} centavos (Total factura: {$upcomingInvoice->total}, Precio bruto plan: {$rawPlanAmountCents})");
                } else {
                    // Si el total es igual o mayor, no se aplicó crédito (o hubo cargos adicionales, aunque es raro para una factura normal)
                    custom_log_subscription("No se detectó crédito efectivo en la factura previa (Total factura: {$upcomingInvoice->total}, Precio bruto plan: {$rawPlanAmountCents})");
                }
                 // Log adicional del objeto starting_balance y amount_due del upcoming invoice
                custom_log_subscription("Upcoming Invoice - amount_due: " . ($upcomingInvoice->amount_due ?? 'N/A') . ", amount_paid: " . ($upcomingInvoice->amount_paid ?? 'N/A') . ", amount_remaining: " . ($upcomingInvoice->amount_remaining ?? 'N/A'));
            } else {
                custom_log_subscription("No se pudo obtener la factura previa (upcoming invoice) o no tiene 'total'. Se usará el precio bruto del plan.");
            }
        } catch (Exception $e) {
            custom_log_subscription("Error al obtener la factura previa (upcoming invoice): " . $e->getMessage() . ". Se usará el precio bruto del plan y saldo cero.");
            // No es crítico si falla, se usarán los fallbacks definidos arriba.
        }
        // --- FIN NUEVA LÓGICA ---
        // Obtener datos del método de pago con lógica mejorada
        $paymentMethodInfo = null;
        $paymentMethodSource = null; // Para logging: 'subscription', 'customer', 'attached'

        // 1. Intentar obtener desde default_payment_method de la suscripción
        if (!empty($stripeSubscription->default_payment_method)) {
            $defaultPaymentMethodId = is_string($stripeSubscription->default_payment_method) ? $stripeSubscription->default_payment_method : $stripeSubscription->default_payment_method->id;
            custom_log_subscription("Consultando método de pago desde suscripción: {$defaultPaymentMethodId}");
            try {
                $paymentMethodStripe = \Stripe\PaymentMethod::retrieve($defaultPaymentMethodId);
                if ($paymentMethodStripe && $paymentMethodStripe->card) {
                    $paymentMethodInfo = [
                        'brand' => $paymentMethodStripe->card->brand,
                        'lastFourDigits' => $paymentMethodStripe->card->last4,
                        'expiryMonth' => $paymentMethodStripe->card->exp_month,
                        'expiryYear' => $paymentMethodStripe->card->exp_year
                    ];
                    $paymentMethodSource = 'subscription';
                }
            } catch (\Stripe\Exception\ApiErrorException $e) {
                custom_log_subscription("Error al obtener método de pago de suscripción: " . $e->getMessage());
            }
        }

        // 2. Fallback: Intentar desde invoice_settings del customer
        if (!$paymentMethodInfo) {
            // Necesitamos obtener el customer completo para acceder a invoice_settings
            try {
                $stripeCustomer = \Stripe\Customer::retrieve($stripeCustomerId);
                if (!empty($stripeCustomer->invoice_settings->default_payment_method)) {
                    $defaultPaymentMethodId = is_string($stripeCustomer->invoice_settings->default_payment_method) ? $stripeCustomer->invoice_settings->default_payment_method : $stripeCustomer->invoice_settings->default_payment_method->id;
                    custom_log_subscription("Consultando método de pago desde customer invoice_settings: {$defaultPaymentMethodId}");
                    $paymentMethodStripe = \Stripe\PaymentMethod::retrieve($defaultPaymentMethodId);
                    if ($paymentMethodStripe && $paymentMethodStripe->card) {
                        $paymentMethodInfo = [
                            'brand' => $paymentMethodStripe->card->brand,
                            'lastFourDigits' => $paymentMethodStripe->card->last4,
                            'expiryMonth' => $paymentMethodStripe->card->exp_month,
                            'expiryYear' => $paymentMethodStripe->card->exp_year
                        ];
                        $paymentMethodSource = 'customer';
                    }
                }
            } catch (\Stripe\Exception\ApiErrorException $e) {
                custom_log_subscription("Error al obtener customer o método de pago: " . $e->getMessage());
            }
        }

        // 3. Último fallback: Buscar cualquier método de pago adjunto al customer
        if (!$paymentMethodInfo) {
            try {
                custom_log_subscription("Buscando métodos de pago adjuntos al customer: {$stripeCustomerId}");
                $paymentMethods = \Stripe\PaymentMethod::all([
                    'customer' => $stripeCustomerId,
                    'type' => 'card',
                    'limit' => 1
                ]);

                if (!empty($paymentMethods->data)) {
                    $paymentMethodStripe = $paymentMethods->data[0];
                    if ($paymentMethodStripe && $paymentMethodStripe->card) {
                        $paymentMethodInfo = [
                            'brand' => $paymentMethodStripe->card->brand,
                            'lastFourDigits' => $paymentMethodStripe->card->last4,
                            'expiryMonth' => $paymentMethodStripe->card->exp_month,
                            'expiryYear' => $paymentMethodStripe->card->exp_year
                        ];
                        $paymentMethodSource = 'attached';
                        custom_log_subscription("Método de pago encontrado en métodos adjuntos: {$paymentMethodStripe->id}");
                    }
                }
            } catch (\Stripe\Exception\ApiErrorException $e) {
                custom_log_subscription("Error al buscar métodos de pago adjuntos: " . $e->getMessage());
            }
        }

        if ($paymentMethodInfo) {
            custom_log_subscription("Método de pago obtenido desde: {$paymentMethodSource}");
        } else {
            custom_log_subscription("No se encontró ningún método de pago válido para el customer: {$stripeCustomerId}");
        }
        // Formatear los datos del plan de Stripe para la respuesta
        $stripePlanData = [
            'id' => $currentStripePrice->id,
            'name' => $currentStripeProduct->name, // Nombre del producto en Stripe
            'amount' => $currentStripePrice->unit_amount / 100,
            'currency' => strtoupper($currentStripePrice->currency),
            'interval' => $currentStripePrice->recurring->interval,
            'interval_count' => $currentStripePrice->recurring->interval_count ?? 1
        ]; 
        // Construir la respuesta final
        $final_response_data = [
            'id' => $userId, // ID del propietario de la agencia, usado por el frontend como user_id
            'agency_id' => $agencyId,
            'is_agency_owner' => $isAgencyOwner, // Propietario que hizo la petición
            'user_data_response' => [ // Datos adicionales que el frontend puede usar
                'agency_name' => $agency_name_from_db,
                'email' => $planDetailsFromDB['user_email'] ?? null,
                'nombre_completo' => $planDetailsFromDB['user_nombre_completo'] ?? null,
                'stripe_customer_id' => $stripeCustomerId,
            ],
            // Campos de suscripción que el frontend espera en la raíz
            'status' => $stripeSubscription->status,
            'planId' => $planDetailsFromDB['plan_slug'] ?? $stripePlanData['id'], // slug del plan
            'planName' => $planDetailsFromDB['plan_name'] ?? $stripePlanData['name'],
            'price' => ($planDetailsFromDB['billing_cycle'] === 'monthly' ? $planDetailsFromDB['price_monthly'] : $planDetailsFromDB['price_annual']) ?? ($currentStripePrice->unit_amount / 100) . ' ' . strtoupper($currentStripePrice->currency),
            'billingCycle' => $planDetailsFromDB['billing_cycle'] ?? $currentStripePrice->recurring->interval,
            'currentPeriodStart' => date('Y-m-d\TH:i:s.v\Z', $stripeSubscription->current_period_start),
            'currentPeriodEnd' => date('Y-m-d\TH:i:s.v\Z', $stripeSubscription->current_period_end),
            'cancelAtPeriodEnd' => $stripeSubscription->cancel_at_period_end,
            'canceledAt' => $stripeSubscription->canceled_at ? date('Y-m-d\TH:i:s.v\Z', $stripeSubscription->canceled_at) : null,
            'paymentMethod' => $paymentMethodInfo,
            'paymentMethodSource' => $paymentMethodSource, // 'subscription', 'customer', 'attached', o null
            'hasPaymentMethod' => !is_null($paymentMethodInfo), // Boolean para facilitar checks en frontend
            'trialPeriod' => [
                'isInTrial' => ($stripeSubscription->trial_end && $stripeSubscription->trial_end > time()),
                'trialStart' => $stripeSubscription->trial_start ? date('Y-m-d\TH:i:s.v\Z', $stripeSubscription->trial_start) : null,
                'trialEnd' => $stripeSubscription->trial_end ? date('Y-m-d\TH:i:s.v\Z', $stripeSubscription->trial_end) : null,
                'daysRemaining' => call_user_func(function() use ($stripeSubscription) {
                    $daysRemainingTrial = 0;
                    if ($stripeSubscription->trial_end && $stripeSubscription->trial_end > time()) {
                        try {
                            $trialEndDateTime = new DateTime(date('Y-m-d H:i:s', $stripeSubscription->trial_end), new DateTimeZone('UTC'));
                            $nowDateTime = new DateTime('now', new DateTimeZone('UTC'));
                            $trialEndDayStart = (clone $trialEndDateTime)->setTime(0, 0, 0);
                            $nowDayStart = (clone $nowDateTime)->setTime(0, 0, 0);
                            if ($trialEndDateTime > $nowDateTime) {
                                $interval = $nowDayStart->diff($trialEndDayStart);
                                $daysRemainingTrial = $interval->days;
                            } else {
                                $daysRemainingTrial = 0;
                            }
                        } catch (Exception $e) {
                            custom_log_subscription("Error calculando daysRemaining: " . $e->getMessage());
                            $daysRemainingTrial = ($stripeSubscription->trial_end && $stripeSubscription->trial_end > time()) ? ceil(($stripeSubscription->trial_end - time()) / (60 * 60 * 24)) : 0;
                        }
                    }
                    return $daysRemainingTrial;
                })
            ],
            'nextBillingDate' => $stripeSubscription->current_period_end ? date('Y-m-d\TH:i:s.v\Z', $stripeSubscription->current_period_end) : null,
            'nextBillingAmount' => $nextBillingAmountForResponse . ' ' . strtoupper($currentStripePrice->currency),
            'startDate' => $stripeSubscription->start_date ? date('Y-m-d\TH:i:s.v\Z', $stripeSubscription->start_date) : null,
            'renewalStatus' => call_user_func(function() use ($stripeSubscription) {
                if ($stripeSubscription->trial_end && $stripeSubscription->trial_end > time()) return 'trial';
                if ($stripeSubscription->status === 'canceled' || $stripeSubscription->cancel_at_period_end) return 'canceled';
                if (in_array($stripeSubscription->status, ['unpaid', 'past_due'])) return 'pending';
                return 'active';
            }),
            // Información adicional para manejo de problemas de pago
            'subscriptionRecoverable' => call_user_func(function() use ($stripeSubscription, $paymentMethodInfo) {
                // Una suscripción es recuperable si:
                // 1. Está en estado past_due o unpaid Y tiene método de pago
                // 2. Está incomplete pero no ha expirado
                if (in_array($stripeSubscription->status, ['past_due', 'unpaid']) && $paymentMethodInfo) {
                    return true;
                }
                if ($stripeSubscription->status === 'incomplete' &&
                    (!$stripeSubscription->trial_end || $stripeSubscription->trial_end > time())) {
                    return true;
                }
                return false;
            }),
            'actionRequired' => call_user_func(function() use ($stripeSubscription, $paymentMethodInfo) {
                // Determinar qué acción se requiere del usuario
                if ($stripeSubscription->status === 'incomplete') {
                    return 'payment_authentication'; // Probablemente 3DS
                }
                if (in_array($stripeSubscription->status, ['past_due', 'unpaid'])) {
                    if ($paymentMethodInfo) {
                        return 'retry_payment'; // Reintentar con método existente
                    } else {
                        return 'update_payment_method'; // Agregar nuevo método
                    }
                }
                if ($stripeSubscription->status === 'canceled' && $paymentMethodInfo) {
                    return 'reactivate_subscription'; // Reactivar suscripción
                }
                return null; // No se requiere acción
            }),
            'customer_balance_cents' => $calculatedCustomerBalanceCents,
            'plan_features' => [ // Objeto plan_features en la raíz
                'id' => $planDetailsFromDB['plan_id'] ?? null, // ID del plan de tu BD
                'name' => $planDetailsFromDB['plan_name'] ?? null,
                'slug' => $planDetailsFromDB['plan_slug'] ?? null,
                'price_monthly' => $planDetailsFromDB['price_monthly'] ?? null,
                'price_annual' => $planDetailsFromDB['price_annual'] ?? null,
                'stripe_price_id_monthly' => $planDetailsFromDB['stripe_price_id_monthly'] ?? null,
                'stripe_price_id_annual' => $planDetailsFromDB['stripe_price_id_annual'] ?? null,
                'max_dashboard_users' => isset($planDetailsFromDB['max_dashboard_users']) ? (int)$planDetailsFromDB['max_dashboard_users'] : null,
                
                'allow_multiple_sequences' => isset($planDetailsFromDB['allow_multiple_sequences']) ? (bool)$planDetailsFromDB['allow_multiple_sequences'] : null,
                'allow_custom_domain_email' => isset($planDetailsFromDB['allow_custom_domain_email']) ? (bool)$planDetailsFromDB['allow_custom_domain_email'] : null,
                'max_valoradores' => isset($planDetailsFromDB['max_valoradores']) ? (int)$planDetailsFromDB['max_valoradores'] : null,
                'has_analytics' => isset($planDetailsFromDB['has_analytics']) ? (bool)$planDetailsFromDB['has_analytics'] : null,
                'additional_config' => $planDetailsFromDB['additional_config'] ?? null // Ya debería ser un array o null
            ],
        ];

        // Agregar estado detallado de la suscripción
        try {
            $statusService = new SubscriptionStatusService($pdo);
            $stripe_subscription_data = [
                'stripe_subscription_id' => $stripeSubscriptionId,
                'stripe_customer_id' => $stripeCustomerId,
                'status' => $stripeSubscription->status,
                'trial_end' => $stripeSubscription->trial_end,
                'cancel_at_period_end' => $stripeSubscription->cancel_at_period_end,
                'canceled_at' => $stripeSubscription->canceled_at
            ];

            $detailed_status = $statusService->getDetailedSubscriptionStatus($userId, $stripe_subscription_data);
            $final_response_data['detailedStatus'] = $detailed_status;

            custom_log_subscription("Estado detallado agregado: " . $detailed_status['detailed_status']);
        } catch (Exception $e) {
            custom_log_subscription("Error obteniendo estado detallado: " . $e->getMessage(), 'WARNING');
            // No fallar completamente si no se puede obtener el estado detallado
        }

        // Sobrescribir nextBillingAmount en la respuesta final con el valor de la factura previa
        $final_response_data['nextBillingAmount'] = $nextBillingAmountForResponse . ' ' . strtoupper($currentStripePrice->currency);
        custom_log_subscription("Datos completos a enviar: " . json_encode($final_response_data)); // Log para ver los datos completos
        // Limpiar cualquier salida accidental ANTES del json_encode final
        if (ob_get_length()) ob_clean(); 
        echo json_encode($final_response_data); 
    } catch (\Stripe\Exception\ApiErrorException $e) {
        custom_log_subscription("Error de API de Stripe: " . $e->getMessage() . " Status: " . $e->getHttpStatus());
        // Aquí podrías intentar un fallback a datos locales si la suscripción de Stripe no se puede recuperar
        // pero $planDetailsFromDB podría tener la info necesaria si la BD está sincronizada.
        ob_end_clean();
        http_response_code($e->getHttpStatus() ?: 500);
        echo json_encode(['success' => false, 'message' => 'Error al obtener datos de Stripe: ' . $e->getMessage()]);
        exit();
    } catch (Exception $e) {
        custom_log_subscription("EXCEPCIÓN general en GET: " . $e->getMessage());
        ob_end_clean();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error en el servidor: ' . $e->getMessage()]);
        exit();
    }
} else {
    custom_log_subscription("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}
?>
