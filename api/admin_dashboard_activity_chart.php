<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// api/admin_dashboard_activity_chart.php
// error_reporting(E_ALL); // Manejado por bootstrap.php
// ini_set('display_errors', 1); // Manejado por bootstrap.php
 // <--- INTEGRAR BOOTSTRAP
require_once __DIR__ . '/utils/auth_check.php';
// Use Firebase\JWT\JWT; // auth_check.php o bootstrap.php manejan esto
// Use Firebase\JWT\Key;
header('Content-Type: application/json');
// --- Carga de .env y autoload manejada por bootstrap.php ---
// try { $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); $dotenv->load(); } catch ... (eliminado)
// --- Constantes de BD y JWT_SECRET vienen de bootstrap.php ---
// $dbHost = $_ENV['DB_HOST'] ?? null; (eliminado)
// ... (eliminación de otras asignaciones de variables de entorno)
// auth_check.php ahora usará JWT_SECRET de bootstrap.php
$authResult = verifyTokenAndAdminRole(); // <--- SIN ARGUMENTOS
if (!$authResult['success']) {
    http_response_code($authResult['status_code']);
    echo json_encode(['success' => false, 'message' => $authResult['message']]);
    exit();
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en admin_dashboard_activity_chart: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

$period = $_GET['period'] ?? '30D'; // 7D, 30D, 90D, YTD
$date_format_group = '%Y-%m-%d'; // Agrupar por día por defecto
$interval_unit = 'DAY';
$interval_value = 0;
switch ($period) {
    case '7D':
        $interval_value = 7;
        break;
    case '30D':
        $interval_value = 30;
        break;
    case '90D':
        $interval_value = 90;
        break;
    case 'YTD': // Year to Date
        // Agrupar por semana para YTD si son muchos días, o por mes
        $startOfYear = date('Y-01-01 00:00:00');
        $today = date('Y-m-d H:i:s');
        $diff = date_diff(date_create($startOfYear), date_create($today));
        if ($diff->days > 90) { // Más de ~3 meses, agrupar por semana
            $date_format_group = '%Y-%u'; // Año-Semana
        }
        // Si es > 365, considerar agrupar por mes '%Y-%m'
        $interval_value = $diff->days; // Cubrir desde inicio de año hasta hoy
        break;
    default:
        $interval_value = 30;
        break;
}
$start_date = date('Y-m-d H:i:s', strtotime("-$interval_value $interval_unit"));
$activity_data = [];
$current_date = new DateTime(date('Y-m-d', strtotime("-$interval_value $interval_unit")));
$end_loop_date = new DateTime(date('Y-m-d'));
// Inicializar datos para el gráfico
while($current_date <= $end_loop_date){
    $formatted_date_key = $current_date->format('Y-m-d');
    if ($date_format_group === '%Y-%u') {
        $formatted_date_key = $current_date->format('Y-W'); // Semana del año
    }
    $activity_data[$formatted_date_key] = [
        'date' => $formatted_date_key,
        'newUsers' => 0,
        'newValoraciones' => 0,
        'newLeads' => 0
    ];
    $current_date->modify('+1 day');
}

try {
    // Nuevos Usuarios
    $stmt = $pdo->prepare("SELECT DATE_FORMAT(created_at, ?) as entry_date, COUNT(*) as count FROM usuarios WHERE created_at >= ? GROUP BY entry_date ORDER BY entry_date ASC");
    $stmt->execute([$date_format_group, $start_date]);
    while ($row = $stmt->fetch()) {
        if (isset($activity_data[$row['entry_date']])) {
            $activity_data[$row['entry_date']]['newUsers'] = (int)$row['count'];
        }
    }

    // Nuevas Valoraciones
    $stmt = $pdo->prepare("SELECT DATE_FORMAT(fecha_creacion, ?) as entry_date, COUNT(*) as count FROM valorador_valoraciones WHERE fecha_creacion >= ? GROUP BY entry_date ORDER BY entry_date ASC");
    $stmt->execute([$date_format_group, $start_date]);
    while ($row = $stmt->fetch()) {
        if (isset($activity_data[$row['entry_date']])) {
            $activity_data[$row['entry_date']]['newValoraciones'] = (int)$row['count'];
        }
    }

    // Nuevos Leads
    $stmt = $pdo->prepare("SELECT DATE_FORMAT(fecha_creacion, ?) as entry_date, COUNT(*) as count FROM valorador_leads WHERE fecha_creacion >= ? GROUP BY entry_date ORDER BY entry_date ASC");
    $stmt->execute([$date_format_group, $start_date]);
    while ($row = $stmt->fetch()) {
        if (isset($activity_data[$row['entry_date']])) {
            $activity_data[$row['entry_date']]['newLeads'] = (int)$row['count'];
        }
    }

    echo json_encode(['success' => true, 'data' => array_values($activity_data)]); // Devolver como array

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al ejecutar consultas para activity_chart: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error al procesar los datos de actividad.']);
    exit();
}
?>