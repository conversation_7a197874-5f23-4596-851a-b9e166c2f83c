<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// api/upload_logo.php
use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;
use Google\Cloud\Storage\StorageClient;
header('Content-Type: application/json');
$response = ['success' => false, 'error' => '', 'logoUrl' => ''];
// --- Autenticación y Autorización ---
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
$decoded_payload = null;
$user_agency_id = null;
if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    $token = $matches[1];
    if (!empty(JWT_SECRET)) {
        try {
            JWT::$leeway = 60; // Añadir leeway para flexibilidad en la validación del tiempo
            $decoded_payload = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
        } catch (ExpiredException $e) {
            error_log("JWT Expired en upload_logo: " . $e->getMessage());
            $response['error'] = 'Token de autenticación expirado. Por favor, inicia sesión de nuevo.';
            http_response_code(401);
            echo json_encode($response);
            exit();
        } catch (SignatureInvalidException $e) {
            error_log("JWT Signature Invalid en upload_logo: " . $e->getMessage());
            $response['error'] = 'Token de autenticación inválido (firma incorrecta).';
            http_response_code(401);
            echo json_encode($response);
            exit();
        } catch (BeforeValidException $e) {
            error_log("JWT Not Yet Valid en upload_logo: " . $e->getMessage());
            $response['error'] = 'Token de autenticación aún no es válido.';
            http_response_code(401);
            echo json_encode($response);
            exit();
        } catch (\Throwable $e) { // Captura genérica para otros errores de JWT o relacionados
            error_log("JWT Decode Error General en upload_logo: " . $e->getMessage());
            $response['error'] = 'Error al procesar el token de autenticación.';
            http_response_code(401);
            echo json_encode($response);
            exit();
        }
    } else {
        error_log("JWT_SECRET no está definido en upload_logo.php. Esto es un error de configuración del servidor.");
        $response['error'] = 'Error interno del servidor (configuración de autenticación).';
        http_response_code(500); 
        echo json_encode($response);
        exit();
    }
}
// Verificar si el payload fue decodificado y contiene agency_id
if (!$decoded_payload || !isset($decoded_payload->agency_id)) {
    if (!$authHeader) {
        $response['error'] = 'Autenticación requerida: No se proporcionó token.';
    } elseif (empty(JWT_SECRET)) {
        // Este caso ya se maneja arriba con error 500, pero por completitud.
        $response['error'] = 'Error de configuración del servidor (autenticación interna).';
    } elseif (!$decoded_payload && $authHeader && !empty(JWT_SECRET)) {
        // Si había token y secret, pero la decodificación falló y fue capturada por \Throwable,
        // ya se debería haber salido. Si llegamos aquí, es una condición inesperada post-error.
        // O el token no era Bearer.
        $response['error'] = 'Token de autenticación inválido o malformado.';
    } else { // $decoded_payload es válido, pero no tiene agency_id
        error_log("Token decodificado en upload_logo no contiene agency_id. Payload: " . json_encode($decoded_payload));
        $response['error'] = 'Token de autenticación válido, pero no contiene la identificación de agencia necesaria.';
    }
    http_response_code(401);
    echo json_encode($response);
    exit();
}
$user_agency_id = $decoded_payload->agency_id;
// --- FIN Autenticación y Autorización ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['error'] = 'Método no permitido. Solo se aceptan solicitudes POST.';
    http_response_code(405);
    echo json_encode($response);
    exit();
}
// Validar que el client_identifier fue enviado
if (!isset($_POST['client_identifier']) || empty(trim($_POST['client_identifier']))) {
    $response['error'] = 'Identificador del cliente no proporcionado.';
    http_response_code(400);
    echo json_encode($response);
    exit();
}
$clientIdentifier = basename(trim($_POST['client_identifier'])); // Limpiar un poco
// Verificar si se está procesando una URL o un archivo subido
$isUrlMode = isset($_POST['logo_url']) && !empty($_POST['logo_url']);
$isFileMode = isset($_FILES['agencyLogoFile']) && $_FILES['agencyLogoFile']['error'] === UPLOAD_ERR_OK;

// Si no hay ni URL ni archivo, mostrar error
if (!$isUrlMode && !$isFileMode) {
    $errorMessage = 'No se recibió ningún archivo ni URL para procesar.';
    if (isset($_FILES['agencyLogoFile']['error']) && $_FILES['agencyLogoFile']['error'] !== UPLOAD_ERR_OK) {
        switch ($_FILES['agencyLogoFile']['error']) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errorMessage = "El archivo excede el tamaño máximo permitido.";
                break;
            case UPLOAD_ERR_PARTIAL:
                $errorMessage = "El archivo fue solo parcialmente subido.";
                break;
            case UPLOAD_ERR_NO_FILE:
                $errorMessage = "No se subió ningún archivo.";
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $errorMessage = "Falta la carpeta temporal.";
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $errorMessage = "No se pudo escribir el archivo en el disco.";
                break;
            case UPLOAD_ERR_EXTENSION:
                $errorMessage = "Una extensión de PHP detuvo la subida del archivo.";
                break;
            default:
                $errorMessage = "Error desconocido durante la subida del archivo.";
                break;
        }
    }
    $response['error'] = $errorMessage;
    http_response_code(400);
    echo json_encode($response);
    exit();
}

// Variables para almacenar información del archivo
$fileMimeType = null;
$fileContent = null;
$originalFileName = null;
$tempFilePath = null;

// Procesar según el modo (URL o archivo)
if ($isUrlMode) {
    $logoUrl = $_POST['logo_url'];
    
    // Verificar si la URL ya es del bucket de Google Cloud Storage
    $bucketUrl = $_ENV['LOGOS_BASE_URL'] ?? 'https://storage.googleapis.com/inmoautomation-logos';
    if (strpos($logoUrl, $bucketUrl) === 0) {
        // La URL ya es de nuestro bucket, no necesitamos procesarla
        $response['success'] = true;
        $response['logoUrl'] = $logoUrl;
        echo json_encode($response);
        exit();
    }
    
    // Validar URL
    if (!filter_var($logoUrl, FILTER_VALIDATE_URL)) {
        $response['error'] = 'La URL proporcionada no es válida.';
        http_response_code(400);
        echo json_encode($response);
        exit();
    }
    
    // Intentar descargar la imagen
    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10, // Timeout en segundos
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]
        ]);
        
        $fileContent = @file_get_contents($logoUrl, false, $context);
        
        if ($fileContent === false) {
            throw new Exception('No se pudo descargar la imagen desde la URL proporcionada.');
        }
        
        // Crear un archivo temporal para la imagen descargada
        $tempFilePath = tempnam(sys_get_temp_dir(), 'logo_');
        if ($tempFilePath === false) {
            throw new Exception('No se pudo crear un archivo temporal para la imagen.');
        }
        
        if (file_put_contents($tempFilePath, $fileContent) === false) {
            throw new Exception('No se pudo guardar la imagen en el archivo temporal.');
        }
        
        // Obtener el tipo MIME del archivo descargado
        $fileMimeType = mime_content_type($tempFilePath);
        
        // Extraer el nombre del archivo de la URL
        $urlPath = parse_url($logoUrl, PHP_URL_PATH);
        $originalFileName = pathinfo($urlPath, PATHINFO_BASENAME);
        if (empty($originalFileName)) {
            $originalFileName = 'logo_' . uniqid();
        }
    } catch (Exception $e) {
        if ($tempFilePath && file_exists($tempFilePath)) {
            @unlink($tempFilePath);
        }
        $response['error'] = 'Error al procesar la URL de la imagen: ' . $e->getMessage();
        error_log('Error procesando URL de imagen: ' . $e->getMessage());
        http_response_code(400);
        echo json_encode($response);
        exit();
    }
} else {
    // Modo archivo (subida tradicional)
    $file = $_FILES['agencyLogoFile'];
    $fileMimeType = mime_content_type($file['tmp_name']);
    $originalFileName = $file['name'];
    $tempFilePath = $file['tmp_name'];
    $fileContent = file_get_contents($tempFilePath);
}
// --- Validaciones del archivo ---
$allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'];
if (!in_array($fileMimeType, $allowedMimeTypes)) {
    if ($isUrlMode && $tempFilePath && file_exists($tempFilePath)) {
        @unlink($tempFilePath);
    }
    $response['error'] = 'Tipo de archivo no permitido. Solo se permiten PNG, JPG, GIF, SVG o WebP.';
    http_response_code(400);
    echo json_encode($response);
    exit();
}

// Validar tamaño del archivo
$maxFileSize = 5 * 1024 * 1024; // 5 MB
$fileSize = $isFileMode ? $file['size'] : strlen($fileContent);
if ($fileSize > $maxFileSize) {
    if ($isUrlMode && $tempFilePath && file_exists($tempFilePath)) {
        @unlink($tempFilePath);
    }
    $response['error'] = 'El archivo es demasiado grande. Máximo 5 MB.';
    http_response_code(400);
    echo json_encode($response);
    exit();
}
// --- Configuración de Cloud Storage ---
$bucketName = $_ENV['GOOGLE_CLOUD_STORAGE_BUCKET'] ?? 'inmoautomation-logos';
$logosBaseUrl = $_ENV['LOGOS_BASE_URL'] ?? 'https://storage.googleapis.com/inmoautomation-logos';

// --- Verificación de Pertenencia del Client Identifier a la Agencia ---
// Se comenta este bloque porque al crear un valorador por primera vez, el registro
// en la tabla 'clientes_valorador' aún no existe, lo que causa un error de "Acceso denegado".
// La autenticación por token JWT ya asegura que la petición es realizada por un usuario autenticado de una agencia.
/*
try {
    $pdo = Database::getInstance()->getConnection();
    $stmt_check_client = $pdo->prepare("SELECT COUNT(*) FROM clientes_valorador WHERE client_identifier = :client_id AND agency_id = :agency_id");
    $stmt_check_client->bindParam(':client_id', $clientIdentifier, PDO::PARAM_STR);
    $stmt_check_client->bindParam(':agency_id', $user_agency_id, PDO::PARAM_INT);
    $stmt_check_client->execute();
    if ($stmt_check_client->fetchColumn() == 0) {
        $response['error'] = 'Acceso denegado: El identificador del cliente no pertenece a tu agencia.';
        http_response_code(403);
        echo json_encode($response);
        exit();
    }
} catch (PDOException $e) {
    error_log("Error de BD verificando client_identifier en upload_logo: " . $e->getMessage());
    $response['error'] = 'Error interno del servidor al verificar permisos.';
    http_response_code(500);
    echo json_encode($response);
    exit();
}
*/
// Generar un nombre de archivo único
$fileExtension = strtolower(pathinfo($originalFileName, PATHINFO_EXTENSION));
// Si no se pudo determinar la extensión del archivo desde el nombre, intentar obtenerla del MIME type
if (empty($fileExtension)) {
    switch ($fileMimeType) {
        case 'image/jpeg':
            $fileExtension = 'jpg';
            break;
        case 'image/png':
            $fileExtension = 'png';
            break;
        case 'image/gif':
            $fileExtension = 'gif';
            break;
        case 'image/svg+xml':
            $fileExtension = 'svg';
            break;
        case 'image/webp':
            $fileExtension = 'webp';
            break;
        default:
            $fileExtension = 'jpg'; // Default fallback
    }
}
$safeFileName = preg_replace("/[^a-zA-Z0-9_.-]/", "", pathinfo($originalFileName, PATHINFO_FILENAME));
if (empty($safeFileName)) {
    $safeFileName = 'logo';
}
$uniqueFileName = $safeFileName . '_' . uniqid() . '.' . $fileExtension;

// Ruta en Cloud Storage: logos/{client_identifier}/{filename}
$cloudStoragePath = 'logos/' . $clientIdentifier . '/' . $uniqueFileName;

try {
    // Inicializar cliente de Cloud Storage
    $storage = new StorageClient([
        'projectId' => $_ENV['GOOGLE_CLOUD_PROJECT_ID'] ?? 'inmoautomation'
    ]);

    $bucket = $storage->bucket($bucketName);

    // Subir archivo a Cloud Storage
    $object = $bucket->upload($fileContent, [
        'name' => $cloudStoragePath,
        'metadata' => [
            'contentType' => $fileMimeType,
            'cacheControl' => 'public, max-age=31536000', // Cache por 1 año
        ]
    ]);

    $response['success'] = true;
    $response['logoUrl'] = $logosBaseUrl . '/' . $cloudStoragePath;

    error_log("Logo subido exitosamente a Cloud Storage: " . $response['logoUrl']);
    http_response_code(200);

} catch (Exception $e) {
    $response['error'] = 'Error interno al subir el archivo: ' . $e->getMessage();
    error_log('Error subiendo a Cloud Storage: ' . $e->getMessage());
    http_response_code(500);
} finally {
    // Limpiar archivo temporal si se creó uno desde URL
    if ($isUrlMode && $tempFilePath && file_exists($tempFilePath)) {
        @unlink($tempFilePath);
    }
}
echo json_encode($response);
?> 