<?php
declare(strict_types=1);

// Endpoint de diagnóstico para subscription.php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;

// Función de logging específica
function debug_log($message) {
    $logFile = __DIR__ . '/debug_subscription_response.log';
    $timestamp = date('Y-m-d H:i:s');
    @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
}

debug_log("=== INICIO DEBUG SUBSCRIPTION RESPONSE ===");

// Verificar método
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    debug_log("Método incorrecto: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

try {
    debug_log("Intentando conectar a BD...");
    $pdo = Database::getInstance()->getConnection();
    debug_log("Conexión BD exitosa");
    
    // Buscar un usuario con suscripción activa
    $stmt = $pdo->query("
        SELECT u.id as user_id, u.email, a.id as agency_id, a.name as agency_name, 
               s.estado, s.stripe_subscription_id
        FROM usuarios u 
        JOIN agencies a ON u.id = a.owner_user_id 
        JOIN suscripciones s ON u.id = s.user_id 
        WHERE s.estado IN ('active', 'trialing', 'past_due', 'unpaid')
        ORDER BY s.fecha_creacion DESC 
        LIMIT 1
    ");
    
    $user_data = $stmt->fetch();
    
    if (!$user_data) {
        debug_log("No se encontró usuario con suscripción activa");
        echo json_encode(['error' => 'No hay usuarios con suscripción activa']);
        exit;
    }
    
    debug_log("Usuario encontrado: " . $user_data['email']);
    
    // Cargar PlanFeatures
    require_once __DIR__ . '/utils/PlanFeatures.php';
    debug_log("PlanFeatures cargado");
    
    $planDetails = \Api\Utils\PlanFeatures::getUserActivePlanDetails($user_data['user_id'], $pdo);
    
    if (!$planDetails) {
        debug_log("No se encontraron detalles del plan");
        echo json_encode(['error' => 'No se encontraron detalles del plan']);
        exit;
    }
    
    debug_log("Plan encontrado: " . $planDetails['plan_name']);
    
    // Construir respuesta simplificada
    $response = [
        'debug' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'user_id' => $user_data['user_id'],
        'agency_id' => $user_data['agency_id'],
        'email' => $user_data['email'],
        'agency_name' => $user_data['agency_name'],
        'subscription_status' => $planDetails['subscription_status'],
        'plan_name' => $planDetails['plan_name'],
        'stripe_subscription_id' => $planDetails['stripe_subscription_id'],
        'connection_info' => [
            'db_host' => defined('DB_HOST') ? DB_HOST : 'undefined',
            'db_socket' => defined('DB_SOCKET') ? DB_SOCKET : 'undefined',
            'is_production' => getenv('K_SERVICE') !== false
        ]
    ];
    
    debug_log("Respuesta construida, tamaño: " . strlen(json_encode($response)) . " bytes");
    
    // Limpiar cualquier output buffer
    if (ob_get_level()) {
        ob_clean();
    }
    
    // Establecer headers
    header('Content-Type: application/json');
    
    // Enviar respuesta
    $json_response = json_encode($response);
    debug_log("JSON generado exitosamente");
    
    echo $json_response;
    debug_log("Respuesta enviada");
    
} catch (Exception $e) {
    debug_log("ERROR: " . $e->getMessage());
    debug_log("Archivo: " . $e->getFile());
    debug_log("Línea: " . $e->getLine());
    
    if (ob_get_level()) {
        ob_clean();
    }
    
    http_response_code(500);
    echo json_encode(['error' => 'Error interno: ' . $e->getMessage()]);
}

debug_log("=== FIN DEBUG SUBSCRIPTION RESPONSE ===");
?>
