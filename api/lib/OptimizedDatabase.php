<?php
// api/lib/OptimizedDatabase.php
namespace Api\lib;

use PDO;
use PDOException;

/**
 * Clase optimizada para conexiones externas a MySQL
 * Implementa connection pooling, retry logic y optimizaciones de latencia
 */
class OptimizedDatabase {
    private static ?self $instance = null;
    private PDO $pdo;
    private int $retryAttempts = 3;
    private int $retryDelay = 100; // milliseconds
    
    private function __construct() {
        if (!defined('DB_HOST')) {
            throw new PDOException('Configuración de base de datos no cargada.');
        }
        
        $this->connect();
    }
    
    private function connect(): void {
        $dsn = 'mysql:host=' . DB_HOST . ';port=' . DB_PORT . ';dbname=' . DB_DATABASE . ';charset=' . DB_CHARSET;
        
        $options = [
            // Configuraciones básicas
            PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES   => false,
            PDO::ATTR_STRINGIFY_FETCHES  => false,
            
            // Optimizaciones para conexiones externas
            PDO::ATTR_PERSISTENT         => true,  // Conexiones persistentes críticas
            PDO::ATTR_TIMEOUT            => 3,     // Timeout agresivo
            PDO::MYSQL_ATTR_COMPRESS     => true,  // Compresión para reducir latencia
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            
            // Configuraciones de red optimizadas
            PDO::MYSQL_ATTR_INIT_COMMAND => implode('; ', [
                "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'",
                "SET SESSION net_read_timeout=30",
                "SET SESSION net_write_timeout=30",
                "SET SESSION wait_timeout=300",
                "SET SESSION interactive_timeout=300"
            ]),
        ];
        
        $lastException = null;
        
        // Retry logic para conexiones externas inestables
        for ($attempt = 1; $attempt <= $this->retryAttempts; $attempt++) {
            try {
                $this->pdo = new PDO($dsn, DB_USERNAME, DB_PASSWORD, $options);

                // Configurar zona horaria de MySQL para que coincida con PHP
                // Usar offset +02:00 para Madrid (UTC+2 en horario de verano)
                $this->pdo->exec("SET time_zone = '+02:00'");

                // Verificar que la conexión esté realmente activa
                $this->pdo->query('SELECT 1');

                error_log("OptimizedDatabase: Conexión exitosa en intento $attempt");
                return;
                
            } catch (PDOException $e) {
                $lastException = $e;
                error_log("OptimizedDatabase: Intento $attempt falló: " . $e->getMessage());
                
                if ($attempt < $this->retryAttempts) {
                    usleep($this->retryDelay * 1000 * $attempt); // Backoff exponencial
                }
            }
        }
        
        error_log("OptimizedDatabase: Todos los intentos de conexión fallaron");
        throw new PDOException("Error de conexión a la base de datos después de {$this->retryAttempts} intentos.", 
                               (int)$lastException->getCode());
    }
    
    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection(): PDO {
        // Verificar si la conexión sigue activa
        try {
            $this->pdo->query('SELECT 1');
        } catch (PDOException $e) {
            error_log("OptimizedDatabase: Conexión perdida, reconectando...");
            $this->connect();
        }
        
        return $this->pdo;
    }
    
    public function query(string $sql, array $params = []): \PDOStatement {
        $lastException = null;
        
        // Retry logic para consultas
        for ($attempt = 1; $attempt <= $this->retryAttempts; $attempt++) {
            try {
                $pdo = $this->getConnection();
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                return $stmt;
                
            } catch (PDOException $e) {
                $lastException = $e;
                error_log("OptimizedDatabase: Query intento $attempt falló: " . $e->getMessage());
                
                // Si es un error de conexión, intentar reconectar
                if ($this->isConnectionError($e) && $attempt < $this->retryAttempts) {
                    $this->connect();
                    usleep($this->retryDelay * 1000);
                } else if ($attempt >= $this->retryAttempts) {
                    break;
                }
            }
        }
        
        error_log("OptimizedDatabase: Query falló después de {$this->retryAttempts} intentos: " . $sql);
        throw $lastException;
    }
    
    private function isConnectionError(PDOException $e): bool {
        $connectionErrors = [
            2006, // MySQL server has gone away
            2013, // Lost connection to MySQL server during query
            2003, // Can't connect to MySQL server
            1053, // Server shutdown in progress
        ];
        
        return in_array($e->getCode(), $connectionErrors);
    }
    
    public function beginTransaction(): bool {
        return $this->getConnection()->beginTransaction();
    }
    
    public function commit(): bool {
        return $this->getConnection()->commit();
    }
    
    public function rollback(): bool {
        return $this->getConnection()->rollback();
    }
    
    public function lastInsertId() {
        return $this->getConnection()->lastInsertId();
    }
    
    /**
     * Método para obtener estadísticas de conexión
     */
    public function getConnectionStats(): array {
        try {
            $stmt = $this->query("SHOW STATUS LIKE 'Threads_connected'");
            $threadsConnected = $stmt->fetch()['Value'] ?? 'unknown';
            
            $stmt = $this->query("SHOW STATUS LIKE 'Aborted_connects'");
            $abortedConnects = $stmt->fetch()['Value'] ?? 'unknown';
            
            return [
                'threads_connected' => $threadsConnected,
                'aborted_connects' => $abortedConnects,
                'persistent_enabled' => 'true'
            ];
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
