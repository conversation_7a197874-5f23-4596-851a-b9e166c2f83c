<?php
declare(strict_types=1);

namespace Api\lib;

class OpenAIService
{
    private string $apiKey;
    private string $endpoint = "https://api.openai.com/v1/chat/completions";

    public function __construct(string $apiKey)
    {
        $this->apiKey = $apiKey;
    }

    public function generateEmailContent(string $promptTemplate, array $leadData, array $propertyData, array $agencyData, string $subjectReference = ''): array
    {
        // 1. Reemplazar placeholders y construir el prompt final
        $userPrompt = $this->replacePlaceholders($promptTemplate, $leadData, $propertyData, $agencyData);

        // 2. Agregar instrucciones específicas para asunto hiperpersonalizado
        $subjectInstruction = $subjectReference ?
            "\n\nTema del email: {$subjectReference}. Genera un asunto hiperpersonalizado y único (máximo 60 caracteres) que sea específico para este lead y su situación." :
            "\n\nGenera un asunto hiperpersonalizado y único (máximo 60 caracteres) que sea específico para este lead y su situación.";

        $finalPrompt = $userPrompt . $subjectInstruction;

        // 2. Preparar el payload para la API de OpenAI con modo JSON
        $messages = [
            ['role' => 'system', 'content' => 'Eres un asistente de marketing inmobiliario experto. Tu tarea es redactar un email persuasivo y personalizado. Debes responder SIEMPRE con un objeto JSON válido, sin texto adicional antes o después. El JSON debe tener dos claves: "subject" para el asunto del email y "body" para el contenido HTML del email.'],
            ['role' => 'user', 'content' => $finalPrompt]
        ];

        $payload = [
            'model' => 'gpt-4o-mini', // Modelo más moderno y económico
            'messages' => $messages,
            'temperature' => 0.7,
            'max_tokens' => 1500, // Aumentamos por si el HTML es largo
            'response_format' => ['type' => 'json_object'],
        ];

        // 3. Realizar la llamada a la API de OpenAI
        $ch = curl_init($this->endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ]);

        $rawResponse = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        // 4. Manejar la respuesta de la API
        if ($rawResponse === false) {
            $errorMessage = "cURL Error: " . $curlError;
            Logger::error("OpenAIService " . $errorMessage);
            return ['success' => false, 'error' => $errorMessage, 'prompt' => $finalPrompt, 'raw_response' => null, 'subject' => null, 'body' => null];
        }

        if ($httpCode !== 200) {
            $errorMessage = "API Error (HTTP {$httpCode}): " . $rawResponse;
            Logger::error("OpenAIService " . $errorMessage);
            return ['success' => false, 'error' => $errorMessage, 'prompt' => $finalPrompt, 'raw_response' => $rawResponse, 'subject' => null, 'body' => null];
        }

        $responseData = json_decode($rawResponse, true);
        $contentJson = $responseData['choices'][0]['message']['content'] ?? null;

        if (empty($contentJson)) {
            $errorMessage = "Respuesta inesperada de la API, no se encontró contenido.";
            Logger::error("OpenAIService: " . $errorMessage . " Raw: " . $rawResponse);
            return ['success' => false, 'error' => $errorMessage, 'prompt' => $finalPrompt, 'raw_response' => $rawResponse, 'subject' => null, 'body' => null];
        }

        $emailData = json_decode($contentJson, true);

        if (json_last_error() !== JSON_ERROR_NONE || !isset($emailData['subject']) || !isset($emailData['body'])) {
            $errorMessage = "La respuesta de la IA no es un JSON válido o le faltan las claves 'subject' o 'body'.";
            Logger::error("OpenAIService: " . $errorMessage . " Contenido recibido: " . $contentJson);
            return ['success' => false, 'error' => $errorMessage, 'prompt' => $finalPrompt, 'raw_response' => $rawResponse, 'subject' => null, 'body' => null];
        }

        // Éxito: devolver el array estructurado
        return [
            'success' => true,
            'subject' => $emailData['subject'],
            'body' => $emailData['body'],
            'prompt' => $finalPrompt,
            'raw_response' => $rawResponse,
            'error' => null
        ];
    }

    private function replacePlaceholders(string $template, array $leadData, array $propertyData, array $agencyData = []): string
    {
        $replacements = [];

        // Placeholders de Lead Data
        $replacements['{{lead_name}}'] = $leadData['nombre'] ?? 'cliente';
        $replacements['{{lead_email}}'] = $leadData['email'] ?? '';
        $replacements['{{lead_phone}}'] = $leadData['telefono'] ?? '';
        $replacements['{{lead_necesidad}}'] = $leadData['necesidad'] ?? '';

        // Placeholders de Property Data (de valorador_valoraciones)
        $replacements['{{property_address}}'] = $propertyData['direccion'] ?? 'tu propiedad';
        $replacements['{{property_value_min}}'] = number_format((float)($propertyData['valor_estimado_min'] ?? 0), 0, ',', '.');
        $replacements['{{property_value_max}}'] = number_format((float)($propertyData['valor_estimado_max'] ?? 0), 0, ',', '.');
        $replacements['{{property_type}}'] = $propertyData['tipo_principal'] ?? '';
        $replacements['{{property_surface}}'] = $propertyData['superficie'] ?? '';
        $replacements['{{property_rooms}}'] = $propertyData['habitaciones'] ?? '';
        $replacements['{{property_bathrooms}}'] = $propertyData['banos'] ?? '';
        $replacements['{{property_year}}'] = $propertyData['ano_construccion_catastro'] ?? '';

        // Placeholders de Zone Data (de valorador_valoraciones)
        $replacements['{{zone_avg_price_m2}}'] = number_format((float)($propertyData['precio_m2_promedio'] ?? 0), 0, ',', '.');
        $replacements['{{zone_avg_size}}'] = number_format((float)($propertyData['tamano_promedio'] ?? 0), 0, ',', '.');

        // Placeholders de Agency Data
        $replacements['{{agency_name}}'] = $agencyData['nombre'] ?? 'nuestra agencia';
        $replacements['{{agent_name}}'] = $agencyData['agente_nombre'] ?? 'tu consultor inmobiliario';
        $replacements['{{agent_phone}}'] = $agencyData['agente_telefono'] ?? '';
        $replacements['{{agent_email}}'] = $agencyData['agente_email'] ?? '';

        // Placeholders de fechas
        $replacements['{{current_date}}'] = date('d/m/Y');
        $replacements['{{year}}'] = date('Y');
        $replacements['{{month_name}}'] = date('F');

        return str_replace(array_keys($replacements), array_values($replacements), $template);
    }

    public function improveEmailContent(string $currentSubject, string $currentBody, string $instructions): array
    {
        // 1. Construir el prompt para la mejora
        $prompt = <<<PROMPT
Actúa como un experto en copywriting y marketing inmobiliario. A continuación, te proporciono el asunto y el cuerpo de un email. Tu tarea es reescribirlo siguiendo las instrucciones específicas para mejorarlo. Debes devolver SIEMPRE un objeto JSON válido con las claves "subject" y "body".

**Email Actual:**

*   **Asunto:** {$currentSubject}
*   **Cuerpo (HTML):**
    ```html
    {$currentBody}
    ```

**Instrucciones de Mejora:**
"{$instructions}"

Por favor, genera la nueva versión del email.
PROMPT;

        // 2. Preparar el payload para la API de OpenAI
        $messages = [
            ['role' => 'system', 'content' => 'Eres un asistente experto en copywriting que responde siempre con un objeto JSON válido con las claves "subject" y "body".'],
            ['role' => 'user', 'content' => $prompt]
        ];

        $payload = [
            'model' => 'gpt-4o', // Usamos un modelo más avanzado para tareas de refinamiento
            'messages' => $messages,
            'temperature' => 0.6,
            'max_tokens' => 2000,
            'response_format' => ['type' => 'json_object'],
        ];

        // 3. Realizar la llamada a la API
        $ch = curl_init($this->endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
            'User-Agent: InmoAutomation-EmailSequence/1.0'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $rawResponse = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        // 4. Manejar la respuesta
        if ($rawResponse === false) {
            $errorMessage = "cURL Error: " . $curlError;
            Logger::error("OpenAIService (improve): " . $errorMessage);
            return ['success' => false, 'error' => $errorMessage, 'subject' => null, 'body' => null];
        }

        if ($httpCode !== 200) {
            $errorMessage = "API Error (HTTP {$httpCode}): " . $rawResponse;
            Logger::error("OpenAIService (improve): " . $errorMessage);
            return ['success' => false, 'error' => $errorMessage, 'subject' => null, 'body' => null];
        }

        $responseData = json_decode($rawResponse, true);
        $contentJson = $responseData['choices'][0]['message']['content'] ?? null;

        if (empty($contentJson)) {
            $errorMessage = "Respuesta inesperada de la API, no se encontró contenido.";
            Logger::error("OpenAIService (improve): " . $errorMessage . " Raw: " . $rawResponse);
            return ['success' => false, 'error' => $errorMessage, 'subject' => null, 'body' => null];
        }

        $emailData = json_decode($contentJson, true);

        if (json_last_error() !== JSON_ERROR_NONE || !isset($emailData['subject']) || !isset($emailData['body'])) {
            $errorMessage = "La respuesta de la IA no es un JSON válido o le faltan las claves 'subject' o 'body'.";
            Logger::error("OpenAIService (improve): " . $errorMessage . " Contenido recibido: " . $contentJson);
            return ['success' => false, 'error' => $errorMessage, 'subject' => null, 'body' => null];
        }

        return [
            'success' => true,
            'subject' => $emailData['subject'],
            'body' => $emailData['body'],
            'error' => null
        ];
    }
}