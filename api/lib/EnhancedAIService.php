<?php
declare(strict_types=1);

namespace Api\lib;

class EnhancedAIService
{
    private string $openaiApiKey;
    private string $geminiApiKey;
    private string $openaiEndpoint = "https://api.openai.com/v1/chat/completions";
    
    public function __construct()
    {
        $this->openaiApiKey = defined('OPENAI_API_KEY') ? OPENAI_API_KEY : '';
        $this->geminiApiKey = defined('GEMINI_API_KEY') ? GEMINI_API_KEY : '';
    }
    
    /**
     * Genera contenido de email usando OpenAI gpt-4o-mini
     */
    public function generateWithOpenAI(
        string $promptTemplate,
        array $leadData,
        array $propertyData,
        array $agencyData,
        string $baseSubject,
        bool $useWebSearch = false
    ): array {
        if (empty($this->openaiApiKey)) {
            return [
                'success' => false,
                'error' => 'OpenAI API key no configurada',
                'subject' => '',
                'body' => ''
            ];
        }
        
        try {
            // 1. Reemplazar placeholders y construir el prompt final
            $userPrompt = $this->replacePlaceholders($promptTemplate, $leadData, $propertyData, $agencyData);
            
            // 2. Agregar instrucciones específicas para asunto hiperpersonalizado
            $subjectInstruction = $baseSubject ?
                "\n\nTema del email: {$baseSubject}. Genera un asunto hiperpersonalizado y único (máximo 60 caracteres) que sea específico para este lead y su situación." :
                "\n\nGenera un asunto hiperpersonalizado y único (máximo 60 caracteres) que sea específico para este lead y su situación.";
            
            $finalPrompt = $userPrompt . $subjectInstruction;
            
            // 3. Preparar mensajes
            $systemPrompt = $this->buildSystemPrompt($useWebSearch);
            $messages = [
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $finalPrompt]
            ];
            
            // 4. Configurar payload
            $payload = [
                'model' => 'gpt-4o-mini',
                'messages' => $messages,
                'temperature' => 0.7,
                'max_tokens' => 1500,
                'response_format' => ['type' => 'json_object']
            ];
            
            // 5. Realizar llamada a OpenAI
            $response = $this->makeOpenAIRequest($payload);
            
            if (!$response['success']) {
                return [
                    'success' => false,
                    'error' => $response['error'],
                    'subject' => '',
                    'body' => ''
                ];
            }
            
            // 6. Procesar respuesta JSON
            $jsonResponse = json_decode($response['content'], true);
            if (!$jsonResponse || !isset($jsonResponse['subject']) || !isset($jsonResponse['body'])) {
                return [
                    'success' => false,
                    'error' => 'Respuesta JSON inválida de OpenAI',
                    'subject' => '',
                    'body' => ''
                ];
            }
            
            return [
                'success' => true,
                'subject' => $jsonResponse['subject'],
                'body' => $jsonResponse['body'],
                'model_used' => 'OpenAI GPT-4o-mini',
                'web_search_used' => $useWebSearch,
                'search_query_used' => $useWebSearch ? 'Búsqueda automática activada' : '',
                'error' => null
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Error OpenAI: ' . $e->getMessage(),
                'subject' => '',
                'body' => ''
            ];
        }
    }

    /**
     * Genera contenido de email usando Gemini 2.5 Flash
     */
    public function generateWithGemini(
        string $promptTemplate,
        array $leadData,
        array $propertyData,
        array $agencyData,
        string $baseSubject,
        bool $useWebSearch = false
    ): array {
        if (empty($this->geminiApiKey)) {
            return [
                'success' => false,
                'error' => 'Gemini API key no configurada',
                'subject' => '',
                'body' => ''
            ];
        }

        try {
            // 1. Reemplazar placeholders y construir el prompt final
            $userPrompt = $this->replacePlaceholders($promptTemplate, $leadData, $propertyData, $agencyData);

            // 2. Agregar instrucciones específicas para asunto hiperpersonalizado
            $subjectInstruction = $baseSubject ?
                "\n\nTema del email: {$baseSubject}. Genera un asunto hiperpersonalizado y único (máximo 60 caracteres) que sea específico para este lead y su situación." :
                "\n\nGenera un asunto hiperpersonalizado y único (máximo 60 caracteres) que sea específico para este lead y su situación.";

            $finalPrompt = $userPrompt . $subjectInstruction;

            // 3. Construir prompt completo para Gemini
            $systemPrompt = $this->buildSystemPrompt($useWebSearch);
            $fullPrompt = $systemPrompt . "\n\n" . $finalPrompt;

            // 4. Configurar payload para Gemini
            $payload = [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $fullPrompt]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.7,
                    'topK' => 40,
                    'topP' => 0.95,
                    'maxOutputTokens' => 1500
                ]
            ];

            // 5. Realizar llamada a Gemini
            $response = $this->makeGeminiRequest($payload);

            if (!$response['success']) {
                return [
                    'success' => false,
                    'error' => $response['error'],
                    'subject' => '',
                    'body' => ''
                ];
            }

            // 6. Procesar respuesta JSON de Gemini
            $jsonResponse = json_decode($response['content'], true);
            if (!$jsonResponse || !isset($jsonResponse['subject']) || !isset($jsonResponse['body'])) {
                return [
                    'success' => false,
                    'error' => 'Respuesta JSON inválida de Gemini',
                    'subject' => '',
                    'body' => ''
                ];
            }

            return [
                'success' => true,
                'subject' => $jsonResponse['subject'],
                'body' => $jsonResponse['body'],
                'model_used' => 'Google Gemini 2.5 Flash',
                'web_search_used' => $useWebSearch,
                'search_query_used' => $useWebSearch ? 'Búsqueda automática activada' : '',
                'error' => null
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Error Gemini: ' . $e->getMessage(),
                'subject' => '',
                'body' => ''
            ];
        }
    }

    /**
     * Construye el prompt del sistema limpio
     */
    private function buildSystemPrompt(bool $useWebSearch = false): string
    {
        $prompt = "Eres un asistente de marketing inmobiliario experto. Tu tarea es redactar un email persuasivo y personalizado.";
        
        if ($useWebSearch) {
            $prompt .= " Tienes acceso a información actualizada del mercado inmobiliario para hacer tu respuesta más precisa y valiosa.";
        }
        
        $prompt .= " Debes responder SIEMPRE con un objeto JSON válido, sin texto adicional antes o después. El JSON debe tener dos claves: \"subject\" para el asunto del email y \"body\" para el contenido HTML del email.";
        
        return $prompt;
    }
    
    /**
     * Reemplaza placeholders en el template (copiado de OpenAIService original)
     */
    private function replacePlaceholders(string $template, array $leadData, array $propertyData, array $agencyData): string
    {
        $placeholders = [
            '{{lead_name}}' => $leadData['nombre'] ?? '',
            '{{lead_email}}' => $leadData['email'] ?? '',
            '{{lead_phone}}' => $leadData['telefono'] ?? '',
            '{{property_address}}' => $propertyData['direccion'] ?? '',
            '{{property_type}}' => $propertyData['tipo_principal'] ?? '',
            '{{property_value_min}}' => isset($propertyData['valor_estimado_min']) ? number_format($propertyData['valor_estimado_min'], 0, ',', '.') . '€' : '',
            '{{property_value_max}}' => isset($propertyData['valor_estimado_max']) ? number_format($propertyData['valor_estimado_max'], 0, ',', '.') . '€' : '',
            '{{property_value_range}}' => $this->formatValueRange($propertyData),
            '{{agency_name}}' => $agencyData['nombre_display'] ?? '',
            '{{current_date}}' => date('d/m/Y'),
            '{{current_month}}' => $this->getCurrentMonthName(),
            '{{current_year}}' => date('Y')
        ];
        
        return str_replace(array_keys($placeholders), array_values($placeholders), $template);
    }
    
    /**
     * Formatea el rango de valor de la propiedad
     */
    private function formatValueRange(array $propertyData): string
    {
        $min = $propertyData['valor_estimado_min'] ?? null;
        $max = $propertyData['valor_estimado_max'] ?? null;
        
        if ($min && $max) {
            return number_format($min, 0, ',', '.') . '€ - ' . number_format($max, 0, ',', '.') . '€';
        } elseif ($min) {
            return 'Desde ' . number_format($min, 0, ',', '.') . '€';
        } elseif ($max) {
            return 'Hasta ' . number_format($max, 0, ',', '.') . '€';
        }
        
        return 'Consultar precio';
    }
    
    /**
     * Obtiene el nombre del mes actual en español
     */
    private function getCurrentMonthName(): string
    {
        $months = [
            1 => 'enero', 2 => 'febrero', 3 => 'marzo', 4 => 'abril',
            5 => 'mayo', 6 => 'junio', 7 => 'julio', 8 => 'agosto',
            9 => 'septiembre', 10 => 'octubre', 11 => 'noviembre', 12 => 'diciembre'
        ];
        
        return $months[(int)date('n')];
    }

    /**
     * Realiza petición a OpenAI API
     */
    private function makeOpenAIRequest(array $data): array
    {
        $ch = curl_init($this->openaiEndpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->openaiApiKey
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return ['success' => false, 'content' => '', 'error' => 'HTTP ' . $httpCode];
        }

        $decoded = json_decode($response, true);
        if (!$decoded || !isset($decoded['choices'][0]['message']['content'])) {
            return ['success' => false, 'content' => '', 'error' => 'Respuesta inválida'];
        }

        return ['success' => true, 'content' => $decoded['choices'][0]['message']['content']];
    }

    /**
     * Realiza petición a Gemini API
     */
    private function makeGeminiRequest(array $data): array
    {
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=' . $this->geminiApiKey;

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return ['success' => false, 'content' => '', 'error' => 'HTTP ' . $httpCode];
        }

        $decoded = json_decode($response, true);
        if (!$decoded || !isset($decoded['candidates'][0]['content']['parts'][0]['text'])) {
            return ['success' => false, 'content' => '', 'error' => 'Respuesta inválida'];
        }

        return ['success' => true, 'content' => $decoded['candidates'][0]['content']['parts'][0]['text']];
    }
}
