<?php
// api/lib/EmailSequenceProcessorService.php
declare(strict_types=1);

namespace Api\lib;

use PDO;
use DateTime;
use DateTimeZone;
use Exception;
use Api\Utils\AIService;

class EmailSequenceProcessorService {
    private Database $db;
    private AIService $aiService;
    private BrevoService $emailSender;
    private Logger $logger;

    public function __construct(Database $db, AIService $aiService, BrevoService $emailSender, Logger $logger) {
        $this->db = $db;
        $this->aiService = $aiService;
        $this->emailSender = $emailSender;
        $this->logger = $logger;
    }

    public function processScheduledEmails(): array {
        $this->logger->info("Iniciando proceso de emails programados de secuencia.");
        $processedCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        $scheduledJobs = $this->getScheduledEmailJobs();
        $this->logger->info(count($scheduledJobs) . " trabajos de email encontrados para procesar.");

        foreach ($scheduledJobs as $job) {
            $jobId = $job['historial_id'];
            $this->logger->debug("Procesando job ID: {$jobId}");

            if ($job['estado_envio'] === 'ENVIADO' || $job['estado_envio'] === 'FALLIDO_PERMANENTE') {
                $this->logger->info("Job ID: {$jobId} ya procesado o fallido permanente. Saltando.");
                $skippedCount++;
                continue;
            }
            
            // Verificar si el lead sigue activo y suscrito (lógica a implementar si es necesario)
            // if (!$this->isLeadStillActiveAndSubscribed($job['lead_id'])) {
            //     $this->updateEmailJobStatus($jobId, 'CANCELADO', 'Lead inactivo o desuscrito');
            //     $this->logger->info("Job ID: {$jobId} cancelado, lead inactivo/desuscrito.");
            //     $skippedCount++;
            //     continue;
            // }

            $contextData = $this->gatherContextDataForEmailJob((int)$job['lead_id'], (int)$job['paso_secuencia_id']);
            if (empty($contextData)) {
                $this->logger->error("No se pudieron recolectar datos de contexto para job ID: {$jobId}. Saltando.");
                $this->updateEmailJobStatus($jobId, 'FALLIDO', 'Error recolectando datos de contexto');
                $errorCount++;
                continue;
            }

            // Generar cuerpo del email con IA
            $aiResult = $this->aiService->generarContenidoEmailCuerpo($job['prompt_ia_base'], $contextData['placeholders']);
            if (!$aiResult['success'] || empty($aiResult['cuerpo_html'])) {
                $this->logger->error("Fallo al generar contenido IA para job ID: {$jobId}", ['error' => $aiResult['error']]);
                $this->updateEmailJobStatus($jobId, 'FALLIDO_GENERACION_IA', substr("Error IA: " . ($aiResult['error'] ?? 'Sin contenido'), 0, 250));
                $errorCount++;
                continue;
            }
            $cuerpoHtmlGeneradoIA = $aiResult['cuerpo_html'];

            // Determinar asunto final
            $asuntoFinal = $this->determineFinalSubject($job['prompt_ia_base'], $contextData['placeholders'], $job['nombre_paso_display']);

            // Renderizar plantilla HTML final
            $htmlCompleto = $this->renderEmailTemplate(
                $job['plantilla_contenido_html'], // Contenido de la plantilla base ya obtenido del JOIN
                $contextData['placeholders'], // Placeholders para la plantilla (ej. nombre_lead, saludo, despedida)
                $cuerpoHtmlGeneradoIA,          // Cuerpo HTML generado por la IA
                $asuntoFinal
            );
            
            if (empty($htmlCompleto)) {
                 $this->logger->error("Fallo al renderizar la plantilla HTML para job ID: {$jobId}");
                 $this->updateEmailJobStatus($jobId, 'FALLIDO', 'Error renderizando plantilla HTML');
                 $errorCount++;
                 continue;
            }

            // Enviar email
            $sendResult = $this->emailSender->sendEmail(
                $job['lead_email'],
                $job['lead_nombre'] ?? 'Cliente',
                $asuntoFinal,
                $htmlCompleto,
                strip_tags($htmlCompleto), // Alt body simple
                $contextData['datos_agencia']['email_notificaciones_leads'] ?? null, // From email (si está configurado)
                $contextData['datos_agencia']['nombre_display'] ?? null // From name
            );

            if ($sendResult['success']) {
                $this->updateEmailJobStatus($jobId, 'ENVIADO', 'Email enviado correctamente vía Brevo', $sendResult['message_id']);
                $this->logger->info("Email para job ID: {$jobId} enviado vía Brevo. Message ID: " . ($sendResult['message_id'] ?? 'N/A'));
                $processedCount++;
                $this->scheduleNextStepInSequence((int)$job['lead_id'], (int)$job['secuencia_id'], (int)$job['orden_paso_actual']);
            } else {
                $this->logger->error("Fallo al enviar email para job ID: {$jobId} vía Brevo", ['error' => $sendResult['error']]);
                $this->updateEmailJobStatus($jobId, 'FALLIDO', substr("Error Brevo envío: " . ($sendResult['error'] ?? 'Desconocido'), 0, 250));
                $errorCount++;
            }
        }
        $this->logger->info("Proceso de emails de secuencia finalizado. Procesados: {$processedCount}, Errores: {$errorCount}, Saltados: {$skippedCount}");
        return ['processed' => $processedCount, 'errors' => $errorCount, 'skipped' => $skippedCount];
    }

    private function getScheduledEmailJobs(): array {
        // Obtener emails que están 'PENDIENTE' o 'REINTENTO' y cuya fecha_programada es ahora o en el pasado.
        // El JOIN con email_plantillas_html es crucial para obtener el contenido de la plantilla.
        $sql = "SELECT 
                    leh.id AS historial_id,
                    leh.lead_id,
                    leh.secuencia_id,
                    leh.paso_secuencia_id,
                    leh.fecha_programada,
                    leh.estado_envio,
                    l.email AS lead_email,
                    l.nombre AS lead_nombre,
                    ispd.orden_paso AS orden_paso_actual,
                    ispd.nombre_paso_display,
                    ispd.prompt_ia_base,
                    eph.plantilla_contenido_html, -- Contenido de la plantilla
                    eph.asunto_por_defecto AS plantilla_asunto_default
                FROM lead_emails_historial leh
                JOIN leads l ON leh.lead_id = l.id
                JOIN ia_secuencia_pasos_definicion ispd ON leh.paso_secuencia_id = ispd.id
                LEFT JOIN email_plantillas_html eph ON ispd.plantilla_html_id = eph.id
                                WHERE leh.estado_envio = 'scheduled'
                  AND leh.fecha_programada <= NOW()
                  AND l.unsubscribed = 0 -- Asegurarse que el lead no esté desuscrito
                  AND ispd.activo = 1       -- Asegurarse que el paso de secuencia esté activo
                ORDER BY leh.fecha_programada ASC
                LIMIT 50"; // Limitar para no sobrecargar en una sola ejecución
        try {
            return $this->db->fetchAll($sql);
        } catch (Exception $e) {
            $this->logger->critical("Error al obtener trabajos de email programados: " . $e->getMessage());
            return [];
        }
    }

    private function gatherContextDataForEmailJob(int $leadId, int $pasoSecuenciaId): array {
        $this->logger->debug("Recolectando datos de contexto para Lead ID: {$leadId}, Paso Secuencia ID: {$pasoSecuenciaId}");
        $context = [
            'datos_lead' => [],
            'datos_agencia' => [],
            'datos_ultima_valoracion' => [],
            'datos_interacciones_previas' => [],
            'placeholders' => [] // Para reemplazo directo en prompt y plantilla
        ];

        try {
            // Datos del Lead
            $leadData = $this->db->fetch("SELECT id, nombre, email, telefono, fecha_creacion, client_identifier FROM leads WHERE id = ?", [$leadId]);
            if (!$leadData) {
                $this->logger->error("No se encontró el Lead ID: {$leadId}");
                return [];
            }
            $context['datos_lead'] = $leadData;
            $context['placeholders']['nombre_lead'] = $leadData['nombre'] ?? 'Cliente';
            $context['placeholders']['email_lead'] = $leadData['email'];

            // Datos de la Agencia (usando client_identifier del lead)
            if (!empty($leadData['client_identifier'])) {
                $agencyData = $this->db->fetch(
                    "SELECT nombre_display, logo_url, color_primario, color_secundario, email_notificaciones_leads, cta_contacto_url, politica_privacidad_url, terminos_uso_url 
                     FROM valorador_config WHERE client_identifier = ?", 
                    [$leadData['client_identifier']]
                );
                if ($agencyData) {
                    $context['datos_agencia'] = $agencyData;
                    $context['placeholders']['nombre_agencia'] = $agencyData['nombre_display'] ?? 'Tu Agencia';
                    $context['placeholders']['logo_url_agencia'] = $agencyData['logo_url'] ?? '';
                    $context['placeholders']['color_primario_agencia'] = $agencyData['color_primario'] ?? '#051f33';
                     // Asegurar que la URL base para enlaces está disponible
                    $appBaseUrl = defined('APP_BASE_URL') ? APP_BASE_URL : 'https://inmoautomation.com';
                    $context['placeholders']['enlace_baja'] = $appBaseUrl . '/unsubscribe/' . base64_encode((string)$leadId . ':' . $leadData['email']);
                } else {
                     $this->logger->warning("No se encontraron datos de agencia para client_identifier: " . $leadData['client_identifier']);
                }
            } else {
                $this->logger->warning("Lead ID: {$leadId} no tiene client_identifier.");
            }
            
            // Datos de la última valoración (si aplica)
            $lastValuation = $this->db->fetch("SELECT direccion, tipo_principal, valor_estimado_min, valor_estimado_max, fecha_valoracion FROM valoraciones WHERE lead_id = ? ORDER BY fecha_valoracion DESC LIMIT 1", [$leadId]);
            if ($lastValuation) {
                $context['datos_ultima_valoracion'] = $lastValuation;
                $context['placeholders']['ultima_propiedad_valorada_direccion'] = $lastValuation['direccion'] ?? 'N/A';
                $context['placeholders']['ultima_propiedad_valorada_tipo'] = $lastValuation['tipo_principal'] ?? 'N/A';
            }
            
            // Podríamos añadir más contexto, como interacciones previas, emails abiertos, etc.
            // Por ahora, mantenemos estos datos básicos.
            return $context;

        } catch (Exception $e) {
            $this->logger->critical("Error recolectando datos de contexto para Lead ID {$leadId}: " . $e->getMessage());
            return [];
        }
    }

    private function determineFinalSubject(string $promptBase, array $placeholders, string $defaultSubjectFromStep): string {
        // Intenta extraer un asunto del prompt si está marcado, ej: "ASUNTO: [asunto_dinamico] - Resto del prompt"
        // O simplemente usa el nombre del paso como asunto por defecto.
        // Aquí, para simplificar, usaremos el nombre_paso_display o un default de la plantilla.
        
        $subject = $defaultSubjectFromStep; // Usar el nombre del paso como base

        // Reemplazar placeholders en el asunto
        foreach ($placeholders as $key => $value) {
            if (is_scalar($value)) {
                 $subject = str_replace("{{" . $key . "}}", (string)$value, $subject);
            }
        }
        
        // Si el prompt contiene una directiva de asunto específica como "[ASUNTO: Mi Asunto con {{nombre_lead}}]"
        if (preg_match('/\[ASUNTO:(.*?)\]/i', $promptBase, $matches)) {
            $extractedSubject = trim($matches[1]);
            // Reemplazar placeholders en el asunto extraído también
             foreach ($placeholders as $key => $value) {
                if (is_scalar($value)) {
                     $extractedSubject = str_replace("{{" . $key . "}}", (string)$value, $extractedSubject);
                }
            }
            if (!empty($extractedSubject)) {
                $subject = $extractedSubject;
            }
        }

        return !empty($subject) ? $subject : ($placeholders['plantilla_asunto_default'] ?? 'Información de tu interés');
    }

    private function renderEmailTemplate(string $plantillaHtmlBase, array $placeholders, string $cuerpoHtmlGeneradoIA, string $asuntoFinal): string {
        if (empty($plantillaHtmlBase)) {
            $this->logger->error("Plantilla HTML base está vacía. No se puede renderizar.");
            return ''; // Devuelve vacío para indicar fallo
        }
        
        $htmlOutput = $plantillaHtmlBase;

        // Placeholder principal para el contenido generado por IA
        $htmlOutput = str_replace('{{cuerpo_html_generado_por_ia}}', $cuerpoHtmlGeneradoIA, $htmlOutput);
        
        // Placeholder para el título/asunto en el <title> del HTML si existe
        $htmlOutput = str_replace('{{asunto_email}}', htmlspecialchars($asuntoFinal, ENT_QUOTES, 'UTF-8'), $htmlOutput);

        // Reemplazar otros placeholders generales (datos de agencia, lead, etc.)
        foreach ($placeholders as $key => $value) {
            if (is_scalar($value) || (is_object($value) && method_exists($value, '__toString'))) {
                $htmlOutput = str_replace("{{" . $key . "}}", (string)$value, $htmlOutput);
            } elseif (is_array($value) && $key === 'datos_agencia') { // Manejo especial para datos de agencia en la plantilla
                 foreach($value as $agKey => $agVal) {
                     if (is_scalar($agVal)) {
                        $htmlOutput = str_replace("{{agencia_" . $agKey . "}}", (string)$agVal, $htmlOutput);
                     }
                 }
            }
        }
        
        // Placeholders por defecto si alguno no se reemplazó y es crítico (ej. {{nombre_agencia}})
        // Esto es una salvaguarda, idealmente todos los placeholders deberían tener un valor desde $contextData.
        $htmlOutput = str_replace('{{nombre_agencia}}', $placeholders['nombre_agencia'] ?? 'Tu Agencia Inmobiliaria', $htmlOutput);
        $htmlOutput = str_replace('{{logo_url_agencia}}', $placeholders['logo_url_agencia'] ?? '', $htmlOutput);
        $htmlOutput = str_replace('{{color_primario_agencia}}', $placeholders['color_primario_agencia'] ?? '#051f33', $htmlOutput);
        $appBaseUrl = defined('APP_BASE_URL') ? APP_BASE_URL : 'https://inmoautomation.com';
        $unsubscribeLinkFallback = $appBaseUrl . '/unsubscribe/error';
        $htmlOutput = str_replace('{{enlace_baja}}', $placeholders['enlace_baja'] ?? $unsubscribeLinkFallback, $htmlOutput);
        
        // Podríamos añadir lógica para MJML aquí si las plantillas se guardan en MJML y necesitan compilación
        // $mjmlEngine = new MjmlEngine();
        // $compiledHtml = $mjmlEngine->compile($htmlOutput); 
        // return $compiledHtml;

        return $htmlOutput;
    }

    private function updateEmailJobStatus(string $jobId, string $status, string $notes = '', ?string $messageId = null): void {
        $sql = "UPDATE lead_emails_historial 
                SET estado_envio = ?, 
                    fecha_envio_real = IF(? IN ('ENVIADO'), NOW(), fecha_envio_real), 
                    notas_internas = ?, 
                    message_id_proveedor = ?
                WHERE id = ?";
        try {
            $this->db->query($sql, [$status, $status, $notes, $messageId, $jobId]);
            $this->logger->info("Estado del Job ID {$jobId} actualizado a {$status}. Notas: {$notes}");
        } catch (Exception $e) {
            $this->logger->error("Error actualizando estado del Job ID {$jobId}: " . $e->getMessage());
        }
    }

    public function scheduleNextStepInSequence(int $leadId, int $currentSequenceId, int $currentStepOrder): void {
        $this->logger->debug("Intentando programar siguiente paso para Lead ID {$leadId} desde Secuencia ID {$currentSequenceId}, Paso Orden {$currentStepOrder}");
        
        $nextStepSql = "SELECT id, uuid, secuencia_id, orden_paso, nombre_paso_display, dias_delay_desde_anterior 
                        FROM ia_secuencia_pasos_definicion 
                        WHERE secuencia_id = ? AND orden_paso > ? AND activo = 1 
                        ORDER BY orden_paso ASC 
                        LIMIT 1";
        $nextStep = $this->db->fetch($nextStepSql, [$currentSequenceId, $currentStepOrder]);

        if ($nextStep) {
            $delayDays = (int)$nextStep['dias_delay_desde_anterior'];
            $scheduledDate = (new DateTime('now', new DateTimeZone('Europe/Madrid')))
                                ->modify("+{$delayDays} days")
                                ->format('Y-m-d H:i:s');
            
            // Verificar si ya existe un PENDIENTE para este lead y este siguiente paso (evitar duplicados si el cron se ejecuta muy rápido)
                        $checkExistingSql = "SELECT id FROM lead_emails_historial WHERE lead_id = ? AND paso_secuencia_id = ? AND estado_envio = 'scheduled' LIMIT 1";
            $existingPending = $this->db->fetch($checkExistingSql, [$leadId, $nextStep['id']]);

            if ($existingPending) {
                $this->logger->info("Siguiente paso (ID: {$nextStep['id']}) ya está PENDIENTE para Lead ID {$leadId}. No se reprograma.");
                return;
            }

            $insertSql = "INSERT INTO lead_emails_historial 
                                                        (uuid, lead_id, secuencia_id, paso_secuencia_id, fecha_programada, estado_envio, nombre_paso_programado, fecha_creacion)
                          VALUES 
                            (UUID(), ?, ?, ?, ?, 'scheduled', ?, NOW())"; 
                            // UUID() es función de MySQL
            try {
                $this->db->query($insertSql, [$leadId, $nextStep['secuencia_id'], $nextStep['id'], $scheduledDate, $nextStep['nombre_paso_display']]);
                $this->logger->info("Siguiente paso (ID: {$nextStep['id']}) programado para Lead ID {$leadId} en {$scheduledDate}.");
            } catch (Exception $e) {
                $this->logger->error("Error al programar siguiente paso para Lead ID {$leadId}: " . $e->getMessage(), ['next_step_id' => $nextStep['id']]);
            }
        } else {
            $this->logger->info("No hay más pasos en la secuencia ID {$currentSequenceId} para Lead ID {$leadId} después del paso orden {$currentStepOrder}.");
            // Aquí podrías marcar la secuencia como completada para el lead si tienes una tabla para eso.
        }
    }
    
    // Esta función es un placeholder, deberías implementarla si necesitas esta lógica
    // private function isLeadStillActiveAndSubscribed(int $leadId): bool {
    //     $lead = $this->db->fetch("SELECT unsubscribed, status FROM leads WHERE id = ?", [$leadId]);
    //     if ($lead && $lead['unsubscribed'] == 0 && ($lead['status'] == 'active' || $lead['status'] == 'new')) { // Ajusta estados según tu lógica
    //         return true;
    //     }
    //     return false;
    // }

    private function replacePlaceholders(string $content, array $lead, array $valoracion, array $lastInteraction): string
    {
        // Asegurarse de que las URLs base están definidas
        $appBaseUrl = defined('APP_BASE_URL') ? rtrim(APP_BASE_URL, '/') : '';
        $dashboardBaseUrl = defined('DASHBOARD_BASE_URL') ? rtrim(DASHBOARD_BASE_URL, '/') : $appBaseUrl;

        $placeholders = [
            '{{nombre_lead}}' => $lead['nombre'] ?? '[Nombre del Lead]',
            '{{email_lead}}' => $lead['email'] ?? '[Email del Lead]',
            '{{fecha_valoracion}}' => isset($valoracion['fecha_creacion']) ? date('d/m/Y', strtotime($valoracion['fecha_creacion'])) : '[Fecha de Valoración]',
            '{{enlace_valoracion_publica}}' => $appBaseUrl . '/valoracion/' . ($valoracion['uuid'] ?? ''),
            '{{enlace_dashboard_lead}}' => $dashboardBaseUrl . '/leads/' . ($lead['uuid'] ?? ''),
        ];

        return str_replace(array_keys($placeholders), array_values($placeholders), $content);
    }
}
?> 