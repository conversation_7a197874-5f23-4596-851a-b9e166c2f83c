<?php
// api/lib/Logger.php
namespace Api\lib;

// bootstrap.php debería ser incluido por el script que usa esta clase.

class Logger {
    public const DEBUG = 100;
    public const INFO = 200;
    public const WARNING = 300;
    public const ERROR = 400;
    public const CRITICAL = 500;

    private static array $logLevels = [
        self::DEBUG   => 'DEBUG',
        self::INFO    => 'INFO',
        self::WARNING => 'WARNING',
        self::ERROR   => 'ERROR',
        self::CRITICAL => 'CRITICAL',
    ];

    public static function log(string $message, int $level = self::INFO, array $context = []): void {
        // Verificar nivel mínimo de log (solo LOG_LEVEL_VALUE es necesario ahora)
        if (defined('LOG_LEVEL_VALUE') && $level < LOG_LEVEL_VALUE) {
            return;
        }

        $levelName = self::$logLevels[$level] ?? 'UNKNOWN';

        // Obtener información adicional para Cloud Logging
        $logData = [
            'timestamp' => (new \DateTime())->format('Y-m-d H:i:s'),
            'level' => $levelName,
            'message' => $message,
            'context' => $context,
            'endpoint' => self::getCurrentEndpoint(),
            'request_id' => self::getRequestId(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'ip' => self::getClientIP(),
            'app_env' => $_SERVER['APP_ENV'] ?? 'unknown'
        ];

        // Formatear para Cloud Logging (JSON estructurado)
        try {
            $logMessage = json_encode($logData, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
            if ($logMessage === false) {
                $logMessage = "Error al codificar log a JSON: " . json_last_error_msg() . " | Mensaje original: {$message}";
            }
        } catch (\Throwable $th) {
            $logMessage = "Excepción al codificar log: " . $th->getMessage() . " | Mensaje original: {$message}";
        }

        // Enviar a Google Cloud Logging usando error_log()
        // En Cloud Run, error_log() va automáticamente a Cloud Logging
        error_log("[{$levelName}] {$logMessage}");
    }

    public static function debug(string $message, array $context = []): void {
        self::log($message, self::DEBUG, $context);
    }
    public static function info(string $message, array $context = []): void {
        self::log($message, self::INFO, $context);
    }
    public static function warning(string $message, array $context = []): void {
        self::log($message, self::WARNING, $context);
    }
    public static function error(string $message, array $context = []): void {
        self::log($message, self::ERROR, $context);
    }
     public static function critical(string $message, array $context = []): void {
        self::log($message, self::CRITICAL, $context);
    }

    /**
     * Funciones auxiliares para Cloud Logging
     */
    private static function getCurrentEndpoint(): string {
        $script = $_SERVER['SCRIPT_NAME'] ?? 'unknown';
        return basename($script, '.php');
    }

    private static function getRequestId(): string {
        static $requestId = null;
        if ($requestId === null) {
            $requestId = uniqid('req_', true);
        }
        return $requestId;
    }

    private static function getClientIP(): string {
        $headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                return trim($ips[0]);
            }
        }

        return 'unknown';
    }
}
?> 