<?php

/**
 * Clase para registrar y gestionar fallos de pago
 */
class PaymentFailureLogger {
    
    private $db;
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
    }
    
    /**
     * Registra un fallo de pago con información detallada
     */
    public function logPaymentFailure($data) {
        try {
            // Generar UUID
            $uuid = $this->generateUUID();
            
            // Determinar el tipo de fallo basado en la razón
            $failure_type = $this->determineFailureType($data['failure_reason'] ?? null);
            
            // Determinar si es recuperable
            $is_recoverable = $this->isRecoverable($failure_type, $data);
            
            // Preparar datos de método de pago si están disponibles
            $payment_method_details = null;
            if (isset($data['payment_method_details']) && !empty($data['payment_method_details'])) {
                if (is_array($data['payment_method_details'])) {
                    $payment_method_details = json_encode($data['payment_method_details']);
                } else {
                    $payment_method_details = $data['payment_method_details'];
                }
            }
            
            $sql = "INSERT INTO payment_failures (
                uuid, user_id, stripe_customer_id, stripe_subscription_id, 
                stripe_invoice_id, stripe_payment_intent_id, failure_reason, 
                failure_type, attempt_count, amount_failed, currency,
                subscription_status_before, subscription_status_after,
                has_payment_method, payment_method_details, is_recoverable,
                notes, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $this->db->prepare($sql);
            if (!$stmt) {
                throw new Exception("Error preparando statement: " . $this->db->error);
            }
            
            // Preparar variables para bind_param
            $user_id = $data['user_id'];
            $stripe_customer_id = $data['stripe_customer_id'];
            $stripe_subscription_id = $data['stripe_subscription_id'] ?? null;
            $stripe_invoice_id = $data['stripe_invoice_id'];
            $stripe_payment_intent_id = $data['stripe_payment_intent_id'] ?? null;
            $failure_reason = $data['failure_reason'] ?? null;
            $attempt_count = $data['attempt_count'] ?? 1;
            $amount_failed = $data['amount_failed'] ?? null;
            $currency = $data['currency'] ?? 'EUR';
            $subscription_status_before = $data['subscription_status_before'] ?? null;
            $subscription_status_after = $data['subscription_status_after'] ?? null;
            $has_payment_method = $data['has_payment_method'] ?? 0;
            $notes = $data['notes'] ?? null;

            $stmt->bind_param("sissssssissssiiss",
                $uuid,
                $user_id,
                $stripe_customer_id,
                $stripe_subscription_id,
                $stripe_invoice_id,
                $stripe_payment_intent_id,
                $failure_reason,
                $failure_type,
                $attempt_count,
                $amount_failed,
                $currency,
                $subscription_status_before,
                $subscription_status_after,
                $has_payment_method,
                $payment_method_details,
                $is_recoverable,
                $notes
            );
            
            if ($stmt->execute()) {
                $failure_id = $this->db->insert_id;
                $stmt->close();
                
                error_log("[PaymentFailureLogger] Fallo de pago registrado. ID: {$failure_id}, UUID: {$uuid}");
                return $failure_id;
            } else {
                throw new Exception("Error ejecutando statement: " . $stmt->error);
            }
            
        } catch (Exception $e) {
            error_log("[PaymentFailureLogger] Error registrando fallo de pago: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Marca un fallo como recuperado exitosamente
     */
    public function markAsRecovered($failure_id, $notes = null) {
        try {
            $sql = "UPDATE payment_failures 
                    SET recovery_successful = 1, 
                        recovery_attempted = 1,
                        notes = CONCAT(COALESCE(notes, ''), ?, ' [Recuperado exitosamente]'),
                        updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            if (!$stmt) {
                throw new Exception("Error preparando statement: " . $this->db->error);
            }
            
            $notes_text = $notes ? " - {$notes}" : "";
            $stmt->bind_param("si", $notes_text, $failure_id);
            
            return $stmt->execute();
            
        } catch (Exception $e) {
            error_log("[PaymentFailureLogger] Error marcando como recuperado: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obtiene fallos de pago recientes para un usuario
     */
    public function getRecentFailures($user_id, $limit = 10) {
        try {
            $sql = "SELECT * FROM payment_failures 
                    WHERE user_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT ?";
            
            $stmt = $this->db->prepare($sql);
            if (!$stmt) {
                throw new Exception("Error preparando statement: " . $this->db->error);
            }
            
            $stmt->bind_param("ii", $user_id, $limit);
            $stmt->execute();
            
            $result = $stmt->get_result();
            $failures = [];
            
            while ($row = $result->fetch_assoc()) {
                if ($row['payment_method_details']) {
                    $row['payment_method_details'] = json_decode($row['payment_method_details'], true);
                }
                $failures[] = $row;
            }
            
            $stmt->close();
            return $failures;
            
        } catch (Exception $e) {
            error_log("[PaymentFailureLogger] Error obteniendo fallos recientes: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Determina el tipo de fallo basado en la razón de Stripe
     */
    private function determineFailureType($failure_reason) {
        if (!$failure_reason) return 'other';
        
        $failure_reason = strtolower($failure_reason);
        
        if (in_array($failure_reason, ['card_declined', 'generic_decline', 'fraudulent'])) {
            return 'card_declined';
        }
        
        if (in_array($failure_reason, ['insufficient_funds'])) {
            return 'insufficient_funds';
        }
        
        if (in_array($failure_reason, ['authentication_required', 'requires_action'])) {
            return 'authentication_required';
        }
        
        if (in_array($failure_reason, ['expired_card', 'card_expired'])) {
            return 'card_expired';
        }
        
        if (in_array($failure_reason, ['processing_error', 'issuer_not_available'])) {
            return 'processing_error';
        }
        
        return 'other';
    }
    
    /**
     * Determina si un fallo es recuperable
     */
    private function isRecoverable($failure_type, $data) {
        // Los fallos por autenticación requerida son siempre recuperables
        if ($failure_type === 'authentication_required') {
            return 1;
        }
        
        // Si hay método de pago, la mayoría de fallos son recuperables
        if (!empty($data['has_payment_method'])) {
            return in_array($failure_type, ['card_declined', 'insufficient_funds', 'processing_error']) ? 1 : 0;
        }
        
        // Sin método de pago, solo algunos tipos son recuperables
        return in_array($failure_type, ['authentication_required', 'card_expired']) ? 1 : 0;
    }
    
    /**
     * Genera un UUID v4
     */
    private function generateUUID() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
?>
