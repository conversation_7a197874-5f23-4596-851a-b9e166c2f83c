<?php
// api/lib/Database.php
namespace Api\lib;

use PDO;
use PDOException;

// bootstrap.php debería ser incluido por el script que usa esta clase (ej. el cron o un endpoint API)
// No es necesario incluirlo aquí directamente si se sigue esa práctica.

class Database {
    private static ?self $instance = null; // Usar ?self para indicar que puede ser null inicialmente
    private PDO $pdo;

    private function __construct() {
        // Lógica de conexión a la base de datos
        $dsn = '';

        // Prioridad 1: Conexión por Socket (Cloud Run)
        if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {
            $dsn = 'mysql:unix_socket=' . DB_SOCKET . ';dbname=' . DB_DATABASE . ';charset=' . DB_CHARSET;
        
        // Prioridad 2: Conexión por Host/Puerto (Desarrollo Local)
        } elseif (defined('DB_HOST')) {
            $dsn = 'mysql:host=' . DB_HOST . ';port=' . DB_PORT . ';dbname=' . DB_DATABASE . ';charset=' . DB_CHARSET;
        
        // Si no hay configuración de conexión, fallar
        } else {
            error_log('Database Error: No se encontraron constantes de conexión (DB_SOCKET o DB_HOST). Asegúrate de que bootstrap.php se cargó correctamente.');
            throw new PDOException('Configuración de base de datos no cargada.');
        }
        $options = [
            PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES   => false,
            PDO::ATTR_STRINGIFY_FETCHES  => false,
            // Optimizaciones para conexiones externas
            PDO::ATTR_PERSISTENT         => true,  // Conexiones persistentes
            PDO::ATTR_TIMEOUT            => 5,     // Timeout de conexión reducido
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'",
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            PDO::MYSQL_ATTR_COMPRESS     => true,  // Compresión de datos
        ];
        try {
            $this->pdo = new PDO($dsn, DB_USERNAME, DB_PASSWORD, $options);

            // Configurar zona horaria de MySQL para que coincida con PHP
            // Usar offset +02:00 para Madrid (UTC+2 en horario de verano)
            $this->pdo->exec("SET time_zone = '+02:00'");

        } catch (PDOException $e) {
            // En un entorno de producción, loguear esto de forma más robusta y no exponer detalles.
            // Logger::critical("Error de conexión a BBDD: " . $e->getMessage()); // Si Logger está disponible
            error_log("Error de conexión a BBDD: " . $e->getMessage());
            throw new PDOException("Error de conexión a la base de datos.", (int)$e->getCode()); // Mensaje genérico al cliente
        }
    }

    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection(): PDO {
        return $this->pdo;
    }

    public function query(string $sql, array $params = []): \PDOStatement {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            // Logger::error("Error en consulta SQL: " . $e->getMessage(), ['sql' => $sql, 'params' => $params]);
            error_log("Error en consulta SQL: " . $e->getMessage() . " SQL: " . $sql);
            throw $e; // Re-lanzar la excepción para que sea manejada más arriba
        }
    }

    public function fetch(string $sql, array $params = []) {
        return $this->query($sql, $params)->fetch();
    }

    public function fetchAll(string $sql, array $params = []): array {
        return $this->query($sql, $params)->fetchAll();
    }

    /**
     * @return string|false
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}