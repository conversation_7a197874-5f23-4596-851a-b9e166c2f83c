<?php
declare(strict_types=1);

namespace Api\lib;

use Api\lib\Database;

class EmailVariablesService
{
    /**
     * Obtiene todas las variables estandarizadas para un lead específico
     */
    public static function getVariablesForLead(int $leadId): array
    {
        $db = Database::getInstance()->getConnection();
        
        // Consulta completa para obtener todos los datos necesarios
        $stmt = $db->prepare('
            SELECT 
                -- Lead data
                vl.id as lead_id,
                vl.nombre as lead_nombre,
                vl.email as lead_email,
                vl.telefono as lead_telefono,
                vl.necesidad as lead_necesidad,
                
                -- Property data
                vv.direccion as property_address,
                vv.valor_estimado_min as property_value_min,
                vv.valor_estimado_max as property_value_max,
                vv.tipo_principal as property_type,
                vv.subtipo,
                vv.superficie as property_surface,
                vv.superficie_parcela,
                vv.habitaciones as property_rooms,
                vv.banos as property_bathrooms,
                vv.ano_construccion_catastro as property_year,
                vv.planta,
                vv.estado,
                vv.extras,
                vv.precio_m2_promedio as zone_avg_price_m2,
                vv.tamano_promedio as zone_avg_size,
                vv.numero_propiedades_analizadas,
                vv.porcentaje_con_piscina,
                vv.porcentaje_con_parking,
                vv.porcentaje_con_terraza,
                vv.porcentaje_reformadas,
                vv.precio_vs_zona_porcentaje,
                vv.tamano_vs_zona_porcentaje,
                
                -- Agency data
                a.id as agency_id,
                a.name as agency_name,

                -- Client valorador data
                cv.nombre_display as valorador_name,
                cv.color_primario as agency_color,
                cv.logo_url as agency_logo_url,
                cv.email_notificaciones_leads as valorador_email,
                cv.client_identifier as valorador_client_id,
                cv.cta_contacto_url as valorador_cta_url
                
            FROM valorador_leads vl
            JOIN valorador_valoraciones vv ON vl.valoracion_id = vv.id
            JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id
            LEFT JOIN agencies a ON cv.agency_id = a.id
            WHERE vl.id = ?
        ');
        
        $stmt->execute([$leadId]);
        $data = $stmt->fetch();
        
        if (!$data) {
            throw new \Exception("Lead con ID {$leadId} no encontrado");
        }
        
        return self::buildVariablesArray($data);
    }
    
    /**
     * Construye el array completo de variables estandarizadas
     */
    private static function buildVariablesArray(array $data): array
    {
        $variables = [];
        
        // === VARIABLES DE LEAD ===
        $variables['{{lead_name}}'] = $data['lead_nombre'] ?? 'cliente';
        $variables['{{lead_email}}'] = $data['lead_email'] ?? '';
        $variables['{{lead_phone}}'] = $data['lead_telefono'] ?? '';
        $variables['{{lead_necesidad}}'] = $data['lead_necesidad'] ?? '';
        
        // === VARIABLES DE PROPIEDAD ===
        $variables['{{property_address}}'] = $data['property_address'] ?? 'tu propiedad';
        $variables['{{property_value_min}}'] = self::formatCurrency((float)($data['property_value_min'] ?? 0), false);
        $variables['{{property_value_max}}'] = self::formatCurrency((float)($data['property_value_max'] ?? 0), false);
        $variables['{{property_value_range}}'] = $variables['{{property_value_min}}'] . ' - ' . $variables['{{property_value_max}}'];
        $variables['{{property_type}}'] = $data['property_type'] ?? '';
        $variables['{{property_subtype}}'] = $data['subtipo'] ?? '';
        $variables['{{property_surface}}'] = $data['property_surface'] ? $data['property_surface'] . ' m²' : '';
        $variables['{{property_surface_plot}}'] = $data['superficie_parcela'] ? $data['superficie_parcela'] . ' m²' : '';
        $variables['{{property_rooms}}'] = $data['property_rooms'] ?? '';
        $variables['{{property_bathrooms}}'] = $data['property_bathrooms'] ?? '';
        $variables['{{property_year}}'] = $data['property_year'] ?? '';
        $variables['{{property_floor}}'] = $data['planta'] ?? '';
        $variables['{{property_state}}'] = $data['estado'] ?? '';

        // === VARIABLES DE EXTRAS Y CARACTERÍSTICAS ===
        $extras = json_decode($data['extras'] ?? '[]', true);
        $variables['{{property_has_parking}}'] = in_array('Parking', $extras) ? 'Sí' : 'No';
        $variables['{{property_has_pool}}'] = in_array('Piscina', $extras) ? 'Sí' : 'No';
        $variables['{{property_has_terrace}}'] = in_array('Terraza', $extras) ? 'Sí' : 'No';
        $variables['{{property_has_garden}}'] = in_array('Jardin', $extras) ? 'Sí' : 'No';
        $variables['{{property_has_ac}}'] = in_array('AireAcondicionado', $extras) ? 'Sí' : 'No';
        $variables['{{property_has_storage}}'] = in_array('Trastero', $extras) ? 'Sí' : 'No';

        // === ALIAS PARA COMPATIBILIDAD CON IA ===
        $variables['{{estimated_value_min}}'] = $variables['{{property_value_min}}'];
        $variables['{{estimated_value_max}}'] = $variables['{{property_value_max}}'];
        
        // === VARIABLES DE ZONA ===
        $variables['{{zone_avg_price_m2}}'] = self::formatCurrency((float)($data['zone_avg_price_m2'] ?? 0), false) . '/m²';
        $variables['{{zone_avg_size}}'] = $data['zone_avg_size'] ? number_format((float)$data['zone_avg_size'], 0, ',', '.') . ' m²' : '';
        $variables['{{zone_name}}'] = self::extractZoneFromAddress($data['property_address'] ?? '');
        $variables['{{zone_properties_analyzed}}'] = $data['numero_propiedades_analizadas'] ?? '';
        $variables['{{zone_pool_percentage}}'] = $data['porcentaje_con_piscina'] ? number_format((float)$data['porcentaje_con_piscina'], 1) . '%' : '';
        $variables['{{zone_parking_percentage}}'] = $data['porcentaje_con_parking'] ? number_format((float)$data['porcentaje_con_parking'], 1) . '%' : '';
        $variables['{{zone_terrace_percentage}}'] = $data['porcentaje_con_terraza'] ? number_format((float)$data['porcentaje_con_terraza'], 1) . '%' : '';
        $variables['{{zone_renovated_percentage}}'] = $data['porcentaje_reformadas'] ? number_format((float)$data['porcentaje_reformadas'], 1) . '%' : '';
        $variables['{{property_vs_zone_price}}'] = $data['precio_vs_zona_porcentaje'] ? number_format((float)$data['precio_vs_zona_porcentaje'], 1) . '%' : '';
        $variables['{{property_vs_zone_size}}'] = $data['tamano_vs_zona_porcentaje'] ? number_format((float)$data['tamano_vs_zona_porcentaje'], 1) . '%' : '';
        
        // === VARIABLES DE AGENCIA/VALORADOR ===
        $variables['{{agency_name}}'] = $data['agency_name'] ?? 'nuestra agencia'; // Mantener para compatibilidad interna
        $variables['{{nombre_agencia}}'] = $data['valorador_name'] ?? 'nuestra inmobiliaria'; // NOMBRE COMERCIAL - usar en plantillas
        $variables['{{agency_color}}'] = $data['agency_color'] ?? '#2563eb';
        $variables['{{color_primario_agencia}}'] = $data['agency_color'] ?? '#2563eb'; // Alias para compatibilidad
        $variables['{{agency_logo_url}}'] = $data['agency_logo_url'] ?? '';
        $variables['{{logo_agencia_url}}'] = $data['agency_logo_url'] ?? ''; // Alias para compatibilidad

        // === VARIABLES DE AGENTE ===
        $variables['{{agent_name}}'] = $data['valorador_name'] ?? 'tu consultor inmobiliario';
        $variables['{{agent_phone}}'] = ''; // No disponible en la estructura actual
        $variables['{{agent_email}}'] = $data['valorador_email'] ?? '';

        // === VARIABLES DE CONTACTO ===
        $variables['{{valorador_contact_url}}'] = $data['valorador_cta_url'] ?? '#'; // URL CTA configurada por la agencia
        
        // === VARIABLES DE FECHA ===
        $variables['{{current_date}}'] = date('d/m/Y');
        $variables['{{current_date_long}}'] = date('d \d\e F \d\e Y');
        $variables['{{year}}'] = date('Y');
        $variables['{{month_name}}'] = self::getSpanishMonthName((int)date('n'));
        $variables['{{day_name}}'] = self::getSpanishDayName((int)date('w'));
        
        // === VARIABLES CALCULADAS ===
        $avgValue = ((float)($data['property_value_min'] ?? 0) + (float)($data['property_value_max'] ?? 0)) / 2;
        $variables['{{property_value_avg}}'] = self::formatCurrency($avgValue);
        
        // Precio por m² de la propiedad
        if ($data['property_surface'] && $avgValue > 0) {
            $pricePerM2 = $avgValue / (float)$data['property_surface'];
            $variables['{{property_price_m2}}'] = self::formatCurrency($pricePerM2) . '/m²';
        } else {
            $variables['{{property_price_m2}}'] = '';
        }
        
        return $variables;
    }
    
    /**
     * Formatea una cantidad como moneda española
     */
    private static function formatCurrency(float $amount, bool $includeSymbol = true): string
    {
        if ($amount == 0) return $includeSymbol ? '0€' : '0';
        $formatted = number_format($amount, 0, ',', '.');
        return $includeSymbol ? $formatted . '€' : $formatted;
    }
    
    /**
     * Extrae el nombre de la zona de una dirección
     */
    private static function extractZoneFromAddress(string $address): string
    {
        // Buscar patrones comunes de ciudades españolas
        if (preg_match('/\b(\d{5})\s+([^,\(]+)/i', $address, $matches)) {
            return trim($matches[2]);
        }
        
        // Si no encuentra código postal, tomar la última parte después de la coma
        $parts = explode(',', $address);
        if (count($parts) > 1) {
            return trim(end($parts));
        }
        
        return 'la zona';
    }
    
    /**
     * Obtiene el nombre del mes en español
     */
    private static function getSpanishMonthName(int $month): string
    {
        $months = [
            1 => 'enero', 2 => 'febrero', 3 => 'marzo', 4 => 'abril',
            5 => 'mayo', 6 => 'junio', 7 => 'julio', 8 => 'agosto',
            9 => 'septiembre', 10 => 'octubre', 11 => 'noviembre', 12 => 'diciembre'
        ];
        
        return $months[$month] ?? 'mes';
    }
    
    /**
     * Obtiene el nombre del día en español
     */
    private static function getSpanishDayName(int $day): string
    {
        $days = [
            0 => 'domingo', 1 => 'lunes', 2 => 'martes', 3 => 'miércoles',
            4 => 'jueves', 5 => 'viernes', 6 => 'sábado'
        ];
        
        return $days[$day] ?? 'día';
    }
    
    /**
     * Reemplaza todas las variables en un texto
     */
    public static function replaceVariables(string $text, array $variables): string
    {
        return str_replace(array_keys($variables), array_values($variables), $text);
    }
    
    /**
     * Obtiene las variables separadas por categorías para uso en prompts de IA
     */
    public static function getVariablesForAI(int $leadId): array
    {
        $db = Database::getInstance()->getConnection();
        
        $stmt = $db->prepare('
            SELECT
                vl.nombre, vl.email, vl.telefono, vl.necesidad, vl.estado,
                vv.direccion, vv.valor_estimado_min, vv.valor_estimado_max,
                vv.tipo_principal, vv.subtipo, vv.superficie, vv.superficie_parcela,
                vv.habitaciones, vv.banos, vv.ano_construccion_catastro, vv.planta,
                vv.extras, vv.precio_m2_promedio, vv.tamano_promedio,
                vv.numero_propiedades_analizadas, vv.porcentaje_con_piscina,
                vv.porcentaje_con_parking, vv.porcentaje_con_terraza, vv.porcentaje_reformadas,
                vv.precio_vs_zona_porcentaje, vv.tamano_vs_zona_porcentaje,
                a.name as agency_name, cv.nombre_display as agent_name,
                cv.email_notificaciones_leads as agent_email
            FROM valorador_leads vl
            JOIN valorador_valoraciones vv ON vl.valoracion_id = vv.id
            JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id
            LEFT JOIN agencies a ON cv.agency_id = a.id
            WHERE vl.id = ?
        ');
        
        $stmt->execute([$leadId]);
        $data = $stmt->fetch();
        
        if (!$data) {
            throw new \Exception("Lead con ID {$leadId} no encontrado");
        }
        
        return [
            'leadData' => [
                'nombre' => $data['nombre'],
                'email' => $data['email'],
                'telefono' => $data['telefono'],
                'necesidad' => $data['necesidad']
            ],
            'propertyData' => [
                'direccion' => $data['direccion'],
                'valor_estimado_min' => $data['valor_estimado_min'],
                'valor_estimado_max' => $data['valor_estimado_max'],
                'tipo_principal' => $data['tipo_principal'],
                'superficie' => $data['superficie'],
                'habitaciones' => $data['habitaciones'],
                'banos' => $data['banos'],
                'precio_m2_promedio' => $data['precio_m2_promedio'],
                'tamano_promedio' => $data['tamano_promedio']
            ],
            'agencyData' => [
                'nombre' => $data['agency_name'],
                'agente_nombre' => $data['agent_name'],
                'agente_telefono' => '',
                'agente_email' => $data['agent_email']
            ]
        ];
    }

    /**
     * Obtiene variables de ejemplo para mostrar en la interfaz
     */
    public static function getExampleVariables(): array
    {
        return [
            '{{lead_name}}' => 'Juan Pérez',
            '{{lead_email}}' => '<EMAIL>',
            '{{lead_phone}}' => '+34 600 123 456',
            '{{property_address}}' => 'Calle Mayor 123, Madrid',
            '{{property_value_min}}' => '250.000€',
            '{{property_value_max}}' => '300.000€',
            '{{property_value_range}}' => '250.000€ - 300.000€',
            '{{property_type}}' => 'Piso',
            '{{property_surface}}' => '85 m²',
            '{{property_rooms}}' => '3',
            '{{property_bathrooms}}' => '2',
            '{{zone_name}}' => 'Madrid Centro',
            '{{zone_avg_price_m2}}' => '3.500€/m²',
            '{{agency_name}}' => 'Inmobiliaria Ejemplo',
            '{{agent_name}}' => 'María García',
            '{{valorador_contact_url}}' => 'https://wa.me/34600123456',
            '{{current_date}}' => date('d/m/Y'),
            '{{current_month}}' => date('F'),
            '{{current_year}}' => date('Y'),
            '{{price_per_sqm}}' => '2.941€/m²',
            '{{market_position}}' => 'Precio competitivo',
            '{{investment_potential}}' => 'Alto potencial',
            '{{roi_estimate}}' => '4.2% anual',
            '{{market_comparison}}' => '+5% sobre media de zona',
            '{{contenido_ia_generado}}' => 'Contenido generado por IA aparecerá aquí'
        ];
    }
}
?>
