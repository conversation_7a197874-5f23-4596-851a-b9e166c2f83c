<?php
// api/lib/BrevoService.php
declare(strict_types=1);

namespace Api\lib;

use Brevo\Client\Configuration;
use Brevo\Client\Api\TransactionalEmailsApi;
use Brevo\Client\Model\SendSmtpEmail;
use GuzzleHttp\Client; // El SDK de Brevo usa GuzzleHttp
use Api\lib\Logger;

class BrevoService {
    private ?TransactionalEmailsApi $brevoApiInstance = null;
    private string $defaultSenderEmail;
    private string $defaultSenderName;
    private bool $isConfigured = false;

    public function __construct() {
        if (defined('BREVO_API_KEY') && !empty(BREVO_API_KEY)) {
            try {
                $config = Configuration::getDefaultConfiguration()->setApiKey('api-key', BREVO_API_KEY);
                // Si necesitas especificar una región para la API (ej. para Europa: https://api.brevo.com/v3)
                // $config->setHost('https://api.brevo.com/v3'); 

                $this->brevoApiInstance = new TransactionalEmailsApi(
                    new Client(), // GuzzleHttp\Client
                    $config
                );
                $this->isConfigured = true;
                Logger::debug('BrevoService: Instancia creada con API Key.');
            } catch (\Throwable $th) {
                Logger::critical('BrevoService: Fallo al instanciar Brevo SDK: ' . $th->getMessage(), ['trace' => $th->getTraceAsString()]);
                $this->isConfigured = false;
            }
        } else {
            Logger::error('BrevoService: BREVO_API_KEY no está definida o está vacía. El servicio no funcionará.');
            $this->isConfigured = false;
        }
        $this->defaultSenderEmail = defined('DEFAULT_SENDER_EMAIL') ? DEFAULT_SENDER_EMAIL : '<EMAIL>';
        $this->defaultSenderName = defined('DEFAULT_SENDER_NAME') ? DEFAULT_SENDER_NAME : 'Sistema';
    }

    public function sendEmail(
        string $toEmail,
        string $toName,
        string $subject,
        string $htmlBody,
        string $textBody = '',
        ?string $fromEmail = null,
        ?string $fromName = null
    ): array {
        if (!$this->isConfigured || $this->brevoApiInstance === null) {
            $errorMsg = 'BrevoService no está configurado o falló la inicialización. Email no enviado.';
            Logger::error($errorMsg, ['to' => $toEmail, 'subject' => $subject]);
            return ['success' => false, 'message_id' => null, 'error' => $errorMsg];
        }

        $currentFromEmail = $fromEmail ?? $this->defaultSenderEmail;
        $currentFromName = $fromName ?? $this->defaultSenderName;

        $sendSmtpEmail = new SendSmtpEmail();
        $sendSmtpEmail['sender'] = ['name' => $currentFromName, 'email' => $currentFromEmail];
        $sendSmtpEmail['to'] = [[ 'email' => $toEmail, 'name' => $toName ]];
        $sendSmtpEmail['subject'] = $subject;
        $sendSmtpEmail['htmlContent'] = $htmlBody;
        if (!empty($textBody)) {
            $sendSmtpEmail['textContent'] = $textBody;
        } else {
            $sendSmtpEmail['textContent'] = strip_tags($htmlBody);
        }
        // Brevo maneja el tracking de aperturas/clics a nivel de cuenta/configuración de API
        // o se puede especificar por mensaje con `headers` o `params` si la API lo soporta así.
        // Por defecto, suele estar activado si lo tienes en tu cuenta Brevo.

        try {
            $result = $this->brevoApiInstance->sendTransacEmail($sendSmtpEmail);
            $messageId = $result->getMessageId(); // El SDK v8 devuelve un objeto con getMessageId()

            Logger::info("Email enviado a {$toEmail} vía Brevo. Message ID: {$messageId}", ['subject' => $subject]);
            return ['success' => true, 'message_id' => $messageId, 'error' => null];

        } catch (\Brevo\Client\ApiException $e) {
            $responseBody = $e->getResponseBody();
            $errorMessage = $e->getMessage();
            if (is_object($responseBody) || is_array($responseBody)) {
                 $decodedBody = json_decode(json_encode($responseBody), true); // Convertir objeto stdClass a array
                 $errorMessage = $decodedBody['message'] ?? $e->getMessage();
            }
            Logger::error("Error enviando email vía Brevo (ApiException) a {$toEmail}: " . $errorMessage, [
                'subject' => $subject, 
                'response_code' => $e->getCode(), 
                'response_body_preview' => substr(is_string($responseBody) ? $responseBody : json_encode($responseBody), 0, 200)
            ]);
            return ['success' => false, 'message_id' => null, 'error' => "Error Brevo API ({$e->getCode()}): " . $errorMessage];
        } catch (\Throwable $e) { // Para otros errores
            Logger::critical("Excepción enviando email vía Brevo a {$toEmail}: " . $e->getMessage(), ['trace' => $e->getTraceAsString(), 'subject' => $subject]);
            return ['success' => false, 'message_id' => null, 'error' => "Excepción Brevo: " . $e->getMessage()];
        }
    }
}
?> 