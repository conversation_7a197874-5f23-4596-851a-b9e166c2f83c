<?php

/**
 * Servicio para determinar estados granulares de suscripciones
 */
class SubscriptionStatusService {
    
    private $db;
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
    }
    
    /**
     * Obtiene el estado detallado de una suscripción
     */
    public function getDetailedSubscriptionStatus($user_id, $stripe_subscription_data = null) {
        try {
            // Si no se proporciona data de Stripe, obtenerla
            if (!$stripe_subscription_data) {
                $stripe_subscription_data = $this->getStripeSubscriptionData($user_id);
            }
            
            if (!$stripe_subscription_data) {
                return [
                    'status' => 'no_subscription',
                    'detailed_status' => 'no_subscription',
                    'user_message' => 'No tienes una suscripción activa.',
                    'action_required' => 'subscribe',
                    'is_recoverable' => false,
                    'priority' => 'high'
                ];
            }
            
            $stripe_status = $stripe_subscription_data['status'];
            $has_payment_method = $this->hasValidPaymentMethod($stripe_subscription_data['stripe_customer_id']);
            $recent_failures = $this->getRecentPaymentFailures($user_id);
            
            return $this->determineDetailedStatus($stripe_status, $has_payment_method, $recent_failures, $stripe_subscription_data);
            
        } catch (Exception $e) {
            error_log("[SubscriptionStatusService] Error obteniendo estado detallado: " . $e->getMessage());
            return [
                'status' => 'error',
                'detailed_status' => 'error',
                'user_message' => 'Error al verificar el estado de tu suscripción.',
                'action_required' => 'contact_support',
                'is_recoverable' => false,
                'priority' => 'high'
            ];
        }
    }
    
    /**
     * Determina el estado detallado basado en múltiples factores
     */
    private function determineDetailedStatus($stripe_status, $has_payment_method, $recent_failures, $subscription_data) {
        
        // Estados activos
        if ($stripe_status === 'active') {
            if (empty($recent_failures)) {
                return [
                    'status' => 'active',
                    'detailed_status' => 'active_healthy',
                    'user_message' => 'Tu suscripción está activa y funcionando correctamente.',
                    'action_required' => null,
                    'is_recoverable' => true,
                    'priority' => 'low'
                ];
            } else {
                return [
                    'status' => 'active',
                    'detailed_status' => 'active_with_recent_issues',
                    'user_message' => 'Tu suscripción está activa, pero hubo problemas de pago recientes.',
                    'action_required' => 'review_payment_method',
                    'is_recoverable' => true,
                    'priority' => 'medium'
                ];
            }
        }
        
        // Estados de trial
        if ($stripe_status === 'trialing') {
            $trial_end = $subscription_data['trial_end'] ?? null;
            $days_remaining = $trial_end ? ceil(($trial_end - time()) / (60 * 60 * 24)) : 0;
            
            if ($days_remaining > 3) {
                return [
                    'status' => 'trialing',
                    'detailed_status' => 'trial_active',
                    'user_message' => "Tu período de prueba está activo. Quedan {$days_remaining} días.",
                    'action_required' => $has_payment_method ? null : 'add_payment_method',
                    'is_recoverable' => true,
                    'priority' => 'low'
                ];
            } else {
                return [
                    'status' => 'trialing',
                    'detailed_status' => 'trial_ending_soon',
                    'user_message' => "Tu período de prueba termina en {$days_remaining} días. " . 
                                    ($has_payment_method ? 'El cobro se realizará automáticamente.' : 'Agrega un método de pago para continuar.'),
                    'action_required' => $has_payment_method ? null : 'add_payment_method',
                    'is_recoverable' => true,
                    'priority' => $has_payment_method ? 'low' : 'high'
                ];
            }
        }
        
        // Estados problemáticos
        if ($stripe_status === 'past_due') {
            $latest_failure = !empty($recent_failures) ? $recent_failures[0] : null;
            
            if ($has_payment_method) {
                if ($latest_failure && $latest_failure['failure_type'] === 'authentication_required') {
                    return [
                        'status' => 'past_due',
                        'detailed_status' => 'payment_authentication_required',
                        'user_message' => 'Tu banco requiere autenticación adicional para procesar el pago.',
                        'action_required' => 'authenticate_payment',
                        'is_recoverable' => true,
                        'priority' => 'high'
                    ];
                } else {
                    return [
                        'status' => 'past_due',
                        'detailed_status' => 'payment_failed_recoverable',
                        'user_message' => 'El último pago falló, pero puedes reintentarlo o actualizar tu método de pago.',
                        'action_required' => 'retry_payment',
                        'is_recoverable' => true,
                        'priority' => 'high'
                    ];
                }
            } else {
                return [
                    'status' => 'past_due',
                    'detailed_status' => 'payment_method_required',
                    'user_message' => 'Tu suscripción está vencida. Agrega un método de pago para reactivarla.',
                    'action_required' => 'add_payment_method',
                    'is_recoverable' => true,
                    'priority' => 'high'
                ];
            }
        }
        
        if ($stripe_status === 'unpaid') {
            return [
                'status' => 'unpaid',
                'detailed_status' => 'payment_failed_multiple_attempts',
                'user_message' => 'Tu suscripción está suspendida por fallos de pago repetidos.',
                'action_required' => $has_payment_method ? 'update_payment_method' : 'add_payment_method',
                'is_recoverable' => true,
                'priority' => 'critical'
            ];
        }
        
        if ($stripe_status === 'incomplete') {
            return [
                'status' => 'incomplete',
                'detailed_status' => 'setup_incomplete',
                'user_message' => 'Tu suscripción no se pudo activar completamente. Es posible que se requiera autenticación.',
                'action_required' => 'complete_setup',
                'is_recoverable' => true,
                'priority' => 'high'
            ];
        }
        
        if ($stripe_status === 'incomplete_expired') {
            return [
                'status' => 'incomplete_expired',
                'detailed_status' => 'setup_expired',
                'user_message' => 'El proceso de configuración de tu suscripción expiró.',
                'action_required' => 'restart_subscription',
                'is_recoverable' => false,
                'priority' => 'high'
            ];
        }
        
        if ($stripe_status === 'canceled') {
            $cancel_reason = $this->determineCancelReason($subscription_data, $recent_failures);
            
            return [
                'status' => 'canceled',
                'detailed_status' => $cancel_reason['detailed_status'],
                'user_message' => $cancel_reason['message'],
                'action_required' => $cancel_reason['action'],
                'is_recoverable' => $cancel_reason['recoverable'],
                'priority' => 'medium'
            ];
        }
        
        // Estado por defecto
        return [
            'status' => $stripe_status,
            'detailed_status' => 'unknown_status',
            'user_message' => 'Estado de suscripción desconocido. Contacta con soporte.',
            'action_required' => 'contact_support',
            'is_recoverable' => false,
            'priority' => 'medium'
        ];
    }
    
    /**
     * Verifica si el cliente tiene un método de pago válido
     */
    private function hasValidPaymentMethod($stripe_customer_id) {
        try {
            \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
            
            $customer = \Stripe\Customer::retrieve($stripe_customer_id);
            
            // Verificar método predeterminado en invoice_settings
            if ($customer->invoice_settings->default_payment_method) {
                return true;
            }
            
            // Verificar métodos adjuntos
            $payment_methods = \Stripe\PaymentMethod::all([
                'customer' => $stripe_customer_id,
                'type' => 'card',
                'limit' => 1
            ]);
            
            return !empty($payment_methods->data);
            
        } catch (\Stripe\Exception\ApiErrorException $e) {
            error_log("[SubscriptionStatusService] Error verificando método de pago: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obtiene fallos de pago recientes
     */
    private function getRecentPaymentFailures($user_id, $days = 7) {
        try {
            $sql = "SELECT * FROM payment_failures 
                    WHERE user_id = ? 
                    AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                    ORDER BY created_at DESC 
                    LIMIT 5";
            
            $stmt = $this->db->prepare($sql);
            if (!$stmt) {
                return [];
            }
            
            $stmt->bindParam(1, $user_id, PDO::PARAM_INT);
            $stmt->bindParam(2, $days, PDO::PARAM_INT);
            $stmt->execute();

            $failures = $stmt->fetchAll(PDO::FETCH_ASSOC);
            return $failures;
            
        } catch (Exception $e) {
            error_log("[SubscriptionStatusService] Error obteniendo fallos recientes: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Obtiene datos de suscripción de Stripe para un usuario
     */
    private function getStripeSubscriptionData($user_id) {
        try {
            $sql = "SELECT s.stripe_subscription_id, s.estado, u.stripe_customer_id
                    FROM suscripciones s
                    JOIN usuarios u ON s.user_id = u.id
                    WHERE s.user_id = ?
                    ORDER BY s.fecha_creacion DESC
                    LIMIT 1";
            
            $stmt = $this->db->prepare($sql);
            if (!$stmt) {
                return null;
            }
            
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $stmt->close();
            
            if (!$row) {
                return null;
            }
            
            // Obtener datos actualizados de Stripe
            \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
            $subscription = \Stripe\Subscription::retrieve($row['stripe_subscription_id']);
            
            return [
                'stripe_subscription_id' => $row['stripe_subscription_id'],
                'stripe_customer_id' => $row['stripe_customer_id'],
                'status' => $subscription->status,
                'trial_end' => $subscription->trial_end,
                'cancel_at_period_end' => $subscription->cancel_at_period_end,
                'canceled_at' => $subscription->canceled_at
            ];
            
        } catch (Exception $e) {
            error_log("[SubscriptionStatusService] Error obteniendo datos de suscripción: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Determina la razón de cancelación
     */
    private function determineCancelReason($subscription_data, $recent_failures) {
        if ($subscription_data['cancel_at_period_end']) {
            return [
                'detailed_status' => 'canceled_by_user',
                'message' => 'Cancelaste tu suscripción. Se mantendrá activa hasta el final del período actual.',
                'action' => 'reactivate_subscription',
                'recoverable' => true
            ];
        }
        
        if (!empty($recent_failures)) {
            return [
                'detailed_status' => 'canceled_payment_failure',
                'message' => 'Tu suscripción fue cancelada debido a fallos de pago repetidos.',
                'action' => 'restart_subscription',
                'recoverable' => true
            ];
        }
        
        return [
            'detailed_status' => 'canceled_unknown',
            'message' => 'Tu suscripción fue cancelada.',
            'action' => 'restart_subscription',
            'recoverable' => true
        ];
    }
}
?>
