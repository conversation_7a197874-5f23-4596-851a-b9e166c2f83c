<?php
declare(strict_types=1);

namespace Api\lib;

use Api\lib\Database;
use Api\lib\EmailVariablesService;

class EmailTemplateProcessor
{
    /**
     * Procesa una plantilla HTML con contenido generado por IA y variables del lead
     */
    public static function processTemplate(
        int $templateId, 
        string $aiGeneratedContent, 
        int $leadId,
        array $additionalVariables = []
    ): array {
        try {
            $db = Database::getInstance()->getConnection();
            
            // 1. Obtener la plantilla HTML
            $stmt = $db->prepare('
                SELECT 
                    nombre_interno_plantilla,
                    contenido_html,
                    asunto_predeterminado
                FROM email_plantillas_html 
                WHERE id = ? AND activa = 1
            ');
            $stmt->execute([$templateId]);
            $template = $stmt->fetch();
            
            if (!$template) {
                throw new \Exception("Plantilla con ID {$templateId} no encontrada o inactiva");
            }
            
            // 2. Obtener todas las variables estandarizadas para el lead
            $variables = EmailVariablesService::getVariablesForLead($leadId);
            
            // 3. Agregar variables adicionales si se proporcionan
            $variables = array_merge($variables, $additionalVariables);
            
            // 4. Primero reemplazar variables en el contenido IA
            $aiContentWithVariables = self::replaceVariables($aiGeneratedContent, $variables);

            // 5. Procesar el contenido IA para asegurar que sea HTML válido
            $processedAiContent = self::processAiContent($aiContentWithVariables);

            // 6. Insertar el contenido IA procesado en la plantilla
            $variables['{{contenido_ia_generado}}'] = $processedAiContent;
            
            // 6. Reemplazar todas las variables en el HTML
            $finalHtml = self::replaceVariables($template['contenido_html'], $variables);
            
            // 7. Procesar condicionales de Handlebars ({{#if}} y {{/if}})
            $finalHtml = self::processConditionals($finalHtml, $variables);
            
            // 8. Limpiar variables no reemplazadas
            $finalHtml = self::cleanUnusedVariables($finalHtml);
            
            return [
                'success' => true,
                'html' => $finalHtml,
                'template_name' => $template['nombre_interno_plantilla'],
                'variables_used' => array_keys($variables),
                'error' => null
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'html' => null,
                'template_name' => null,
                'variables_used' => [],
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Procesa el contenido generado por IA para asegurar HTML válido
     */
    private static function processAiContent(string $content): string
    {
        // Si el contenido ya tiene etiquetas HTML, lo dejamos como está
        if (strpos($content, '<') !== false && strpos($content, '>') !== false) {
            return $content;
        }
        
        // Si es texto plano, lo convertimos a HTML básico
        $lines = explode("\n", trim($content));
        $htmlContent = '';
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }
            
            // Detectar si es un título (línea que termina con :)
            if (substr($line, -1) === ':') {
                $htmlContent .= '<h3>' . htmlspecialchars($line) . '</h3>' . "\n";
            } else {
                $htmlContent .= '<p>' . htmlspecialchars($line) . '</p>' . "\n";
            }
        }
        
        return $htmlContent;
    }
    
    /**
     * Reemplaza todas las variables en el texto
     */
    private static function replaceVariables(string $text, array $variables): string
    {
        return str_replace(array_keys($variables), array_values($variables), $text);
    }
    
    /**
     * Procesa condicionales simples de Handlebars {{#if variable}} y {{/if}}
     */
    private static function processConditionals(string $html, array $variables): string
    {
        // Patrón para encontrar bloques condicionales
        $pattern = '/\{\{#if\s+([^}]+)\}\}(.*?)\{\{\/if\}\}/s';
        
        return preg_replace_callback($pattern, function($matches) use ($variables) {
            $variableName = '{{' . trim($matches[1]) . '}}';
            $content = $matches[2];
            
            // Si la variable existe y no está vacía, mostrar el contenido
            if (isset($variables[$variableName]) && !empty($variables[$variableName])) {
                return $content;
            }
            
            // Si no existe o está vacía, no mostrar nada
            return '';
        }, $html);
    }
    
    /**
     * Limpia variables no reemplazadas del HTML final
     */
    private static function cleanUnusedVariables(string $html): string
    {
        // Remover variables no reemplazadas (que siguen con formato {{variable}})
        $html = preg_replace('/\{\{[^}]+\}\}/', '', $html);
        
        // Limpiar espacios extra y líneas vacías
        $html = preg_replace('/\n\s*\n/', "\n", $html);
        
        return trim($html);
    }
    
    /**
     * Obtiene información de una plantilla sin procesarla
     */
    public static function getTemplateInfo(int $templateId): ?array
    {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare('
                SELECT 
                    id,
                    nombre_interno_plantilla,
                    descripcion,
                    asunto_predeterminado,
                    tipo_plantilla,
                    activa
                FROM email_plantillas_html 
                WHERE id = ?
            ');
            $stmt->execute([$templateId]);
            $template = $stmt->fetch();
            
            return $template ?: null;
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Lista todas las plantillas disponibles para secuencias
     */
    public static function getAvailableTemplates(): array
    {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->query('
                SELECT 
                    id,
                    nombre_interno_plantilla,
                    descripcion,
                    tipo_plantilla
                FROM email_plantillas_html 
                WHERE activa = 1 AND tipo_plantilla = "secuencia_email"
                ORDER BY nombre_interno_plantilla
            ');
            
            return $stmt->fetchAll() ?: [];
            
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * Valida que una plantilla sea compatible con secuencias
     */
    public static function validateTemplate(int $templateId): bool
    {
        $template = self::getTemplateInfo($templateId);
        
        if (!$template || !$template['activa']) {
            return false;
        }
        
        // Verificar que tenga el placeholder para contenido IA
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare('SELECT contenido_html FROM email_plantillas_html WHERE id = ?');
        $stmt->execute([$templateId]);
        $content = $stmt->fetchColumn();
        
        return $content && strpos($content, '{{contenido_ia_generado}}') !== false;
    }
    
    /**
     * Genera una vista previa de la plantilla con datos de ejemplo
     */
    public static function generatePreview(int $templateId): array
    {
        // Datos de ejemplo para la vista previa
        $exampleVariables = [
            '{{lead_name}}' => 'Juan Pérez',
            '{{property_address}}' => 'Calle Mayor 123, Madrid',
            '{{property_value_range}}' => '250.000 € - 300.000 €',
            '{{agency_name}}' => 'Inmobiliaria Ejemplo',
            '{{agent_name}}' => 'María García',
            '{{zone_name}}' => 'Madrid Centro',
            '{{agency_color}}' => '#2563eb',
            '{{year}}' => date('Y'),
            '{{contenido_ia_generado}}' => '
                <p>Estimado Juan,</p>
                <p>Gracias por confiar en nosotros para la valoración de tu propiedad. Hemos completado el análisis y queremos compartir contigo los resultados.</p>
                <p>Tu propiedad tiene un gran potencial en el mercado actual.</p>
                <p>¿Te gustaría que conversemos sobre los próximos pasos?</p>
            '
        ];
        
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare('SELECT contenido_html FROM email_plantillas_html WHERE id = ? AND activa = 1');
            $stmt->execute([$templateId]);
            $template = $stmt->fetchColumn();
            
            if (!$template) {
                throw new \Exception("Plantilla no encontrada");
            }
            
            $html = self::replaceVariables($template, $exampleVariables);
            $html = self::processConditionals($html, $exampleVariables);
            $html = self::cleanUnusedVariables($html);
            
            return [
                'success' => true,
                'html' => $html,
                'error' => null
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'html' => null,
                'error' => $e->getMessage()
            ];
        }
    }
}
?>
