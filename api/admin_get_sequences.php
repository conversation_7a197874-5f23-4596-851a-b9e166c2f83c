<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en admin_get_sequences: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

try {
    $sql = "
        SELECT 
            s.*, 
            COUNT(ss.id) as step_count,
            GROUP_CONCAT(psa.plan_id) as assigned_plan_ids
        FROM sequences s
        LEFT JOIN sequence_steps ss ON s.id = ss.sequence_id
        LEFT JOIN plan_sequence_assignments psa ON s.id = psa.sequence_id
        GROUP BY s.id
        ORDER BY s.created_at DESC
    ";

    $stmt = $pdo->query($sql);
    $sequences = [];
    while ($row = $stmt->fetch()) {
        $row['is_active'] = (bool)$row['is_active'];
        $row['step_count'] = (int)$row['step_count'];
        $row['assigned_plan_ids'] = $row['assigned_plan_ids'] ? array_map('intval', explode(',', $row['assigned_plan_ids'])) : [];
        $sequences[] = $row;
    }

    echo json_encode(['success' => true, 'data' => $sequences]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al obtener secuencias: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error en la consulta a la base de datos.']);
    exit;
}
