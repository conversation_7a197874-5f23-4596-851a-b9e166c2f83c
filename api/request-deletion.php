<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
ob_start();
 // <--- INTEGRAR BOOTSTRAP
 // Usar config central de CORS
// ENCABEZADOS CORS Y MANEJO DE OPTIONS AHORA EN cors.php
header('Content-Type: application/json; charset=utf-8');
if (!function_exists('custom_log_request_deletion')) {
    function custom_log_request_deletion($message) {
        $logFile = __DIR__ . '/debug_profile.log'; // Usamos el mismo log de perfil
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [request-deletion-LOG] " . $message . PHP_EOL);
    }
}
custom_log_request_deletion("--- [request-deletion.php] INICIADO ---");
if (ob_get_length()) ob_clean(); // Limpiar buffer antes de cualquier salida JSON
// Autenticación JWT
$user_id_from_token = null;
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_set('display_errors', '0');
        try {
            // Usar constante JWT_SECRET de bootstrap.php
            if (empty(JWT_SECRET)) { 
                throw new Exception('JWT_SECRET no configurada (constante).');
            }
            $decoded = Firebase\JWT\JWT::decode($token, new Firebase\JWT\Key(JWT_SECRET, 'HS256'));
            if (isset($decoded->user_id)) { 
                $user_id_from_token = $decoded->user_id;
                custom_log_request_deletion("JWT User ID: $user_id_from_token");
            } else { 
                throw new Exception('Token JWT inválido: no contiene user_id.');
            }
        } catch (Exception $e) {
            custom_log_request_deletion("JWT Error: " . $e->getMessage());
            http_response_code(401);
            if(ob_get_length()) ob_clean();
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors);
        }
    } else {
        http_response_code(401); 
        if(ob_get_length()) ob_clean();
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']); exit();
    }
} else {
    http_response_code(401); 
    if(ob_get_length()) ob_clean();
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']); exit();
}
if (empty($user_id_from_token)) {
    http_response_code(401); 
    if(ob_get_length()) ob_clean();
    echo json_encode(['success' => false, 'message' => 'Autenticación fallida (ID de usuario no resuelto).']); exit();
}
$user_id = $user_id_from_token;
$response = ['success' => false, 'message' => 'Error desconocido al procesar la solicitud.'];

try {
    custom_log_request_deletion("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_request_deletion("Conexión PDO establecida a través de la clase Database.");

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        custom_log_request_deletion("[POST Request] Procesando para user_id: " . $user_id);
        $pdo->beginTransaction();
        // Solo desactivar la cuenta
        $stmt_deactivate = $pdo->prepare("UPDATE usuarios SET activo = 0, fecha_modificacion = NOW() WHERE id = :user_id AND activo = 1");
        $stmt_deactivate->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt_deactivate->execute();
        $deactivated_rows = $stmt_deactivate->rowCount();
        if ($deactivated_rows > 0) {
            $pdo->commit();
            custom_log_request_deletion("Cuenta desactivada para user_id: " . $user_id);
            $response['success'] = true;
            $response['message'] = 'Tu cuenta ha sido desactivada. Serás redirigido.';
        } else {
            // Verificar si ya estaba inactiva
            $check_stmt = $pdo->prepare("SELECT activo FROM usuarios WHERE id = :user_id");
            $check_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $check_stmt->execute();
            $user_status = $check_stmt->fetch(PDO::FETCH_ASSOC);
            if ($user_status && $user_status['activo'] == 0) {
                $pdo->rollBack(); // No hubo cambios reales si ya estaba inactiva
                custom_log_request_deletion("Cuenta ya estaba inactiva para user_id: " . $user_id);
                $response['success'] = true; // Considerarlo éxito si ya está inactiva
                $response['message'] = 'Esta cuenta ya ha sido desactivada.';
            } else {
                $pdo->rollBack();
                custom_log_request_deletion("No se afectaron filas al desactivar user_id: " . $user_id . " (error inesperado o usuario no encontrado)");
                $response['message'] = 'No se pudo procesar la solicitud en este momento. Inténtalo de nuevo más tarde.';
            }
        }
        echo json_encode($response);
    } else {
        custom_log_request_deletion("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Método no permitido. Solo POST.']);
    }
} catch (PDOException $e) {
    custom_log_request_deletion("PDOException: " . $e->getMessage());
    http_response_code(500);
    if(ob_get_length()) ob_clean();
    echo json_encode(['success' => false, 'message' => 'Error de BD: ' . $e->getMessage()]);
    exit(); 
} catch (Exception $e) {
    custom_log_request_deletion("General Exception: " . $e->getMessage());
    $http_code = 500;
    if (strpos($e->getMessage(), 'Token expirado') !== false || strpos($e->getMessage(), 'Token inválido') !== false) {
        $http_code = 401;
    }
    http_response_code($http_code);
    if(ob_get_length()) ob_clean();
    echo json_encode(['success' => false, 'message' => 'Error del servidor: ' . $e->getMessage()]);
    exit(); 
}
custom_log_request_deletion("--- [request-deletion.php] FINALIZADO ---");
ob_end_flush();
exit;
?>