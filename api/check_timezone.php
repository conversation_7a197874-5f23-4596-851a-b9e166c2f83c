<?php
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;

try {
    // Conectar a la base de datos usando la clase Database (que ya tiene la configuración de zona horaria)
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "=== VERIFICACIÓN DE ZONA HORARIA ===\n\n";
    
    // 1. Verificar zona horaria de PHP
    echo "1. Zona horaria de PHP: " . date_default_timezone_get() . "\n";
    echo "   Hora actual PHP: " . date('Y-m-d H:i:s') . "\n\n";
    
    // 2. Verificar zona horaria de MySQL
    $stmt = $pdo->query("SELECT @@global.time_zone AS global_tz, @@session.time_zone AS session_tz, NOW() AS mysql_now, UTC_TIMESTAMP() AS mysql_utc");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "2. Zona horaria de MySQL:\n";
    echo "   Global: " . $result['global_tz'] . "\n";
    echo "   Session: " . $result['session_tz'] . "\n";
    echo "   NOW(): " . $result['mysql_now'] . "\n";
    echo "   UTC_TIMESTAMP(): " . $result['mysql_utc'] . "\n\n";
    
    // 3. Verificar algunas fechas de ejemplo de la base de datos
    echo "3. Ejemplos de fechas en la base de datos:\n";
    
    // Verificar lead_emails_historial
    $stmt = $pdo->query("SELECT fecha_creacion, abierto_timestamp, clickeado_timestamp FROM lead_emails_historial ORDER BY id DESC LIMIT 3");
    $emails = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   lead_emails_historial (últimos 3):\n";
    foreach ($emails as $email) {
        echo "     fecha_creacion: " . ($email['fecha_creacion'] ?? 'NULL') . "\n";
        echo "     abierto_timestamp: " . ($email['abierto_timestamp'] ?? 'NULL') . "\n";
        echo "     clickeado_timestamp: " . ($email['clickeado_timestamp'] ?? 'NULL') . "\n";
        echo "     ---\n";
    }
    
    // Verificar valorador_valoraciones
    $stmt = $pdo->query("SELECT fecha_creacion FROM valorador_valoraciones ORDER BY id DESC LIMIT 3");
    $valoraciones = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n   valorador_valoraciones (últimas 3):\n";
    foreach ($valoraciones as $valoracion) {
        echo "     fecha_creacion: " . ($valoracion['fecha_creacion'] ?? 'NULL') . "\n";
    }
    
    // 4. Verificar que la zona horaria ya está configurada por la clase Database
    echo "\n4. Verificando configuración actual después de usar Database class...\n";
    $stmt = $pdo->query("SELECT @@session.time_zone AS session_tz, NOW() AS mysql_now_configured");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "   Zona horaria de sesión actual: " . $result['session_tz'] . "\n";
    echo "   NOW() configurado: " . $result['mysql_now_configured'] . "\n";
    
    // 5. Probar inserción con nueva zona horaria
    echo "\n5. Probando inserción con zona horaria Madrid...\n";
    $pdo->exec("CREATE TEMPORARY TABLE test_timezone (id INT AUTO_INCREMENT PRIMARY KEY, fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP)");
    $pdo->exec("INSERT INTO test_timezone () VALUES ()");
    
    $stmt = $pdo->query("SELECT fecha_creacion FROM test_timezone");
    $test_result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "   Fecha insertada con NOW(): " . $test_result['fecha_creacion'] . "\n";
    
    // Comparar con fecha de PHP
    echo "   Fecha actual de PHP: " . date('Y-m-d H:i:s') . "\n";
    
    echo "\n=== FIN DE VERIFICACIÓN ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
