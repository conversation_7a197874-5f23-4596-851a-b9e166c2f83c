<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// /api/invoice-pdf.php - Obtiene la URL para descargar una factura en PDF
// ini_set('display_errors', 1); // Eliminado
// ini_set('display_startup_errors', 1); // Eliminado
// error_reporting(E_ALL); // Eliminado
// --- Logger Setup ---
if (!function_exists('custom_log_invoice_pdf')) {
    function custom_log_invoice_pdf($message) {
        $logFile = __DIR__ . '/debug_invoice_pdf.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_invoice_pdf("--- [invoice-pdf.php] INICIADO ---");
// Incluir bootstrap para configuración global, carga de .env, autoloader, manejo de errores, etc.
// --- Autoload y Carga de .env ---
// $autoloadPath = __DIR__ . '/vendor/autoload.php'; // Eliminado, bootstrap.php lo maneja
// custom_log_invoice_pdf("Intentando cargar autoload desde: " . $autoloadPath);
// if (!file_exists($autoloadPath)) {
//     custom_log_invoice_pdf("CRÍTICO: vendor/autoload.php NO ENCONTRADO en " . $autoloadPath);
//     http_response_code(500);
//     echo json_encode(['success' => false, 'message' => 'Error de configuración del servidor (autoload).']);
//     exit();
// }
// require_once $autoloadPath; // Eliminado
// custom_log_invoice_pdf("vendor/autoload.php cargado correctamente.");
// Cargar variables de entorno
// $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); // Eliminado, bootstrap.php lo maneja
// try {
//     $dotenv->load(); // Eliminado
//     custom_log_invoice_pdf(".env cargado correctamente.");
// } catch (Exception $e) {
//     custom_log_invoice_pdf("Error al cargar .env: " . $e->getMessage());
//     http_response_code(500);
//     echo json_encode(['success' => false, 'message' => 'Error de configuración del servidor (.env error).']);
//     exit();
// }
// --- Verificación de autenticación ---
$userId = null;
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
custom_log_invoice_pdf("Authorization header: " . ($authHeader ? 'Presente (token oculto)' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            custom_log_invoice_pdf("Intentando decodificar JWT...");
            $key = $_ENV['JWT_SECRET_KEY'];
            // Inicio de supresión de errores para JWT::decode
            $original_display_errors = ini_get('display_errors');
            ini_set('display_errors', '0');
            // Versión actualizada de JWT::decode compatible con versiones recientes
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            // Restaurar configuración de display_errors
            ini_set('display_errors', $original_display_errors);
            // Fin de supresión de errores
            $userId = $decoded->user_id;
            custom_log_invoice_pdf("JWT decodificado correctamente. User ID: " . $userId);
        } catch (Exception $e) {
            custom_log_invoice_pdf("Error al decodificar JWT: " . $e->getMessage());
            // Si hay un error aquí, es importante no tener salida HTML de error PHP
             if ($original_display_errors === false || $original_display_errors === '' || $original_display_errors === null) {
                // Si no se pudo obtener el valor original, simplemente apagarlo por si acaso.
                // Esto es improbable si bootstrap.php funciona bien.
                ini_set('display_errors', '0');
            } else {
                ini_set('display_errors', $original_display_errors);
            }
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        }
    } else {
        custom_log_invoice_pdf("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_invoice_pdf("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Conexión a la base de datos ---
try {
    custom_log_invoice_pdf("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_invoice_pdf("Conexión PDO a la base de datos establecida correctamente.");
} catch (Exception $e) {
    custom_log_invoice_pdf("Error de conexión a la base de datos: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos.']);
    exit();
}

// --- Obtener URL de la factura ---
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Verificar que se proporcionó un ID de factura
    if (!isset($_GET['invoice_id']) || empty($_GET['invoice_id'])) {
        custom_log_invoice_pdf("No se proporcionó ID de factura.");
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Se requiere ID de factura.']);
        exit();
    }
    $invoiceId = $_GET['invoice_id'];
    custom_log_invoice_pdf("[GET Request] Procesando para user_id: " . $userId . ", invoice_id: " . $invoiceId);
    // Consulta a la base de datos para obtener el stripe_customer_id
    $query = "SELECT stripe_customer_id FROM usuarios WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $stripeCustomerId = $row['stripe_customer_id'];
        custom_log_invoice_pdf("Stripe Customer ID encontrado: " . $stripeCustomerId);
        // Usar la API real de Stripe para obtener la URL de la factura
        try {
            \Stripe\Stripe::setApiKey($_ENV['STRIPE_SECRET_KEY']);
            // Recuperar la factura de Stripe
            $invoice = \Stripe\Invoice::retrieve($invoiceId);
            // Verificar que la factura pertenece al cliente
            if ($invoice->customer !== $stripeCustomerId) {
                custom_log_invoice_pdf("La factura no pertenece a este cliente. Customer ID de la factura: " . $invoice->customer);
                throw new Exception('La factura no pertenece a este cliente.');
            }
            // Obtener la URL del PDF de la factura
            $pdfUrl = $invoice->invoice_pdf;
            custom_log_invoice_pdf("URL de factura obtenida correctamente: " . $pdfUrl);
        } catch (\Exception $e) {
            custom_log_invoice_pdf("Error al obtener la factura de Stripe: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Error al obtener la factura: ' . $e->getMessage()]);
            exit();
        }
        echo json_encode(['success' => true, 'url' => $pdfUrl]);
        exit();
    } else {
        custom_log_invoice_pdf("No se encontró información del cliente para el usuario ID: " . $userId);
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'No se encontró información del cliente.']);
        exit();
    }
} else {
    custom_log_invoice_pdf("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}
