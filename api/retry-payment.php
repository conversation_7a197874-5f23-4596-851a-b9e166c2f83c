<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Logger;

header('Content-Type: application/json');

function custom_log_retry_payment($message) {
    Logger::info("[retry-payment.php] " . $message);
}

custom_log_retry_payment("--- [retry-payment.php] INICIADO ---");

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}

// Verificar autenticación JWT
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token de autorización requerido.']);
    exit();
}

$token = $matches[1];

try {
    $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key(JWT_SECRET, 'HS256'));
    $userId = $decoded->user_id ?? null;
    $agencyId = $decoded->agency_id ?? null;
    
    if (!$userId || !$agencyId) {
        throw new Exception('Token inválido');
    }
    
    custom_log_retry_payment("Usuario autenticado: ID = $userId, Agency = $agencyId");
    
} catch (Exception $e) {
    custom_log_retry_payment("Error de autenticación: " . $e->getMessage());
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token inválido.']);
    exit();
}

try {
    // Conectar a la base de datos
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_retry_payment("Conexión a la base de datos establecida.");

    // Obtener el stripe_customer_id del usuario
    $query = "SELECT stripe_customer_id FROM usuarios WHERE id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user || empty($user['stripe_customer_id'])) {
        throw new Exception('Usuario no encontrado o sin Stripe Customer ID');
    }
    
    $stripeCustomerId = $user['stripe_customer_id'];
    custom_log_retry_payment("Stripe Customer ID: $stripeCustomerId");
    
    // Obtener la suscripción activa del usuario
    $query = "SELECT stripe_subscription_id FROM subscriptions WHERE user_id = ? AND status IN ('past_due', 'unpaid') ORDER BY created_at DESC LIMIT 1";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$userId]);
    $subscription = $stmt->fetch();
    
    if (!$subscription) {
        throw new Exception('No se encontró una suscripción con pagos pendientes');
    }
    
    $stripeSubscriptionId = $subscription['stripe_subscription_id'];
    custom_log_retry_payment("Stripe Subscription ID: $stripeSubscriptionId");
    
    // Inicializar Stripe
    \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
    
    // Obtener la suscripción de Stripe
    $stripeSubscription = \Stripe\Subscription::retrieve($stripeSubscriptionId);
    
    if (!in_array($stripeSubscription->status, ['past_due', 'unpaid'])) {
        throw new Exception('La suscripción no tiene pagos pendientes');
    }
    
    // Obtener la factura más reciente que está abierta
    $invoices = \Stripe\Invoice::all([
        'customer' => $stripeCustomerId,
        'subscription' => $stripeSubscriptionId,
        'status' => 'open',
        'limit' => 1
    ]);

    if (empty($invoices->data)) {
        // Si no hay facturas abiertas, intentar crear una nueva factura para la suscripción
        custom_log_retry_payment("No hay facturas abiertas, creando nueva factura para la suscripción");

        try {
            $newInvoice = \Stripe\Invoice::create([
                'customer' => $stripeCustomerId,
                'subscription' => $stripeSubscriptionId,
                'auto_advance' => false // No finalizar automáticamente
            ]);

            // Finalizar la factura para que sea pagable
            $invoice = \Stripe\Invoice::finalizeInvoice($newInvoice->id);
            custom_log_retry_payment("Nueva factura creada y finalizada: " . $invoice->id);

        } catch (Exception $e) {
            throw new Exception('No se pudo crear una nueva factura: ' . $e->getMessage());
        }
    } else {
        $invoice = $invoices->data[0];
        custom_log_retry_payment("Factura existente encontrada: " . $invoice->id);
    }

    // Verificar si la factura ya tiene un PaymentIntent
    if ($invoice->payment_intent) {
        $paymentIntent = \Stripe\PaymentIntent::retrieve($invoice->payment_intent);
        custom_log_retry_payment("PaymentIntent existente: " . $paymentIntent->id . " - Estado: " . $paymentIntent->status);

        // Si el PaymentIntent está cancelado o falló, crear uno nuevo
        if (in_array($paymentIntent->status, ['canceled', 'failed'])) {
            custom_log_retry_payment("PaymentIntent en estado " . $paymentIntent->status . ", creando nuevo PaymentIntent");

            // Crear un nuevo PaymentIntent para la factura
            $newPaymentIntent = \Stripe\PaymentIntent::create([
                'amount' => $invoice->amount_due,
                'currency' => $invoice->currency,
                'customer' => $stripeCustomerId,
                'payment_method' => $stripeSubscription->default_payment_method,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => 'https://app.inmoautomation.com/suscripcion',
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'always'
                ],
                'metadata' => [
                    'invoice_id' => $invoice->id,
                    'subscription_id' => $stripeSubscriptionId
                ]
            ]);

            custom_log_retry_payment("Nuevo PaymentIntent creado: " . $newPaymentIntent->id . " - Estado: " . $newPaymentIntent->status);
            $paymentIntent = $newPaymentIntent;
        }
    } else {
        // Si no hay PaymentIntent, intentar pagar la factura directamente
        try {
            $paidInvoice = $invoice->pay([
                'payment_method' => $stripeSubscription->default_payment_method,
                'expand' => ['payment_intent']
            ]);
            $invoice = $paidInvoice;

            if ($invoice->payment_intent) {
                $paymentIntent = \Stripe\PaymentIntent::retrieve($invoice->payment_intent);
                custom_log_retry_payment("PaymentIntent del pago directo: " . $paymentIntent->id . " - Estado: " . $paymentIntent->status);
            }
        } catch (\Stripe\Exception\CardException $cardError) {
            // Si falla el pago directo, manejar el error más adelante
            throw $cardError;
        }
    }
    
    // Manejar el resultado basado en el estado del PaymentIntent
    if (isset($paymentIntent)) {
        custom_log_retry_payment("Procesando resultado del PaymentIntent: " . $paymentIntent->status);

        if ($paymentIntent->status === 'succeeded') {
            // Pago exitoso - actualizar estado en BD
            $updateQuery = "UPDATE suscripciones SET estado = 'active' WHERE stripe_subscription_id = ?";
            $updateStmt = $pdo->prepare($updateQuery);
            $updateStmt->execute([$stripeSubscriptionId]);
            $updateStmt->close();

            custom_log_retry_payment("Pago exitoso - suscripción reactivada");

            echo json_encode([
                'success' => true,
                'message' => 'Pago procesado exitosamente. Tu suscripción ha sido reactivada.',
                'status' => 'paid',
                'invoice_id' => $invoice->id
            ]);

        } else if ($paymentIntent->status === 'requires_action') {
            custom_log_retry_payment("Pago requiere autenticación 3DS");

            echo json_encode([
                'success' => true,
                'message' => 'El pago requiere autenticación adicional.',
                'status' => 'requires_action',
                'payment_intent' => [
                    'id' => $paymentIntent->id,
                    'client_secret' => $paymentIntent->client_secret
                ],
                'invoice_id' => $invoice->id
            ]);

        } else if ($paymentIntent->status === 'requires_payment_method') {
            throw new Exception('El método de pago no es válido. Por favor, actualiza tu método de pago.');

        } else {
            throw new Exception('El pago falló con estado: ' . $paymentIntent->status);
        }
    } else if (isset($invoice) && $invoice->status === 'paid') {
        // Factura pagada directamente
        $updateQuery = "UPDATE suscripciones SET estado = 'active' WHERE stripe_subscription_id = ?";
        $updateStmt = $pdo->prepare($updateQuery);
        $updateStmt->execute([$stripeSubscriptionId]);
        $updateStmt->close();

        custom_log_retry_payment("Factura pagada directamente - suscripción reactivada");

        echo json_encode([
            'success' => true,
            'message' => 'Pago procesado exitosamente. Tu suscripción ha sido reactivada.',
            'status' => 'paid',
            'invoice_id' => $invoice->id
        ]);
    } else {
        throw new Exception('No se pudo procesar el pago. Estado desconocido.');
    }
    
} catch (\Stripe\Exception\CardException $e) {
    custom_log_retry_payment("Error de tarjeta: " . $e->getMessage());

    // Verificar si es un error que requiere autenticación
    if (strpos($e->getMessage(), 'requires additional user action') !== false) {
        // Obtener el PaymentIntent de la factura para autenticación
        try {
            $invoice = \Stripe\Invoice::retrieve($invoice->id);
            if ($invoice->payment_intent) {
                $paymentIntent = \Stripe\PaymentIntent::retrieve($invoice->payment_intent);

                custom_log_retry_payment("Pago requiere autenticación 3DS - PaymentIntent: " . $paymentIntent->id);

                echo json_encode([
                    'success' => true,
                    'message' => 'El pago requiere autenticación adicional.',
                    'status' => 'requires_action',
                    'payment_intent' => [
                        'id' => $paymentIntent->id,
                        'client_secret' => $paymentIntent->client_secret
                    ],
                    'invoice_id' => $invoice->id
                ]);
                exit();
            }
        } catch (Exception $piError) {
            custom_log_retry_payment("Error obteniendo PaymentIntent: " . $piError->getMessage());
        }
    }

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Error con la tarjeta: ' . ($e->getDeclineCode() ?: $e->getMessage()),
        'error_type' => 'card_error'
    ]);
    
} catch (\Stripe\Exception\ApiErrorException $e) {
    custom_log_retry_payment("Error de Stripe API: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Error al procesar el pago: ' . $e->getMessage(),
        'error_type' => 'stripe_error'
    ]);
    
} catch (Exception $e) {
    custom_log_retry_payment("Error general: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_type' => 'general_error'
    ]);
}

$db->close();
custom_log_retry_payment("--- [retry-payment.php] FINALIZADO ---");
?>
