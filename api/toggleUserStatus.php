<?php
require_once __DIR__ . '/config/bootstrap.php';

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

header('Content-Type: application/json');

// --- Autenticación y Autorización ---
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';

if (!$authHeader || !preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token no proporcionado o formato incorrecto']);
    exit();
}

$token = $matches[1];

try {
    $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
    $userRoles = $decoded->roles ?? [];

    if (!is_array($userRoles) || !in_array('admin', $userRoles)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Acceso denegado. Se requiere rol de administrador.']);
        exit();
    }
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.', 'error' => $e->getMessage()]);
    exit();
}
// --- Fin Autenticación ---


try {
    $data = json_decode(file_get_contents('php://input'), true);

    if (!isset($data['id']) || !isset($data['activo'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Datos incompletos']);
        exit();
    }

    $pdo = Api\lib\Database::getInstance()->getConnection();

    $stmt = $pdo->prepare("UPDATE usuarios SET activo = ? WHERE id = ?");
    $stmt->execute([$data['activo'], $data['id']]);

    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => 'Estado del usuario actualizado correctamente']);
    } else {
        echo json_encode(['success' => false, 'message' => 'No se encontró al usuario o el estado ya era el mismo.']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error al actualizar el estado del usuario', 'error' => $e->getMessage()]);
}
