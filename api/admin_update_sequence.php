<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit;
}

if (empty($input['id']) || empty($input['name']) || empty($input['trigger_event'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID, name, and trigger event are required.']);
    exit;
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}
$conn->set_charset(DB_CHARSET);

$conn->begin_transaction();

try {
    $sequence_id = (int)$input['id'];
    $name = trim($input['name']);
    $description = trim($input['description'] ?? '');
    $trigger_event = $input['trigger_event'];
    $is_active = isset($input['is_active']) ? (int)$input['is_active'] : 0;

    // 1. Update main sequence
    $sql_update = "UPDATE sequences SET name = ?, description = ?, trigger_event = ?, is_active = ? WHERE id = ?";
    $stmt_update = $conn->prepare($sql_update);
    if (!$stmt_update) {
        throw new Exception("Prepare failed (update): " . $conn->error);
    }
    $stmt_update->bind_param('ssssi', $name, $description, $trigger_event, $is_active, $sequence_id);
    if (!$stmt_update->execute()) {
        throw new Exception("Execute failed (update): " . $stmt_update->error);
    }
    $stmt_update->close();

    // 2. Delete old assignments
    $sql_delete = "DELETE FROM plan_sequence_assignments WHERE sequence_id = ?";
    $stmt_delete = $conn->prepare($sql_delete);
    if (!$stmt_delete) {
        throw new Exception("Prepare failed (delete assignments): " . $conn->error);
    }
    $stmt_delete->bind_param('i', $sequence_id);
    if (!$stmt_delete->execute()) {
        throw new Exception("Execute failed (delete assignments): " . $stmt_delete->error);
    }
    $stmt_delete->close();
    
    // 3. Insert new assignments
    $plan_ids = $input['assigned_plan_ids'] ?? [];
    if (!empty($plan_ids)) {
        $sql_assign = "INSERT IGNORE INTO plan_sequence_assignments (plan_id, sequence_id) VALUES (?, ?)";
        $stmt_assign = $conn->prepare($sql_assign);
        if (!$stmt_assign) {
            throw new Exception("Prepare failed (assignment): " . $conn->error);
        }
        foreach ($plan_ids as $plan_id) {
            $int_plan_id = (int)$plan_id;
            $stmt_assign->bind_param('ii', $int_plan_id, $sequence_id);
            if (!$stmt_assign->execute()) {
                throw new Exception("Execute failed (assignment): " . $stmt_assign->error);
            }
        }
        $stmt_assign->close();
    }

    $conn->commit();
    echo json_encode(['success' => true, 'message' => 'Sequence updated successfully.']);

} catch (Exception $e) {
    $conn->rollback();
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database transaction failed: ' . $e->getMessage()]);
} finally {
    $conn->close();
}
