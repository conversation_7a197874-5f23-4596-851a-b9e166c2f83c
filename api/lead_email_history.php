<?php
declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
// --- Importar clases de JWT ---
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;

// Función de log local (considerar reemplazar con Logger centralizado de bootstrap.php)
if (!function_exists('custom_log_lead_history')) {
    function custom_log_lead_history($message) {
        $logFile = __DIR__ . '/logs/debug_lead_email_history.log';
        if (!is_dir(__DIR__ . '/logs')) {
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                error_log("Advertencia [lead_email_history]: No se pudo crear el directorio de logs: " . __DIR__ . '/logs');
                return;
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [lead_email_history] {$message}\n", 3, $logFile);
    }
}

// --- Función para obtener y decodificar el token JWT localmente ---
function get_decoded_jwt_payload_lead_history() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded;
            } catch (\Throwable $e) {
                // custom_log_lead_history("JWT Decode Error: " . $e->getMessage());
                if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                    ini_set('display_errors', $previous_display_errors);
                }
            }
        }
    }
    return null;
}

$decoded_payload = get_decoded_jwt_payload_lead_history();

$user_id = $decoded_payload->user_id ?? null;
$agency_id_from_jwt = $decoded_payload->agency_id ?? null;
$roles = isset($decoded_payload->roles) && is_array($decoded_payload->roles) ? $decoded_payload->roles : (isset($decoded_payload->roles) && is_object($decoded_payload->roles) ? (array)$decoded_payload->roles : []);

$is_super_admin = in_array('admin', $roles);

// custom_log_lead_history("Script iniciado. User ID: {$user_id}, Agency ID: {$agency_id_from_jwt}, SuperAdmin: " . ($is_super_admin ? 'Yes' : 'No'));

if (!$user_id) {
    http_response_code(401);
    // custom_log_lead_history("ERROR: Usuario no identificado.");
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Usuario no identificado.']);
    exit;
}

$lead_id_param = filter_input(INPUT_GET, 'lead_id', FILTER_VALIDATE_INT);

if (!$lead_id_param || $lead_id_param <= 0) {
    http_response_code(400);
    // custom_log_lead_history("ERROR: lead_id inválido o faltante: " . print_r($lead_id_param, true));
    echo json_encode(["success" => false, "message" => "Parámetro lead_id inválido o faltante."]);
    exit;
}
// custom_log_lead_history("lead_id solicitado: {$lead_id_param}");

try {
    $pdo = Database::getInstance()->getConnection();
    // custom_log_lead_history("Conexión a BD establecida.");

    // Autorización: Verificar si el usuario puede acceder a este lead
    if (!$is_super_admin) {
        if (!$agency_id_from_jwt) {
            http_response_code(403);
            // custom_log_lead_history("ERROR: Usuario no admin ({$user_id}) sin agency_id intentando acceder al historial del lead {$lead_id_param}.");
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: Información de agencia no disponible.']);
            exit;
        }

        // 1. Obtener el cliente_valorador_id del lead solicitado
        $stmt_lead_client = $pdo->prepare("SELECT cliente_valorador_id FROM valorador_leads WHERE id = :lead_id");
        $stmt_lead_client->bindParam(':lead_id', $lead_id_param, PDO::PARAM_INT);
        $stmt_lead_client->execute();
        $lead_cliente_valorador_id = $stmt_lead_client->fetchColumn();

        if (!$lead_cliente_valorador_id) {
            http_response_code(404);
            // custom_log_lead_history("ERROR: No se encontró el lead con ID {$lead_id_param} para verificar permisos.");
            echo json_encode(['success' => false, 'message' => 'Historial no encontrado o lead inaccesible (LNF).']);
            exit;
        }
        // custom_log_lead_history("Lead {$lead_id_param} pertenece a cliente_valorador_id: {$lead_cliente_valorador_id}. Verificando contra agencia {$agency_id_from_jwt}.");

        // 2. Verificar que el cliente_valorador pertenece a la agencia del usuario
        $stmt_verify_client = $pdo->prepare("SELECT id FROM clientes_valorador WHERE id = :cliente_valorador_id AND agency_id = :agency_id AND activo = 1");
        $stmt_verify_client->bindParam(':cliente_valorador_id', $lead_cliente_valorador_id, PDO::PARAM_INT);
        $stmt_verify_client->bindParam(':agency_id', $agency_id_from_jwt, PDO::PARAM_INT);
        $stmt_verify_client->execute();
        $client_exists = $stmt_verify_client->fetchColumn();

        if (!$client_exists) {
            http_response_code(403);
            // custom_log_lead_history("ERROR: Acceso denegado. Lead {$lead_id_param} (cliente_valorador_id {$lead_cliente_valorador_id}) no pertenece a la agencia {$agency_id_from_jwt}.");
            echo json_encode(['success' => false, 'message' => 'Acceso denegado al historial de este lead.']);
            exit;
        }
        // custom_log_lead_history("Acceso autorizado para agencia {$agency_id_from_jwt} al lead {$lead_id_param}.");
    }

    // Obtener historial de emails. Se une con sequence_steps para obtener el nombre del paso.
    // El email inicial de valoración no tendrá un 'nombre_paso_display'.
    $sql_historial = "SELECT
                            leh.id,
                            leh.uuid,
                            leh.tipo_email,
                            leh.estado_envio,
                            leh.fecha_programada_envio,
                            leh.fecha_envio_real,
                            leh.asunto_final,
                            leh.error_detalle,
                            leh.abierto_timestamp,
                            leh.clickeado_timestamp,
                            ss.name AS nombre_paso_display
                        FROM
                            lead_emails_historial leh
                        LEFT JOIN
                            sequence_steps ss ON leh.sequence_step_id = ss.id
                        WHERE
                            leh.lead_id = :lead_id
                        ORDER BY
                            COALESCE(leh.fecha_envio_real, leh.fecha_programada_envio) DESC, leh.id DESC";
    
    $stmt_historial = $pdo->prepare($sql_historial);
    $stmt_historial->bindParam(':lead_id', $lead_id_param, PDO::PARAM_INT);
    $stmt_historial->execute();
    $history_items = $stmt_historial->fetchAll(PDO::FETCH_ASSOC);
    // custom_log_lead_history(count($history_items) . " items de historial recuperados para lead {$lead_id_param}.");
    
    http_response_code(200);
    echo json_encode(["success" => true, "history" => $history_items]);

} catch (PDOException $e) {
    custom_log_lead_history("ERROR PDO: " . $e->getMessage() . " Trace: " . $e->getTraceAsString());
    error_log("[lead_email_history.php] PDOException: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB).']);
} catch (Exception $e) {
    custom_log_lead_history("ERROR Exception: " . $e->getMessage() . " Trace: " . $e->getTraceAsString());
    error_log("[lead_email_history.php] Exception: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}

?> 