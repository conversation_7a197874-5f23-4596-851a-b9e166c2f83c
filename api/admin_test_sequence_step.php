<?php

declare(strict_types=1);


require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/utils/auth_check.php';

use Api\lib\EnhancedAIService;
use Api\lib\EmailVariablesService;
use Api\lib\EmailTemplateProcessor;

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'JSON inválido']);
    exit;
}

// Validar parámetros requeridos
$required = ['prompt_template', 'lead_id', 'email_template_id'];
foreach ($required as $field) {
    if (empty($input[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => "Campo requerido: {$field}"]);
        exit;
    }
}

try {
    $leadId = (int)$input['lead_id'];
    $promptTemplate = $input['prompt_template'];
    $emailTemplateId = (int)$input['email_template_id'];
    $baseSubject = $input['base_subject'] ?? 'Email de prueba';

    // Nuevos parámetros para IA mejorada
    $aiModel = $input['ai_model'] ?? 'openai'; // 'openai' o 'gemini'
    $useWebSearch = (bool)($input['use_web_search'] ?? false);

    // 1. Obtener datos del lead para el contexto
    $conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
    if ($conn->connect_error) {
        throw new Exception('Error de conexión a BD');
    }
    $conn->set_charset(DB_CHARSET);

    // Obtener datos del lead
    $stmt = $conn->prepare('
        SELECT vl.*, vv.*, cv.nombre_display as agency_name
        FROM valorador_leads vl
        JOIN valorador_valoraciones vv ON vl.valoracion_id = vv.id
        JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id
        WHERE vl.id = ?
    ');
    $stmt->bind_param('i', $leadId);
    $stmt->execute();
    $leadData = $stmt->get_result()->fetch_assoc();

    if (!$leadData) {
        throw new Exception('Lead no encontrado');
    }

    // Preparar datos para IA
    $leadInfo = [
        'nombre' => $leadData['nombre'],
        'email' => $leadData['email']
    ];

    $propertyInfo = [
        'direccion' => $leadData['direccion'],
        'tipo_principal' => $leadData['tipo_principal']
    ];

    $agencyInfo = [
        'nombre_display' => $leadData['agency_name']
    ];

    // 2. Generar contenido con IA mejorada
    $aiService = new EnhancedAIService();

    if ($aiModel === 'gemini') {
        $iaResult = $aiService->generateWithGemini(
            $promptTemplate,
            $leadInfo,
            $propertyInfo,
            $agencyInfo,
            $baseSubject,
            $useWebSearch
        );
    } else {
        $iaResult = $aiService->generateWithOpenAI(
            $promptTemplate,
            $leadInfo,
            $propertyInfo,
            $agencyInfo,
            $baseSubject,
            $useWebSearch
        );
    }

    if (!$iaResult['success']) {
        throw new Exception('Error generando contenido IA: ' . $iaResult['error']);
    }
    
    // 3. Procesar plantilla HTML usando el procesador real
    $stmt = $conn->prepare('SELECT nombre_interno_plantilla, contenido_html FROM email_plantillas_html WHERE id = ?');
    $stmt->bind_param('i', $emailTemplateId);
    $stmt->execute();
    $templateInfo = $stmt->get_result()->fetch_assoc();

    if (!$templateInfo) {
        throw new Exception('Plantilla no encontrada');
    }

    // Procesar plantilla HTML real usando el servicio completo de variables
    $htmlContent = $templateInfo['contenido_html'];

    // Usar EmailVariablesService para obtener todas las variables
    $allVariables = EmailVariablesService::getVariablesForLead($leadId);

    // Agregar el contenido generado por IA
    $allVariables['{{contenido_ia_generado}}'] = nl2br($iaResult['body']);
    $allVariables['{{email_content}}'] = nl2br($iaResult['body']); // Compatibilidad

    // Reemplazar todas las variables en la plantilla
    $finalHtml = str_replace(array_keys($allVariables), array_values($allVariables), $htmlContent);

    // Obtener lista de variables reemplazadas para debugging
    $variablesReplaced = [];
    foreach ($allVariables as $key => $value) {
        if (strpos($htmlContent, $key) !== false) {
            $variablesReplaced[$key] = $value;
        }
    }

    // 4. Estadísticas del contenido generado
    $stats = [
        'subject_length' => strlen($iaResult['subject']),
        'content_length' => strlen($iaResult['body']),
        'html_length' => strlen($finalHtml),
        'variables_available' => count($allVariables),
        'variables_used' => count($variablesReplaced),
        'template_name' => $templateInfo['nombre_interno_plantilla'],
        'ai_model_used' => $iaResult['model_used'],
        'web_search_used' => $useWebSearch
    ];
    
    echo json_encode([
        'success' => true,
        'data' => [
            'ai_generated' => [
                'subject' => $iaResult['subject'],
                'content' => $iaResult['body']
            ],
            'final_email' => [
                'subject' => $iaResult['subject'],
                'html' => $finalHtml
            ],
            'context_used' => [
                'lead_name' => $leadInfo['nombre'],
                'property_address' => $propertyInfo['direccion'],
                'ai_model' => $aiModel,
                'web_search_used' => $useWebSearch,
                'search_query' => $iaResult['search_query_used'] ?? 'Generado automáticamente'
            ],
            'statistics' => $stats,
            'variables_replaced' => $variablesReplaced
        ]
    ]);

    $conn->close();
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error en el testing: ' . $e->getMessage()
    ]);
}
?>
