<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}
$conn->set_charset(DB_CHARSET);

try {

    // Obtener leads con valoraciones completas para testing
    $sql = '
        SELECT
            vl.id,
            vl.nombre,
            vl.email,
            vv.direccion as property_address,
            vv.valor_estimado_min,
            vv.valor_estimado_max,
            vv.tipo_principal,
            cv.nombre_display as agency_name,
            vv.fecha_creacion
        FROM valorador_leads vl
        JOIN valorador_valoraciones vv ON vl.valoracion_id = vv.id
        JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id
        WHERE
            vv.valor_estimado_min > 0
            AND vv.valor_estimado_max > 0
            AND vl.nombre IS NOT NULL
            AND vl.nombre != ""
            AND vv.direccion IS NOT NULL
            AND vv.direccion != ""
        ORDER BY vv.fecha_creacion DESC
        LIMIT 50
    ';

    $result = $conn->query($sql);
    if (!$result) {
        throw new Exception('Error en la consulta: ' . $conn->error);
    }

    $leads = [];
    while ($row = $result->fetch_assoc()) {
        $leads[] = $row;
    }
    
    // Formatear datos para el frontend
    foreach ($leads as &$lead) {
        $lead['id'] = (int)$lead['id'];
        $lead['valor_estimado_min'] = (float)$lead['valor_estimado_min'];
        $lead['valor_estimado_max'] = (float)$lead['valor_estimado_max'];
        $lead['display_name'] = $lead['nombre'] . ' - ' . $lead['property_address'];
        $lead['valuation_range'] = number_format($lead['valor_estimado_min'], 0, ',', '.') . '€ - ' . 
                                  number_format($lead['valor_estimado_max'], 0, ',', '.') . '€';
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'leads' => $leads,
            'total_count' => count($leads)
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener leads: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
