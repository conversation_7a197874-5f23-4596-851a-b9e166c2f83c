<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;

header('Content-Type: application/json');

// Solo permitir GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

// --- Autenticación JWT ---
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
$decoded_payload = null;
$user_agency_id = null;

if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    $token = $matches[1];
    if (!empty(JWT_SECRET)) {
        try {
            JWT::$leeway = 60;
            $decoded_payload = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
        } catch (ExpiredException $e) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token expirado']);
            exit();
        } catch (SignatureInvalidException $e) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido']);
            exit();
        } catch (BeforeValidException $e) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token no válido aún']);
            exit();
        } catch (\Throwable $e) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Error de autenticación']);
            exit();
        }
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error de configuración del servidor']);
        exit();
    }
}

if (!$decoded_payload || !isset($decoded_payload->agency_id)) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Autenticación requerida']);
    exit();
}

$user_agency_id = $decoded_payload->agency_id;

// --- Validar parámetros ---
$client_identifier = $_GET['client_identifier'] ?? null;

if (empty($client_identifier)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Identificador requerido']);
    exit();
}

// Validar formato del identificador
if (!preg_match('/^[a-zA-Z0-9_-]+$/', $client_identifier)) {
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'available' => false,
        'message' => 'Formato inválido. Solo letras, números, guiones y guiones bajos.'
    ]);
    exit();
}

// Validar longitud mínima
if (strlen($client_identifier) < 3) {
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'available' => false,
        'message' => 'Mínimo 3 caracteres requeridos.'
    ]);
    exit();
}

try {
    $pdo = Database::getInstance()->getConnection();
    
    // Verificar si el identificador ya existe (excluyendo el propio usuario si está editando)
    $user_id = $decoded_payload->user_id ?? null;
    
    if ($user_id) {
        // Si es una edición, excluir el registro actual del usuario
        $sql = "SELECT COUNT(*) FROM clientes_valorador WHERE client_identifier = :client_id AND user_id != :user_id AND activo = 1";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':client_id', $client_identifier, PDO::PARAM_STR);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    } else {
        // Si es una creación nueva, verificar si existe en general
        $sql = "SELECT COUNT(*) FROM clientes_valorador WHERE client_identifier = :client_id AND activo = 1";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':client_id', $client_identifier, PDO::PARAM_STR);
    }
    
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    $available = ($count == 0);
    
    echo json_encode([
        'success' => true,
        'available' => $available,
        'message' => $available ? 'Identificador disponible' : 'Identificador ya en uso'
    ]);
    
} catch (PDOException $e) {
    error_log("Error verificando client_identifier: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor']);
}
?>
