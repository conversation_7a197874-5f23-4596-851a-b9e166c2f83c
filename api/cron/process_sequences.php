<?php
declare(strict_types=1);

require_once __DIR__ . '/../config/bootstrap.php';

use Api\lib\Database;
use Api\lib\BrevoService;
use Api\lib\Logger;
use Api\lib\OpenAIService;
use Api\lib\EmailVariablesService;
use Api\lib\EmailTemplateProcessor;

// --- Configuración del Cron Job ---
// Asegurarse de que este script solo se ejecute con el secreto correcto o desde la CLI.
$cron_secret_key = getenv('CRON_SECRET_KEY') ?: 'ESTE_ES_UN_SECRETO_MUY_SECRETO_12345'; // TODO: Mover a variable de entorno

if (php_sapi_name() !== 'cli' && (!isset($_SERVER['HTTP_X_CRON_SECRET']) || $_SERVER['HTTP_X_CRON_SECRET'] !== $cron_secret_key)) {
    http_response_code(403);
    Logger::warning("Acceso denegado a cron. Secreto incorrecto o ausente.");
    echo "Acceso denegado.";
    exit;
}

Logger::info("Cron de Generación: process_sequences.php iniciado.");

// --- Definición de la ventana de generación de emails ---
// Generamos emails con X días de antelación para permitir revisión.
define('GENERATION_LEAD_DAYS', 3);

try {
    $pdo = Database::getInstance()->getConnection();
    // $brevoService = new BrevoService(); // No se usa en este cron, se usará en el cron de envío.
    $openAIService = new OpenAIService(OPENAI_API_KEY);

    // 1. Obtener leads con emails que necesitan ser generados y programados
    $stmt = $pdo->prepare("
        SELECT
            lst.id AS tracking_id,
            lst.lead_id,
            lst.sequence_id,
            lst.current_step_id,
            lst.next_due_date,
            vl.email AS lead_email,
            vl.nombre AS lead_nombre,
            vl.telefono AS lead_telefono,
            vl.necesidad AS lead_necesidad,
            vl.cliente_valorador_id,
            vv.direccion AS property_address,
            vv.valor_estimado_min AS property_value_min,
            vv.valor_estimado_max AS property_value_max,
            vv.tipo_principal AS property_type,
            vv.superficie AS property_surface,
            vv.habitaciones AS property_rooms,
            vv.banos AS property_bathrooms,
            vv.ano_construccion_catastro AS property_year,
            vv.precio_m2_promedio AS zone_avg_price_m2,
            vv.tamano_promedio AS zone_avg_size,
            s.name AS sequence_name,
            ss.id AS step_id,
            ss.step_order,
            ss.name AS step_name,
            ss.email_subject AS base_subject,
            ss.email_template_id,
            ss.prompt_template
        FROM
            lead_sequence_tracking lst
        JOIN
            valorador_leads vl ON lst.lead_id = vl.id
        JOIN
            valorador_valoraciones vv ON vl.valoracion_id = vv.id
        JOIN
            sequences s ON lst.sequence_id = s.id
        JOIN
            sequence_steps ss ON lst.current_step_id = ss.id
        LEFT JOIN
            lead_emails_historial leh ON leh.lead_id = lst.lead_id AND leh.sequence_step_id = lst.current_step_id
        WHERE
            lst.status = 'active'
            AND lst.current_step_id IS NOT NULL
            AND lst.next_due_date IS NOT NULL
            AND lst.next_due_date <= DATE_ADD(NOW(), INTERVAL :lead_days DAY)
            AND leh.id IS NULL
        ORDER BY
            lst.next_due_date ASC
        LIMIT 100
    ");
    $stmt->bindValue(':lead_days', GENERATION_LEAD_DAYS, PDO::PARAM_INT);
    $stmt->execute();
    $leads_to_process = $stmt->fetchAll(PDO::FETCH_ASSOC);

    Logger::info("Cron de Generación: " . count($leads_to_process) . " leads encontrados para generar y programar email.");

    foreach ($leads_to_process as $lead) {
        $pdo->beginTransaction();
        try {
            // --- Lógica de generación y programación del email ---
            
            // 1. Obtener datos estructurados usando el nuevo servicio
            $aiData = EmailVariablesService::getVariablesForAI($lead['lead_id']);
            $contexto_json = json_encode($aiData);

            // 2. Generar contenido del email con IA usando el nuevo sistema
            $ia_result = $openAIService->generateEmailContent(
                $lead['prompt_template'],
                $aiData['leadData'],
                $aiData['propertyData'],
                $aiData['agencyData'],
                $lead['base_subject'] // Usado como referencia, no como asunto final
            );

            if (empty($ia_result['success'])) {
                throw new Exception("Fallo en la generación de contenido IA para Lead ID {$lead['lead_id']}. Respuesta: " . ($ia_result['error'] ?? 'Sin detalles'));
            }

            // 3. Procesar la plantilla HTML con el contenido generado por IA
            $templateResult = EmailTemplateProcessor::processTemplate(
                $lead['email_template_id'],
                $ia_result['body'],
                $lead['lead_id']
            );

            if (!$templateResult['success']) {
                throw new Exception("Error al procesar plantilla para Lead ID {$lead['lead_id']}: " . $templateResult['error']);
            }

            // 4. Guardar el email completo como 'scheduled' en el historial
            $stmtHistorial = $pdo->prepare("
                INSERT INTO lead_emails_historial (
                    uuid, lead_id, sequence_step_id, tipo_email, estado_envio, fecha_programada_envio,
                    datos_contexto_ia_utilizados, ia_prompt_usado, ia_respuesta_original,
                    borrador_asunto, borrador_cuerpo_html, cuerpo_final_html, asunto_final,
                    fecha_creacion, fecha_modificacion
                ) VALUES (
                    UUID(), :lead_id, :sequence_step_id, 'email_secuencia_ia', 'scheduled', :fecha_programada_envio,
                    :datos_contexto, :ia_prompt_usado, :ia_respuesta_original,
                    :borrador_asunto, :borrador_cuerpo_html, :cuerpo_final_html, :asunto_final,
                    NOW(), NOW()
                )
            ");

            $stmtHistorial->execute([
                ':lead_id' => $lead['lead_id'],
                ':sequence_step_id' => $lead['current_step_id'],
                ':fecha_programada_envio' => $lead['next_due_date'],
                ':datos_contexto' => $contexto_json,
                ':ia_prompt_usado' => $ia_result['prompt'] ?? null,
                ':ia_respuesta_original' => $ia_result['raw_response'] ?? null,
                ':borrador_asunto' => $ia_result['subject'],
                ':borrador_cuerpo_html' => $ia_result['body'],
                ':cuerpo_final_html' => $templateResult['html'],
                ':asunto_final' => $ia_result['subject']
            ]);

            $pdo->commit();
            Logger::info("Cron de Generación: Email para Lead ID {$lead['lead_id']} (Step ID: {$lead['current_step_id']}) generado y programado para {$lead['next_due_date']}.");

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            Logger::error("Cron de Generación: Error procesando Lead ID {$lead['lead_id']}: " . $e->getMessage());
            // No actualizamos el tracking a 'error' aquí, para que se pueda reintentar en la próxima ejecución.
        }
    }

    Logger::info("Cron de Generación: process_sequences.php finalizado.");

} catch (Exception $e) {
    Logger::error("Cron de Generación: Fallo crítico en process_sequences.php: " . $e->getMessage());
}

?>