<?php
declare(strict_types=1);

require_once __DIR__ . '/../config/bootstrap.php';

use Api\lib\Database;
use Api\lib\BrevoService;
use Api\lib\Logger;

// --- Configuración del Cron Job ---
$cron_secret_key = getenv('CRON_SECRET_KEY') ?: 'ESTE_ES_UN_SECRETO_MUY_SECRETO_12345';

if (php_sapi_name() !== 'cli' && (!isset($_SERVER['HTTP_X_CRON_SECRET']) || $_SERVER['HTTP_X_CRON_SECRET'] !== $cron_secret_key)) {
    http_response_code(403);
    Logger::warning("Acceso denegado a cron (send_scheduled_emails). Secreto incorrecto o ausente.");
    echo "Acceso denegado.";
    exit;
}

Logger::info("Cron de Envío: send_scheduled_emails.php iniciado.");

try {
    $pdo = Database::getInstance()->getConnection();
    $brevoService = new BrevoService();

    // 1. Obtener emails programados que ya deben ser enviados
    $stmt = $pdo->prepare("
        SELECT
            leh.id AS historial_id,
            COALESCE(leh.asunto_final, leh.borrador_asunto) AS subject,
            COALESCE(leh.cuerpo_final_html, leh.borrador_cuerpo_html) AS body,
            vl.email AS lead_email,
            vl.nombre AS lead_nombre,
            leh.sequence_step_id
        FROM
            lead_emails_historial leh
        JOIN
            valorador_leads vl ON leh.lead_id = vl.id
        WHERE
            leh.estado_envio = 'scheduled'
            AND leh.fecha_programada_envio <= NOW()
        ORDER BY
            leh.fecha_programada_envio ASC
        LIMIT 100 -- Procesar en lotes
    ");
    $stmt->execute();
    $emails_to_send = $stmt->fetchAll(PDO::FETCH_ASSOC);

    Logger::info("Cron de Envío: " . count($emails_to_send) . " emails encontrados para enviar.");

    foreach ($emails_to_send as $email) {
        $historial_id = $email['historial_id'];
        $pdo->beginTransaction();
        try {
            // Marcar como 'sending' para evitar doble envío en ejecuciones paralelas
            $stmt_sending = $pdo->prepare("UPDATE lead_emails_historial SET estado_envio = 'sending', fecha_modificacion = NOW() WHERE id = :id AND estado_envio = 'scheduled'");
            $stmt_sending->bindParam(':id', $historial_id, PDO::PARAM_INT);
            $stmt_sending->execute();

            if ($stmt_sending->rowCount() === 0) {
                // Otro proceso ya tomó este email, lo saltamos
                $pdo->rollBack();
                continue;
            }

            // 2. Enviar el email usando Brevo
            $sendResult = $brevoService->sendEmail(
                $email['lead_email'],
                $email['lead_nombre'] ?? 'Lead',
                $email['subject'],
                $email['body']
            );

            if (!$sendResult['success']) {
                throw new Exception("Fallo al enviar email a {$email['lead_email']}: " . ($sendResult['error'] ?? 'Error desconocido'));
            }

            // 3. Actualizar el historial a 'sent'
            $stmt_sent = $pdo->prepare("
                UPDATE lead_emails_historial
                SET
                    estado_envio = 'sent',
                    fecha_envio_real = NOW(),
                    mensaje_id_esp = :message_id,
                    asunto_final = :subject,
                    cuerpo_final_html = :body,
                    fecha_modificacion = NOW()
                WHERE
                    id = :id
            ");
            $stmt_sent->execute([
                ':message_id' => $sendResult['message_id'] ?? null,
                ':subject' => $email['subject'],
                ':body' => $email['body'],
                ':id' => $historial_id
            ]);

            $pdo->commit();
            Logger::info("Cron de Envío: Email (Historial ID: {$historial_id}) enviado con éxito a {$email['lead_email']}.");

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            Logger::error("Cron de Envío: Error procesando email (Historial ID: {$historial_id}). Causa: " . $e->getMessage());

            // Marcar como 'failed'
            $stmt_failed = $pdo->prepare("UPDATE lead_emails_historial SET estado_envio = 'failed', error_detalle = :error, fecha_modificacion = NOW() WHERE id = :id");
            $stmt_failed->execute([':error' => $e->getMessage(), ':id' => $historial_id]);
        }
    }

    Logger::info("Cron de Envío: send_scheduled_emails.php finalizado.");

} catch (Exception $e) {
    Logger::error("Cron de Envío: Fallo crítico en send_scheduled_emails.php: " . $e->getMessage());
}
?>
