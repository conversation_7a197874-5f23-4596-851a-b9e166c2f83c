<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// api/admin_dashboard_stats.php
// error_reporting(E_ALL); // Manejado por bootstrap.php
// ini_set('display_errors', 1); // Manejado por bootstrap.php
 // <--- INTEGRAR BOOTSTRAP
require_once __DIR__ . '/utils/auth_check.php'; // Incluir auth_check.php
// Use Firebase\JWT\JWT; // auth_check.php debería manejar esto si es necesario directamente, o ya está en bootstrap.
// Use Firebase\JWT\Key;
header('Content-Type: application/json');
// --- Carga de .env y autoload manejada por bootstrap.php ---
// try { $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); $dotenv->load(); } catch ... (eliminado)
// --- Constantes de BD y JWT_SECRET vienen de bootstrap.php ---
// $dbHost = $_ENV['DB_HOST'] ?? null; (eliminado)
// ... (eliminación de otras asignaciones de variables de entorno)
// Verificar autenticación y rol de administrador
// auth_check.php ahora usará JWT_SECRET de bootstrap.php
$authResult = verifyTokenAndAdminRole(); // No es necesario pasar JWT_SECRET si auth_check.php lo obtiene de bootstrap
if (!$authResult['success']) {
    http_response_code($authResult['status_code']);
    echo json_encode(['success' => false, 'message' => $authResult['message']]);
    exit();
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en admin_dashboard_stats: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}

try {
    $stats = [
        'totalUsuarios' => 0,
        'nuevosUsuarios' => 0,
        'valoradoresActivos' => 0,
        'totalValoradores' => 0,
        'totalValoraciones' => 0,
        'nuevasValoraciones' => 0,
        'totalLeads' => 0,
        'nuevosLeads' => 0,
    ];
    $treintaDiasAtras = date('Y-m-d H:i:s', strtotime('-30 days'));

    // Total Usuarios
    $stats['totalUsuarios'] = (int)$pdo->query("SELECT COUNT(*) FROM usuarios")->fetchColumn();
    
    // Nuevos Usuarios (últimos 30 días)
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM usuarios WHERE created_at >= ?");
    $stmt->execute([$treintaDiasAtras]);
    $stats['nuevosUsuarios'] = (int)$stmt->fetchColumn();
    
    // Valoradores Activos
    $stats['valoradoresActivos'] = (int)$pdo->query("SELECT COUNT(*) FROM clientes_valorador WHERE activo = 1")->fetchColumn();
    
    // Total Valoradores
    $stats['totalValoradores'] = (int)$pdo->query("SELECT COUNT(*) FROM clientes_valorador")->fetchColumn();
    
    // Total Valoraciones
    $stats['totalValoraciones'] = (int)$pdo->query("SELECT COUNT(*) FROM valorador_valoraciones")->fetchColumn();
    
    // Nuevas Valoraciones (últimos 30 días)
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM valorador_valoraciones WHERE fecha_creacion >= ?");
    $stmt->execute([$treintaDiasAtras]);
    $stats['nuevasValoraciones'] = (int)$stmt->fetchColumn();
    
    // Total Leads
    $stats['totalLeads'] = (int)$pdo->query("SELECT COUNT(*) FROM valorador_leads")->fetchColumn();
    
    // Nuevos Leads (últimos 30 días)
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM valorador_leads WHERE fecha_creacion >= ?");
    $stmt->execute([$treintaDiasAtras]);
    $stats['nuevosLeads'] = (int)$stmt->fetchColumn();
    
    echo json_encode(['success' => true, 'stats' => $stats]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al ejecutar consultas para admin_dashboard_stats: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error al procesar las estadísticas del dashboard.']);
    exit();
}