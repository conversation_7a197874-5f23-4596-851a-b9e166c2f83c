<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

function validate_jwt_token(string $token): ?object {
    if (!defined('JWT_SECRET') || JWT_SECRET === '') {
        return null;
    }
    try {
        $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
        return $decoded;
    } catch (\Throwable $e) {
        return null;
    }
}

header("Content-Type: application/json");
// 1. Validar JWT
$auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if (!$auth_header) {
    http_response_code(401);
    echo json_encode(["success" => false, "message" => "No se proporcionó token de autorización."]);
    exit;
}
list($token_type, $token) = explode(' ', $auth_header, 2);
if (strcasecmp($token_type, 'Bearer') !== 0 || !$token) {
    http_response_code(401);
    echo json_encode(["success" => false, "message" => "Formato de token inválido."]);
    exit;
}
$decoded_token = validate_jwt_token($token);
if (!$decoded_token) {
    http_response_code(401);
    echo json_encode(["success" => false, "message" => "Token inválido o expirado."]);
    exit;
}
// $user_client_id = $decoded_token->data->client_id ?? null;
// 2. Obtener y validar lead_id del cuerpo JSON
$data = json_decode(file_get_contents("php://input"));
if (!$data || !isset($data->lead_id)) {
    http_response_code(400);
    echo json_encode(["success" => false, "message" => "Datos JSON inválidos o lead_id faltante."]);
    exit;
}
$lead_id = filter_var($data->lead_id, FILTER_VALIDATE_INT);
if (!$lead_id || $lead_id <= 0) {
    http_response_code(400);
    echo json_encode(["success" => false, "message" => "lead_id debe ser un entero positivo."]);
    exit;
}
// Usar el singleton de Database en lugar de intentar instanciar directamente
$conn = Database::getInstance()->getConnection();
$conn->beginTransaction(); // Iniciar transacción
// 3. (Opcional) Verificar permisos
/*
if ($user_client_id) {
    $stmt_perm = $conn->prepare("SELECT cliente_valorador_id FROM valorador_leads WHERE id = :lead_id");
    $stmt_perm->bindParam(':lead_id', $lead_id);
    $stmt_perm->execute();
    $lead_owner = $stmt_perm->fetch(PDO::FETCH_ASSOC);
    if (!$lead_owner || $lead_owner['cliente_valorador_id'] !== $user_client_id) {
        http_response_code(403);
        echo json_encode(["success" => false, "message" => "No tienes permiso para modificar este lead."]);
        $conn->rollBack();
        exit;
    }
}
*/
// 4. Actualizar estado_secuencia_lead en valorador_leads
try {
    $nuevo_estado_secuencia = 'paused_by_user';

    // Verificar si ya existe un registro en lead_sequence_tracking para este lead
    $stmt_check = $conn->prepare("SELECT id, status FROM lead_sequence_tracking WHERE lead_id = :lead_id");
    $stmt_check->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
    $stmt_check->execute();
    $tracking_record = $stmt_check->fetch(PDO::FETCH_ASSOC);

    if (!$tracking_record) {
        throw new Exception("No se encontró un registro de seguimiento de secuencia para este lead. No se puede pausar.");
    }

    if ($tracking_record['status'] === $nuevo_estado_secuencia) {
        http_response_code(200); // No es un error, simplemente no hay nada que hacer
        echo json_encode(["success" => true, "message" => "La secuencia ya estaba pausada.", "newState" => $nuevo_estado_secuencia]);
        exit;
    }

    // Actualizar el estado de la secuencia a 'paused_by_user'
    $stmt_update = $conn->prepare("UPDATE lead_sequence_tracking SET status = :status, updated_at = NOW() WHERE id = :id");
    $stmt_update->bindParam(':status', $nuevo_estado_secuencia, PDO::PARAM_STR);
    $stmt_update->bindParam(':id', $tracking_record['id'], PDO::PARAM_INT);
    $stmt_update->execute();

    // Cancelar todos los emails futuros que estaban programados
    $stmt_cancel_emails = $conn->prepare("
        UPDATE lead_emails_historial
        SET estado_envio = 'cancelled', fecha_modificacion = NOW()
        WHERE lead_id = :lead_id AND estado_envio = 'scheduled'
    ");
    $stmt_cancel_emails->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
    $stmt_cancel_emails->execute();
    $cancelled_emails_count = $stmt_cancel_emails->rowCount();

    // Confirmar transacción
    $conn->commit();

    // Responder con éxito
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "message" => "Secuencia pausada correctamente y emails futuros cancelados.",
        "newState" => $nuevo_estado_secuencia
    ]);

} catch (PDOException $e) {
    $conn->rollBack();
    $error_code = $e->errorInfo[0] ?? 'unknown';
    $error_message = $e->errorInfo[2] ?? $e->getMessage();
    
    error_log("[lead_email_sequence_pause.php] ERROR DB: Code={$error_code}, Message={$error_message}");
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error al pausar la secuencia de emails', 
        'dbError' => $error_message
    ]);
} catch (Exception $e) {
    $conn->rollBack();
    error_log("[lead_email_sequence_pause.php] ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error al pausar la secuencia de emails']);
} finally {
    if ($conn) {
        $conn = null;
    }
}
?>