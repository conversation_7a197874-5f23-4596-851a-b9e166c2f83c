<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// bootstrap.php se encargará de la configuración de errores.
// Las cabeceras CORS y el manejo de OPTIONS ahora están en cors.php
header('Content-Type: application/json');
// Composer autoload es manejado por bootstrap.php
// require_once __DIR__ . '/vendor/autoload.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
// Carga de .env es manejada por bootstrap.php
// $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
// $dotenv->load();
// Configuración ahora usa constantes de bootstrap.php
// $dbHost = $_ENV['DB_HOST'];
// $dbUser = $_ENV['DB_USER'];
// $dbPass = $_ENV['DB_PASS'];
// $dbName = $_ENV['DB_NAME'];
// Mantener el log de login específico por ahora
if (!function_exists('custom_log_login')) {
    function custom_log_login($message) {
        $timestamp = date('Y-m-d H:i:s');

    }
}
$jwtSecret = JWT_SECRET; // Usar constante de bootstrap.php
custom_log_login("Usando JWT_SECRET para firmar. Longitud: " . strlen($jwtSecret));
custom_log_login("Primeros 10 caracteres de la clave: " . substr($jwtSecret, 0, 10));
// JWT_ISSUER puede ser una constante definida en bootstrap.php o tomar un default aquí
$jwtIssuer = defined('JWT_ISSUER') ? JWT_ISSUER : ($_ENV['JWT_ISSUER'] ?? 'inmoautomation.com');
$jwtExpire = 60 * 60 * 24 * 7; // 7 días. Considerar mover a constante también.

try {
    custom_log_login("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_login("Conexión PDO establecida a través de la clase Database.");
} catch (Exception $e) {
    $error_msg = "Error de conexión a la base de datos: " . $e->getMessage();
    error_log($error_msg);
    http_response_code(500);
    die(json_encode([
        'success' => false, 
        'message' => 'Error interno del servidor al conectar con la base de datos.',
        'error' => $e->getMessage()
    ]));
}

// Configurar zona horaria de MySQL para que coincida con PHP
$pdo->exec("SET time_zone = '+02:00'");

// Leer input
$input = json_decode(file_get_contents('php://input'), true);
$email = $input['email'] ?? null;
$password = $input['password'] ?? null;

if (!$email || !$password) {
    custom_log_login("Intento de login fallido: Email o contraseña no proporcionados.");
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Email y contraseña son obligatorios.']);
    exit();
}

// Buscar usuario
custom_log_login("Intentando login para email: " . $email);
$stmt = $pdo->prepare("SELECT id, uuid, nombre_completo, email, password_hash, roles, activo, agency_id FROM usuarios WHERE email = ? LIMIT 1");
$stmt->execute([$email]);
$user = $stmt->fetch();

if (!$user) {
    custom_log_login("Intento de login fallido para email: " . $email . " - Usuario no encontrado.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Credenciales incorrectas.']);
    exit();
}

if (!$user['activo']) {
    custom_log_login("Intento de login fallido para email: " . $email . " - Cuenta inactiva.");
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Cuenta inactiva.']);
    exit();
}

if (!password_verify($password, $user['password_hash'])) {
    custom_log_login("Intento de login fallido para email: " . $email . " - Contraseña incorrecta.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Credenciales incorrectas.']);
    exit();
}

// Obtener datos de la agencia
$agency_id = $user['agency_id'];
$is_agency_owner = false;
$agency_name = null;

if ($agency_id) {
    // Primero, obtener el nombre de la agencia
    $stmt_agency_name = $pdo->prepare("SELECT name FROM agencies WHERE id = ? LIMIT 1");
    $stmt_agency_name->execute([$agency_id]);
    $agency_name_row = $stmt_agency_name->fetch();
    if ($agency_name_row) {
        $agency_name = $agency_name_row['name'];
    }
    custom_log_login("Nombre de agencia obtenido para agency_id: " . $agency_id . ". Nombre: " . ($agency_name ?? 'N/A'));

    // Luego, verificar si es propietario
    $stmt_agency_owner = $pdo->prepare("SELECT owner_user_id FROM agencies WHERE id = ? AND owner_user_id = ? LIMIT 1");
    $stmt_agency_owner->execute([$agency_id, $user['id']]);
    if ($stmt_agency_owner->fetch()) {
        $is_agency_owner = true;
    }
    custom_log_login("Verificación de dueño de agencia para user_id: " . $user['id'] . ", agency_id: " . $agency_id . ". Es dueño: " . ($is_agency_owner ? 'Sí' : 'No'));
} else {
    custom_log_login("Usuario user_id: " . $user['id'] . " no tiene agency_id asignado.");
}
// Generar JWT
$issuedAt = time();
$expire = $issuedAt + $jwtExpire;
$payload = [
    'iat' => $issuedAt,
    'exp' => $expire,
    'iss' => $jwtIssuer,
    'sub' => $user['uuid'], // 'sub' es comúnmente el ID único del usuario
    'user_id' => $user['id'], // Mantener user_id para compatibilidad con el código existente
    'agency_id' => $agency_id,
    'agency_name' => $agency_name,
    'is_agency_owner' => $is_agency_owner,
    'email' => $user['email'],
    'nombre_completo' => $user['nombre_completo'],
    'roles' => $user['roles'] ? json_decode($user['roles'], true) : [], // Asegurar que roles sea un array
];
$jwt = JWT::encode($payload, $jwtSecret, 'HS256');
custom_log_login("Login exitoso para email: " . $email . ". User ID: " . $user['id'] . ". Token generado.");
// Respuesta
echo json_encode([
    'success' => true,
    'token' => $jwt,
    'user' => [
        'uuid' => $user['uuid'],
        'id' => $user['id'],
        'agency_id' => $agency_id,
        'agency_name' => $agency_name,
        'is_agency_owner' => $is_agency_owner,
        'email' => $user['email'],
        'nombre_completo' => $user['nombre_completo'],
        'roles' => $payload['roles'], // Devolver los roles procesados del payload
    ]
]);
?>