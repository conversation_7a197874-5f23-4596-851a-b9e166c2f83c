<?php
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

// --- Autenticación y Autorización ---
$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}
// --- Fin Autenticación ---

try {
    if (!isset($_GET['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID de usuario no proporcionado']);
        exit();
    }

    $pdo = Api\lib\Database::getInstance()->getConnection();

    $stmt = $pdo->prepare("SELECT id, uuid, nombre_completo, email, roles, activo, agency_id, created_at, updated_at FROM usuarios WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $user = $stmt->fetch();

    if (!$user) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Usuario no encontrado']);
        exit();
    }

    // Decodificar roles si es un string JSON
    if (is_string($user['roles'])) {
        $user['roles'] = json_decode($user['roles'], true);
    }


    echo json_encode(['success' => true, 'data' => $user]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error al obtener los detalles del usuario', 'error' => $e->getMessage()]);
}
