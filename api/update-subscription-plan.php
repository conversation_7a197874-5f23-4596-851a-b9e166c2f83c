<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// bootstrap.php se encargará de la configuración de errores y carga de dependencias.
 // CORRECCIÓN: Usar config central de CORS
// Iniciar buffer de salida para evitar cualquier salida accidental si algo se escapa antes de los headers
ob_start(); 
// --- Logger específico para este script ---
if (!function_exists('custom_log_update_plan')) {
    function custom_log_update_plan($message) {
        $logFile = __DIR__ . '/debug_update_plan.log'; // Nombre de log consistente
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] [update-plan-LOG] " . print_r($message, true) . PHP_EOL, FILE_APPEND);
        error_log("[update-plan-SCRIPT-LOG] " . print_r($message, true)); 
    }
}
custom_log_update_plan("--- [update-subscription-plan.php V2 - MANUAL PRORATION] INICIADO ---");
if (ob_get_length()) { 
    custom_log_update_plan("Buffer de salida tenía contenido, limpiando.");
    ob_clean(); 
}
// Las cabeceras CORS y el manejo de OPTIONS ahora son gestionados por cors.php
header('Content-Type: application/json; charset=utf-8');
$user_id_from_token = null;
$user_email_from_token = null; // Para crear nuevo cliente en Stripe
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0');
        try {
            $jwt_secret_key = JWT_SECRET;
            if (empty($jwt_secret_key)) throw new Exception('JWT_SECRET constante no configurada.');
            $decoded = Firebase\JWT\JWT::decode($token, new Firebase\JWT\Key($jwt_secret_key, 'HS256'));
            if (!isset($decoded->user_id)) throw new Exception('Token JWT inválido: no contiene user_id.');
                $user_id_from_token = $decoded->user_id;
            $user_email_from_token = $decoded->email ?? null; // Capturar email del token
            custom_log_update_plan("JWT decodificado. User ID: {$user_id_from_token}, Email: " . ($user_email_from_token ?? 'No presente'));
        } catch (Exception $e) {
            custom_log_update_plan("JWT Decode Error: " . $e->getMessage());
            http_response_code(401);
            if (ob_get_length()) ob_end_clean();
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors);
        }
    } else {
        custom_log_update_plan("Formato de Authorization header incorrecto.");
        http_response_code(401);
        if (ob_get_length()) ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_update_plan("No se proporcionó token de autorización.");
    http_response_code(401);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
if (empty($user_id_from_token)) {
    custom_log_update_plan("CRITICAL: User ID from token is empty after auth block.");
    http_response_code(401);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Autenticación fallida.']);
    exit();
}
$response_payload = ['success' => false, 'message' => 'Error desconocido.'];
$db = null;
$transaction_active = false; // Variable para rastrear el estado de la transacción
try {
    $db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
    if ($db->connect_error) throw new Exception("Error de conexión a BD: " . $db->connect_error);
    $db->set_charset(DB_CHARSET);
    custom_log_update_plan("Conexión mysqli a BD establecida.");
} catch (Exception $e) {
    custom_log_update_plan("DB Connection Error: " . $e->getMessage());
    http_response_code(500);
    $response_payload['message'] = 'Error interno (DB_CONN).';
    if (ob_get_length()) ob_end_clean();
    echo json_encode($response_payload);
    exit;
}
$user_id = $user_id_from_token;
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    $response_payload['message'] = 'Método no permitido.';
    custom_log_update_plan("Error: Method not allowed. Method was: " . $_SERVER['REQUEST_METHOD']);
    if (ob_get_length()) ob_end_clean();
    echo json_encode($response_payload);
    if ($db) $db->close();
    exit;
}
$input = json_decode(file_get_contents('php://input'), true);
$new_plan_slug = $input['new_plan_slug'] ?? null;
$new_billing_cycle = $input['new_billing_cycle'] ?? null;
$couponCode = trim($input['coupon_code'] ?? '');
if (empty($new_plan_slug) || empty($new_billing_cycle) || !in_array($new_billing_cycle, ['monthly', 'annual'])) {
    http_response_code(400);
    $response_payload['message'] = 'Parámetros inválidos.';
    custom_log_update_plan("Error: Missing parameters. Slug: " . ($new_plan_slug ?? 'N/A') . ", Cycle: " . ($new_billing_cycle ?? 'N/A'));
    if (ob_get_length()) ob_end_clean();
    echo json_encode($response_payload);
    if ($db) $db->close();
    exit;
}
Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
try {
    $db->begin_transaction();
    $transaction_active = true;
    // 1. Obtener datos del usuario y su suscripción actual en la BD local
    $stmt_user_details = $db->prepare(
        "SELECT u.stripe_customer_id, u.email as user_db_email, s.stripe_subscription_id, s.stripe_price_id as current_db_price_id, s.estado as current_db_status 
         FROM usuarios u 
         LEFT JOIN suscripciones s ON u.id = s.user_id AND s.estado NOT IN ('pending_deletion', 'canceled') 
         WHERE u.id = ? ORDER BY s.fecha_creacion DESC LIMIT 1"
    );
    if (!$stmt_user_details) throw new Exception("Error preparando consulta de usuario/suscripción: " . $db->error);
    $stmt_user_details->bind_param("i", $user_id);
    $stmt_user_details->execute();
    $user_sub_details_result = $stmt_user_details->get_result();
    custom_log_update_plan("Consulta de detalles de usuario/suscripción local ejecutada. Filas devueltas: " . $user_sub_details_result->num_rows);
    $user_sub_details = $user_sub_details_result->fetch_assoc();
    $stmt_user_details->close();
    if (!$user_sub_details) throw new Exception("Usuario no encontrado en la BD.");
    $stripe_customer_id = $user_sub_details['stripe_customer_id'];
    $user_email_for_stripe = $user_sub_details['user_db_email'] ?? $user_email_from_token; // Usar email de BD o de token
    $current_local_stripe_subscription_id = $user_sub_details['stripe_subscription_id'];
    $current_local_price_id = $user_sub_details['current_db_price_id'];
    $current_local_status = $user_sub_details['current_db_status'];
    custom_log_update_plan("Datos locales: CustID: {$stripe_customer_id}, SubID (local): {$current_local_stripe_subscription_id}");
    // 2. Obtener datos del nuevo plan solicitado desde la BD
    $stmt_plan = $db->prepare("SELECT id, name, stripe_price_id_monthly, stripe_price_id_annual FROM plans WHERE slug = ?");
    if (!$stmt_plan) throw new Exception("Error preparando consulta de plan: " . $db->error);
    $stmt_plan->bind_param("s", $new_plan_slug);
    $stmt_plan->execute();
    $plan_details_db = $stmt_plan->get_result()->fetch_assoc();
    $stmt_plan->close();
    if (!$plan_details_db) throw new Exception("Plan '{$new_plan_slug}' no encontrado.");
    $new_stripe_price_id = ($new_billing_cycle === 'annual') ? $plan_details_db['stripe_price_id_annual'] : $plan_details_db['stripe_price_id_monthly'];
    $db_plan_id = $plan_details_db['id'];
    $db_nombre_plan = $plan_details_db['name'];
    if (empty($new_stripe_price_id)) throw new Exception("ID de precio de Stripe no configurado para el plan {$new_plan_slug} / {$new_billing_cycle}.");
    custom_log_update_plan("Nuevo plan solicitado: {$db_nombre_plan} ({$new_plan_slug}), PriceID Stripe: {$new_stripe_price_id}");
    $stripe_subscription_object = null;
    $force_new_subscription = false;
    if ($current_local_stripe_subscription_id) {
        try {
            $stripe_subscription_object = Stripe\Subscription::retrieve($current_local_stripe_subscription_id);
            custom_log_update_plan("Suscripción {$current_local_stripe_subscription_id} encontrada en Stripe. Estado: " . $stripe_subscription_object->status);
            if (in_array($stripe_subscription_object->status, ['canceled'])) {
                custom_log_update_plan("Suscripción en Stripe está '{$stripe_subscription_object->status}'. Forzando creación de nueva suscripción.");
                $force_new_subscription = true;
            } elseif (in_array($stripe_subscription_object->status, ['past_due', 'unpaid'])) {
                custom_log_update_plan("Suscripción en estado '{$stripe_subscription_object->status}' - Aplicando lógica especial para cambio de plan con factura pendiente.");
                // Para suscripciones past_due/unpaid, necesitamos manejar las facturas pendientes
                $handle_past_due = true;
            }
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            if (strpos(strtolower($e->getMessage()), 'no such subscription') !== false) {
                custom_log_update_plan("Suscripción local {$current_local_stripe_subscription_id} no existe en Stripe. Forzando nueva suscripción.");
                $force_new_subscription = true;
            } else { throw $e; }
        } catch (Exception $e) { throw $e; }
    } else {
        custom_log_update_plan("No hay suscripción activa local registrada. Se creará una nueva.");
        $force_new_subscription = true;
    }
    $is_on_trial = ($stripe_subscription_object && $stripe_subscription_object->trial_end && $stripe_subscription_object->trial_end > time() && !$force_new_subscription);

    // Manejar suscripciones past_due/unpaid antes de proceder
    if (isset($handle_past_due) && $handle_past_due && $stripe_subscription_object) {
        custom_log_update_plan("Manejando suscripción past_due/unpaid - Anulando facturas pendientes y creando nueva factura");

        // 1. Obtener facturas pendientes (open/draft)
        $pending_invoices = \Stripe\Invoice::all([
            'customer' => $stripe_customer_id,
            'subscription' => $stripe_subscription_object->id,
            'status' => 'open',
            'limit' => 10
        ]);

        // 2. Anular facturas pendientes
        foreach ($pending_invoices->data as $invoice) {
            try {
                $voided_invoice = $invoice->voidInvoice();
                custom_log_update_plan("Factura anulada: {$invoice->id} - Monto: " . ($invoice->total / 100) . " EUR");
            } catch (Exception $e) {
                custom_log_update_plan("Error anulando factura {$invoice->id}: " . $e->getMessage());
            }
        }

        // 3. Actualizar la suscripción al nuevo plan
        $update_params = [
            'items' => [[
                'id' => $stripe_subscription_object->items->data[0]->id,
                'price' => $new_stripe_price_id
            ]],
            'proration_behavior' => 'none', // No hacer prorrateos
            'billing_cycle_anchor' => 'now', // Reiniciar ciclo de facturación
        ];

        // Agregar cupón si se proporciona
        if (!empty($couponCode)) {
            try {
                $coupon = \Stripe\Coupon::retrieve($couponCode);
                if ($coupon->valid && (!$coupon->redeem_by || $coupon->redeem_by >= time())) {
                    $update_params['coupon'] = $couponCode;
                    custom_log_update_plan("Cupón aplicado al cambio de plan: $couponCode");
                } else {
                    custom_log_update_plan("Cupón no válido o expirado: $couponCode");
                }
            } catch (\Stripe\Exception\InvalidRequestException $e) {
                custom_log_update_plan("Cupón no encontrado: $couponCode");
            }
        }

        $stripe_subscription_object = \Stripe\Subscription::update($stripe_subscription_object->id, $update_params);

        custom_log_update_plan("Suscripción actualizada sin prorrateos. Nuevo estado: " . $stripe_subscription_object->status);

        // 4. Crear y finalizar nueva factura
        $new_invoice = \Stripe\Invoice::create([
            'customer' => $stripe_customer_id,
            'subscription' => $stripe_subscription_object->id,
            'auto_advance' => false
        ]);

        $finalized_invoice = \Stripe\Invoice::finalizeInvoice($new_invoice->id);
        custom_log_update_plan("Nueva factura creada y finalizada: {$finalized_invoice->id} - Monto: " . ($finalized_invoice->total / 100) . " EUR");

        // Saltar la lógica normal de actualización
        $final_stripe_subscription = $stripe_subscription_object;
        $response_payload['message'] = "Plan actualizado correctamente. Nueva factura generada por " . ($finalized_invoice->total / 100) . " EUR.";
        $response_payload['invoice_id'] = $finalized_invoice->id;
        $response_payload['requires_payment'] = true;

    } elseif (!$force_new_subscription && $stripe_subscription_object && !$is_on_trial) {
        // --- ACTUALIZAR SUSCRIPCIÓN EXISTENTE (FUERA DE PRUEBA) --- 
        custom_log_update_plan("Iniciando actualización de suscripción (fuera de prueba) para User ID: {$user_id}, Sub ID: {$stripe_subscription_object->id} a New Price ID: {$new_stripe_price_id}");
        $original_price_stripe_object = $stripe_subscription_object->items->data[0]->price;
        $new_price_stripe_object = \Stripe\Price::retrieve($new_stripe_price_id);
        $current_stripe_item_id = $stripe_subscription_object->items->data[0]->id;
        $is_upgrade = false;
        $is_downgrade = false;
        // Determinar si es upgrade, downgrade o cambio lateral/equivalente
        // Consideramos un upgrade si el nuevo precio unitario es mayor O si se cambia de mensual a anual (asumiendo que anual es un compromiso mayor)
        // Consideramos un downgrade si el nuevo precio unitario es menor.
        if ($new_price_stripe_object->unit_amount > $original_price_stripe_object->unit_amount) {
            $is_upgrade = true;
        } elseif ($new_price_stripe_object->unit_amount < $original_price_stripe_object->unit_amount) {
            $is_downgrade = true;
        } elseif ($new_price_stripe_object->recurring->interval === 'year' && $original_price_stripe_object->recurring->interval === 'month') {
            // Cambio de mensual a anual con el mismo precio base (ej. 10/mes a 120/año donde precio base unitario es 10)
            // Stripe considera esto un upgrade y prorratea. Usaremos always_invoice para asegurar cobro inmediato del nuevo periodo.
            $is_upgrade = true; 
        } elseif ($new_price_stripe_object->recurring->interval === 'month' && $original_price_stripe_object->recurring->interval === 'year' && $new_price_stripe_object->unit_amount === $original_price_stripe_object->unit_amount) {
            // Anual a mensual con el mismo precio base es un downgrade en términos de ciclo, generará crédito.
             $is_downgrade = true;
        }
        // Si los price_id son diferentes pero el importe y el intervalo son los mismos, se considera un cambio lateral (is_upgrade e is_downgrade seguirán false)
        $update_params = [
            'items' => [[ 'id' => $current_stripe_item_id, 'price' => $new_stripe_price_id ]],
            'cancel_at_period_end' => false,
            'payment_settings' => ['save_default_payment_method' => 'on_subscription'], // Guardar método de pago por defecto
            'expand' => ['latest_invoice.payment_intent', 'pending_setup_intent'],
        ];
        if ($original_price_stripe_object->id === $new_stripe_price_id) {
            custom_log_update_plan("El nuevo Price ID ({$new_stripe_price_id}) es idéntico al actual. No se realizan cambios funcionales en Stripe. Se refrescará el estado y la BD local.");
            $final_stripe_subscription = $stripe_subscription_object; // Usar el objeto ya recuperado
            $response_payload['message'] = 'El plan seleccionado es el mismo que el actual. No se requieren cambios.';
        } elseif ($is_upgrade) {
            custom_log_update_plan("UPGRADE detectado. Price ID: {$new_stripe_price_id}. Usando proration_behavior: 'always_invoice'.");
            $update_params['proration_behavior'] = 'always_invoice';
            // Para always_invoice, Stripe genera una factura que se intenta pagar inmediatamente.
            // payment_settings con invoice_settings.days_until_due = 0 puede ser redundante si el comportamiento por defecto ya es el cobro inmediato.
            // Stripe recomienda configurar default_payment_method en el Customer o Subscription para cobros automáticos.
            $final_stripe_subscription = Stripe\Subscription::update($stripe_subscription_object->id, $update_params);
            $response_payload['message'] = 'Actualización de plan (upgrade) procesada. Se ha generado una factura por el ajuste.';
        } elseif ($is_downgrade) {
            custom_log_update_plan("DOWNGRADE detectado. Price ID: {$new_stripe_price_id}. Usando proration_behavior: 'create_prorations' (crédito al saldo).");
            $update_params['proration_behavior'] = 'create_prorations';
            $final_stripe_subscription = Stripe\Subscription::update($stripe_subscription_object->id, $update_params);
            $response_payload['message'] = 'Actualización de plan (downgrade) procesada. El crédito correspondiente se aplicará a futuras facturas.';
        } else { // Cambio lateral (mismo precio, mismo intervalo, diferente plan; o ningún cambio detectado arriba)
            custom_log_update_plan("Cambio LATERAL o sin cambio financiero detectado. Price ID: {$new_stripe_price_id}. Usando proration_behavior: 'none'.");
            $update_params['proration_behavior'] = 'none';
            $final_stripe_subscription = Stripe\Subscription::update($stripe_subscription_object->id, $update_params);
            $response_payload['message'] = 'Actualización de plan procesada. No se generaron cargos ni créditos inmediatos.';
        }
        if (isset($final_stripe_subscription) && $final_stripe_subscription) {
            custom_log_update_plan("Suscripción actualizada en Stripe. ID: {$final_stripe_subscription->id}, Estado: {$final_stripe_subscription->status}");
            if (isset($final_stripe_subscription->latest_invoice) && $final_stripe_subscription->latest_invoice) {
                $invoice_object = is_string($final_stripe_subscription->latest_invoice) ? 
                                    \Stripe\Invoice::retrieve($final_stripe_subscription->latest_invoice, ['expand' => ['payment_intent']]) : 
                                    $final_stripe_subscription->latest_invoice;
                custom_log_update_plan("Factura asociada al cambio: ID={$invoice_object->id}, Estado={$invoice_object->status}, Total=" . ($invoice_object->total/100) . ", Pagada=" . ($invoice_object->paid ? 'Sí' : 'No'));
                if (isset($invoice_object->payment_intent) && $invoice_object->payment_intent && 
                    in_array($invoice_object->payment_intent->status, ['requires_action', 'requires_payment_method', 'requires_confirmation'])) {
                    $response_payload['client_secret'] = $invoice_object->payment_intent->client_secret;
                    $response_payload['invoice_id'] = $invoice_object->id;
                    custom_log_update_plan("Cambio de plan requiere acción del cliente. Client Secret enviado para Invoice ID: {$invoice_object->id}");
                    if ($is_upgrade) {
                         $response_payload['message'] = 'El cambio de plan requiere confirmación de pago para completar el upgrade.';
                    } else {
                        // Este caso es menos común para downgrades con create_prorations, pero se maneja por si acaso.
                         $response_payload['message'] = 'El cambio de plan requiere acción adicional.';
                    }
                } elseif ($invoice_object->paid) {
                    if ($is_upgrade) {
                        $response_payload['message'] = 'Upgrade de plan completado y cobrado exitosamente.';
                    } elseif ($is_downgrade) {
                        // Para downgrade con create_prorations, latest_invoice puede ser la factura del ciclo anterior, o una nueva de $0 si el crédito cubre todo.
                        // El mensaje ya está seteado arriba para downgrade.
                    } else {
                        $response_payload['message'] = 'Cambio de plan completado y factura asociada pagada.';
                    }
                } elseif ($invoice_object->status === 'open' && $is_upgrade) {
                     $response_payload['message'] = 'Upgrade de plan procesado, la factura está pendiente de pago por Stripe.';
                } // Otros estados de factura se cubren con los mensajes por defecto de upgrade/downgrade.
            }
             // Es importante siempre refrescar la suscripción por si la llamada anterior no expandió todo lo necesario o si el estado cambió post-factura.
            $final_stripe_subscription = Stripe\Subscription::retrieve($final_stripe_subscription->id, ['expand' => ['latest_invoice.payment_intent', 'pending_setup_intent']]);
        } else if ($original_price_stripe_object->id !== $new_stripe_price_id) { // Entra aquí si no se actualizó la suscripción pero los IDs son diferentes (debería ser raro)
             custom_log_update_plan("ADVERTENCIA: No se obtuvo un objeto final_stripe_subscription después del bloque de actualización, pero los price IDs eran diferentes. Esto es inesperado.");
             // Se intentará guardar en BD con los datos que se tengan, pero puede ser inconsistente.
        }
        custom_log_update_plan("Procesamiento en Stripe para suscripción (fuera de prueba) finalizado. ID final: {$final_stripe_subscription->id}, Estado Stripe: {$final_stripe_subscription->status}");
    } else if ($is_on_trial && $stripe_subscription_object) { 
        // Mantenemos la lógica anterior para cambios EN PRUEBA, ya que Stripe suele manejarlos bien.
        custom_log_update_plan("Actualizando suscripción EN PRUEBA: {$stripe_subscription_object->id} al Price ID: {$new_stripe_price_id} usando proration_behavior: 'create_prorations'");
        $update_params = [
            'items' => [[ 'id' => $stripe_subscription_object->items->data[0]->id, 'price' => $new_stripe_price_id ]],
            'proration_behavior' => 'create_prorations',
            'trial_end' => $stripe_subscription_object->trial_end,
            'cancel_at_period_end' => false,
            'payment_behavior' => 'default_incomplete', 
            'expand' => ['latest_invoice.payment_intent'],
        ];
        $final_stripe_subscription = Stripe\Subscription::update($stripe_subscription_object->id, $update_params);
        custom_log_update_plan("Suscripción (en prueba) actualizada. ID: {$final_stripe_subscription->id}, Estado: {$final_stripe_subscription->status}");
        if (isset($final_stripe_subscription->latest_invoice) && $final_stripe_subscription->latest_invoice) {
             $invoice_details = is_string($final_stripe_subscription->latest_invoice) ? \Stripe\Invoice::retrieve($final_stripe_subscription->latest_invoice, ['expand' => ['payment_intent']]) : $final_stripe_subscription->latest_invoice;
             custom_log_update_plan("Factura de cambio en prueba: ID={$invoice_details->id}, Estado={$invoice_details->status}");
             if (isset($invoice_details->payment_intent) && $invoice_details->payment_intent && in_array($invoice_details->payment_intent->status, ['requires_action', 'requires_payment_method', 'requires_confirmation'])){
                 $response_payload['client_secret'] = $invoice_details->payment_intent->client_secret;
                 $response_payload['invoice_id'] = $invoice_details->id;
                 custom_log_update_plan("Cambio en prueba requiere acción. Client Secret enviado.");
             }
        }
        $response_payload['message'] = 'Actualización de plan en período de prueba completada.';
    } else { // $force_new_subscription es true o no hay $stripe_subscription_object
        // --- CREAR NUEVA SUSCRIPCIÓN --- 
        custom_log_update_plan("Creando nueva suscripción para Customer {$stripe_customer_id} con Price ID {$new_stripe_price_id}");
        $creation_params = [
            'customer' => $stripe_customer_id,
            'items' => [['price' => $new_stripe_price_id]],
            'payment_behavior' => 'default_incomplete', 
            'expand' => ['latest_invoice.payment_intent'],
        ];
        // Considerar añadir 'trial_from_plan' => true si los precios en Stripe tienen periodos de prueba configurados
        // o 'trial_period_days' => X si se define una prueba estándar aquí.
        $final_stripe_subscription = Stripe\Subscription::create($creation_params);
        custom_log_update_plan("Nueva suscripción creada. ID: {$final_stripe_subscription->id}, Estado: {$final_stripe_subscription->status}");
        if (isset($final_stripe_subscription->latest_invoice) && $final_stripe_subscription->latest_invoice) {
            $invoice_details_new_sub = is_string($final_stripe_subscription->latest_invoice) ? \Stripe\Invoice::retrieve($final_stripe_subscription->latest_invoice, ['expand' => ['payment_intent']]) : $final_stripe_subscription->latest_invoice;
            custom_log_update_plan("Factura de nueva suscripción: ID={$invoice_details_new_sub->id}, Estado={$invoice_details_new_sub->status}");
            if (isset($invoice_details_new_sub->payment_intent) && $invoice_details_new_sub->payment_intent && in_array($invoice_details_new_sub->payment_intent->status, ['requires_action', 'requires_payment_method', 'requires_confirmation'])) {
                $response_payload['client_secret'] = $invoice_details_new_sub->payment_intent->client_secret;
                $response_payload['invoice_id'] = $invoice_details_new_sub->id;
                custom_log_update_plan("Nueva suscripción requiere acción. Client Secret enviado.");
            }
        }
        $response_payload['message'] = $final_stripe_subscription->status === 'trialing' ? 'Suscripción iniciada en período de prueba.' : 'Suscripción creada correctamente.';
    }
    custom_log_update_plan("Procesamiento en Stripe finalizado. Suscripción Final ID: {$final_stripe_subscription->id}, Estado Stripe: {$final_stripe_subscription->status}");
    $estado_final_para_db = $final_stripe_subscription->status;
    if ($final_stripe_subscription->trial_end && $final_stripe_subscription->trial_end > time() && !in_array($final_stripe_subscription->status, ['canceled', 'incomplete_expired'])) {
        $estado_final_para_db = 'trialing';
    }
    $upsert_data = [
        'user_id' => $user_id,
        'plan_id' => $db_plan_id,
        'stripe_subscription_id' => $final_stripe_subscription->id,
        'stripe_price_id' => $new_stripe_price_id, 
        'estado' => $estado_final_para_db,
        'billing_cycle' => $new_billing_cycle,
        'fecha_inicio_periodo_actual' => date('Y-m-d H:i:s', $final_stripe_subscription->current_period_start),
        'fecha_fin_periodo_actual' => date('Y-m-d H:i:s', $final_stripe_subscription->current_period_end),
        'fecha_inicio_prueba' => $final_stripe_subscription->trial_start ? date('Y-m-d H:i:s', $final_stripe_subscription->trial_start) : null,
        'fecha_fin_prueba' => $final_stripe_subscription->trial_end ? date('Y-m-d H:i:s', $final_stripe_subscription->trial_end) : null,
        'nombre_plan_display' => $db_nombre_plan,
        'agency_id' => null, // Placeholder, se rellena abajo
    ];
    // Obtener agency_id para la suscripción basado en $user_id (que es el owner_user_id)
    $agency_id_for_subscription_upsert = null;
    $stmt_owner_agency = $db->prepare("SELECT id FROM agencies WHERE owner_user_id = ? LIMIT 1");
    if($stmt_owner_agency) {
        $stmt_owner_agency->bind_param("i", $user_id);
        $stmt_owner_agency->execute();
        $owner_agency_res = $stmt_owner_agency->get_result();
        if($owner_agency_row = $owner_agency_res->fetch_assoc()) {
            $agency_id_for_subscription_upsert = $owner_agency_row['id'];
            $upsert_data['agency_id'] = $agency_id_for_subscription_upsert;
        }
        $stmt_owner_agency->close();
    } else {
        custom_log_update_plan("ADVERTENCIA CRÍTICA: No se pudo obtener agency_id para el owner_user_id {$user_id}. La suscripción no se asociará a una agencia y clientes_valorador no se actualizará.");
    }
    $sql_upsert = "INSERT INTO suscripciones 
        (user_id, plan_id, stripe_subscription_id, stripe_price_id, estado, billing_cycle, fecha_inicio_periodo_actual, fecha_fin_periodo_actual, fecha_inicio_prueba, fecha_fin_prueba, nombre_plan_display, fecha_creacion, fecha_modificacion, fecha_cancelacion, fecha_cancelada_en_stripe, agency_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NULL, NULL, ?)
        ON DUPLICATE KEY UPDATE
        plan_id = VALUES(plan_id), 
        stripe_price_id = VALUES(stripe_price_id), 
        estado = VALUES(estado), 
        billing_cycle = VALUES(billing_cycle), 
        fecha_inicio_periodo_actual = VALUES(fecha_inicio_periodo_actual), 
        fecha_fin_periodo_actual = VALUES(fecha_fin_periodo_actual), 
        fecha_inicio_prueba = VALUES(fecha_inicio_prueba), 
        fecha_fin_prueba = VALUES(fecha_fin_prueba), 
        nombre_plan_display = VALUES(nombre_plan_display), 
        fecha_cancelacion = NULL, 
        fecha_cancelada_en_stripe = NULL, 
        agency_id = VALUES(agency_id), 
        fecha_modificacion = NOW()";
    custom_log_update_plan("Datos para UPSERT en BD local: " . json_encode($upsert_data));
    $stmt_upsert_db = $db->prepare($sql_upsert);
    if (!$stmt_upsert_db) throw new Exception("Error preparando UPSERT DB: " . $db->error);
    $stmt_upsert_db->bind_param("iisssssssssi", 
        $upsert_data['user_id'], $upsert_data['plan_id'], $upsert_data['stripe_subscription_id'], $upsert_data['stripe_price_id'], 
        $upsert_data['estado'], $upsert_data['billing_cycle'], $upsert_data['fecha_inicio_periodo_actual'], $upsert_data['fecha_fin_periodo_actual'], 
        $upsert_data['fecha_inicio_prueba'], $upsert_data['fecha_fin_prueba'], $upsert_data['nombre_plan_display'],
        $upsert_data['agency_id']
    );
    $execute_success = $stmt_upsert_db->execute();
    if (!$execute_success) throw new Exception("Error ejecutando UPSERT DB: " . $stmt_upsert_db->error);
    custom_log_update_plan("UPSERT DB ejecutado. Filas afectadas: " . $stmt_upsert_db->affected_rows);
    $stmt_upsert_db->close();
    // Actualizar el campo 'activo' en clientes_valorador según el nuevo estado de la suscripción
    $new_subscription_status_for_valorador = $upsert_data['estado'];
    $valorador_activo_flag = (in_array(strtolower($new_subscription_status_for_valorador), ['active', 'trialing'])) ? 1 : 0;
    $agency_id_for_valorador_update = $upsert_data['agency_id']; // Asumiendo que agency_id está en upsert_data o disponible
    if (isset($agency_id_for_valorador_update) && !empty($agency_id_for_valorador_update)) {
        custom_log_update_plan("Actualizando clientes_valorador.activo a {$valorador_activo_flag} para agency_id: {$agency_id_for_valorador_update}");
        $stmt_update_valorador = $db->prepare("UPDATE clientes_valorador SET activo = ? WHERE agency_id = ?");
        if ($stmt_update_valorador) {
            $stmt_update_valorador->bind_param("ii", $valorador_activo_flag, $agency_id_for_valorador_update);
            $update_v_success = $stmt_update_valorador->execute();
            if ($update_v_success) {
                custom_log_update_plan("clientes_valorador.activo actualizado. Filas afectadas: " . $stmt_update_valorador->affected_rows);
            } else {
                custom_log_update_plan("ERROR al actualizar clientes_valorador.activo: " . $stmt_update_valorador->error);
                // No lanzar excepción aquí para no revertir la transacción principal de suscripción, solo loggear.
            }
            $stmt_update_valorador->close();
        } else {
            custom_log_update_plan("ERROR preparando statement para actualizar clientes_valorador.activo: " . $db->error);
        }
    } else {
        custom_log_update_plan("ADVERTENCIA: No se pudo determinar agency_id para actualizar clientes_valorador.activo.");
    }
    $db->commit();
    $transaction_active = false;
    custom_log_update_plan("Transacción completada (commit ejecutado).");
    $response_payload = [
        'success' => true, 
        'message' => 'Operación de suscripción completada correctamente.',
        'subscription_status' => $final_stripe_subscription->status,
        'stripe_subscription_id' => $final_stripe_subscription->id,
        'client_secret' => null 
    ];
    if ($final_stripe_subscription->status === 'incomplete' && $final_stripe_subscription->latest_invoice && $final_stripe_subscription->latest_invoice->payment_intent) {
        $response_payload['client_secret'] = $final_stripe_subscription->latest_invoice->payment_intent->client_secret;
        $response_payload['message'] = 'Suscripción creada, requiere acción de pago.';
    } elseif ($final_stripe_subscription->status === 'trialing') {
        $response_payload['message'] = 'Suscripción iniciada en período de prueba.';
    }
} catch (Stripe\Exception\ApiErrorException $e) {
    if ($db && $transaction_active) $db->rollback();
    $transaction_active = false;
    custom_log_update_plan("Stripe API Error: " . $e->getMessage() . " (Type: " . $e->getError()->type . ", Code: " . $e->getError()->code . ")");
    http_response_code($e->getHttpStatus() ?: 500);
    $response_payload['message'] = "Error de Stripe: " . $e->getMessage();
} catch (Throwable $e) {
    if ($db && $transaction_active) $db->rollback();
    $transaction_active = false;
    custom_log_update_plan("General Error: " . $e->getMessage() . " en " . $e->getFile() . ":" . $e->getLine() . "\nStack: " . $e->getTraceAsString());
    if (!headers_sent()) http_response_code(500);
    $response_payload['message'] = 'Error interno del servidor: ' . $e->getMessage();
}
if (ob_get_length()) ob_end_clean();
echo json_encode($response_payload);
if ($db) $db->close();
custom_log_update_plan("--- [update-subscription-plan.php V2] FINALIZADO ---");
exit;
?>
