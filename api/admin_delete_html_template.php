<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$template_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($template_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de plantilla requerido']);
    exit;
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}
$conn->set_charset(DB_CHARSET);

try {
    // Verificar que la plantilla existe
    $check_sql = "
        SELECT 
            id, 
            nombre_interno_plantilla,
            (SELECT COUNT(*) FROM sequence_steps WHERE email_template_id = email_plantillas_html.id) as usage_count
        FROM email_plantillas_html 
        WHERE id = ?
    ";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param('i', $template_id);
    $check_stmt->execute();
    $template = $check_stmt->get_result()->fetch_assoc();
    
    if (!$template) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Plantilla no encontrada']);
        exit;
    }
    
    // Verificar que no esté en uso
    if ((int)$template['usage_count'] > 0) {
        http_response_code(400);
        echo json_encode([
            'success' => false, 
            'message' => 'No se puede eliminar la plantilla porque está siendo utilizada en ' . $template['usage_count'] . ' paso(s) de secuencia'
        ]);
        exit;
    }
    
    // Eliminar plantilla
    $delete_sql = "DELETE FROM email_plantillas_html WHERE id = ?";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param('i', $template_id);
    
    if (!$delete_stmt->execute()) {
        throw new Exception('Error eliminando plantilla: ' . $conn->error);
    }
    
    if ($delete_stmt->affected_rows === 0) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Plantilla no encontrada']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Plantilla '{$template['nombre_interno_plantilla']}' eliminada exitosamente"
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error eliminando plantilla: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
