<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// bootstrap.php se encargará de la configuración de errores y carga de dependencias.
// --- Logger específico para este script ---
if (!function_exists('custom_log_preview')) {
    function custom_log_preview($message) {
        $logFile = __DIR__ . '/debug_preview_change.log';
        $timestamp = date('Y-m-d H:i:s');
        // Usar @ para suprimir errores si el log no es escribible, aunque idealmente debería serlo.
        @file_put_contents($logFile, "[$timestamp] [preview-change-LOG] " . print_r($message, true) . PHP_EOL, FILE_APPEND);
        // También enviar a error_log general para visibilidad
        error_log("[preview-change-SCRIPT-LOG] " . print_r($message, true));
    }
}
custom_log_preview("--- [preview-subscription-change.php V3 - Customer Balance Aware] INICIADO ---");
header('Content-Type: application/json; charset=utf-8');
$user_id_from_token = null;
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0'); // Suprimir errores durante la decodificación del JWT
        try {
            $jwt_secret_key = JWT_SECRET; // Asegúrate que JWT_SECRET esté definida en bootstrap o config.
            if (empty($jwt_secret_key)) {
                throw new Exception('JWT_SECRET constante no configurada.');
            }
            $decoded = Firebase\JWT\JWT::decode($token, new Firebase\JWT\Key($jwt_secret_key, 'HS256'));
            if (!isset($decoded->user_id)) {
                throw new Exception('Token JWT inválido: no contiene user_id.');
            }
            $user_id_from_token = $decoded->user_id;
            custom_log_preview("JWT decodificado. User ID: {$user_id_from_token}");
        } catch (Exception $e) {
            custom_log_preview("JWT Decode Error: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors);
        }
    } else {
        custom_log_preview("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_preview("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
if (empty($user_id_from_token)) {
    custom_log_preview("CRITICAL: User ID from token is empty after auth block.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Autenticación fallida.']);
    exit();
}
$response_payload = ['success' => false, 'message' => 'Error desconocido en previsualización.'];
$db = null;
$transaction_active = false; 
$user_id = $user_id_from_token;
$input = json_decode(file_get_contents('php://input'), true);
$new_plan_slug = $input['new_plan_slug'] ?? null;
$new_billing_cycle = $input['new_billing_cycle'] ?? null; // 'monthly' o 'annual'
if (empty($new_plan_slug) || empty($new_billing_cycle) || !in_array($new_billing_cycle, ['monthly', 'annual'])) {
    custom_log_preview("Error: Parámetros inválidos. Slug: " . ($new_plan_slug ?? 'N/A') . ", Cycle: " . ($new_billing_cycle ?? 'N/A'));
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Parámetros inválidos. Se requiere new_plan_slug y new_billing_cycle.']);
    exit;
}
custom_log_preview("Solicitud para previsualizar cambio a Plan Slug: {$new_plan_slug}, Ciclo: {$new_billing_cycle} para User ID: {$user_id}");
try {
    $db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
    if ($db->connect_error) {
        throw new Exception("Error de conexión a BD: " . $db->connect_error);
    }
    $db->set_charset(DB_CHARSET);
    custom_log_preview("Conexión mysqli a BD establecida.");
    $db->begin_transaction();
    $transaction_active = true;
    // 1. Obtener datos del usuario y su suscripción actual
    $stmt_user_sub = $db->prepare(
        "SELECT u.stripe_customer_id, s.stripe_subscription_id, s.stripe_price_id, pl.name as current_plan_name, s.billing_cycle as current_billing_cycle
         FROM usuarios u
         JOIN suscripciones s ON u.id = s.user_id
         JOIN plans pl ON s.plan_id = pl.id
         WHERE u.id = ? AND s.estado IN ('active', 'trialing', 'past_due') 
         ORDER BY s.fecha_creacion DESC LIMIT 1"
    );
    if (!$stmt_user_sub) throw new Exception("Error preparando consulta de usuario/suscripción: " . $db->error);
    $stmt_user_sub->bind_param("i", $user_id);
    $stmt_user_sub->execute();
    $user_sub_details = $stmt_user_sub->get_result()->fetch_assoc();
    $stmt_user_sub->close();
    if (!$user_sub_details || !isset($user_sub_details['stripe_customer_id']) || !isset($user_sub_details['stripe_subscription_id'])) {
        // Tratar de obtener el customer_id incluso si no hay suscripción para el caso de nueva suscripción.
        $stmt_customer = $db->prepare("SELECT stripe_customer_id FROM usuarios WHERE id = ?");
        $stmt_customer->bind_param("i", $user_id);
        $stmt_customer->execute();
        $customer_details_for_new_sub = $stmt_customer->get_result()->fetch_assoc();
        $stmt_customer->close();
        if (!$customer_details_for_new_sub || !isset($customer_details_for_new_sub['stripe_customer_id'])) {
           throw new Exception("No se encontró ID de cliente de Stripe para el usuario.");
        }
        $stripe_customer_id = $customer_details_for_new_sub['stripe_customer_id'];
        $current_stripe_subscription_id = null; // No hay suscripción activa
        $current_stripe_price_id = null;
        $current_plan_name = "Ninguno";
        custom_log_preview("No hay suscripción activa. Se previsualizará como nueva suscripción para Customer ID: {$stripe_customer_id}.");
    } else {
        $stripe_customer_id = $user_sub_details['stripe_customer_id'];
        $current_stripe_subscription_id = $user_sub_details['stripe_subscription_id'];
        $current_stripe_price_id = $user_sub_details['stripe_price_id'];
        $current_plan_name = $user_sub_details['current_plan_name'];
    }
    // 2. Obtener datos del nuevo plan solicitado
    $stmt_new_plan = $db->prepare("SELECT id, name, stripe_price_id_monthly, stripe_price_id_annual FROM plans WHERE slug = ?");
    if (!$stmt_new_plan) throw new Exception("Error preparando consulta de nuevo plan: " . $db->error);
    $stmt_new_plan->bind_param("s", $new_plan_slug);
    $stmt_new_plan->execute();
    $new_plan_details_db = $stmt_new_plan->get_result()->fetch_assoc();
    $stmt_new_plan->close();
    if (!$new_plan_details_db) throw new Exception("Nuevo plan '{$new_plan_slug}' no encontrado.");
    $new_stripe_price_id = ($new_billing_cycle === 'annual') ? $new_plan_details_db['stripe_price_id_annual'] : $new_plan_details_db['stripe_price_id_monthly'];
    $new_plan_name = $new_plan_details_db['name'];
    if (empty($new_stripe_price_id)) throw new Exception("ID de precio de Stripe no configurado para el plan {$new_plan_slug} / {$new_billing_cycle}.");
    custom_log_preview("Nuevo plan '{$new_plan_name}' ({$new_plan_slug}), Ciclo: {$new_billing_cycle}, Nuevo PriceID Stripe: {$new_stripe_price_id}");
    custom_log_preview("Plan Actual: '{$current_plan_name}', PriceID Stripe: " . ($current_stripe_price_id ?? 'N/A'));
    Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
    $customer_balance_cents = 0;
    try {
        $stripe_customer = \Stripe\Customer::retrieve($stripe_customer_id);
        $customer_balance_cents = $stripe_customer->balance; // Negativo si el cliente tiene crédito
        custom_log_preview("Saldo del cliente {$stripe_customer_id} recuperado: {$customer_balance_cents} centavos.");
    } catch (Exception $e) {
        custom_log_preview("Error al recuperar el saldo del cliente {$stripe_customer_id}: " . $e->getMessage() . ". Se asumirá saldo cero.");
    }
    $response_data = [];
    if (!$current_stripe_subscription_id || !$current_stripe_price_id) {
        // Caso: NUEVA SUSCRIPCIÓN
        custom_log_preview("Previsualizando como NUEVA suscripción.");
        $new_price_object_for_new_sub = \Stripe\Price::retrieve($new_stripe_price_id);
        $amount_due_today_final_cents = $new_price_object_for_new_sub->unit_amount;
        $balance_applied_cents = 0;
        if ($customer_balance_cents < 0) { // Cliente tiene crédito
            $credit_available = abs($customer_balance_cents);
            if ($amount_due_today_final_cents > 0) {
                if ($credit_available >= $amount_due_today_final_cents) {
                    $balance_applied_cents = $amount_due_today_final_cents;
                    $amount_due_today_final_cents = 0;
                } else {
                    $balance_applied_cents = $credit_available;
                    $amount_due_today_final_cents -= $credit_available;
                }
            }
        }
        $response_data = [
            'is_new_subscription' => true,
            'is_change' => false,
            'message' => 'Previsualización para nueva suscripción.',
            'amount_due_today' => $amount_due_today_final_cents / 100,
            'prorated_credit_cents' => 0,
            'prorated_charge_cents' => $new_price_object_for_new_sub->unit_amount, // Cargo bruto del nuevo plan
            'next_payment_date' => null, // Se determinará al crear la suscripción
            'new_plan_name' => $new_plan_name,
            'new_plan_price_display' => $new_price_object_for_new_sub->unit_amount / 100,
            'new_plan_price_next_cycle' => $new_price_object_for_new_sub->unit_amount / 100,
            'new_plan_interval' => $new_price_object_for_new_sub->recurring->interval,
            'new_plan_billing_cycle' => $new_billing_cycle,
            'currency' => strtoupper($new_price_object_for_new_sub->currency),
            'current_plan_name' => $current_plan_name,
            'customer_balance_cents' => $customer_balance_cents,
            'balance_applied_to_due_amount_cents' => $balance_applied_cents,
            'is_calculated_upgrade' => false,
            'is_calculated_downgrade' => false,
            'lines' => [] // No hay invoice_preview para nueva sub directamente aquí
        ];
    } elseif ($new_stripe_price_id === $current_stripe_price_id) {
        // Caso: MISMO PLAN
        $stripe_subscription_for_same_plan = \Stripe\Subscription::retrieve($current_stripe_subscription_id);
        $new_price_object_for_same_plan = \Stripe\Price::retrieve($new_stripe_price_id);
        $response_data = [
            'is_same_plan' => true,
            'is_change' => false,
            'message' => 'Estás seleccionando el mismo plan actual.',
            'next_payment_date' => date('Y-m-d', $stripe_subscription_for_same_plan->current_period_end),
            'amount_due_today' => 0,
            'new_plan_name' => $new_plan_name,
            'new_plan_price_display' => $new_price_object_for_same_plan->unit_amount / 100,
            'new_plan_price_next_cycle' => $new_price_object_for_same_plan->unit_amount / 100,
            'new_plan_interval' => $new_price_object_for_same_plan->recurring->interval,
            'new_plan_billing_cycle' => $new_billing_cycle, // El ciclo seleccionado por el usuario
            'currency' => strtoupper($new_price_object_for_same_plan->currency),
            'current_plan_name' => $current_plan_name,
            'customer_balance_cents' => $customer_balance_cents,
            'balance_applied_to_due_amount_cents' => 0,
            'prorated_credit_cents' => 0,
            'prorated_charge_cents' => 0,
            'is_calculated_upgrade' => false,
            'is_calculated_downgrade' => false,
            'lines' => [] // No hay invoice_preview para mismo plan directamente aquí
        ];
    } else {
        // Caso: CAMBIO DE PLAN (UPGRADE, DOWNGRADE O LATERAL)
        $current_price_object = \Stripe\Price::retrieve($current_stripe_price_id);
        $new_price_object = \Stripe\Price::retrieve($new_stripe_price_id);
        $stripe_subscription = \Stripe\Subscription::retrieve($current_stripe_subscription_id);
        $is_on_trial = ($stripe_subscription->status === 'trialing' && $stripe_subscription->trial_end && $stripe_subscription->trial_end > time());
        $is_upgrade = false;
        $is_downgrade = false;
        // Lógica original para determinar upgrade/downgrade
        if ($new_price_object->unit_amount > $current_price_object->unit_amount) {
            $is_upgrade = true;
        } elseif ($new_price_object->unit_amount < $current_price_object->unit_amount) {
            $is_downgrade = true;
        } elseif ($new_price_object->unit_amount == $current_price_object->unit_amount) {
            if ($new_price_object->recurring->interval === 'year' && $current_price_object->recurring->interval === 'month') {
                $is_upgrade = true; // Cambio de mensual a anual (mismo precio base) es upgrade
            } elseif ($new_price_object->recurring->interval === 'month' && $current_price_object->recurring->interval === 'year') {
                $is_downgrade = true; // Cambio de anual a mensual (mismo precio base) es downgrade
            }
        }
        $amount_due_today_final_cents = 0;
        $prorated_credit_generated_cents = 0; 
        $prorated_charge_generated_cents = 0; 
        $balance_applied_cents = 0; // Cuánto del saldo PREVIO se usó en ESTE cobro de upgrade
        $next_payment_date_preview = date('Y-m-d', $stripe_subscription->current_period_end);
        if ($is_upgrade) {
            custom_log_preview("Previsualizando UPGRADE con 'always_invoice'.");
            $preview_params = [
                'customer' => $stripe_customer_id,
                'subscription' => $current_stripe_subscription_id,
                'subscription_items' => [['id' => $stripe_subscription->items->data[0]->id, 'price' => $new_stripe_price_id]],
                'subscription_proration_date' => time(),
                'subscription_proration_behavior' => 'always_invoice'
            ];
            $invoice_preview = \Stripe\Invoice::upcoming($preview_params);
            // $invoice_preview->total ya incluye el descuento del customer_balance si Stripe lo aplica.
            $amount_due_today_final_cents = $invoice_preview->total; 
            $actual_charge_for_new_plan_prorated_cents = 0;
            $actual_credit_for_old_plan_prorated_cents = 0;
            foreach($invoice_preview->lines->data as $line){
                if ($line->type === 'invoiceitem') { // Las líneas de prorrateo son invoiceitems
                    if ($line->amount > 0 && strpos($line->description, 'Remaining time on') === 0) {
                        $actual_charge_for_new_plan_prorated_cents += $line->amount;
                    } elseif ($line->amount < 0 && strpos($line->description, 'Unused time on') === 0) {
                        $actual_credit_for_old_plan_prorated_cents += abs($line->amount);
                    }
                }
            }
            $prorated_charge_generated_cents = $actual_charge_for_new_plan_prorated_cents;
            $prorated_credit_generated_cents = $actual_credit_for_old_plan_prorated_cents;
            // El amount_due_today_final_cents ya viene de $invoice_preview->total que considera el customer_balance.
            // Recalculamos balance_applied_cents basado en el prorrateo bruto y el total.
            $balance_applied_cents = 0;
            if ($customer_balance_cents < 0) { // Cliente tiene crédito
                $net_charge_before_balance = $prorated_charge_generated_cents - $prorated_credit_generated_cents;
                if ($net_charge_before_balance > 0 && $amount_due_today_final_cents < $net_charge_before_balance) {
                    $balance_applied_cents = $net_charge_before_balance - $amount_due_today_final_cents;
                } elseif ($net_charge_before_balance <= 0) { // Si el prorrateo ya es un crédito o cero, no se aplica balance al 'amount_due_today' (que sería cero o negativo)
                    // $balance_applied_cents se queda en 0 para este 'amount_due_today'
                } else if ($amount_due_today_final_cents == 0 && $net_charge_before_balance > 0 && abs($customer_balance_cents) >= $net_charge_before_balance) {
                     // Caso donde el balance cubre exactamente el costo del upgrade
                     $balance_applied_cents = $net_charge_before_balance;
                }
            }
            custom_log_preview("Previsión UPGRADE: Neto a cobrar: {$amount_due_today_final_cents}, Cargo Bruto Prorrateo Nuevo: {$prorated_charge_generated_cents}, Crédito Prorrateo Antiguo: {$prorated_credit_generated_cents}, Saldo Cliente Previo: {$customer_balance_cents}, Saldo Aplicado: {$balance_applied_cents}");
            if ($invoice_preview->lines) {
                foreach($invoice_preview->lines->data as $line) {
                    if ($line->type === 'subscription' && $line->period && $line->period->end) {
                        $next_payment_date_preview = date('Y-m-d', $line->period->end);
                        break;
                    }
                }
            }
        } elseif ($is_downgrade) {
            custom_log_preview("Previsualizando DOWNGRADE con 'create_prorations'.");
            $invoice_preview = \Stripe\Invoice::upcoming([
                'customer' => $stripe_customer_id,
                'subscription' => $current_stripe_subscription_id,
                'subscription_items' => [['id' => $stripe_subscription->items->data[0]->id, 'price' => $new_stripe_price_id]],
                'subscription_proration_date' => time(),
                'subscription_proration_behavior' => 'create_prorations'
            ]);
            $amount_due_today_final_cents = 0; // En downgrade con create_prorations, no hay cobro inmediato.
            $actual_credit_for_old_plan_prorated_cents = 0;
            $actual_charge_for_new_plan_prorated_cents = 0;
            custom_log_preview("DOWNGRADE: Analizando Invoice Preview Lines para calcular crédito y cargo de prorrateo: " . json_encode($invoice_preview->lines->data));
            if (isset($invoice_preview->lines) && isset($invoice_preview->lines->data)) {
                foreach ($invoice_preview->lines->data as $line) {
                    // Buscamos específicamente los invoiceitem que son resultado del prorrateo
                    if ($line->type === 'invoiceitem' && isset($line->proration) && $line->proration === true) {
                        if ($line->amount < 0) { // Crédito por tiempo no usado del plan antiguo
                            $actual_credit_for_old_plan_prorated_cents += abs($line->amount);
                        } elseif ($line->amount > 0) { // Cargo por tiempo restante del nuevo plan en ciclo actual
                            $actual_charge_for_new_plan_prorated_cents += $line->amount;
                        }
                    }
                }
            }
            // Este es el crédito NETO que se generará y se aplicará al saldo del cliente.
            $net_proration_credit_cents = $actual_credit_for_old_plan_prorated_cents - $actual_charge_for_new_plan_prorated_cents;
            if ($net_proration_credit_cents < 0) {
                 // Esto no debería ocurrir en un downgrade normal que genera crédito. Si ocurre, es un cargo neto.
                 custom_log_preview("ADVERTENCIA: net_proration_credit_cents ({$net_proration_credit_cents}) es negativo para un downgrade. Esto indica un cargo neto en lugar de un crédito. El crédito generado se reportará como 0.");
                 $net_proration_credit_cents = 0; 
            }
            // Para la respuesta raíz, 'prorated_credit_cents' debe ser el crédito neto que se aplicará al saldo.
            // 'prorated_charge_cents' en la raíz es 0 para downgrade ya que no hay cargo inmediato HOY.
            $prorated_credit_generated_cents = $net_proration_credit_cents;
            $prorated_charge_generated_cents = 0; // No hay cargo inmediato hoy por el cambio en sí.
            // Para downgrades, la fecha del próximo pago es el final del periodo actual de la suscripción original
            // porque el cambio de precio solo toma efecto en la renovación.
            $next_payment_date_preview = date('Y-m-d', $stripe_subscription->current_period_end);
            custom_log_preview(
                "Previsión DOWNGRADE: " .
                "Crédito Bruto (tiempo no usado plan antiguo): {$actual_credit_for_old_plan_prorated_cents}, " .
                "Cargo Bruto (tiempo restante nuevo plan ciclo actual): {$actual_charge_for_new_plan_prorated_cents}, " .
                "Crédito NETO generado (se aplicará al saldo): {$net_proration_credit_cents}. " .
                "Saldo Cliente (previo): {$customer_balance_cents}. Próximo pago: {$next_payment_date_preview}"
            );
        } else { // Cambio lateral
            custom_log_preview("Previsualizando CAMBIO LATERAL con 'none'.");
            $invoice_preview = \Stripe\Invoice::upcoming([
                'customer' => $stripe_customer_id,
                'subscription' => $current_stripe_subscription_id,
                'subscription_items' => [['id' => $stripe_subscription->items->data[0]->id, 'price' => $new_stripe_price_id]],
                'subscription_proration_date' => time(),
                'subscription_proration_behavior' => 'none'
            ]);
            // Para 'none', el total de la factura upcoming debería ser el costo normal del siguiente ciclo del nuevo plan, o cero si no hay cambio de precio.
            // No hay "amount_due_today" por el cambio en sí.
            $amount_due_today_final_cents = 0; 
            custom_log_preview("Previsión CAMBIO LATERAL: InvoicePreview Total (próxima factura normal): {$invoice_preview->total}\");");
            if ($invoice_preview->lines) {
                 foreach($invoice_preview->lines->data as $line) {
                    if ($line->type === 'subscription' && $line->period && $line->period->end) {
                        $next_payment_date_preview = date('Y-m-d', $line->period->end);
                        break;
                    }
                }
            }
        }
        $proration_details_for_frontend = null;
        if ($is_downgrade) {
            // Para downgrades, usamos los valores calculados específicamente para el desglose.
            if ($actual_credit_for_old_plan_prorated_cents > 0 || $actual_charge_for_new_plan_prorated_cents > 0) {
                $proration_details_for_frontend = [
                    'credit_for_old_plan' => $actual_credit_for_old_plan_prorated_cents / 100,
                    'charge_for_new_plan_current_cycle' => $actual_charge_for_new_plan_prorated_cents / 100,
                    // net_proration_amount aquí es el crédito neto (positivo si es crédito)
                    'net_proration_amount' => ($actual_credit_for_old_plan_prorated_cents - $actual_charge_for_new_plan_prorated_cents) / 100
                ];
            }
        } elseif (!$is_on_trial && ($prorated_credit_generated_cents > 0 || $prorated_charge_generated_cents > 0)) {
            // Para upgrades (y otros casos si tuvieran prorrateo), la lógica anterior se mantiene.
            // $prorated_credit_generated_cents y $prorated_charge_generated_cents vienen del cálculo de upgrade.
            $proration_details_for_frontend = [
                'credit_for_old_plan' => $prorated_credit_generated_cents / 100,
                'charge_for_new_plan_current_cycle' => $prorated_charge_generated_cents / 100,
                'net_proration_amount' => ($prorated_charge_generated_cents - $prorated_credit_generated_cents) / 100
            ];
        }
        $response_data = [
            'is_same_plan' => false,
            'is_new_subscription' => false,
            'is_change' => true,
            'message' => 'Previsualización del cambio de plan.',
            'amount_due_today' => $amount_due_today_final_cents / 100,
            'prorated_credit_cents' => $prorated_credit_generated_cents, // NETO para downgrade, o el crédito del prorrateo de upgrade
            'prorated_charge_cents' => $prorated_charge_generated_cents, // 0 para downgrade, o el cargo del prorrateo de upgrade
            'proration_calculation_details' => $proration_details_for_frontend,
            'next_payment_date' => $next_payment_date_preview,
            'new_plan_name' => $new_plan_name,
            'new_plan_price_display' => $new_price_object->unit_amount / 100,
            'new_plan_price_next_cycle' => $new_price_object->unit_amount / 100,
            'new_plan_interval' => $new_price_object->recurring->interval,
            'new_plan_billing_cycle' => $new_billing_cycle,
            'currency' => strtoupper($new_price_object->currency),
            'current_plan_name' => $current_plan_name,
            'customer_balance_cents' => $customer_balance_cents, 
            'balance_applied_to_due_amount_cents' => $balance_applied_cents,
            'is_calculated_upgrade' => $is_upgrade,
            'is_calculated_downgrade' => $is_downgrade,
            'lines' => isset($invoice_preview->lines) ? $invoice_preview->lines->data : []
        ];
    }
    $db->commit();
    $transaction_active = false;
    custom_log_preview("Transacción de previsualización completada (commit).");
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'data' => $response_data]);
} catch (Stripe\Exception\ApiErrorException $e) {
    if ($db && $transaction_active) $db->rollback();
    custom_log_preview("Stripe API Error en Preview: " . $e->getMessage() . " (Type: " . $e->getError()->type . ", Code: " . $e->getError()->code . ")");
    http_response_code($e->getHttpStatus() ?: 500);
    echo json_encode(['success' => false, 'message' => "Error de Stripe al previsualizar: " . $e->getMessage(), 'stripe_error_code' => $e->getError()->code]);
} catch (Throwable $e) {
    if ($db && $transaction_active) $db->rollback();
    custom_log_preview("General Error en Preview: " . $e->getMessage() . " en " . $e->getFile() . ":" . $e->getLine());
    if (!headers_sent()) {
        http_response_code(500);
    }
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al previsualizar: ' . $e->getMessage()]);
} finally {
    if ($db) {
        $db->close();
        custom_log_preview("Conexión a BD cerrada.");
    }
    custom_log_preview("--- [preview-subscription-change.php V3] FINALIZADO ---");
}
exit;