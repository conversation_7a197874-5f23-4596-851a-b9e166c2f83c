<?php
declare(strict_types=1);
// /api/utils/AIService.php
namespace Api\utils;

use OpenAI;
use Api\lib\Logger; // CAMBIADO a lib minúscula // Asumiendo que Logger está en Api\Lib y bootstrap.php ya lo cargó.

class AIService {
    private ?string $apiKey;
    private bool $simulationMode = false;
    private string $apiEndpoint = 'https://api.openai.com/v1/chat/completions'; 
    private string $apiModel = 'gpt-4o-mini'; // O el modelo configurado, ej. gpt-3.5-turbo

    public function __construct() {
        // Leer API Key desde las constantes definidas en bootstrap.php
        $this->apiKey = defined('OPENAI_API_KEY') ? OPENAI_API_KEY : null;

        // El modo simulación se activa si no hay API Key o si APP_ENV indica un modo de simulación específico.
        if (empty($this->apiKey) || (defined('APP_ENV') && APP_ENV === 'development_simulate_ai')) {
            $this->simulationMode = true;
            Logger::warning("AIService: Operando en modo SIMULACIÓN." . (empty($this->apiKey) ? " (API Key no encontrada)" : ""));
        } else {
            $this->simulationMode = false;
            Logger::debug("AIService: Operando en modo REAL con API Key.");
        }
        // Podrías leer $this->apiModel desde una constante de config si quieres hacerlo más flexible
        // if (defined('OPENAI_DEFAULT_MODEL')) { $this->apiModel = OPENAI_DEFAULT_MODEL; }
    }

    /**
     * Genera el cuerpo HTML de un email utilizando un prompt y datos contextuales.
     *
     * @param string $promptConPlaceholders El prompt con placeholders {{placeholder}}.
     * @param array $datosContextuales Array asociativo para reemplazar placeholders.
     * @return array ['success' => bool, 'cuerpo_html' => string, 'error' => string|null]
     */
    public function generarContenidoEmailCuerpo(string $promptConPlaceholders, array $datosContextuales): array {
        $promptFinal = $promptConPlaceholders;
        foreach ($datosContextuales as $key => $value) {
            if (is_scalar($value) || (is_object($value) && method_exists($value, '__toString'))) {
                $promptFinal = str_replace("{{" . $key . "}}", (string)$value, $promptFinal); 
            } elseif (is_array($value)) {
                Logger::debug("AIService: Placeholder para array '{{{$key}}}' no reemplazado en prompt.", ['key' => $key]);
            }
        }
        
        Logger::debug("AIService: Prompt final para IA:", ['prompt_length' => strlen($promptFinal), 'prompt_extract' => substr($promptFinal, 0, 200) . "..."]);

        if ($this->simulationMode) {
            return $this->simularRespuestaIA($promptFinal, $datosContextuales);
        }

        if (empty($this->apiKey)) {
             Logger::error("AIService: API Key no disponible para llamada real. Forzando simulación.");
             return $this->simularRespuestaIA($promptFinal, $datosContextuales);
        }

        try {
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey,
            ];

            // Prompt de sistema mejorado para guiar a la IA
            $system_prompt = 'Eres un asistente experto en redacción de emails para el sector inmobiliario. \
Genera solo el contenido para el cuerpo del email en formato HTML simple, utilizando párrafos <p>, negritas <strong> donde sea relevante, y listas <ul><li> si son apropiadas. \
NO incluyas un saludo inicial como \'Hola {{nombre_lead}},\' ni una despedida final como \'Saludos,\nEl equipo de...\'. \
El saludo y la despedida se añadirán desde una plantilla de email base. \
Concéntrate exclusivamente en el mensaje principal solicitado en el prompt del usuario. Asegúrate de que el HTML sea válido y esté bien formado.';

            $data = [
                'model' => $this->apiModel,
                'messages' => [
                    ['role' => 'system', 'content' => $system_prompt],
                    ['role' => 'user', 'content' => $promptFinal]
                ],
                'temperature' => 0.65, // Ligeramente menos creativo para mantener profesionalismo
                'max_tokens' => 700,  // Ajusta según la longitud esperada de los emails
            ];

            $ch = curl_init($this->apiEndpoint);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_TIMEOUT, 90); // Timeout de 90 segundos para respuestas más largas
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true); // Forzar verificación SSL

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                Logger::error("AIService: cURL Error en llamada a OpenAI: " . $curlError, ['endpoint' => $this->apiEndpoint]);
                return ['success' => false, 'cuerpo_html' => '', 'error' => "Error cURL IA: " . $curlError];
            }
            
            Logger::debug("AIService: Respuesta cruda de OpenAI (Status {$httpCode})", ['response_body_length' => strlen($response)]);

            if ($httpCode >= 200 && $httpCode < 300) {
                $responseData = json_decode($response, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Logger::error("AIService: Error decodificando JSON de OpenAI.", ['json_error' => json_last_error_msg(), 'body' => $response]);
                    return ['success' => false, 'cuerpo_html' => '', 'error' => "Error decodificando respuesta de IA."];
                }

                if (isset($responseData['choices'][0]['message']['content'])) {
                    $generatedHtmlBody = $responseData['choices'][0]['message']['content'];
                    Logger::info("AIService: Cuerpo HTML generado por OpenAI.", ['length' => strlen($generatedHtmlBody)]);
                    return ['success' => true, 'cuerpo_html' => $generatedHtmlBody, 'error' => null];
                } elseif (isset($responseData['error']['message'])) {
                    Logger::error("AIService: Error API de OpenAI.", ['error_details' => $responseData['error']]);
                    return ['success' => false, 'cuerpo_html' => '', 'error' => "Error API OpenAI: " . $responseData['error']['message']];
                }
                 else {
                    Logger::error("AIService: Respuesta inesperada de OpenAI (sin contenido o error conocido).", ['response' => $responseData]);
                    return ['success' => false, 'cuerpo_html' => '', 'error' => "Respuesta inesperada de IA."];
                }
            } else {
                 Logger::error("AIService: Error HTTP de OpenAI: {$httpCode}", ['response_preview' => substr($response,0,500)]);
                 return ['success' => false, 'cuerpo_html' => '', 'error' => "Error IA HTTP {$httpCode}: " . substr($response,0,200)];
            }

        } catch (\Throwable $e) { // Capturar Throwable para errores más amplios
            Logger::critical("AIService: Excepción en llamada a API de IA real: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return ['success' => false, 'cuerpo_html' => '', 'error' => "Excepción IA: " . $e->getMessage()];
        }
    }

    private function simularRespuestaIA(string $promptProcesado, array $datosContextuales): array {
        $nombreLead = htmlspecialchars($datosContextuales['nombre_lead'] ?? 'estimado cliente');
        $nombreAgencia = htmlspecialchars($datosContextuales['nombre_agencia'] ?? 'nuestra agencia');
        
        $cuerpoHtmlSimulado = "<p>Este es un email <strong>SIMULADO</strong> generado para <strong>{$nombreLead}</strong> de parte de <strong>{$nombreAgencia}</strong>.</p>\n";
        $cuerpoHtmlSimulado .= "<p>El prompt de sistema y de usuario combinados fueron procesados internamente.</p>\n";
        $cuerpoHtmlSimulado .= "<p>Prompt específico recibido (ya procesado): <br><em><pre style='white-space: pre-wrap; background-color: #f0f0f0; padding: 10px; border-radius: 5px; font-size: 0.9em;'>" . htmlspecialchars($promptProcesado) . "</pre></em></p>\n";
        $cuerpoHtmlSimulado .= "<p>Normalmente, aquí recibirías contenido valioso y personalizado generado por nuestra IA. ¡Gracias por tu comprensión!</p>";

        return ['success' => true, 'cuerpo_html' => $cuerpoHtmlSimulado, 'error' => null];
    }
}
?> 