<?php
// api/utils/WkhtmltopdfGenerator.php
declare(strict_types=1);

namespace Api\utils;

use Exception;

/**
 * Generador de PDFs usando wkhtmltopdf
 * Proporciona mejor soporte para CSS moderno, fuentes web y colores dinámicos
 */
class WkhtmltopdfGenerator
{
    private string $wkhtmltopdfPath;
    private string $tempDir;
    
    public function __construct()
    {
        // Ruta al ejecutable wkhtmltopdf
        $this->wkhtmltopdfPath = $_ENV['WKHTMLTOPDF_PATH'] ?? '/usr/local/bin/wkhtmltopdf-wrapper';
        $this->tempDir = sys_get_temp_dir();
    }

    /**
     * Verificar si wkhtmltopdf está disponible
     */
    public function isAvailable(): bool
    {
        return $this->isWkhtmltopdfAvailable();
    }
    
    /**
     * Generar PDF desde HTML
     */
    public function generatePdfFromHtml(string $html, array $options = []): string
    {
        // Configuración por defecto
        $defaultOptions = [
            'page-size' => 'A4',
            'orientation' => 'Portrait',
            'margin-top' => '15mm',
            'margin-right' => '15mm',
            'margin-bottom' => '15mm',
            'margin-left' => '15mm',
            'encoding' => 'UTF-8',
            'enable-local-file-access' => true,
            'enable-javascript' => false,
            'no-stop-slow-scripts' => true,
            'debug-javascript' => false,
            'load-error-handling' => 'ignore',
            'load-media-error-handling' => 'ignore',
            'disable-smart-shrinking' => true,
            'print-media-type' => true,
            'dpi' => 150,
            'image-quality' => 100,
            'image-dpi' => 150
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        // Crear archivo temporal para HTML
        $htmlFile = tempnam($this->tempDir, 'pdf_html_') . '.html';
        $pdfFile = tempnam($this->tempDir, 'pdf_output_') . '.pdf';
        
        try {
            // Escribir HTML al archivo temporal
            if (file_put_contents($htmlFile, $html) === false) {
                throw new Exception("No se pudo escribir el archivo HTML temporal");
            }
            
            // Construir comando wkhtmltopdf
            $command = $this->buildCommand($htmlFile, $pdfFile, $options);
            
            // Ejecutar comando
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);
            
            if ($returnCode !== 0) {
                $errorMsg = "Error ejecutando wkhtmltopdf. Código: {$returnCode}. Output: " . implode("\n", $output);
                error_log("WkhtmltopdfGenerator Error: " . $errorMsg);
                throw new Exception($errorMsg);
            }
            
            // Verificar que el PDF se generó
            if (!file_exists($pdfFile) || filesize($pdfFile) === 0) {
                throw new Exception("El archivo PDF no se generó correctamente");
            }
            
            // Leer contenido del PDF
            $pdfContent = file_get_contents($pdfFile);
            if ($pdfContent === false) {
                throw new Exception("No se pudo leer el archivo PDF generado");
            }
            
            return $pdfContent;
            
        } finally {
            // Limpiar archivos temporales
            if (file_exists($htmlFile)) {
                unlink($htmlFile);
            }
            if (file_exists($pdfFile)) {
                unlink($pdfFile);
            }
        }
    }
    
    /**
     * Construir comando wkhtmltopdf
     */
    private function buildCommand(string $htmlFile, string $pdfFile, array $options): string
    {
        $command = escapeshellcmd($this->wkhtmltopdfPath);
        
        // Agregar opciones
        foreach ($options as $key => $value) {
            if (is_bool($value)) {
                if ($value) {
                    $command .= " --{$key}";
                }
            } else {
                $command .= " --{$key} " . escapeshellarg((string)$value);
            }
        }
        
        // Agregar archivos de entrada y salida
        $command .= " " . escapeshellarg($htmlFile) . " " . escapeshellarg($pdfFile);
        
        return $command;
    }
    
    /**
     * Verificar si wkhtmltopdf está disponible
     */
    private function isWkhtmltopdfAvailable(): bool
    {
        $command = escapeshellcmd($this->wkhtmltopdfPath) . " --version 2>&1";
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        return $returnCode === 0;
    }
    
    /**
     * Obtener versión de wkhtmltopdf
     */
    public function getVersion(): string
    {
        $command = escapeshellcmd($this->wkhtmltopdfPath) . " --version 2>&1";
        $output = [];
        exec($command, $output);
        
        return implode("\n", $output);
    }
}
?>
