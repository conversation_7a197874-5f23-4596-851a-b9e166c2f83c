<?php
    // api/utils/PDFReportGenerator.php
    declare(strict_types=1);

    namespace Api\utils;

    require_once __DIR__ . '/../vendor/autoload.php';

    use Google\Cloud\Storage\StorageClient;
    use Api\utils\WkhtmltopdfGenerator;
    use Exception;

    class PDFReportGenerator {

        private $db;
        private $googleMapsApiKey;

        public function __construct(\PDO $db) {
            $this->db = $db;
            // Obtener Google Maps API Key desde variables de entorno
            $this->googleMapsApiKey = $_ENV['GOOGLE_PLACES_API_KEY'] ?? null;
        }

        public function generateAndSaveReport(string $valoracionUuid): ?string 
        {
            // 1. Obtener datos de la valoración por UUID (actualizada con nuevos campos)
            $stmtVal = $this->db->prepare("SELECT vv.*, vl.nombre as lead_nombre, vl.email as lead_email, vl.necesidad as lead_necesidad,
                                                cv.client_identifier as agencia_client_identifier, cv.nombre_display as agencia_nombre_display,
                                                cv.logo_url as agencia_logo_url, cv.color_primario as agencia_color_primario,
                                                cv.color_secundario as agencia_color_secundario, cv.cta_contacto_url as agencia_cta_url,
                                                cv.direccion_fisica as agencia_direccion_fisica, cv.telefono_contacto as agencia_telefono_contacto,
                                                cv.email_contacto_publico as agencia_email_contacto_publico, cv.sitio_web_url as agencia_sitio_web_url,
                                                cv.whatsapp_numero as agencia_whatsapp_numero, cv.descripcion_breve as agencia_descripcion_breve
                                         FROM valorador_valoraciones vv
                                         JOIN valorador_leads vl ON vv.lead_id = vl.id
                                         JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id
                                         WHERE vv.uuid = :uuid_valoracion LIMIT 1");
            $stmtVal->bindParam(':uuid_valoracion', $valoracionUuid);
            $stmtVal->execute();
            $valoracionData = $stmtVal->fetch(\PDO::FETCH_ASSOC);

            if (!$valoracionData) {
                // Log error: Valoración no encontrada
                error_log("PDFGenerator: Valoración no encontrada para UUID: " . $valoracionUuid);
                return null;
            }

            // Preparar las variables para la plantilla
            $datosAgencia = [
                'nombre_display' => $valoracionData['agencia_nombre_display'] ?? 'Nombre Agencia',
                'logo_url' => $valoracionData['agencia_logo_url'] ?? null,
                'cta_contacto_url' => $valoracionData['agencia_cta_url'] ?? '#',
                'sitio_web_url' => $valoracionData['agencia_sitio_web_url'] ?? null,
                'telefono_contacto' => $valoracionData['agencia_telefono_contacto'] ?? null,
                'direccion_fisica' => $valoracionData['agencia_direccion_fisica'] ?? null,
                'email_contacto_publico' => $valoracionData['agencia_email_contacto_publico'] ?? null,
                'whatsapp_numero' => $valoracionData['agencia_whatsapp_numero'] ?? null,
                'descripcion_breve' => $valoracionData['agencia_descripcion_breve'] ?? null
            ];

            $coloresAgencia = [
                'primario' => $valoracionData['agencia_color_primario'] ?? '#053B5E',
                'secundario' => $valoracionData['agencia_color_secundario'] ?? '#ED8725',
                // Podrías calcular/definir versiones 'light' aquí si es necesario para el CSS
                'primario_light' => $this->calculateLighterColor($valoracionData['agencia_color_primario'], 0.9) ?? '#E6F0F5',
                'secundario_light' => $this->calculateLighterColor($valoracionData['agencia_color_secundario'], 0.9) ?? '#FCE9D4',
            ];
            
            $datosLead = [
                'nombre' => $valoracionData['lead_nombre'],
                'email' => $valoracionData['lead_email'],
                'necesidad_lead' => $valoracionData['lead_necesidad']
            ];

            $datosValoracion = $valoracionData; // Contiene todos los campos de valorador_valoraciones (incluyendo nuevos campos enriquecidos)

            // Ya no necesitamos parsear datos_adicionales porque ahora tenemos campos individuales
            // El campo notas_agente contiene solo las notas del agente, no datos de valoración
            if (isset($datosValoracion['valor_estimado_min'], $datosValoracion['valor_estimado_max'])) {
                $datosValoracion['valor_estimado_promedio'] = round(($datosValoracion['valor_estimado_min'] + $datosValoracion['valor_estimado_max']) / 2);
            }

            // Generar URL del mapa de Google Maps si tenemos coordenadas
            $mapaUrl = null;
            if ($this->googleMapsApiKey &&
                isset($datosValoracion['latitud']) && isset($datosValoracion['longitud']) &&
                is_numeric($datosValoracion['latitud']) && is_numeric($datosValoracion['longitud'])) {

                $lat = $datosValoracion['latitud'];
                $lng = $datosValoracion['longitud'];
                $zoom = 15;
                $size = '600x400';
                $maptype = 'roadmap';

                $mapaUrl = "https://maps.googleapis.com/maps/api/staticmap?" .
                          "center={$lat},{$lng}" .
                          "&zoom={$zoom}" .
                          "&size={$size}" .
                          "&maptype={$maptype}" .
                          "&markers=color:red%7Clabel:P%7C{$lat},{$lng}" .
                          "&style=feature:poi%7Cvisibility:simplified" .
                          "&style=feature:transit%7Cvisibility:off" .
                          "&key={$this->googleMapsApiKey}";
            }

            // --- INICIO: Los datos ya están disponibles directamente en las columnas ---
            // Ya no necesitamos extraer de JSON porque tenemos campos individuales:
            // - ano_construccion_catastro (directo de la columna)
            // - precio_m2_promedio, tamano_promedio, etc. (estadísticas zonales)
            // - precio_vs_zona_porcentaje, tamano_vs_zona_porcentaje (análisis comparativo)
            // - notas_agente (solo notas del agente, no datos de valoración)

            // Los datos están listos para usar directamente en el template avanzado
            // --- FIN: Datos ya disponibles ---


            // Lógica para $textoSegunNecesidad (como se definió antes)
            $necesidad_db = $datosLead['necesidad_lead'] ?? null;
            $textoSegunNecesidad = '';
            $nombreAgenciaDisplay = htmlspecialchars($datosAgencia['nombre_display'] ?? 'nuestra agencia');
            $necesidad_key = null;
            if ($necesidad_db) { /* ... (lógica switch completa como arriba) ... */ }
            // ... (pegar aquí la lógica switch completa para $textoSegunNecesidad)
            // START switch
            if ($necesidad_db) {
                $necesidad_lower = strtolower($necesidad_db);
                if (str_contains($necesidad_lower, 'vender')) $necesidad_key = 'vender';
                elseif (str_contains($necesidad_lower, 'comprar')) $necesidad_key = 'comprar';
                elseif (str_contains($necesidad_lower, 'curiosidad')) $necesidad_key = 'curiosidad';
                elseif (str_contains($necesidad_lower, 'alquilar')) $necesidad_key = 'alquilar';
                elseif (str_contains($necesidad_lower, 'herencia') || str_contains($necesidad_lower, 'legal')) $necesidad_key = 'legal';
                elseif (str_contains($necesidad_lower, 'otro')) $necesidad_key = 'otro';
                else $necesidad_key = 'generico';
            } else {
                $necesidad_key = 'generico';
            }

            switch ($necesidad_key) {
                case 'vender': $textoSegunNecesidad = "Entendemos que estás considerando vender tu propiedad. En {$nombreAgenciaDisplay}, estamos listos para ayudarte a maximizar su valor y asegurar una venta rápida y eficiente. Nuestro equipo de expertos te guiará en cada paso del proceso."; break;
                case 'comprar': $textoSegunNecesidad = "Si estás buscando comprar una nueva propiedad, esta valoración te da una idea del mercado. En {$nombreAgenciaDisplay}, contamos con una amplia cartera de inmuebles y podemos ayudarte a encontrar tu hogar ideal ajustado a tus necesidades y presupuesto."; break;
                case 'curiosidad': $textoSegunNecesidad = "Es natural tener curiosidad sobre el valor de una propiedad. Esperamos que esta estimación te sea útil. Si en algún momento decides dar el siguiente paso, ya sea vender, comprar o alquilar, en {$nombreAgenciaDisplay} estaremos encantados de asesorarte."; break;
                case 'alquilar': $textoSegunNecesidad = "Conocer el valor de tu propiedad es el primer paso si estás considerando alquilarla. En {$nombreAgenciaDisplay}, ofrecemos servicios de gestión de alquiler para ayudarte a encontrar inquilinos fiables y optimizar la rentabilidad de tu inversión."; break;
                case 'legal': $textoSegunNecesidad = "En situaciones de herencias o trámites legales, conocer el valor estimado de una propiedad es fundamental. Esta información te servirá de base. Para una valoración con validez legal o asesoramiento específico, contacta con nuestros expertos en {$nombreAgenciaDisplay}."; break;
                case 'otro': case 'generico': default: $textoSegunNecesidad = "Sea cual sea tu motivo para solicitar esta valoración (" . ($necesidad_db ? htmlspecialchars($necesidad_db) : "no especificado") . "), esperamos que la información te resulte de utilidad. En {$nombreAgenciaDisplay}, estamos a tu disposición para cualquier consulta inmobiliaria que puedas tener."; break;
            }
            // END switch


            // Renderizar el HTML de la plantilla actualizada (v7.0 - wkhtmltopdf con iconos de texto y fuentes mejoradas)
            ob_start();
            include __DIR__ . '/../templates/pdf/informe_valoracion_tpl.phtml'; // Template principal actualizado
            $htmlContent = ob_get_clean();

            // Debug: Log para confirmar que se está usando el template actualizado
            error_log("PDFGenerator: Usando template v7.0 con wkhtmltopdf para UUID: " . $valoracionUuid);

            // Generar PDF con wkhtmltopdf
            $wkhtmlGenerator = new WkhtmltopdfGenerator();
            error_log("PDFGenerator: wkhtmltopdf disponible, versión: " . $wkhtmlGenerator->getVersion());

            $pdfContent = $wkhtmlGenerator->generatePdfFromHtml($htmlContent, [
                'page-size' => 'A4',
                'orientation' => 'Portrait',
                'margin-top' => '15mm',
                'margin-right' => '15mm',
                'margin-bottom' => '15mm',
                'margin-left' => '15mm',
                'encoding' => 'UTF-8',
                'enable-local-file-access' => true,
                'disable-smart-shrinking' => true,
                'print-media-type' => true,
                'dpi' => 150
            ]);

            error_log("PDFGenerator: PDF generado exitosamente con wkhtmltopdf");
            $pdfFileName = 'informe_valoracion_v7_' . $valoracionUuid . '.pdf';

            // Guardar el PDF en Google Cloud Storage
            try {
                $clientIdentifier = $valoracionData['agencia_client_identifier'];

                // Configuración de Cloud Storage para reportes
                $bucketName = $_ENV['GOOGLE_CLOUD_REPORTS_BUCKET'] ?? 'inmoautomation-reports';
                $reportsBaseUrl = $_ENV['REPORTS_BASE_URL'] ?? 'https://storage.googleapis.com/inmoautomation-reports';

                // Usar el nombre de archivo correcto según el generador usado
                $cloudStoragePath = 'reports/' . $clientIdentifier . '/' . $pdfFileName;

                // Subir a Google Cloud Storage
                $storage = new StorageClient([
                    'projectId' => $_ENV['GOOGLE_CLOUD_PROJECT_ID'] ?? 'inmoautomation'
                ]);

                $bucket = $storage->bucket($bucketName);

                $object = $bucket->upload($pdfContent, [
                    'name' => $cloudStoragePath,
                    'metadata' => [
                        'contentType' => 'application/pdf',
                        'cacheControl' => 'public, max-age=86400', // Cache por 1 día
                        'contentDisposition' => 'inline; filename="' . $pdfFileName . '"'
                    ]
                ]);

                $pdfPublicUrl = $reportsBaseUrl . '/' . $cloudStoragePath;

                error_log("PDFGenerator: Informe generado y guardado en Cloud Storage: " . $pdfPublicUrl);
                return $pdfPublicUrl; // Devolver la URL pública directamente

            } catch (\Exception $e) {
                error_log("PDFGenerator: Exception: " . $e->getMessage());
                return null;
            }
    }
        
        // Helper para calcular un color más claro (simple)
        private function calculateLighterColor(?string $hexColor, float $percentLighter = 0.8): ?string {
            if (!$hexColor) return null;
            $hexColor = ltrim($hexColor, '#');
            if (strlen($hexColor) != 6) return $hexColor; // Devuelve original si no es 6-char hex

            list($r, $g, $b) = array_map('hexdec', str_split($hexColor, 2));

            $r = min(255, (int)($r + (255 - $r) * $percentLighter));
            $g = min(255, (int)($g + (255 - $g) * $percentLighter));
            $b = min(255, (int)($b + (255 - $b) * $percentLighter));

            return sprintf("#%02x%02x%02x", $r, $g, $b);
        }
    }
    ?>