<?php
namespace Api\utils;

use PDO; // AÑADIDO para usar la clase PDO global

// Función de logging local para PlanFeatures, intentará usar custom_log_subscription si está definida.
if (!function_exists('plan_features_logger')) {
    function plan_features_logger($message) {
        if (function_exists('custom_log_subscription')) {
            custom_log_subscription("[PlanFeaturesLOG] " . $message);
        } else {
            error_log("[PlanFeaturesERRORLOG] " . $message);
        }
    }
}

class PlanFeatures {
    /**
     * Obtiene los detalles y características del plan activo de un usuario.
     *
     * @param int $userId El ID del usuario.
     * @param PDO $pdo La instancia de la conexión PDO a la base de datos.
     * @return array|null Un array asociativo con los detalles del plan si se encuentra, o null en caso contrario.
     */
    public static function getUserActivePlanDetails(int $userId, PDO $pdo): ?array {
        plan_features_logger("getUserActivePlanDetails INVOCADO para User ID: " . $userId);

        $sql = "SELECT
                    p.id AS plan_id,
                    p.slug AS plan_slug,
                    p.name AS plan_name,
                    p.price_monthly,
                    p.price_annual,
                    p.stripe_price_id_monthly,
                    p.stripe_price_id_annual,
                    p.max_dashboard_users,
                    p.allow_multiple_sequences,
                    p.allow_custom_domain_email,
                    p.max_valoradores,
                    p.has_analytics,
                    p.additional_config_json,
                    s.estado as subscription_status,
                    s.stripe_subscription_id,
                    s.fecha_inicio_periodo_actual as current_period_start,
                    s.fecha_fin_periodo_actual as current_period_end,
                    s.fecha_fin_prueba as trial_ends_at,
                    (s.fecha_cancelacion IS NOT NULL OR s.fecha_cancelada_en_stripe IS NOT NULL) as cancel_at_period_end,
                    s.billing_cycle
                FROM suscripciones s
                JOIN plans p ON s.plan_id = p.id
                WHERE s.user_id = :user_id
                AND s.estado IN ('active', 'trialing', 'incomplete', 'past_due', 'unpaid')
                ORDER BY s.fecha_creacion DESC
                LIMIT 1";
        
        plan_features_logger("SQL a ejecutar: " . preg_replace('/\s+/', ' ', $sql)); // Limpiar espacios extra para el log

        try {
            $stmt = $pdo->prepare($sql);
            plan_features_logger("Statement preparado.");
            
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            plan_features_logger("Parámetro user_id enlazado con valor: " . $userId);
            
            $stmt->execute();
            plan_features_logger("Statement ejecutado.");
            
            $planDetails = $stmt->fetch(PDO::FETCH_ASSOC);
            // Usar var_export para una representación más clara que print_r para logs
            plan_features_logger("Resultado de fetch: " . var_export($planDetails, true));

            if ($planDetails) {
                plan_features_logger("planDetails es TRUE (se encontró una fila). Datos: " . var_export($planDetails, true));
                // Convertir cancel_at_period_end a booleano si es necesario (MySQL devuelve 0 o 1 para expresiones booleanas)
                if (isset($planDetails['cancel_at_period_end'])) {
                    $planDetails['cancel_at_period_end'] = (bool)$planDetails['cancel_at_period_end'];
                }

                if (isset($planDetails['additional_config_json']) && $planDetails['additional_config_json'] !== null) {
                    $decodedConfig = json_decode($planDetails['additional_config_json'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $planDetails['additional_config'] = $decodedConfig;
                    } else {
                        $planDetails['additional_config'] = [];
                        plan_features_logger("Error al decodificar additional_config_json: " . json_last_error_msg() . ". Se devuelve array vacío.");
                    }
                } else {
                    $planDetails['additional_config'] = [];
                }
                return $planDetails;
            } else {
                plan_features_logger("planDetails es FALSE o NULL (no se encontró ninguna fila o fetch falló).");
                return null;
            }
        } catch (PDOException $e) {
            plan_features_logger("PDOException en getUserActivePlanDetails: " . $e->getMessage());
            return null;
        }
    }
}

?> 