<?php
// api/utils/auth_check.php

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;

/**
 * Verifica el token JWT y si el usuario tiene el rol de administrador.
 *
 * @return array Un array con ['success' => bool, 'message' => string, 'user_id' => int|null, 'status_code' => int]
 */
function verifyTokenAndAdminRole(): array
{
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if (!$authHeader) {
        return ['success' => false, 'message' => 'Acceso no autorizado: Token no proporcionado.', 'user_id' => null, 'status_code' => 401];
    }

    $tokenParts = explode(' ', $authHeader);
    if (count($tokenParts) !== 2 || strcasecmp($tokenParts[0], 'Bearer') !== 0 || empty($tokenParts[1])) {
        return ['success' => false, 'message' => 'Acceso no autorizado: Token malformado.', 'user_id' => null, 'status_code' => 401];
    }
    $jwt = $tokenParts[1];

    try {
        JWT::$leeway = 60; // Permitir 60 segundos de desviación para exp, nbf, iat
        // Usar la constante JWT_SECRET definida en bootstrap.php (que es incluido por el script que llama a esta función)
        if (!defined('JWT_SECRET') || empty(JWT_SECRET)) {
            error_log("auth_check: JWT_SECRET no está definida o está vacía.");
            return ['success' => false, 'message' => 'Error de configuración interna del servidor (JWT).', 'user_id' => null, 'status_code' => 500];
        }
        $decoded = JWT::decode($jwt, new Key(JWT_SECRET, 'HS256'));
        $user_id = $decoded->user_id ?? null;

        if (!$user_id) {
            return ['success' => false, 'message' => 'Acceso no autorizado: Información de usuario no encontrada en el token.', 'user_id' => null, 'status_code' => 401];
        }

        // Conectar a la BD para verificar roles (usar constantes de bootstrap.php)
        if (!defined('DB_HOST') || !defined('DB_DATABASE') || !defined('DB_USERNAME') || !defined('DB_PASSWORD') || !defined('DB_CHARSET')) {
            error_log("auth_check: Constantes de BD no definidas.");
            return ['success' => false, 'message' => 'Error de configuración interna del servidor (DB).', 'user_id' => $user_id, 'status_code' => 500];
        }
        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE);
        if ($conn->connect_error) {
            error_log("auth_check: Error de conexión a BD: " . $conn->connect_error);
            return ['success' => false, 'message' => 'Error interno del servidor al verificar el rol.', 'user_id' => $user_id, 'status_code' => 500];
        }
        $conn->set_charset(DB_CHARSET);

        $stmt = $conn->prepare("SELECT roles FROM usuarios WHERE id = ? LIMIT 1");
        if (!$stmt) {
             error_log("auth_check: Error preparando statement para roles: " . $conn->error);
            $conn->close();
            return ['success' => false, 'message' => 'Error interno del servidor al preparar la consulta de rol.', 'user_id' => $user_id, 'status_code' => 500];
        }
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $isAdmin = false;
        if ($user_row = $result->fetch_assoc()) {
            $roles = json_decode($user_row['roles'] ?? '[]', true);
            if (is_array($roles) && in_array('admin', $roles)) {
                $isAdmin = true;
            }
        }
        $stmt->close();
        $conn->close();

        if (!$isAdmin) {
            return ['success' => false, 'message' => 'Acceso denegado: Se requiere rol de administrador.', 'user_id' => $user_id, 'status_code' => 403];
        }

        return ['success' => true, 'message' => 'Token y rol de administrador verificados.', 'user_id' => (int)$user_id, 'status_code' => 200];

    } catch (ExpiredException $e) {
        return ['success' => false, 'message' => 'Acceso no autorizado: Token expirado. (' . $e->getMessage() . ')', 'user_id' => null, 'status_code' => 401];
    } catch (SignatureInvalidException $e) {
        return ['success' => false, 'message' => 'Acceso no autorizado: Firma de token inválida. (' . $e->getMessage() . ')', 'user_id' => null, 'status_code' => 401];
    } catch (BeforeValidException $e) {
        return ['success' => false, 'message' => 'Acceso no autorizado: Token aún no válido. (' . $e->getMessage() . ')', 'user_id' => null, 'status_code' => 401];
    } catch (\UnexpectedValueException $e) {
        return ['success' => false, 'message' => 'Acceso no autorizado: Token inválido (valor inesperado). (' . $e->getMessage() . ')', 'user_id' => null, 'status_code' => 401];
    } catch (Exception $e) {
        error_log("auth_check: Error general de decodificación de token: " . $e->getMessage());
        return ['success' => false, 'message' => 'Acceso no autorizado: Token inválido o error general. (' . $e->getMessage() . ')', 'user_id' => null, 'status_code' => 401];
    }
}

?> 