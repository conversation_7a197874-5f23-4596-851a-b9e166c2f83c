<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// Habilitar errores solo en desarrollo - Estas líneas deben ser controladas por bootstrap.php
// error_reporting(E_ALL); // Comentado - Correcto
// ini_set('display_errors', 1); // Comentado - Correcto
// Función para registro personalizado
function custom_log_roles($message) {
    $logFile = __DIR__ . '/debug_roles.log';
    $timestamp = date('Y-m-d H:i:s');
    @file_put_contents($logFile, "[$timestamp] " . $message . "\n", FILE_APPEND);
}
custom_log_roles("--- [check-roles.php] INICIADO ---");
header('Content-Type: application/json');
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    custom_log_roles("Error de conexión a la base de datos: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Error interno del servidor.']);
    exit();
}

// Función para obtener el token JWT
function getToken() {
    // Depurar todos los encabezados recibidos
    $allHeaders = getallheaders();
    custom_log_roles("Todos los encabezados recibidos: " . json_encode($allHeaders));
    // Depurar todos los parámetros GET
    custom_log_roles("Todos los parámetros GET: " . json_encode($_GET));
    // Código para permitir pasar el token por URL (solo para desarrollo)
    if (isset($_GET['token'])) {
        custom_log_roles("Token recibido por URL: " . substr($_GET['token'], 0, 10) . '...');
        return $_GET['token']; // Devolver directamente el token de la URL
    }
    // Intentar obtener el token del encabezado HTTP_AUTHORIZATION
    $authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
    custom_log_roles("HTTP_AUTHORIZATION header: " . ($authHeader ? 'Presente' : 'No presente'));
    if ($authHeader) {
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            custom_log_roles("Token Bearer encontrado en HTTP_AUTHORIZATION: " . substr($token, 0, 10) . '...');
            return $token;
        } else {
            custom_log_roles("Formato de HTTP_AUTHORIZATION header incorrecto: " . $authHeader);
        }
    }
    // Intentar obtener el token de los headers usando getallheaders (como respaldo)
    if (isset($allHeaders['Authorization'])) {
        $authHeader = $allHeaders['Authorization'];
        custom_log_roles("Authorization header de getallheaders: " . $authHeader);
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
            custom_log_roles("Token Bearer encontrado en getallheaders: " . substr($token, 0, 10) . '...');
            return $token;
        } else {
            custom_log_roles("Formato de Authorization header incorrecto: " . $authHeader);
        }
    }
    custom_log_roles("No se encontró token de autenticación");
    return null;
}
// Función para verificar el token JWT
function verifyToken($token) {
    global $jwtSecret;
    if (!$token) {
        custom_log_roles("Error: Token no proporcionado");
        return null;
    }
    $previous_display_errors = ini_get('display_errors'); // Guardar estado actual
    ini_set('display_errors', '0'); // Desactivar temporalmente
    try {
        custom_log_roles("Intentando decodificar token con clave secreta. Primeros 10 caracteres de la clave: " . substr($jwtSecret, 0, 10));
        $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($jwtSecret, 'HS256'));
        custom_log_roles("Token decodificado correctamente. User ID: " . ($decoded->sub ?? 'No encontrado'));
        ini_set('display_errors', $previous_display_errors); // Restaurar estado
        return $decoded;
    } catch (\Firebase\JWT\ExpiredException $e) {
        custom_log_roles("Error: Token expirado: " . $e->getMessage());
        ini_set('display_errors', $previous_display_errors); // Restaurar estado
        return null;
    } catch (\Firebase\JWT\SignatureInvalidException $e) {
        custom_log_roles("Error: Firma del token inválida: " . $e->getMessage());
        ini_set('display_errors', $previous_display_errors); // Restaurar estado
        return null;
    } catch (\Firebase\JWT\BeforeValidException $e) {
        custom_log_roles("Error: Token no válido todavía: " . $e->getMessage());
        ini_set('display_errors', $previous_display_errors); // Restaurar estado
        return null;
    } catch (\DomainException $e) {
        custom_log_roles("Error de dominio: " . $e->getMessage());
        ini_set('display_errors', $previous_display_errors); // Restaurar estado
        return null;
    } catch (Exception $e) {
        custom_log_roles("Error general al verificar token: " . $e->getMessage());
        ini_set('display_errors', $previous_display_errors); // Restaurar estado
        return null;
    }
}
// Función para obtener los roles del usuario
function getUserRoles($userId) {
    global $db_conn;
    $stmt = $db_conn->prepare("SELECT roles FROM usuarios WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $roles = $row['roles'];
        // Intentar decodificar JSON
        if ($roles) {
            $decoded = json_decode($roles, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                // Si es un array, devolverlo directamente
                if (is_array($decoded)) {
                    return $decoded;
                }
                // Si es un objeto, convertirlo a array
                if (is_object($decoded)) {
                    return (array)$decoded;
                }
            }
            // Si no es JSON válido, verificar si el string contiene "admin"
            if (stripos($roles, 'admin') !== false) {
                return ['admin'];
            }
            // Si no contiene "admin", devolver un array vacío
            return [];
        }
    }
    return [];
}
// Función para verificar si el usuario tiene un rol específico
function hasRole($userId, $role) {
    $roles = getUserRoles($userId);
    return in_array($role, $roles);
}
// Función para actualizar los roles del usuario
function updateUserRoles($userId, $roles) {
    global $db_conn;
    // Convertir roles a JSON
    $rolesJson = json_encode($roles);
    // Actualizar los roles en la base de datos
    $stmt = $db_conn->prepare("UPDATE usuarios SET roles = ? WHERE id = ?");
    $stmt->bind_param("si", $rolesJson, $userId);
    $result = $stmt->execute();
    return $result;
}
// Función para agregar un rol al usuario
function addRole($userId, $role) {
    $roles = getUserRoles($userId);
    // Agregar el rol si no existe
    if (!in_array($role, $roles)) {
        $roles[] = $role;
        return updateUserRoles($userId, $roles);
    }
    return true; // El rol ya existe
}
// Función para quitar un rol al usuario
function removeRole($userId, $role) {
    $roles = getUserRoles($userId);
    // Quitar el rol si existe
    if (in_array($role, $roles)) {
        $roles = array_filter($roles, function($r) use ($role) {
            return $r !== $role;
        });
        return updateUserRoles($userId, array_values($roles));
    }
    return true; // El rol no existe
}
// Obtener el token
$token = getToken();
if (!$token) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Token no proporcionado'
    ]);
    exit;
}
// Verificar el token
$decoded = verifyToken($token);
if (!$decoded) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Token inválido'
    ]);
    exit;
}
// Obtener el ID del usuario desde el token
// En login.php, el ID se guarda como 'user_id' en el payload
$userId = $decoded->user_id ?? null;
custom_log_roles("ID de usuario obtenido del token: " . ($userId ? $userId : 'No encontrado'));
custom_log_roles("Contenido completo del token: " . json_encode($decoded));
if (!$userId) {
    echo json_encode([
        'status' => 'error',
        'message' => 'ID de usuario no encontrado en el token'
    ]);
    exit;
}
// Determinar la acción a realizar
$action = $_GET['action'] ?? 'check';
$role = $_GET['role'] ?? 'admin'; // Por defecto, trabajamos con el rol "admin"
switch ($action) {
    case 'check':
        // Verificar si el usuario tiene el rol especificado
        $hasRole = hasRole($userId, $role);
        echo json_encode([
            'status' => 'success',
            'hasRole' => $hasRole,
            'role' => $role,
            'allRoles' => getUserRoles($userId)
        ]);
        break;
    case 'add':
        // Agregar el rol al usuario
        $result = addRole($userId, $role);
        if ($result) {
            echo json_encode([
                'status' => 'success',
                'message' => "Rol '$role' asignado correctamente",
                'role' => $role,
                'allRoles' => getUserRoles($userId)
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => "Error al asignar el rol '$role'"
            ]);
        }
        break;
    case 'remove':
        // Quitar el rol al usuario
        $result = removeRole($userId, $role);
        if ($result) {
            echo json_encode([
                'status' => 'success',
                'message' => "Rol '$role' removido correctamente",
                'role' => $role,
                'allRoles' => getUserRoles($userId)
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => "Error al remover el rol '$role'"
            ]);
        }
        break;
    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'Acción no válida'
        ]);
}
