<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// /api/cancel-subscription.php - Cancela la suscripción del usuario
// bootstrap.php se encargará de la configuración de errores y carga de dependencias.
 // CORRECCIÓN: Usar config central de CORS
// --- Logger Setup (específico para este endpoint, se mantiene por ahora) ---
if (!function_exists('custom_log_cancel')) {
    function custom_log_cancel($message) {
        $logFile = __DIR__ . '/debug_cancel_subscription.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_cancel("--- [cancel-subscription.php] INICIADO ---");
// Las cabeceras CORS y el manejo de OPTIONS ahora son gestionados por cors.php
// Establecer Content-Type aquí para asegurar que se envíe antes de cualquier output JSON
header('Content-Type: application/json');
// --- Autoload y Carga de .env son manejados por bootstrap.php ---
// $autoloadPath = __DIR__ . '/vendor/autoload.php';
// ... (código de carga de autoload eliminado)
// $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
// ... (código de carga de .env eliminado)
// --- Verificación de autenticación ---
$userId = null;
$agencyId = null;
$isAgencyOwner = false;
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
custom_log_cancel("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0');
        try {
            custom_log_cancel("Intentando decodificar JWT...");
            $key = JWT_SECRET;
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            $userId = $decoded->user_id ?? null;
            $agencyId = $decoded->agency_id ?? null;
            $isAgencyOwner = isset($decoded->is_agency_owner) ? (bool)$decoded->is_agency_owner : false;
            if (!$userId || !$agencyId) {
                custom_log_cancel("Error: user_id o agency_id no encontrado en el token JWT.");
                throw new Exception('user_id o agency_id no encontrado en el token JWT');
            }
            custom_log_cancel("JWT decodificado correctamente. User ID: {$userId}, Agency ID: {$agencyId}, Is Owner: " . ($isAgencyOwner ? 'Sí' : 'No'));
        } catch (Exception $e) {
            custom_log_cancel("Error al decodificar JWT: " . $e->getMessage());
            http_response_code(401);
            if (ob_get_length()) ob_end_clean();
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.']);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors);
        }
    } else {
        custom_log_cancel("Formato de Authorization header incorrecto.");
        http_response_code(401);
        if (ob_get_length()) ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_cancel("No se proporcionó token de autorización.");
    http_response_code(401);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Verificación de Permiso de Dueño de Agencia ---
if (!$isAgencyOwner) {
    custom_log_cancel("Acceso denegado: Usuario user_id={$userId} no es dueño de la agencia agency_id={$agencyId}.");
    http_response_code(403);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo el propietario de la agencia puede cancelar la suscripción.']);
    exit();
}
// --- Conexión a la base de datos (usando constantes de bootstrap.php) ---
try {
    custom_log_cancel("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_cancel("Conexión PDO a la base de datos establecida correctamente.");
} catch (Exception $e) {
    custom_log_cancel("Error de conexión a la base de datos: " . $e->getMessage());
    http_response_code(500);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']); 
    exit();
}

// --- Procesar la cancelación de suscripción ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    custom_log_cancel("[POST Request] Procesando para user_id: " . $userId);
    $data = json_decode(file_get_contents('php://input'), true);
    $cancelAtPeriodEnd = isset($data['cancel_at_period_end']) ? (bool)$data['cancel_at_period_end'] : true;
    custom_log_cancel("Datos recibidos: cancel_at_period_end = " . ($cancelAtPeriodEnd ? 'true' : 'false'));
    
    try {
        $query = "SELECT u.stripe_customer_id, s.stripe_subscription_id
                  FROM usuarios u
                  JOIN suscripciones s ON u.id = s.user_id
                  WHERE u.id = ? AND s.estado IN ('active', 'trialing', 'past_due')";
        
        custom_log_cancel("Ejecutando consulta: {$query} con user_id={$userId}");
        $stmt = $pdo->prepare($query);
        $stmt->execute([$userId]);
        $subscription_data = $stmt->fetch();

        if (!$subscription_data) {
            custom_log_cancel("No se encontró suscripción activa para el usuario: " . $userId);
            http_response_code(404);
            if (ob_get_length()) ob_end_clean();
            echo json_encode(['success' => false, 'message' => 'No se encontró una suscripción activa para cancelar.']);
            exit();
        }

        $stripeSubscriptionId = $subscription_data['stripe_subscription_id'];
        custom_log_cancel("Stripe Subscription ID encontrado: " . $stripeSubscriptionId);

        // Inicializar Stripe
        \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
        \Stripe\Stripe::setApiVersion("2022-11-15");
        custom_log_cancel("Stripe SDK inicializado.");

        // Cancelar la suscripción en Stripe
        $updatedSubscription = \Stripe\Subscription::update($stripeSubscriptionId, [
            'cancel_at_period_end' => $cancelAtPeriodEnd,
        ]);
        custom_log_cancel("Suscripción actualizada en Stripe. Nuevo estado de cancel_at_period_end: " . ($updatedSubscription->cancel_at_period_end ? 'true' : 'false'));

        // Actualizar el estado en la base de datos local
        $new_status = $updatedSubscription->cancel_at_period_end ? 'cancelled_at_period_end' : 'active'; // Si se "des-cancela"
        $update_query = "UPDATE suscripciones SET estado = ?, fecha_modificacion = NOW() WHERE stripe_subscription_id = ?";
        $update_stmt = $pdo->prepare($update_query);
        $update_stmt->execute([$new_status, $stripeSubscriptionId]);
        custom_log_cancel("Estado de la suscripción actualizado en la base de datos local a: " . $new_status);

        // Limpiar buffer de salida antes de la respuesta final
        if (ob_get_length()) ob_end_clean();
        
        // Enviar respuesta
        echo json_encode([
            'success' => true, 
            'message' => 'La configuración de tu suscripción ha sido actualizada.',
            'subscription_status' => $new_status,
            'cancel_at_period_end' => $updatedSubscription->cancel_at_period_end,
            'current_period_end' => $updatedSubscription->current_period_end
        ]);

    } catch (\Stripe\Exception\ApiErrorException $e) {
        custom_log_cancel("Error de Stripe: " . $e->getMessage());
        http_response_code(500);
        if (ob_get_length()) ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Error al comunicarse con el procesador de pagos.']);
    } catch (Exception $e) {
        custom_log_cancel("Error general: " . $e->getMessage());
        http_response_code(500);
        if (ob_get_length()) ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Error interno del servidor: ' . $e->getMessage()]);
    }
} else {
    custom_log_cancel("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405); // Method Not Allowed
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
}
