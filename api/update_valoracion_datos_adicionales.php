<?php
declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

// --- Función para obtener y decodificar el token JWT ---
function get_decoded_jwt_payload_update_valoracion() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded;
            } catch (\Throwable $e) {
                error_log("JWT Decode Error en update_valoracion_datos_adicionales: " . $e->getMessage());
                if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                    ini_set('display_errors', $previous_display_errors);
                }
            }
        }
    }
    return null;
}

// --- Obtener datos de la solicitud ---
$request_body = file_get_contents('php://input');
$data = json_decode($request_body, true);

$valoracion_id = $data['valoracion_id'] ?? null;
$datos_adicionales = $data['datos_adicionales'] ?? null;

if (!$valoracion_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de valoración no proporcionado.']);
    exit;
}

// --- Autenticación y Autorización ---
$decoded_payload = get_decoded_jwt_payload_update_valoracion();
$user_id = $decoded_payload->user_id ?? null;
$agency_id = $decoded_payload->agency_id ?? null;
$roles = isset($decoded_payload->roles) && is_array($decoded_payload->roles) ? $decoded_payload->roles : (isset($decoded_payload->roles) && is_object($decoded_payload->roles) ? (array)$decoded_payload->roles : []);
$is_super_admin = in_array('admin', $roles);

if (!$user_id) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Usuario no identificado.']);
    exit;
}

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // --- Verificar pertenencia de la valoración (si no es superadmin) ---
    if (!$is_super_admin) {
        if (!$agency_id) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: No se encontró información de agencia para el usuario.']);
            exit;
        }

        $stmt_check = $pdo->prepare("SELECT vv.id FROM valorador_valoraciones vv JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id WHERE vv.id = :valoracion_id AND cv.agency_id = :agency_id");
        $stmt_check->bindParam(':valoracion_id', $valoracion_id, PDO::PARAM_INT);
        $stmt_check->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
        $stmt_check->execute();

        if ($stmt_check->fetchColumn() === false) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: La valoración no pertenece a su agencia o no existe.']);
            exit;
        }
    }

    // --- Validar y preparar datos_adicionales ---
    if (!is_array($datos_adicionales) && !is_object($datos_adicionales)) {
        // Si no es un array o un objeto, lo tratamos como nulo o un objeto vacío para evitar errores.
        // Opcionalmente, se podría devolver un error 400 si se espera un formato específico.
        $datos_adicionales_json = null;
    } else {
        $datos_adicionales_json = json_encode($datos_adicionales);
        if (json_last_error() !== JSON_ERROR_NONE) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'El formato de los datos adicionales no es un JSON válido.']);
            exit;
        }
    }

    // --- Actualizar notas_agente (anteriormente datos_adicionales) ---
    $sql_update = "UPDATE valorador_valoraciones SET notas_agente = :notas_agente, fecha_modificacion = NOW() WHERE id = :valoracion_id";
    $stmt_update = $pdo->prepare($sql_update);
    $stmt_update->bindParam(':notas_agente', $datos_adicionales_json, PDO::PARAM_STR);
    $stmt_update->bindParam(':valoracion_id', $valoracion_id, PDO::PARAM_INT);

    if ($stmt_update->execute()) {
        if ($stmt_update->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => 'Apuntes guardados correctamente.']);
        } else {
            // Esto podría pasar si la valoracion_id no existe (aunque superadmin podría intentarlo)
            // o si los datos adicionales no cambiaron (algunas DBs no cuentan esto como rowCount > 0 en UPDATE)
            // Para ser más indulgente, podríamos considerarlo éxito si no hay error.
            echo json_encode(['success' => true, 'message' => 'Operación completada (puede que no hubiera cambios).']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error al guardar los apuntes.']);
    }

} catch (PDOException $e) {
    error_log("Error de BD en update_valoracion_datos_adicionales.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al actualizar la valoración.']);
} catch (\Exception $e) {
    error_log("Error general en update_valoracion_datos_adicionales.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}

?>