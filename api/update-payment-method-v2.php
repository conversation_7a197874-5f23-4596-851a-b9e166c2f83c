<?php

declare(strict_types=1);


/**
 * Endpoint mejorado para actualización de métodos de pago con soporte completo para 3DS
 * Versión 2.0 - Usa SetupIntent para manejar autenticación 3D Secure
 */

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

// Función de logging específica para este endpoint
function custom_log_payment_v2($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] [update-payment-method-v2.php] {$message}" . PHP_EOL;
    error_log($logMessage, 3, __DIR__ . '/logs/payment_method_v2.log');
}

// Content-Type header
header('Content-Type: application/json');

custom_log_payment_v2("--- [update-payment-method-v2.php] INICIADO ---");

// Verificar autenticación (usando el mismo método que el endpoint original)
$userId = null;
$agencyId = null;
$isAgencyOwner = false;
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
custom_log_payment_v2("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));

if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            custom_log_payment_v2("Intentando decodificar JWT...");
            $key = JWT_SECRET;
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            $userId = $decoded->user_id ?? null;
            $agencyId = $decoded->agency_id ?? null;
            $isAgencyOwner = isset($decoded->is_agency_owner) ? (bool)$decoded->is_agency_owner : false;

            if (!$userId || !$agencyId) {
                throw new Exception('user_id o agency_id no encontrado en el token JWT');
            }
            custom_log_payment_v2("JWT decodificado correctamente. User ID: {$userId}, Agency ID: {$agencyId}, Is Owner: " . ($isAgencyOwner ? 'Sí' : 'No'));
        } catch (Exception $e) {
            custom_log_payment_v2("Error al decodificar JWT: " . $e->getMessage(), 'ERROR');
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.']);
            exit();
        }
    } else {
        custom_log_payment_v2("Formato de Authorization header incorrecto.", 'ERROR');
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_payment_v2("No se proporcionó token de autorización.", 'ERROR');
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}

// Verificar que sea dueño de la agencia
if (!$isAgencyOwner) {
    custom_log_payment_v2("Acceso denegado: Usuario user_id={$userId} no es dueño de la agencia agency_id={$agencyId}.", 'ERROR');
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo el propietario de la agencia puede actualizar el método de pago.']);
    exit();
}

// Conexión a la base de datos
try {
    $db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
    if ($db->connect_error) {
        throw new Exception("Error de conexión a BD: " . $db->connect_error);
    }
    $db->set_charset(DB_CHARSET);
    custom_log_payment_v2("Conexión a la base de datos establecida correctamente.");
} catch (Exception $e) {
    custom_log_payment_v2("Error de conexión a BD: " . $e->getMessage(), 'ERROR');
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos.']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    custom_log_payment_v2("[POST Request] Procesando para user_id: " . $userId);
    
    $data = json_decode(file_get_contents('php://input'), true);
    $action = $data['action'] ?? 'create_setup_intent'; // 'create_setup_intent' o 'confirm_setup_intent'
    
    custom_log_payment_v2("Acción solicitada: " . $action);
    
    try {
        // Obtener información del cliente y suscripción (usando la misma consulta que el endpoint original)
        $query = "SELECT u.stripe_customer_id, s.stripe_subscription_id, s.estado as subscription_status
                  FROM usuarios u
                  LEFT JOIN suscripciones s ON u.id = s.user_id
                  WHERE u.id = ? AND (s.estado = 'active' OR s.estado = 'trialing' OR s.estado = 'past_due' OR s.estado = 'unpaid' OR s.estado = 'incomplete')
                  ORDER BY s.fecha_creacion DESC
                  LIMIT 1";
        
        $stmt = $db->prepare($query);
        if (!$stmt) {
            throw new Exception("Error al preparar consulta: " . $db->error);
        }
        
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $stripeCustomerId = $row['stripe_customer_id'];
            $stripeSubscriptionId = $row['stripe_subscription_id'];
            $subscriptionStatus = $row['subscription_status'];
            
            custom_log_payment_v2("Cliente encontrado - Customer ID: {$stripeCustomerId}, Subscription ID: {$stripeSubscriptionId}, Status: {$subscriptionStatus}");
        } else {
            throw new Exception("No se encontró información del cliente para el usuario ID: " . $userId);
        }
        $stmt->close();
        
        if (empty($stripeCustomerId)) {
            throw new Exception("El usuario no tiene un Customer ID de Stripe válido.");
        }
        
        // Configurar Stripe
        \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
        
        if ($action === 'create_setup_intent') {
            // Crear SetupIntent para manejar 3DS
            custom_log_payment_v2("Creando SetupIntent para customer: {$stripeCustomerId}");
            
            $setupIntent = \Stripe\SetupIntent::create([
                'customer' => $stripeCustomerId,
                'usage' => 'off_session', // Para pagos futuros
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'always'
                ],
                'metadata' => [
                    'user_id' => $userId,
                    'subscription_id' => $stripeSubscriptionId,
                    'source' => 'dashboard_payment_update'
                ]
            ]);
            
            custom_log_payment_v2("SetupIntent creado: {$setupIntent->id}");
            
            echo json_encode([
                'success' => true,
                'setup_intent_client_secret' => $setupIntent->client_secret,
                'setup_intent_id' => $setupIntent->id,
                'requires_action' => $setupIntent->status === 'requires_action',
                'message' => 'SetupIntent creado correctamente.'
            ]);
            
        } elseif ($action === 'confirm_setup_intent') {
            // Confirmar SetupIntent y actualizar método de pago
            $setupIntentId = $data['setup_intent_id'] ?? null;
            
            if (!$setupIntentId) {
                throw new Exception("setup_intent_id es requerido para confirmar.");
            }
            
            custom_log_payment_v2("Confirmando SetupIntent: {$setupIntentId}");
            
            // Recuperar el SetupIntent
            $setupIntent = \Stripe\SetupIntent::retrieve($setupIntentId);
            
            if ($setupIntent->status !== 'succeeded') {
                throw new Exception("SetupIntent no está en estado 'succeeded'. Estado actual: " . $setupIntent->status);
            }
            
            $paymentMethodId = $setupIntent->payment_method;
            custom_log_payment_v2("SetupIntent confirmado. Payment Method ID: {$paymentMethodId}");
            
            // Actualizar el método de pago predeterminado del cliente
            \Stripe\Customer::update($stripeCustomerId, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId,
                ],
            ]);
            
            custom_log_payment_v2("Método de pago predeterminado actualizado en Customer: {$stripeCustomerId}");
            
            // Si hay una suscripción activa, también actualizarla
            if ($stripeSubscriptionId) {
                try {
                    \Stripe\Subscription::update($stripeSubscriptionId, [
                        'default_payment_method' => $paymentMethodId,
                    ]);
                    custom_log_payment_v2("Método de pago actualizado en Subscription: {$stripeSubscriptionId}");
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    custom_log_payment_v2("Advertencia: No se pudo actualizar la suscripción: " . $e->getMessage(), 'WARNING');
                    // No fallar completamente si solo la suscripción no se puede actualizar
                }
            }
            
            // Si la suscripción está en estado problemático, intentar reactivarla
            if (in_array($subscriptionStatus, ['past_due', 'unpaid', 'incomplete'])) {
                custom_log_payment_v2("Intentando reactivar suscripción en estado: {$subscriptionStatus}");
                
                try {
                    if ($subscriptionStatus === 'incomplete') {
                        // Para suscripciones incompletas, puede que necesitemos confirmar el último PaymentIntent
                        $subscription = \Stripe\Subscription::retrieve($stripeSubscriptionId, [
                            'expand' => ['latest_invoice.payment_intent']
                        ]);
                        
                        if ($subscription->latest_invoice && 
                            $subscription->latest_invoice->payment_intent && 
                            $subscription->latest_invoice->payment_intent->status === 'requires_payment_method') {
                            
                            // Actualizar el PaymentIntent con el nuevo método de pago
                            \Stripe\PaymentIntent::update($subscription->latest_invoice->payment_intent->id, [
                                'payment_method' => $paymentMethodId
                            ]);
                            
                            // Confirmar el PaymentIntent
                            $confirmedPI = \Stripe\PaymentIntent::confirm($subscription->latest_invoice->payment_intent->id);
                            custom_log_payment_v2("PaymentIntent confirmado para suscripción incomplete: " . $confirmedPI->status);
                        }
                    } else {
                        // Para past_due o unpaid, simplemente actualizar el método de pago debería ser suficiente
                        // Stripe intentará automáticamente cobrar con el nuevo método
                        custom_log_payment_v2("Suscripción actualizada con nuevo método de pago. Stripe reintentará automáticamente.");
                    }
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    custom_log_payment_v2("Error al reactivar suscripción: " . $e->getMessage(), 'WARNING');
                    // No fallar completamente, el método de pago se actualizó correctamente
                }
            }
            
            echo json_encode([
                'success' => true,
                'payment_method_updated' => true,
                'subscription_reactivation_attempted' => in_array($subscriptionStatus, ['past_due', 'unpaid', 'incomplete']),
                'message' => 'Método de pago actualizado correctamente.'
            ]);
            
        } else {
            throw new Exception("Acción no válida: " . $action);
        }
        
    } catch (\Stripe\Exception\ApiErrorException $e) {
        custom_log_payment_v2("Error de Stripe API: " . $e->getMessage(), 'ERROR');
        http_response_code($e->getHttpStatus() ?: 500);
        echo json_encode([
            'success' => false, 
            'message' => 'Error de Stripe: ' . $e->getMessage(),
            'stripe_error' => true
        ]);
    } catch (Exception $e) {
        custom_log_payment_v2("Error general: " . $e->getMessage(), 'ERROR');
        http_response_code(500);
        echo json_encode([
            'success' => false, 
            'message' => $e->getMessage()
        ]);
    } finally {
        if (isset($db)) {
            $db->close();
        }
    }
    
} else {
    custom_log_payment_v2("Método no permitido: " . $_SERVER['REQUEST_METHOD'], 'ERROR');
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
}
?>
