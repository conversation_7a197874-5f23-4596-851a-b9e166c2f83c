<?php

declare(strict_types=1);


require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/lib/PaymentFailureLogger.php';

// Aumentar el tiempo máximo de ejecución si es necesario para procesos largos de webhook
// set_time_limit(120); 

// Logger específico para webhooks
if (!function_exists('custom_log_webhook')) {
    function custom_log_webhook($message, $level = 'INFO') {
        $logFile = __DIR__ . '/debug_stripe_webhook.log';
        $timestamp = date('Y-m-d H:i:s');
        $formatted_message = "[$timestamp] [$level] " . print_r($message, true) . PHP_EOL;
        @file_put_contents($logFile, $formatted_message, FILE_APPEND);
        if ($level === 'ERROR' || $level === 'CRITICAL') {
            error_log("[STRIPE_WEBHOOK_ERROR] " . print_r($message, true));
        }
    }
}

custom_log_webhook("--- [stripe-webhook.php] INICIADO ---");

// Conectar a la base de datos para logging
$db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($db->connect_error) {
    custom_log_webhook("ERROR: No se pudo conectar a la base de datos: " . $db->connect_error, 'ERROR');
    http_response_code(500);
    exit();
}
$db->set_charset('utf8mb4');

// Responder a Stripe rápidamente
// Los detalles del procesamiento se harán después de enviar la respuesta 200 OK.
// Esto es importante para evitar timeouts de Stripe si tu lógica tarda mucho.
// Sin embargo, para la depuración inicial, podríamos procesar antes de responder.
// header('Content-Type: application/json'); // Stripe no siempre espera JSON de vuelta, solo un 200.

$payload = @file_get_contents('php://input');
$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? null;
$event = null;

if (!$sig_header) {
    custom_log_webhook("No se recibió HTTP_STRIPE_SIGNATURE.", 'ERROR');
    http_response_code(400);
    exit();
}

if (empty(STRIPE_WEBHOOK_SECRET)) {
    custom_log_webhook("CRÍTICO: STRIPE_WEBHOOK_SECRET no está configurado en bootstrap.php o .env.", 'CRITICAL');
    http_response_code(500);
    exit();
}

try {
    $event = \Stripe\Webhook::constructEvent(
        $payload, $sig_header, STRIPE_WEBHOOK_SECRET
    );
    custom_log_webhook("Evento de Stripe verificado y construido. ID: " . $event->id . ", Tipo: " . $event->type);

    // Registrar evento en la base de datos
    $stmt = $db->prepare("INSERT INTO stripe_webhooks (stripe_event_id, event_type, data, processed) VALUES (?, ?, ?, 0)");
    $event_data = json_encode($event);
    $stmt->bind_param("sss", $event->id, $event->type, $event_data);
    if (!$stmt->execute()) {
        custom_log_webhook("ERROR: No se pudo registrar evento en BD: " . $stmt->error, 'ERROR');
    }
    $stmt->close();

} catch(\UnexpectedValueException $e) {
    custom_log_webhook("Error de Webhook: Payload inválido. " . $e->getMessage(), 'ERROR');
    http_response_code(400);
    exit();
} catch(\Stripe\Exception\SignatureVerificationException $e) {
    custom_log_webhook("Error de Webhook: Falló la verificación de la firma. " . $e->getMessage(), 'ERROR');
    http_response_code(400);
    exit();
} catch(Exception $e) {
    custom_log_webhook("Error de Webhook: Error desconocido al construir el evento. " . $e->getMessage(), 'ERROR');
    http_response_code(500);
    exit();
}

// Enviar respuesta 200 OK a Stripe aquí, antes de procesar lógicas largas.
// Esto asegura que Stripe no marque el webhook como fallido si el procesamiento tarda.
http_response_code(200);
// echo json_encode(['status' => 'success', 'event_received' => $event->id]); // Opcional: puedes enviar un JSON si quieres.
// exit(); // Descomentar esto si quieres que el script termine aquí tras enviar el 200 OK.
            // Y luego tendrías que mover la lógica de abajo a un manejador de colas/proceso en background.
            // Por ahora, para simplificar, procesaremos en línea.

$db = null;
try {
    $db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
    if ($db->connect_error) {
        throw new Exception("Error de conexión a BD (webhook): " . $db->connect_error);
    }
    $db->set_charset(DB_CHARSET);
    custom_log_webhook("Conexión mysqli a la BD establecida para procesar evento: " . $event->type);

    // Obtener el objeto de suscripción del evento
    $stripeSubscriptionObject = null;
    if (isset($event->data) && isset($event->data->object) && $event->data->object->object === 'subscription') {
        $stripeSubscriptionObject = $event->data->object;
    }

    $affected_rows_total = 0;

    switch ($event->type) {
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
            custom_log_webhook("Procesando evento: " . $event->type . " para Subscription ID: " . $stripeSubscriptionObject->id);
            if ($stripeSubscriptionObject) {
                $stripe_subscription_id = $stripeSubscriptionObject->id;
                $stripe_customer_id = $stripeSubscriptionObject->customer;
                $stripe_status = $stripeSubscriptionObject->status; // active, trialing, past_due, canceled, incomplete, etc.
                $current_period_start_ts = $stripeSubscriptionObject->current_period_start;
                $current_period_end_ts = $stripeSubscriptionObject->current_period_end;
                $cancel_at_period_end = $stripeSubscriptionObject->cancel_at_period_end ? 1 : 0; // boolean to int
                $canceled_at_ts = $stripeSubscriptionObject->canceled_at; // timestamp o null
                $trial_start_ts = $stripeSubscriptionObject->trial_start;
                $trial_end_ts = $stripeSubscriptionObject->trial_end;

                // Convertir timestamps a formato de fecha para BD
                $current_period_start = date('Y-m-d H:i:s', $current_period_start_ts);
                $current_period_end = date('Y-m-d H:i:s', $current_period_end_ts);
                $canceled_at = $canceled_at_ts ? date('Y-m-d H:i:s', $canceled_at_ts) : null;
                $trial_start = $trial_start_ts ? date('Y-m-d H:i:s', $trial_start_ts) : null;
                $trial_end = $trial_end_ts ? date('Y-m-d H:i:s', $trial_end_ts) : null;

                // El price ID y otros detalles del plan
                $stripe_price_id = null;
                $price_paid = 0;
                $currency = 'eur'; // default
                if (isset($stripeSubscriptionObject->items) && isset($stripeSubscriptionObject->items->data[0]) && isset($stripeSubscriptionObject->items->data[0]->price)) {
                    $price_object = $stripeSubscriptionObject->items->data[0]->price;
                    $stripe_price_id = $price_object->id;
                    $price_paid = $price_object->unit_amount / 100;
                    $currency = strtoupper($price_object->currency);
                }

                // Obtener user_id y plan_id de nuestra BD
                $user_id = null;
                $stmt_user = $db->prepare("SELECT id FROM usuarios WHERE stripe_customer_id = ?");
                if ($stmt_user) {
                    $stmt_user->bind_param("s", $stripe_customer_id);
                    $stmt_user->execute();
                    $result_user = $stmt_user->get_result();
                    if ($row_user = $result_user->fetch_assoc()) {
                        $user_id = $row_user['id'];
                    }
                    $stmt_user->close();
                } else {
                    custom_log_webhook("Error preparando consulta para obtener user_id: " . $db->error, 'ERROR');
                }

                if (!$user_id) {
                    custom_log_webhook("No se encontró user_id para stripe_customer_id: {$stripe_customer_id}. No se puede actualizar/insertar la suscripción.", 'WARNING');
                    break; // Salir del case si no hay user_id
                }
                
                // Buscar el plan_id correspondiente al stripe_price_id
                $plan_id = null;
                $nombre_plan_contratado = 'Plan Desconocido';
                $billing_cycle = 'monthly'; // Default
                if ($stripe_price_id) {
                    $stmt_plan = $db->prepare("SELECT `id`, `name`, `slug`, IF(? = `stripe_price_id_annual`, 'annual', 'monthly') as cycle FROM `plans` WHERE `stripe_price_id_monthly` = ? OR `stripe_price_id_annual` = ? LIMIT 1");
                    if ($stmt_plan) {
                        $stmt_plan->bind_param("sss", $stripe_price_id, $stripe_price_id, $stripe_price_id);
                        $stmt_plan->execute();
                        $result_plan = $stmt_plan->get_result();
                        if ($row_plan = $result_plan->fetch_assoc()) {
                            $plan_id = $row_plan['id'];
                            $nombre_plan_contratado = $row_plan['name'];
                            $billing_cycle = $row_plan['cycle'];
                        }
                        $stmt_plan->close();
                    } else {
                        custom_log_webhook("Error preparando consulta para obtener plan_id: " . $db->error, 'ERROR');
                    }
                }
                custom_log_webhook("Plan mapeado: ID={$plan_id}, Nombre='{$nombre_plan_contratado}', Ciclo='{$billing_cycle}' para Price ID: {$stripe_price_id}");

                // CORREGIDO: Asegurar que los nombres de columna coincidan con la estructura de la BD.
                // `plan_id` es el ID numérico del plan.
                // `nombre_plan_display` es el nombre textual del plan.
                // `stripe_customer_id` SÍ debe estar aquí.
                $estado_para_db = $stripe_status;
                $fecha_cancelacion_para_db = null; // Columna `fecha_cancelacion` en BD
                $fecha_cancelada_stripe_para_db = null; // Columna `fecha_cancelada_en_stripe` en BD

                if ($stripe_status === 'canceled' && $canceled_at) {
                    // Cancelación inmediata/directa confirmada por Stripe
                    $estado_para_db = 'canceled';
                    $fecha_cancelacion_para_db = $canceled_at;
                    $fecha_cancelada_stripe_para_db = $canceled_at;
                    $cancel_at_period_end = 0; // Si está cancelada, ya no está pendiente de cancelación futura
                    custom_log_webhook("Evento: {$event->type}. Sub ID: {$stripe_subscription_id}. Estado Stripe 'canceled' con fecha. DB estado: canceled, DB fecha_cancelacion: {$fecha_cancelacion_para_db}");
                } elseif ($cancel_at_period_end) {
                    // Programada para cancelar al final del período
                    $fecha_cancelacion_para_db = $current_period_end; // Se cancelará en esta fecha
                    // $fecha_cancelada_stripe_para_db sigue null porque aún no ha sido cancelada por Stripe
                    if ($trial_end && $trial_end_ts > time()) {
                        $estado_para_db = 'trialing';
                    } else {
                        $estado_para_db = 'active'; // Sigue activa (o lo que sea que Stripe diga si no es trialing)
                    }
                    custom_log_webhook("Evento: {$event->type}. Sub ID: {$stripe_subscription_id}. Stripe cancel_at_period_end=true. DB estado: {$estado_para_db}, DB fecha_cancelacion programada: {$fecha_cancelacion_para_db}");
                } else {
                    // No está cancelada ni programada para cancelar (podría ser una reactivación o una actualización normal)
                    // $fecha_cancelacion_para_db y $fecha_cancelada_stripe_para_db permanecen NULL
                    if ($trial_end && $trial_end_ts > time() && $stripe_status !== 'canceled') {
                        $estado_para_db = 'trialing';
                        custom_log_webhook("Evento: {$event->type}. Sub ID: {$stripe_subscription_id}. Trial en curso. DB estado: trialing. Stripe original: {$stripe_status}");
                    } else {
                        // Para otros estados (active, past_due, incomplete), usar el de Stripe.
                        // Si $stripe_status fuera 'canceled' pero sin $canceled_at o sin $cancel_at_period_end, algo es raro.
                        // En este caso, confiamos en $stripe_status como estado principal.
                        custom_log_webhook("Evento: {$event->type}. Sub ID: {$stripe_subscription_id}. Ni cancelada, ni programada, ni trial activo. DB estado será el de Stripe: {$estado_para_db}");
                    }
                }

                $sql_upsert = "INSERT INTO `suscripciones` 
                                (`user_id`, `plan_id`, `stripe_subscription_id`, `stripe_price_id`, `stripe_customer_id`, `estado`, `billing_cycle`, `price_paid`, `currency`, `fecha_inicio_periodo_actual`, `fecha_fin_periodo_actual`, `fecha_inicio_prueba`, `fecha_fin_prueba`, `nombre_plan_display`, `cancel_at_period_end`, `fecha_cancelacion`, `fecha_cancelada_en_stripe`, `fecha_creacion`, `fecha_modificacion`)
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                               ON DUPLICATE KEY UPDATE
                                `plan_id` = VALUES(`plan_id`),
                                `stripe_price_id` = VALUES(`stripe_price_id`),
                                `stripe_customer_id` = VALUES(`stripe_customer_id`), 
                                `estado` = VALUES(`estado`),
                                `billing_cycle` = VALUES(`billing_cycle`),
                                `price_paid` = VALUES(`price_paid`),
                                `currency` = VALUES(`currency`),
                                `fecha_inicio_periodo_actual` = VALUES(`fecha_inicio_periodo_actual`),
                                `fecha_fin_periodo_actual` = VALUES(`fecha_fin_periodo_actual`),
                                `fecha_inicio_prueba` = VALUES(`fecha_inicio_prueba`),
                                `fecha_fin_prueba` = VALUES(`fecha_fin_prueba`),
                                `nombre_plan_display` = VALUES(`nombre_plan_display`),
                                `cancel_at_period_end` = VALUES(`cancel_at_period_end`), // Este es el boolean de Stripe
                                `fecha_cancelacion` = VALUES(`fecha_cancelacion`), // Nuestra fecha de cuándo se cancelará o se canceló
                                `fecha_cancelada_en_stripe` = VALUES(`fecha_cancelada_en_stripe`), // Fecha real de cancelación de Stripe
                                `fecha_modificacion` = NOW()";
                
                $stmt_upsert = $db->prepare($sql_upsert);
                if ($stmt_upsert) {
                    // La cadena de bind_param ahora es: iisssssdssssssiss (18 parámetros)
                    $stmt_upsert->bind_param("iisssssdssssssiss", 
                        $user_id, $plan_id, $stripe_subscription_id, $stripe_price_id, $stripe_customer_id, 
                        $estado_para_db, $billing_cycle, $price_paid, $currency, 
                        $current_period_start, $current_period_end, $trial_start, $trial_end, 
                        $nombre_plan_contratado, 
                        $cancel_at_period_end, // int (0 o 1)
                        $fecha_cancelacion_para_db, // string (date o null)
                        $fecha_cancelada_stripe_para_db // string (date o null)
                    );
                    if ($stmt_upsert->execute()) {
                        $current_affected = $stmt_upsert->affected_rows;
                        $affected_rows_total += $current_affected;
                        custom_log_webhook("Suscripción UPSERTED para User ID: {$user_id}, Sub ID: {$stripe_subscription_id}, DB Estado: {$estado_para_db}, DB Fecha Cancelación: {$fecha_cancelacion_para_db}, Stripe cancel_at_period_end: {$cancel_at_period_end}. Filas: {$current_affected}");

                        // --- INICIO: Actualizar clientes_valorador.activo ---
                        if ($user_id && ($current_affected > 0 || $event->type === 'customer.subscription.created')) { // Actualizar si hubo cambios o es creación
                            $agency_id_for_valorador = null;
                            $stmt_get_agency = $db->prepare("SELECT id FROM agencies WHERE owner_user_id = ? LIMIT 1");
                            if ($stmt_get_agency) {
                                $stmt_get_agency->bind_param("i", $user_id);
                                $stmt_get_agency->execute();
                                $agency_res = $stmt_get_agency->get_result();
                                if ($agency_row = $agency_res->fetch_assoc()) {
                                    $agency_id_for_valorador = $agency_row['id'];
                                }
                                $stmt_get_agency->close();
                            }

                            if ($agency_id_for_valorador) {
                                $valorador_activo_flag = (in_array(strtolower($estado_para_db), ['active', 'trialing'])) ? 1 : 0;
                                custom_log_webhook("Actualizando clientes_valorador.activo a {$valorador_activo_flag} para agency_id: {$agency_id_for_valorador} (basado en estado: {$estado_para_db})");
                                $stmt_update_cv = $db->prepare("UPDATE clientes_valorador SET activo = ? WHERE agency_id = ?");
                                if ($stmt_update_cv) {
                                    $stmt_update_cv->bind_param("ii", $valorador_activo_flag, $agency_id_for_valorador);
                                    if ($stmt_update_cv->execute()) {
                                        custom_log_webhook("clientes_valorador.activo actualizado para agency_id: {$agency_id_for_valorador}. Filas: " . $stmt_update_cv->affected_rows);
                                    } else {
                                        custom_log_webhook("ERROR al actualizar clientes_valorador.activo para agency_id: {$agency_id_for_valorador}: " . $stmt_update_cv->error, 'ERROR');
                                    }
                                    $stmt_update_cv->close();
                                } else {
                                    custom_log_webhook("ERROR preparando statement para actualizar clientes_valorador.activo: " . $db->error, 'ERROR');
                                }
                            } else {
                                custom_log_webhook("ADVERTENCIA: No se encontró agency_id para owner_user_id: {$user_id} al intentar actualizar clientes_valorador.activo.", 'WARNING');
                            }
                        }
                        // --- FIN: Actualizar clientes_valorador.activo ---

                    } else {
                        custom_log_webhook("Error al ejecutar UPSERT de suscripción: " . $stmt_upsert->error, 'ERROR');
                    }
                    $stmt_upsert->close();
                } else {
                    custom_log_webhook("Error preparando UPSERT de suscripción: " . $db->error, 'ERROR');
                }

                // Lógica adicional para actualizar usuarios_planes podría ir aquí si es necesario
                // Por ejemplo, si el plan_id en suscripciones es la fuente de verdad para el plan activo.

            } else {
                 custom_log_webhook("Evento " . $event->type . " no contenía un objeto de suscripción válido.", 'WARNING');
            }
            break;

        case 'customer.subscription.deleted':
            custom_log_webhook("Procesando evento: customer.subscription.deleted para Subscription ID: " . $stripeSubscriptionObject->id);
            if ($stripeSubscriptionObject) {
                $stripe_subscription_id = $stripeSubscriptionObject->id;
                $final_status = 'canceled'; // Forzado, ya que el evento es .deleted
                $canceled_at_timestamp = $stripeSubscriptionObject->canceled_at ?? time(); // Usar canceled_at si está, sino el tiempo actual
                $fecha_cancelada_en_stripe_db = date('Y-m-d H:i:s', $canceled_at_timestamp);
                $fecha_cancelacion_db = $fecha_cancelada_en_stripe_db; // Para .deleted, ambas fechas son la misma

                // Actualizar la suscripción en la base de datos a 'canceled'
                $sql_update_deleted = "UPDATE `suscripciones` 
                                       SET `estado` = ?, `fecha_cancelacion` = ?, `fecha_cancelada_en_stripe` = ?, `cancel_at_period_end` = 0, `fecha_modificacion` = NOW() 
                                       WHERE `stripe_subscription_id` = ?";
                $stmt_update_deleted = $db->prepare($sql_update_deleted);
                if ($stmt_update_deleted) {
                    $stmt_update_deleted->bind_param("ssss", $final_status, $fecha_cancelacion_db, $fecha_cancelada_en_stripe_db, $stripe_subscription_id);
                    if ($stmt_update_deleted->execute()) {
                        $affected_rows_total += $stmt_update_deleted->affected_rows;
                        custom_log_webhook("Suscripción ID: {$stripe_subscription_id} marcada como '{$final_status}' por evento .deleted. Fecha cancelación BD: {$fecha_cancelacion_db}. Filas: " . $stmt_update_deleted->affected_rows);
                    } else {
                        custom_log_webhook("Error al actualizar suscripción a cancelada: " . $stmt_update_deleted->error, 'ERROR');
                    }
                    $stmt_update_deleted->close();
                } else {
                    custom_log_webhook("Error preparando UPDATE para suscripción cancelada: " . $db->error, 'ERROR');
                }
            } else {
                 custom_log_webhook("Evento customer.subscription.deleted no contenía un objeto de suscripción válido.", 'WARNING');
            }
            break;
        
        case 'invoice.paid':
            // Una factura pagada puede significar que una suscripción 'past_due' o 'incomplete' ahora está 'active'.
            custom_log_webhook("Procesando evento: invoice.paid. Invoice ID: " . $event->data->object->id . ", Subscription ID: " . ($event->data->object->subscription ?? 'N/A'));
            if (isset($event->data->object->subscription)) {
                $subscription_id_from_invoice = $event->data->object->subscription;
                $stripe_customer_id_from_invoice = $event->data->object->customer;

                // Es buena idea re-obtener el estado actual de la suscripción desde Stripe
                // para asegurar que tenemos la información más reciente después del pago.
                try {
                    $stripe = new \Stripe\StripeClient(STRIPE_SECRET_KEY);
                    $refreshed_subscription = $stripe->subscriptions->retrieve($subscription_id_from_invoice, []);
                    $new_status_after_invoice_paid = $refreshed_subscription->status;
                    custom_log_webhook("Suscripción {$subscription_id_from_invoice} refrescada tras invoice.paid. Nuevo estado Stripe: {$new_status_after_invoice_paid}");

                    // Actualizar la BD local con este nuevo estado si es diferente o relevante
                    $user_id_for_invoice_sub = null;
                    $stmt_user_inv = $db->prepare("SELECT id FROM usuarios WHERE stripe_customer_id = ?");
                     if ($stmt_user_inv) {
                        $stmt_user_inv->bind_param("s", $stripe_customer_id_from_invoice);
                        $stmt_user_inv->execute();
                        $result_user_inv = $stmt_user_inv->get_result();
                        if ($row_user_inv = $result_user_inv->fetch_assoc()) {
                            $user_id_for_invoice_sub = $row_user_inv['id'];
                        }
                        $stmt_user_inv->close();
                    }

                    if ($user_id_for_invoice_sub) {
                        // Lógica similar a customer.subscription.updated para determinar $estado_para_db
                        $current_period_start_ts_inv = $refreshed_subscription->current_period_start;
                        $current_period_end_ts_inv = $refreshed_subscription->current_period_end;
                        $cancel_at_period_end_inv = $refreshed_subscription->cancel_at_period_end ? 1 : 0;
                        $canceled_at_ts_inv = $refreshed_subscription->canceled_at;
                        $trial_start_ts_inv = $refreshed_subscription->trial_start;
                        $trial_end_ts_inv = $refreshed_subscription->trial_end;

                        $estado_para_db_inv = $new_status_after_invoice_paid;
                        $fecha_cancelacion_para_db_inv = null;
                        $fecha_cancelada_stripe_para_db_inv = null;

                        if ($new_status_after_invoice_paid === 'canceled' && $canceled_at_ts_inv) {
                            $fecha_cancelacion_para_db_inv = date('Y-m-d H:i:s', $canceled_at_ts_inv);
                            $fecha_cancelada_stripe_para_db_inv = date('Y-m-d H:i:s', $canceled_at_ts_inv);
                        } elseif ($cancel_at_period_end_inv) {
                             $fecha_cancelacion_para_db_inv = date('Y-m-d H:i:s', $current_period_end_ts_inv);
                             if ($trial_end_ts_inv && $trial_end_ts_inv > time()) { $estado_para_db_inv = 'trialing'; } 
                             else { $estado_para_db_inv = 'active';} // Si está programada para cancelar, debería estar activa ahora
                        } elseif ($trial_end_ts_inv && $trial_end_ts_inv > time() && $new_status_after_invoice_paid !== 'canceled') {
                            $estado_para_db_inv = 'trialing';
                        }

                        $sql_update_on_invoice = "UPDATE suscripciones 
                                                  SET estado = ?, 
                                                      fecha_inicio_periodo_actual = ?, 
                                                      fecha_fin_periodo_actual = ?,
                                                      fecha_cancelacion = ?,
                                                      fecha_cancelada_en_stripe = ?,
                                                      cancel_at_period_end = ?,
                                                      fecha_modificacion = NOW()
                                                  WHERE stripe_subscription_id = ? AND user_id = ?";
                        $stmt_update_inv = $db->prepare($sql_update_on_invoice);
                        if ($stmt_update_inv) {
                            $current_period_start_dt_inv = date('Y-m-d H:i:s', $current_period_start_ts_inv);
                            $current_period_end_dt_inv = date('Y-m-d H:i:s', $current_period_end_ts_inv);
                            $stmt_update_inv->bind_param("ssssissi", 
                                $estado_para_db_inv, 
                                $current_period_start_dt_inv, 
                                $current_period_end_dt_inv,
                                $fecha_cancelacion_para_db_inv,
                                $fecha_cancelada_stripe_para_db_inv,
                                $cancel_at_period_end_inv,
                                $subscription_id_from_invoice, 
                                $user_id_for_invoice_sub
                            );
                            if($stmt_update_inv->execute()){
                                $current_affected = $stmt_update_inv->affected_rows;
                                $affected_rows_total += $current_affected;
                                custom_log_webhook("Suscripción local actualizada por invoice.paid. User ID: {$user_id_for_invoice_sub}, Sub ID: {$subscription_id_from_invoice}, Nuevo DB Estado: {$estado_para_db_inv}. Filas: {$current_affected}");
                                
                                // --- INICIO: Actualizar clientes_valorador.activo ---
                                if ($current_affected > 0) {
                                    $agency_id_for_valorador = null;
                                    $stmt_get_agency = $db->prepare("SELECT id FROM agencies WHERE owner_user_id = ? LIMIT 1");
                                    if ($stmt_get_agency) {
                                        $stmt_get_agency->bind_param("i", $user_id_for_invoice_sub);
                                        $stmt_get_agency->execute();
                                        $agency_res = $stmt_get_agency->get_result();
                                        if ($agency_row = $agency_res->fetch_assoc()) {
                                            $agency_id_for_valorador = $agency_row['id'];
                                        }
                                        $stmt_get_agency->close();
                                    }

                                    if ($agency_id_for_valorador) {
                                        $valorador_activo_flag = (in_array(strtolower($estado_para_db_inv), ['active', 'trialing'])) ? 1 : 0;
                                        custom_log_webhook("Actualizando clientes_valorador.activo a {$valorador_activo_flag} para agency_id: {$agency_id_for_valorador} (basado en estado tras invoice.paid: {$estado_para_db_inv})");
                                        $stmt_update_cv = $db->prepare("UPDATE clientes_valorador SET activo = ? WHERE agency_id = ?");
                                        if ($stmt_update_cv) {
                                            $stmt_update_cv->bind_param("ii", $valorador_activo_flag, $agency_id_for_valorador);
                                            if ($stmt_update_cv->execute()) {
                                                custom_log_webhook("clientes_valorador.activo actualizado para agency_id: {$agency_id_for_valorador}. Filas: " . $stmt_update_cv->affected_rows);
                                            } else {
                                                custom_log_webhook("ERROR al actualizar clientes_valorador.activo para agency_id: {$agency_id_for_valorador}: " . $stmt_update_cv->error, 'ERROR');
                                            }
                                            $stmt_update_cv->close();
                                        } else {
                                            custom_log_webhook("ERROR preparando statement para actualizar clientes_valorador.activo: " . $db->error, 'ERROR');
                                        }
                                    } else {
                                        custom_log_webhook("ADVERTENCIA: No se encontró agency_id para owner_user_id: {$user_id_for_invoice_sub} al intentar actualizar clientes_valorador.activo (invoice.paid).", 'WARNING');
                                    }
                                }
                                // --- FIN: Actualizar clientes_valorador.activo ---

                            } else {
                                custom_log_webhook("Error actualizando suscripción local por invoice.paid: " . $stmt_update_inv->error, 'ERROR');
                            }
                            $stmt_update_inv->close();
                        } else {
                             custom_log_webhook("Error preparando SQL para actualizar suscripción local por invoice.paid: " . $db->error, 'ERROR');
                        }
                    } else {
                        custom_log_webhook("invoice.paid: No se encontró user_id para Stripe Customer ID: {$stripe_customer_id_from_invoice}. No se pudo actualizar la BD local.", 'WARNING');
                    }

                } catch (\Stripe\Exception\ApiErrorException $e) {
                    custom_log_webhook("Error de Stripe API al intentar refrescar suscripción {$subscription_id_from_invoice} tras invoice.paid: " . $e->getMessage(), 'ERROR');
                } catch (Exception $e) {
                    custom_log_webhook("Error general al procesar lógica de refresco para invoice.paid, suscripción {$subscription_id_from_invoice}: " . $e->getMessage(), 'ERROR');
                }
            }
            break;

        case 'invoice.payment_failed':
            // Un pago fallido puede llevar una suscripción a 'past_due' o 'unpaid'.
            $invoice_object = $event->data->object;
            $payment_intent_id = $invoice_object->payment_intent ?? null;
            $attempt_count = $invoice_object->attempt_count ?? 1;
            $failure_reason = null;

            custom_log_webhook("Procesando evento: invoice.payment_failed. Invoice ID: " . $invoice_object->id . ", Subscription ID: " . ($invoice_object->subscription ?? 'N/A') . ", Attempt: {$attempt_count}");

            // Obtener información detallada del fallo si hay PaymentIntent
            if ($payment_intent_id) {
                try {
                    $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
                    if ($payment_intent->last_payment_error) {
                        $failure_reason = $payment_intent->last_payment_error->code ?? $payment_intent->last_payment_error->type ?? 'unknown';
                        custom_log_webhook("Razón del fallo de pago: {$failure_reason}");
                    }
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    custom_log_webhook("No se pudo obtener detalles del PaymentIntent: " . $e->getMessage(), 'WARNING');
                }
            }

            if (isset($invoice_object->subscription)) {
                $subscription_id_from_failed_invoice = $invoice_object->subscription;
                $stripe_customer_id_from_failed_invoice = $invoice_object->customer;

                // Similar a invoice.paid, refrescar el estado de la suscripción.
                try {
                    $stripe = new \Stripe\StripeClient(STRIPE_SECRET_KEY);
                    $refreshed_subscription_failed = $stripe->subscriptions->retrieve($subscription_id_from_failed_invoice, []);
                    $new_status_after_invoice_failed = $refreshed_subscription_failed->status;
                    custom_log_webhook("Suscripción {$subscription_id_from_failed_invoice} refrescada tras invoice.payment_failed. Nuevo estado Stripe: {$new_status_after_invoice_failed}");

                    // Actualizar BD local
                    $user_id_for_failed_inv_sub = null;
                    $stmt_user_fail_inv = $db->prepare("SELECT id FROM usuarios WHERE stripe_customer_id = ?");
                     if ($stmt_user_fail_inv) {
                        $stmt_user_fail_inv->bind_param("s", $stripe_customer_id_from_failed_invoice);
                        $stmt_user_fail_inv->execute();
                        $result_user_fail_inv = $stmt_user_fail_inv->get_result();
                        if ($row_user_fail_inv = $result_user_fail_inv->fetch_assoc()) {
                            $user_id_for_failed_inv_sub = $row_user_fail_inv['id'];
                        }
                        $stmt_user_fail_inv->close();
                    }

                    if ($user_id_for_failed_inv_sub) {
                         // Lógica similar a customer.subscription.updated para determinar $estado_para_db
                        $current_period_start_ts_fail = $refreshed_subscription_failed->current_period_start;
                        $current_period_end_ts_fail = $refreshed_subscription_failed->current_period_end;
                        $cancel_at_period_end_fail = $refreshed_subscription_failed->cancel_at_period_end ? 1 : 0;
                        $canceled_at_ts_fail = $refreshed_subscription_failed->canceled_at;
                        $trial_start_ts_fail = $refreshed_subscription_failed->trial_start;
                        $trial_end_ts_fail = $refreshed_subscription_failed->trial_end;

                        $estado_para_db_fail = $new_status_after_invoice_failed; // ej. 'past_due'
                        $fecha_cancelacion_para_db_fail = null;
                        $fecha_cancelada_stripe_para_db_fail = null;

                        // ... (lógica de fechas de cancelación si aplica, aunque para past_due no suele aplicar directamente)
                        if ($new_status_after_invoice_failed === 'canceled' && $canceled_at_ts_fail) { // Si el fallo lleva a cancelación inmediata
                            $fecha_cancelacion_para_db_fail = date('Y-m-d H:i:s', $canceled_at_ts_fail);
                            $fecha_cancelada_stripe_para_db_fail = date('Y-m-d H:i:s', $canceled_at_ts_fail);
                        } elseif ($cancel_at_period_end_fail) { // Si ya estaba programada para cancelar y falla el último pago
                             $fecha_cancelacion_para_db_fail = date('Y-m-d H:i:s', $current_period_end_ts_fail);
                             // El estado podría seguir siendo el que era antes de la falla (ej. 'active' pero con pago fallido) o Stripe lo pone 'past_due'
                        }
                        // Si está en trial y falla un pago de setup, no debería afectar el 'trialing' status.
                        // Nos fiamos de $new_status_after_invoice_failed y ajustamos el estado de trialing si es necesario.
                        if ($trial_end_ts_fail && $trial_end_ts_fail > time() && $new_status_after_invoice_failed !== 'canceled') {
                             $estado_para_db_fail = 'trialing'; // Mantener en trialing si el pago fallido no cancela el trial
                        }


                        $sql_update_on_fail = "UPDATE suscripciones 
                                               SET estado = ?, 
                                                   fecha_cancelacion = ?,
                                                   fecha_cancelada_en_stripe = ?,
                                                   cancel_at_period_end = ?,
                                                   fecha_modificacion = NOW()
                                               WHERE stripe_subscription_id = ? AND user_id = ?";
                        $stmt_update_fail = $db->prepare($sql_update_on_fail);
                        if ($stmt_update_fail) {
                             $stmt_update_fail->bind_param("sssis", 
                                $estado_para_db_fail,
                                $fecha_cancelacion_para_db_fail,
                                $fecha_cancelada_stripe_para_db_fail,
                                $cancel_at_period_end_fail,
                                $subscription_id_from_failed_invoice // user_id no está en esta query, ¿debería? Sí.
                                // $user_id_for_failed_inv_sub --> Falta un `i` en "sssis" y el user_id en el bind
                            );
                            // CORRECCIÓN: la query es `... WHERE stripe_subscription_id = ? AND user_id = ?`
                            // El bind_param debe ser "sssisi"
                            $stmt_update_fail->bind_param("sssisi", 
                                $estado_para_db_fail,
                                $fecha_cancelacion_para_db_fail,
                                $fecha_cancelada_stripe_para_db_fail,
                                $cancel_at_period_end_fail,
                                $subscription_id_from_failed_invoice,
                                $user_id_for_failed_inv_sub
                            );

                            if($stmt_update_fail->execute()){
                                $current_affected = $stmt_update_fail->affected_rows;
                                $affected_rows_total += $current_affected;
                                custom_log_webhook("Suscripción local actualizada por invoice.payment_failed. User ID: {$user_id_for_failed_inv_sub}, Sub ID: {$subscription_id_from_failed_invoice}, Nuevo DB Estado: {$estado_para_db_fail}. Filas: {$current_affected}");

                                // --- INICIO: Registrar fallo de pago detallado ---
                                try {
                                    $paymentFailureLogger = new PaymentFailureLogger($db);

                                    // Verificar si hay método de pago disponible
                                    $has_payment_method = 0;
                                    $payment_method_details = null;

                                    try {
                                        $stripe_customer = \Stripe\Customer::retrieve($stripe_customer_id_from_failed_invoice);
                                        if ($stripe_customer->invoice_settings->default_payment_method) {
                                            $has_payment_method = 1;
                                            $pm = \Stripe\PaymentMethod::retrieve($stripe_customer->invoice_settings->default_payment_method);
                                            if ($pm->card) {
                                                $payment_method_details = [
                                                    'brand' => $pm->card->brand,
                                                    'last4' => $pm->card->last4,
                                                    'exp_month' => $pm->card->exp_month,
                                                    'exp_year' => $pm->card->exp_year
                                                ];
                                            }
                                        }
                                    } catch (\Stripe\Exception\ApiErrorException $e) {
                                        custom_log_webhook("No se pudo verificar método de pago para logging: " . $e->getMessage(), 'WARNING');
                                    }

                                    $failure_data = [
                                        'user_id' => $user_id_for_failed_inv_sub,
                                        'stripe_customer_id' => $stripe_customer_id_from_failed_invoice,
                                        'stripe_subscription_id' => $subscription_id_from_failed_invoice,
                                        'stripe_invoice_id' => $invoice_object->id,
                                        'stripe_payment_intent_id' => $payment_intent_id,
                                        'failure_reason' => $failure_reason,
                                        'attempt_count' => $attempt_count,
                                        'amount_failed' => $invoice_object->amount_due ? ($invoice_object->amount_due / 100) : null,
                                        'currency' => strtoupper($invoice_object->currency ?? 'EUR'),
                                        'subscription_status_before' => $estado_para_db_fail, // El estado antes era diferente, pero no lo tenemos aquí
                                        'subscription_status_after' => $estado_para_db_fail,
                                        'has_payment_method' => $has_payment_method,
                                        'payment_method_details' => $payment_method_details,
                                        'notes' => "Webhook invoice.payment_failed procesado. Attempt: {$attempt_count}"
                                    ];

                                    $failure_id = $paymentFailureLogger->logPaymentFailure($failure_data);
                                    if ($failure_id) {
                                        custom_log_webhook("Fallo de pago registrado con ID: {$failure_id}");
                                    }

                                } catch (Exception $e) {
                                    custom_log_webhook("Error registrando fallo de pago detallado: " . $e->getMessage(), 'ERROR');
                                }
                                // --- FIN: Registrar fallo de pago detallado ---

                                // --- INICIO: Actualizar clientes_valorador.activo ---
                                if ($current_affected > 0) {
                                    $agency_id_for_valorador = null;
                                    $stmt_get_agency = $db->prepare("SELECT id FROM agencies WHERE owner_user_id = ? LIMIT 1");
                                    if ($stmt_get_agency) {
                                        $stmt_get_agency->bind_param("i", $user_id_for_failed_inv_sub);
                                        $stmt_get_agency->execute();
                                        $agency_res = $stmt_get_agency->get_result();
                                        if ($agency_row = $agency_res->fetch_assoc()) {
                                            $agency_id_for_valorador = $agency_row['id'];
                                        }
                                        $stmt_get_agency->close();
                                    }

                                    if ($agency_id_for_valorador) {
                                        $valorador_activo_flag = (in_array(strtolower($estado_para_db_fail), ['active', 'trialing'])) ? 1 : 0;
                                        custom_log_webhook("Actualizando clientes_valorador.activo a {$valorador_activo_flag} para agency_id: {$agency_id_for_valorador} (basado en estado tras invoice.payment_failed: {$estado_para_db_fail})");
                                        $stmt_update_cv = $db->prepare("UPDATE clientes_valorador SET activo = ? WHERE agency_id = ?");
                                        if ($stmt_update_cv) {
                                            $stmt_update_cv->bind_param("ii", $valorador_activo_flag, $agency_id_for_valorador);
                                            if ($stmt_update_cv->execute()) {
                                                custom_log_webhook("clientes_valorador.activo actualizado para agency_id: {$agency_id_for_valorador}. Filas: " . $stmt_update_cv->affected_rows);
                                            } else {
                                                custom_log_webhook("ERROR al actualizar clientes_valorador.activo para agency_id: {$agency_id_for_valorador}: " . $stmt_update_cv->error, 'ERROR');
                                            }
                                            $stmt_update_cv->close();
                                        } else {
                                            custom_log_webhook("ERROR preparando statement para actualizar clientes_valorador.activo: " . $db->error, 'ERROR');
                                        }
                                    } else {
                                        custom_log_webhook("ADVERTENCIA: No se encontró agency_id para owner_user_id: {$user_id_for_failed_inv_sub} al intentar actualizar clientes_valorador.activo (invoice.payment_failed).", 'WARNING');
                                    }
                                }
                                // --- FIN: Actualizar clientes_valorador.activo ---

                            } else {
                                 custom_log_webhook("Error actualizando suscripción local por invoice.payment_failed: " . $stmt_update_fail->error, 'ERROR');
                            }
                            $stmt_update_fail->close();
                        } else {
                            custom_log_webhook("Error preparando SQL para actualizar suscripción local por invoice.payment_failed: " . $db->error, 'ERROR');
                        }
                    } else {
                         custom_log_webhook("invoice.payment_failed: No se encontró user_id para Stripe Customer ID: {$stripe_customer_id_from_failed_invoice}. No se pudo actualizar la BD local.", 'WARNING');
                    }
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    custom_log_webhook("Error de Stripe API al intentar refrescar suscripción {$subscription_id_from_failed_invoice} tras invoice.payment_failed: " . $e->getMessage(), 'ERROR');
                } catch (Exception $e) {
                    custom_log_webhook("Error general al procesar lógica de refresco para invoice.payment_failed, suscripción {$subscription_id_from_failed_invoice}: " . $e->getMessage(), 'ERROR');
                }
            }
            break;

        // ... otros tipos de eventos de Stripe que podrías querer manejar
        // default:
        // custom_log_webhook('Evento no manejado: ' . $event->type);
    }
    
    if ($affected_rows_total > 0) {
         custom_log_webhook("Procesamiento de webhook {$event->type} (ID: {$event->id}) completado. Total filas afectadas en BD: {$affected_rows_total}");
    } else {
         custom_log_webhook("Procesamiento de webhook {$event->type} (ID: {$event->id}) completado. Sin filas afectadas directamente en este manejador principal (podría ser manejado por sub-lógicas o no requerir acción DB directa).");
    }

} catch (Exception $e) {
    custom_log_webhook("Error CRÍTICO durante el procesamiento del webhook (después de recibir evento): " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString(), 'CRITICAL');
    // No se puede enviar http_response_code aquí si ya se envió 200 OK antes.
    // Este error debe ser monitoreado en los logs.
} finally {
    if ($db) {
        $db->close();
        custom_log_webhook("Conexión a BD cerrada después de procesar evento.");
    }
}

custom_log_webhook("--- [stripe-webhook.php] FINALIZADO ---");
exit(); // Asegurar que el script termina.

?> 