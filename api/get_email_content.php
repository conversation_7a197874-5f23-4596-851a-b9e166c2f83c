<?php
declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

// Función de log local (reutilizada o adaptada si es necesario)
if (!function_exists('custom_log_get_email_content')) {
    function custom_log_get_email_content($message) {
        $logFile = __DIR__ . '/logs/debug_get_email_content.log';
        if (!is_dir(__DIR__ . '/logs')) {
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                error_log("Advertencia [get_email_content]: No se pudo crear el directorio de logs: " . __DIR__ . '/logs');
                return;
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [get_email_content] {$message}\n", 3, $logFile);
    }
}

// Función para obtener y decodificar el token JWT (similar a otros scripts)
function get_decoded_jwt_payload_email_content() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded;
            } catch (\Throwable $e) {
                if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                    ini_set('display_errors', $previous_display_errors);
                }
            }
        }
    }
    return null;
}

$decoded_payload = get_decoded_jwt_payload_email_content();
$user_id_from_jwt = $decoded_payload->user_id ?? null;
$agency_id_from_jwt = $decoded_payload->agency_id ?? null;
$roles_from_jwt = isset($decoded_payload->roles) && is_array($decoded_payload->roles) ? $decoded_payload->roles : (isset($decoded_payload->roles) && is_object($decoded_payload->roles) ? (array)$decoded_payload->roles : []);
$is_super_admin = in_array('admin', $roles_from_jwt);

header('Content-Type: application/json');

if (!$user_id_from_jwt) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Usuario no identificado.']);
    exit;
}

$email_historial_id = filter_input(INPUT_GET, 'email_id', FILTER_VALIDATE_INT);

if (!$email_historial_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Parámetro email_id no válido o faltante.']);
    exit;
}

try {
    $pdo = Database::getInstance()->getConnection();

    $sql = "SELECT
                leh.asunto_final,
                leh.cuerpo_final_html,
                leh.borrador_asunto,
                leh.borrador_cuerpo_html,
                vl.cliente_valorador_id AS client_id
            FROM lead_emails_historial leh
            JOIN valorador_leads vl ON leh.lead_id = vl.id
            WHERE leh.id = :email_historial_id";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':email_historial_id', $email_historial_id, PDO::PARAM_INT);
    $stmt->execute();
    $email_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$email_data) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Email no encontrado.']);
        exit;
    }

    // Verificación de Permisos: Super Admin o si el email pertenece a un cliente de la agencia del usuario
    if (!$is_super_admin) {
        if (!$agency_id_from_jwt) {
            http_response_code(403);
            custom_log_get_email_content("Acceso denegado: Usuario {$user_id_from_jwt} sin agency_id intentando acceder a email {$email_historial_id}");
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: Información de agencia no disponible.']);
            exit;
        }

        // Obtener los client_identifiers de la agencia del usuario
        $stmt_clients = $pdo->prepare("SELECT id FROM clientes_valorador WHERE agency_id = :agency_id");
        $stmt_clients->bindParam(':agency_id', $agency_id_from_jwt, PDO::PARAM_INT);
        $stmt_clients->execute();
        $allowed_client_identifiers = $stmt_clients->fetchAll(PDO::FETCH_COLUMN);
        
        // Convertir a enteros para comparación segura
        $allowed_client_identifiers = array_map('intval', $allowed_client_identifiers);
        $client_id_to_check = intval($email_data['client_id']);
        
        custom_log_get_email_content("DEBUG permisos: agency_id={$agency_id_from_jwt}, user_id={$user_id_from_jwt}, email_client_id={$client_id_to_check}, allowed=" . json_encode($allowed_client_identifiers));
        
        if (empty($allowed_client_identifiers)) {
            http_response_code(403);
            custom_log_get_email_content("Acceso denegado: Usuario {$user_id_from_jwt} (agencia {$agency_id_from_jwt}) sin clientes asociados");
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: Su agencia no tiene clientes asociados.']);
            exit;
        }

        if (!in_array($client_id_to_check, $allowed_client_identifiers, true)) {
            http_response_code(403);
            custom_log_get_email_content("Acceso denegado: Usuario {$user_id_from_jwt} (agencia {$agency_id_from_jwt}) intentando acceder a email {$email_historial_id} del client_id {$client_id_to_check}");
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: No tiene permiso para ver este email.']);
            exit;
        }
    }

    // Usar contenido final si existe, sino usar borrador
    $asunto = $email_data['asunto_final'] ?: $email_data['borrador_asunto'];
    $cuerpoHTML = $email_data['cuerpo_final_html'] ?: $email_data['borrador_cuerpo_html'];

    echo json_encode([
        'success' => true,
        'data' => [
            'asunto' => $asunto,
            'cuerpoHTML' => $cuerpoHTML
        ]
    ]);

} catch (PDOException $e) {
    custom_log_get_email_content("ERROR PDO: " . $e->getMessage());
    error_log("[get_email_content.php] PDOException: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB).']);
} catch (Exception $e) {
    custom_log_get_email_content("ERROR Exception: " . $e->getMessage());
    error_log("[get_email_content.php] Exception: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}

?> 