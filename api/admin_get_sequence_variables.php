<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

use Api\lib\EmailVariablesService;

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}
$conn->set_charset(DB_CHARSET);

try {
    // Buscar un lead con valoración completa para mostrar variables reales
    $sql = '
        SELECT vl.id
        FROM valorador_leads vl
        JOIN valorador_valoraciones vv ON vl.valoracion_id = vv.id
        WHERE vv.valor_estimado_min > 0
        LIMIT 1
    ';

    $result = $conn->query($sql);
    $sampleLead = $result ? $result->fetch_assoc() : null;

    if ($sampleLead) {
        // Obtener variables reales pero convertir a formato placeholder-only para prompts IA
        $realVariables = EmailVariablesService::getVariablesForLead($sampleLead['id']);

        // Para prompts de IA, convertir a placeholders solamente
        $variables = [];
        foreach ($realVariables as $placeholder => $realValue) {
            $variables[$placeholder] = $placeholder; // Key y value son el mismo placeholder
        }
    } else {
        // Si no hay leads, crear variables básicas como placeholders
        $variables = [
            '{{lead_name}}' => '{{lead_name}}',
            '{{lead_email}}' => '{{lead_email}}',
            '{{lead_phone}}' => '{{lead_phone}}',
            '{{property_address}}' => '{{property_address}}',
            '{{property_value_min}}' => '{{property_value_min}}',
            '{{property_value_max}}' => '{{property_value_max}}',
            '{{property_value_range}}' => '{{property_value_range}}',
            '{{property_type}}' => '{{property_type}}',
            '{{property_surface}}' => '{{property_surface}}',
            '{{property_rooms}}' => '{{property_rooms}}',
            '{{property_bathrooms}}' => '{{property_bathrooms}}',
            '{{zone_name}}' => '{{zone_name}}',
            '{{zone_avg_price_m2}}' => '{{zone_avg_price_m2}}',
            '{{nombre_agencia}}' => '{{nombre_agencia}}',
            '{{agent_name}}' => '{{agent_name}}',
            '{{valorador_contact_url}}' => '{{valorador_contact_url}}',
            '{{current_date}}' => '{{current_date}}',
            '{{year}}' => '{{year}}'
        ];
    }

    // Las variables ya están obtenidas del servicio arriba
    
    // Organizar variables por categorías
    $categorizedVariables = [
        'lead' => [
            'title' => '👤 Variables del Lead',
            'description' => 'Información personal del cliente',
            'variables' => []
        ],
        'property' => [
            'title' => '🏠 Variables de la Propiedad',
            'description' => 'Características y valoración del inmueble',
            'variables' => []
        ],
        'zone' => [
            'title' => '📍 Variables de la Zona',
            'description' => 'Estadísticas del mercado local',
            'variables' => []
        ],
        'agency' => [
            'title' => '🏢 Variables de la Agencia',
            'description' => 'Información de la inmobiliaria y agente',
            'variables' => []
        ],
        'date' => [
            'title' => '📅 Variables de Fecha',
            'description' => 'Fechas y tiempo actual',
            'variables' => []
        ],
        'calculated' => [
            'title' => '🧮 Variables Calculadas',
            'description' => 'Valores derivados y comparativas',
            'variables' => []
        ]
    ];
    
    // Clasificar variables por categoría
    foreach ($variables as $key => $value) {
        $variable = [
            'key' => $key,
            'value' => $value,
            'description' => getVariableDescription($key)
        ];
        
        if (strpos($key, 'lead_') !== false) {
            $categorizedVariables['lead']['variables'][] = $variable;
        } elseif (strpos($key, 'property_') !== false || strpos($key, 'estimated_') !== false) {
            $categorizedVariables['property']['variables'][] = $variable;
        } elseif (strpos($key, 'zone_') !== false) {
            $categorizedVariables['zone']['variables'][] = $variable;
        } elseif (strpos($key, 'agency_') !== false || strpos($key, 'agent_') !== false || strpos($key, 'nombre_agencia') !== false || strpos($key, 'valorador_') !== false) {
            $categorizedVariables['agency']['variables'][] = $variable;
        } elseif (strpos($key, 'date') !== false || strpos($key, 'year') !== false || strpos($key, 'month') !== false || strpos($key, 'day') !== false || strpos($key, 'current_') !== false) {
            $categorizedVariables['date']['variables'][] = $variable;
        } elseif (strpos($key, 'price_per_') !== false || strpos($key, 'market_') !== false || strpos($key, 'investment_') !== false || strpos($key, 'roi_') !== false) {
            $categorizedVariables['calculated']['variables'][] = $variable;
        } else {
            $categorizedVariables['calculated']['variables'][] = $variable;
        }
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'categories' => $categorizedVariables,
            'total_variables' => count($variables),
            'sample_lead_id' => $sampleLead['id'] ?? null
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener variables: ' . $e->getMessage()
    ]);
}

$conn->close();

/**
 * Obtiene la descripción de una variable
 */
function getVariableDescription($key) {
    $descriptions = [
        '{{lead_name}}' => 'Nombre completo del lead',
        '{{lead_email}}' => 'Email del lead',
        '{{lead_phone}}' => 'Teléfono del lead',
        '{{lead_necesidad}}' => 'Necesidad del lead (vender/comprar)',
        '{{property_address}}' => 'Dirección de la propiedad',
        '{{property_value_min}}' => 'Valor mínimo estimado',
        '{{property_value_max}}' => 'Valor máximo estimado',
        '{{property_value_range}}' => 'Rango de valor completo',
        '{{property_type}}' => 'Tipo de propiedad',
        '{{property_surface}}' => 'Superficie de la propiedad',
        '{{property_rooms}}' => 'Número de habitaciones',
        '{{property_bathrooms}}' => 'Número de baños',
        '{{zone_name}}' => 'Nombre de la zona',
        '{{zone_avg_price_m2}}' => 'Precio promedio por m² en la zona',
        '{{agency_name}}' => 'Nombre de la agencia',
        '{{agent_name}}' => 'Nombre del agente',
        '{{current_date}}' => 'Fecha actual',
        '{{current_year}}' => 'Año actual',
        '{{contenido_ia_generado}}' => 'Contenido generado por IA'
    ];

    return $descriptions[$key] ?? 'Variable disponible';
}

?>
