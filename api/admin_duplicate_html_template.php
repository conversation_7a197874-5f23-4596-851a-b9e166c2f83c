<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['template_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de plantilla requerido']);
    exit;
}

$template_id = (int)$input['template_id'];
$new_name = isset($input['new_name']) ? trim($input['new_name']) : '';

if ($template_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de plantilla inválido']);
    exit;
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect): ' . $e->getMessage()]);
    exit();
}

try {
    // Obtener plantilla original
    $get_sql = "
        SELECT 
            nombre_interno_plantilla,
            descripcion,
            asunto_predeterminado,
            contenido_html,
            tipo_plantilla,
            activa
        FROM email_plantillas_html 
        WHERE id = ?
    ";
    
    $get_stmt = $pdo->prepare($get_sql);
    $get_stmt->execute([$template_id]);
    $original = $get_stmt->fetch();
    
    if (!$original) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Plantilla original no encontrada']);
        exit;
    }
    
    // Generar nuevo nombre si no se proporcionó
    if (empty($new_name)) {
        $base_name = $original['nombre_interno_plantilla'];
        $counter = 1;
        
        do {
            $new_name = $base_name . '_copia_' . $counter;
            $check_sql = "SELECT id FROM email_plantillas_html WHERE nombre_interno_plantilla = ?";
            $check_stmt = $pdo->prepare($check_sql);
            $check_stmt->execute([$new_name]);
            $exists = $check_stmt->fetch();
            $counter++;
        } while ($exists && $counter <= 100);
        
        if ($exists) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No se pudo generar un nombre único para la copia']);
            exit;
        }
    } else {
        // Verificar que el nuevo nombre no exista
        $check_sql = "SELECT id FROM email_plantillas_html WHERE nombre_interno_plantilla = ?";
        $check_stmt = $pdo->prepare($check_sql);
        $check_stmt->execute([$new_name]);
        if ($check_stmt->fetch()) {
            http_response_code(409); // Conflict
            echo json_encode(['success' => false, 'message' => 'El nuevo nombre de plantilla ya existe']);
            exit;
        }
    }
    
    // Insertar la nueva plantilla
    $insert_sql = "
        INSERT INTO email_plantillas_html (
            nombre_interno_plantilla,
            descripcion,
            asunto_predeterminado,
            contenido_html,
            tipo_plantilla,
            activa,
            fecha_creacion,
            fecha_modificacion
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    ";
    
    $insert_stmt = $pdo->prepare($insert_sql);
    $insert_stmt->execute([
        $new_name,
        $original['descripcion'],
        $original['asunto_predeterminado'],
        $original['contenido_html'],
        $original['tipo_plantilla'],
        $original['activa']
    ]);
    
    $new_template_id = $pdo->lastInsertId();
    
    // Obtener la nueva plantilla para devolverla
    $new_template_stmt = $pdo->prepare("SELECT * FROM email_plantillas_html WHERE id = ?");
    $new_template_stmt->execute([$new_template_id]);
    $new_template = $new_template_stmt->fetch();
    
    http_response_code(201);
    echo json_encode(['success' => true, 'message' => 'Plantilla duplicada correctamente', 'data' => $new_template]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error en la operación de base de datos: ' . $e->getMessage()]);
    exit;
}
