<?php
declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;

// --- Función de log local ---
if (!function_exists('custom_log_brevo_webhook')) {
    function custom_log_brevo_webhook($message) {
        // Usar error_log para que aparezca en Cloud Logging
        error_log("[brevo_webhook] {$message}");

        // También mantener el log local para debugging
        $logFile = __DIR__ . '/logs/debug_brevo_webhook.log';
        if (!is_dir(__DIR__ . '/logs')) {
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                error_log("Advertencia [brevo_webhook]: No se pudo crear el directorio de logs.");
                return;
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [brevo_webhook] {$message}\n", 3, $logFile);
    }
}

// --- Función para activar secuencia por interacción ---
if (!function_exists('activarSecuenciaPorInteraccion')) {
    function activarSecuenciaPorInteraccion($pdo, $lead_id, $tracking_record, $interaction_type = 'click') {
        // 3. Obtener toda la información necesaria para encontrar la secuencia correcta
        $stmt_lead_info = $pdo->prepare("SELECT cliente_valorador_id FROM valorador_leads WHERE id = :lead_id");
        $stmt_lead_info->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
        $stmt_lead_info->execute();
        $lead_cliente_valorador_id = $stmt_lead_info->fetchColumn();
        if (!$lead_cliente_valorador_id) throw new Exception("No se encontró cliente_valorador_id para el lead ID: {$lead_id}.");

        $stmt_client_info = $pdo->prepare("SELECT agency_id FROM clientes_valorador WHERE id = :cliente_valorador_id");
        $stmt_client_info->bindParam(':cliente_valorador_id', $lead_cliente_valorador_id, PDO::PARAM_INT);
        $stmt_client_info->execute();
        $agency_id = $stmt_client_info->fetchColumn();
        if (!$agency_id) throw new Exception("No se encontró agency_id para el cliente_valorador_id: {$lead_cliente_valorador_id}.");

        $stmt_plan_info = $pdo->prepare("SELECT plan_id FROM suscripciones WHERE agency_id = :agency_id AND estado IN ('active', 'trialing') LIMIT 1");
        $stmt_plan_info->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
        $stmt_plan_info->execute();
        $plan_id = $stmt_plan_info->fetchColumn();
        if (!$plan_id) throw new Exception("No se encontró suscripción activa para la agencia ID: {$agency_id}.");

        $stmt_sequence_assignment = $pdo->prepare("SELECT sequence_id FROM plan_sequence_assignments WHERE plan_id = :plan_id LIMIT 1");
        $stmt_sequence_assignment->bindParam(':plan_id', $plan_id, PDO::PARAM_INT);
        $stmt_sequence_assignment->execute();
        $assigned_sequence_id = $stmt_sequence_assignment->fetchColumn();
        if (!$assigned_sequence_id) throw new Exception("No se encontró asignación de secuencia para el plan ID: {$plan_id}.");

        // 4. Obtener el primer paso de la secuencia para inicializarla
        $stmt_first_step = $pdo->prepare("SELECT id, delay_days FROM sequence_steps WHERE sequence_id = :sequence_id ORDER BY step_order ASC LIMIT 1");
        $stmt_first_step->bindParam(':sequence_id', $assigned_sequence_id, PDO::PARAM_INT);
        $stmt_first_step->execute();
        $first_step = $stmt_first_step->fetch(PDO::FETCH_ASSOC);

        if ($first_step) {
            $next_due_date = date('Y-m-d H:i:s', strtotime("+{$first_step['delay_days']} days"));
            $first_step_id = $first_step['id'];

            // 5. Actualizar el registro de tracking con los datos de inicio de la secuencia
            $stmt_activate = $pdo->prepare("
                UPDATE lead_sequence_tracking
                SET
                    status = 'active',
                    current_step_id = :current_step_id,
                    next_due_date = :next_due_date,
                    updated_at = NOW()
                WHERE
                    id = :tracking_id
            ");

            $stmt_activate->bindParam(':current_step_id', $first_step_id, PDO::PARAM_INT);
            $stmt_activate->bindParam(':next_due_date', $next_due_date, PDO::PARAM_STR);
            $stmt_activate->bindParam(':tracking_id', $tracking_record['id'], PDO::PARAM_INT);
            $stmt_activate->execute();

            custom_log_brevo_webhook("Secuencia activada para Lead ID: $lead_id por $interaction_type. Siguiente paso programado para: $next_due_date");
        } else {
            throw new Exception("No se encontró el primer paso para la secuencia ID: {$assigned_sequence_id}. La secuencia no puede iniciarse.");
        }
    }
}

header('Content-Type: application/json');

// Recibir el payload del webhook
$payload = file_get_contents('php://input');
error_log("[brevo_webhook] INICIO - Payload recibido: " . substr($payload, 0, 200) . "...");

$data = json_decode($payload, true);
error_log("[brevo_webhook] JSON decodificado: " . print_r($data, true));

if (!$data || !isset($data['event']) || !isset($data['message-id'])) {
    error_log("[brevo_webhook] ERROR - Payload inválido o incompleto: " . $payload);
    custom_log_brevo_webhook("Payload inválido o incompleto: " . $payload);
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Payload inválido o incompleto']);
    exit;
}

// Registrar el evento recibido
error_log("[brevo_webhook] Evento recibido: " . $data['event'] . ", Message ID: " . $data['message-id']);
custom_log_brevo_webhook("Evento recibido: " . $data['event'] . ", Message ID: " . $data['message-id']);

try {
    error_log("[brevo_webhook] Conectando a base de datos...");
    $pdo = Database::getInstance()->getConnection();
    error_log("[brevo_webhook] Conexión exitosa");

    // Buscar el registro correspondiente en la base de datos
    error_log("[brevo_webhook] Buscando registro para Message ID: " . $data['message-id']);
    $stmt = $pdo->prepare("SELECT id, lead_id FROM lead_emails_historial WHERE mensaje_id_esp = :message_id");
    $stmt->bindParam(':message_id', $data['message-id'], PDO::PARAM_STR);
    $stmt->execute();
    $email_record = $stmt->fetch(PDO::FETCH_ASSOC);

    error_log("[brevo_webhook] Resultado búsqueda: " . print_r($email_record, true));

    if (!$email_record) {
        error_log("[brevo_webhook] No se encontró registro para el Message ID: " . $data['message-id']);
        custom_log_brevo_webhook("No se encontró registro para el Message ID: " . $data['message-id']);
        http_response_code(200); // Aceptamos el webhook pero no hacemos nada
        echo json_encode(['success' => true, 'message' => 'Evento recibido pero no se encontró registro correspondiente']);
        exit;
    }
    
    $email_id = $email_record['id'];
    $lead_id = $email_record['lead_id'];

    error_log("[brevo_webhook] Email ID: {$email_id}, Lead ID: {$lead_id}");
    error_log("[brevo_webhook] Procesando evento: " . $data['event']);

    // Procesar el evento según su tipo
    switch ($data['event']) {
        case 'unique_opened':
        case 'opened':
        case 'proxy_open':
            // Actualizar timestamp de apertura si no existe
            $stmt = $pdo->prepare("UPDATE lead_emails_historial SET abierto_timestamp = :timestamp WHERE id = :id AND abierto_timestamp IS NULL");
            $timestamp = date('Y-m-d H:i:s', (int)($data['ts_epoch'] / 1000)); // Usar date() para Madrid, igual que MySQL
            $stmt->bindParam(':timestamp', $timestamp, PDO::PARAM_STR);
            $stmt->bindParam(':id', $email_id, PDO::PARAM_INT);
            $stmt->execute();
            custom_log_brevo_webhook("Actualizado abierto_timestamp para email ID: $email_id, Lead ID: $lead_id");

            // --- Lógica para activar la secuencia de nutrición IA también por apertura ---
            $pdo->beginTransaction();
            try {
                // 1. Obtener el estado actual de la secuencia para el lead (y bloquear la fila para evitar condiciones de carrera)
                $stmt_track = $pdo->prepare("SELECT id, status FROM lead_sequence_tracking WHERE lead_id = :lead_id FOR UPDATE");
                $stmt_track->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
                $stmt_track->execute();
                $tracking_record = $stmt_track->fetch(PDO::FETCH_ASSOC);

                // 2. Solo proceder si la secuencia está pendiente de activación ('pending_activation')
                if ($tracking_record && $tracking_record['status'] === 'pending_activation') {
                    custom_log_brevo_webhook("Activando secuencia por APERTURA para Lead ID: $lead_id");

                    // Usar la misma lógica que para clicks
                    activarSecuenciaPorInteraccion($pdo, $lead_id, $tracking_record, 'opened');
                } else {
                    custom_log_brevo_webhook("La secuencia para el Lead ID: $lead_id no requiere activación por apertura (Estado actual: " . ($tracking_record['status'] ?? 'no encontrado') . ").");
                }

                $pdo->commit();
            } catch (Exception $e) {
                $pdo->rollBack();
                custom_log_brevo_webhook("Error al activar secuencia por apertura para Lead ID: $lead_id. Error: " . $e->getMessage());
            }
            break;
            
        case 'click':
            // Actualizar timestamp de clic
            $stmt = $pdo->prepare("UPDATE lead_emails_historial SET clickeado_timestamp = :timestamp WHERE id = :id");
            $timestamp = date('Y-m-d H:i:s', (int)($data['ts_epoch'] / 1000)); // Usar date() para Madrid, igual que MySQL
            $stmt->bindParam(':timestamp', $timestamp, PDO::PARAM_STR);
            $stmt->bindParam(':id', $email_id, PDO::PARAM_INT);
            $stmt->execute();
            custom_log_brevo_webhook("Actualizado clickeado_timestamp para email ID: $email_id, Lead ID: $lead_id");

            // --- Lógica para activar la secuencia de nutrición IA ---
            $pdo->beginTransaction();
            try {
                // 1. Obtener el estado actual de la secuencia para el lead (y bloquear la fila para evitar condiciones de carrera)
                $stmt_track = $pdo->prepare("SELECT id, status FROM lead_sequence_tracking WHERE lead_id = :lead_id FOR UPDATE");
                $stmt_track->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
                $stmt_track->execute();
                $tracking_record = $stmt_track->fetch(PDO::FETCH_ASSOC);

                // 2. Solo proceder si la secuencia está pendiente de activación ('pending_activation')
                if ($tracking_record && $tracking_record['status'] === 'pending_activation') {
                    custom_log_brevo_webhook("Activando secuencia por CLICK para Lead ID: $lead_id");

                    // Usar la función reutilizable
                    activarSecuenciaPorInteraccion($pdo, $lead_id, $tracking_record, 'click');
                } else {
                    custom_log_brevo_webhook("La secuencia para el Lead ID: $lead_id no requiere activación por click (Estado actual: " . ($tracking_record['status'] ?? 'no encontrado') . "). No se realizaron cambios.");
                }
                
                $pdo->commit();

            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                custom_log_brevo_webhook("ERROR al activar secuencia para Lead ID {$lead_id}: " . $e->getMessage());
                // No relanzamos la excepción para no enviar un error 500 a Brevo, ya que no pueden hacer nada.
                // El error ya está registrado para nuestra revisión.
            }
            break;
            
        case 'hard_bounce':
        case 'soft_bounce':
        case 'invalid_email':
        case 'spam':
        case 'blocked':
        case 'error':
        case 'unsubscribed':
            // Actualizar estado_envio para errores y cancelaciones
            $nuevo_estado = 'error_' . $data['event']; // Por ejemplo: error_hard_bounce, error_spam, etc.
            $stmt = $pdo->prepare("UPDATE lead_emails_historial SET estado_envio = :estado, error_detalle = :detalle WHERE id = :id");
            $detalle = isset($data['reason']) ? $data['reason'] : json_encode($data);
            $stmt->bindParam(':estado', $nuevo_estado, PDO::PARAM_STR);
            $stmt->bindParam(':detalle', $detalle, PDO::PARAM_STR);
            $stmt->bindParam(':id', $email_id, PDO::PARAM_INT);
            $stmt->execute();
            custom_log_brevo_webhook("Actualizado estado_envio a '$nuevo_estado' para email ID: $email_id, Lead ID: $lead_id");
            
            // Si es un error grave, marcar la secuencia del lead como pausada
            if (in_array($data['event'], ['hard_bounce', 'invalid_email', 'spam', 'blocked', 'unsubscribed'])) {
                $stmt = $pdo->prepare("UPDATE lead_sequence_tracking SET status = 'paused_error' WHERE lead_id = :lead_id");
                $stmt->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
                $stmt->execute();
                custom_log_brevo_webhook("Secuencia pausada por error para Lead ID: $lead_id debido a evento: " . $data['event']);
            }
            break;
            
        case 'delivered':
            // Actualizar estado_envio a entregado si no está ya marcado como error
                        $stmt = $pdo->prepare("UPDATE lead_emails_historial SET estado_envio = 'sent' WHERE id = :id AND estado_envio != 'failed'");
            $stmt->bindParam(':id', $email_id, PDO::PARAM_INT);
            $stmt->execute();
            custom_log_brevo_webhook("Actualizado estado_envio a 'entregado' para email ID: $email_id, Lead ID: $lead_id");
            break;
            
        case 'request':
            // Email enviado, actualizar fecha_envio_real si no existe
                        $stmt = $pdo->prepare("UPDATE lead_emails_historial SET fecha_envio_real = :timestamp, estado_envio = 'sent' WHERE id = :id AND fecha_envio_real IS NULL");
            $timestamp = date('Y-m-d H:i:s', (int)($data['ts_epoch'] / 1000)); // Usar date() para Madrid, igual que MySQL
            $stmt->bindParam(':timestamp', $timestamp, PDO::PARAM_STR);
            $stmt->bindParam(':id', $email_id, PDO::PARAM_INT);
            $stmt->execute();
            custom_log_brevo_webhook("Actualizado fecha_envio_real para email ID: $email_id, Lead ID: $lead_id");
            break;
            
        default:
            custom_log_brevo_webhook("Evento no procesado: " . $data['event']);
            break;
    }
    
    http_response_code(200);
    echo json_encode(['success' => true, 'message' => 'Evento procesado correctamente']);
    
} catch (PDOException $e) {
    error_log("[brevo_webhook] ERROR PDO: " . $e->getMessage());
    error_log("[brevo_webhook] Stack trace: " . $e->getTraceAsString());
    custom_log_brevo_webhook("Error PDO: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log("[brevo_webhook] ERROR GENERAL: " . $e->getMessage());
    error_log("[brevo_webhook] Stack trace: " . $e->getTraceAsString());
    custom_log_brevo_webhook("Error general: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno: ' . $e->getMessage()]);
}
?>
