<?php
declare(strict_types=1);

header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Api\lib\Auth;
use Api\lib\Logger;

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $auth = new Auth();
    $user_data = $auth->verifyToken();

    if (!$user_data || !$user_data['agency_id']) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Acceso no autorizado.']);
        exit;
    }
    $agency_id = $user_data['agency_id'];

    $lead_id = filter_input(INPUT_GET, 'lead_id', FILTER_VALIDATE_INT);

    if (!$lead_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Parámetro lead_id es requerido.']);
        exit;
    }

    $pdo = Database::getInstance()->getConnection();

    // Verificar que el lead pertenece a la agencia del usuario
    $stmt_check = $pdo->prepare("
        SELECT cv.agency_id FROM valorador_leads vl
        JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
        WHERE vl.id = :lead_id
    ");
    $stmt_check->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
    $stmt_check->execute();
    $lead_agency_id = $stmt_check->fetchColumn();

    if ($lead_agency_id != $agency_id) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'No tienes permiso para ver los emails de este lead.']);
        exit;
    }

    // Obtener los emails programados
    $stmt = $pdo->prepare("
        SELECT
            id AS historial_id,
            uuid,
            sequence_step_id,
            estado_envio,
            fecha_programada_envio,
            borrador_asunto,
            borrador_cuerpo_html
        FROM
            lead_emails_historial
        WHERE
            lead_id = :lead_id
            AND estado_envio = 'scheduled'
        ORDER BY
            fecha_programada_envio ASC
    ");
    $stmt->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
    $stmt->execute();
    $scheduled_emails = $stmt->fetchAll(PDO::FETCH_ASSOC);

    http_response_code(200);
    echo json_encode(['success' => true, 'data' => $scheduled_emails]);

} catch (Exception $e) {
    Logger::error("API Error (get_scheduled_emails): " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}
?>
