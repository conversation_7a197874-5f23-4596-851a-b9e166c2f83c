<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// /api/reactivate-subscription.php - Reactivar una suscripción cancelada
// bootstrap.php se encargará de la configuración de errores y carga de dependencias.
 // Usar config central de CORS
// --- Logger Setup (específico para este endpoint, se mantiene por ahora) ---
if (!function_exists('custom_log_reactivate')) {
    function custom_log_reactivate($message) {
        $logFile = __DIR__ . '/debug_reactivate.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_reactivate("--- [reactivate-subscription.php] INICIADO ---");
// Las cabeceras CORS y el manejo de OPTIONS ahora son gestionados por cors.php
header('Content-Type: application/json'); // Asegurar que se envíe antes de cualquier output JSON
// --- Autoload y Carga de .env son manejados por bootstrap.php ---
// ... (código de carga de autoload y .env eliminado)
// --- Verificación de autenticación ---
$userId = null;
$agencyId = null;
$isAgencyOwner = false;
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
custom_log_reactivate("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0');
        try {
            custom_log_reactivate("Intentando decodificar JWT...");
            $key = JWT_SECRET;
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            $userId = $decoded->user_id ?? null;
            $agencyId = $decoded->agency_id ?? null;
            $isAgencyOwner = isset($decoded->is_agency_owner) ? (bool)$decoded->is_agency_owner : false;
            if (!$userId || !$agencyId) {
                custom_log_reactivate("Error: user_id o agency_id no encontrado en el token JWT.");
                throw new Exception('user_id o agency_id no encontrado en el token JWT');
            }
            custom_log_reactivate("JWT decodificado correctamente. User ID: {$userId}, Agency ID: {$agencyId}, Is Owner: " . ($isAgencyOwner ? 'Sí' : 'No'));
        } catch (Exception $e) {
            custom_log_reactivate("Error al decodificar JWT: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.']);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors);
        }
    } else {
        custom_log_reactivate("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_reactivate("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Verificación de Permiso de Dueño de Agencia ---
if (!$isAgencyOwner) {
    custom_log_reactivate("Acceso denegado: Usuario user_id={$userId} no es dueño de la agencia agency_id={$agencyId}.");
    http_response_code(403);
    if (ob_get_length()) ob_end_clean(); // Asegurar que el buffer se limpie si hay algo
    echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo el propietario de la agencia puede reactivar la suscripción.']);
    exit();
}
// --- Conexión a la base de datos (usando constantes de bootstrap.php) ---
$db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($db->connect_error) {
    custom_log_reactivate("Error de conexión a la base de datos: " . $db->connect_error);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
    exit();
}
$db->set_charset(DB_CHARSET);
custom_log_reactivate("Conexión a la base de datos establecida correctamente.");
// --- Procesar la reactivación de la suscripción ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    custom_log_reactivate("[POST Request] Procesando para user_id: " . $userId);
    // MODIFICADO: Consultar suscripción activa o en trial con fecha de cancelación futura
    $query = "SELECT u.stripe_customer_id, s.stripe_subscription_id, s.estado as estado_db, s.fecha_cancelacion 
              FROM usuarios u 
              LEFT JOIN suscripciones s ON u.id = s.user_id 
              WHERE u.id = ? AND s.estado IN ('active', 'trialing') AND s.fecha_cancelacion IS NOT NULL AND s.fecha_cancelacion > NOW()
              ORDER BY s.fecha_creacion DESC 
              LIMIT 1";
    $stmt = $db->prepare($query);
    if (!$stmt) {
        custom_log_reactivate("Error al preparar la consulta para obtener suscripción: " . $db->error);
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
        exit();
    }
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $stripeCustomerId = $row['stripe_customer_id'];
        $stripeSubscriptionId = $row['stripe_subscription_id'];
        $estadoDb = $row['estado_db'];
        // MODIFICADO: Obtener fecha_cancelacion de la BD para logging
        $fechaCancelacionDb = $row['fecha_cancelacion']; 
        custom_log_reactivate("Datos obtenidos de BD: Customer ID = " . $stripeCustomerId . ", Subscription ID = " . $stripeSubscriptionId . ", Estado DB = " . $estadoDb . ", Fecha Cancelación DB = " . $fechaCancelacionDb);
        if (empty($stripeCustomerId) || empty($stripeSubscriptionId)) {
            custom_log_reactivate("Faltan datos de la suscripción (Stripe Customer ID o Subscription ID) para reactivar.");
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No se encontraron datos de suscripción válidos para reactivar.']);
            $stmt->close(); $db->close(); exit();
        }
        try {
            $stripeSecretKeyConst = STRIPE_SECRET_KEY; // Usar constante de bootstrap.php
            if (empty($stripeSecretKeyConst)) {
                custom_log_reactivate("CRÍTICO: STRIPE_SECRET_KEY no está definida.");
                throw new Exception("Error de configuración de Stripe.");
            }
            \Stripe\Stripe::setApiKey($stripeSecretKeyConst);
            $subscription = \Stripe\Subscription::retrieve($stripeSubscriptionId);
            custom_log_reactivate("Suscripción obtenida de Stripe. Status: {$subscription->status}, cancel_at_period_end: " . ($subscription->cancel_at_period_end ? 'true' : 'false'));
            // Lógica para reactivar:
            // MODIFICADO: Aceptar status 'trialing' O 'active' de Stripe si cancel_at_period_end es true
            if (($subscription->status === 'active' || $subscription->status === 'trialing') && $subscription->cancel_at_period_end) {
                $updatedSubscription = \Stripe\Subscription::update($stripeSubscriptionId, [
                    'cancel_at_period_end' => false
                ]);
                // Determinar el estado correcto para la BD después de la reactivación
                $final_stripe_status = $updatedSubscription->status;
                $final_stripe_trial_end = $updatedSubscription->trial_end;
                $estado_para_db_despues_reactivar = $final_stripe_status; // Default al estado de Stripe
                if ($final_stripe_trial_end && $final_stripe_trial_end > time() && $final_stripe_status !== 'canceled') {
                    $estado_para_db_despues_reactivar = 'trialing';
                    custom_log_reactivate("Reactivación: Trial en curso (fin: " . date('Y-m-d H:i:s', $final_stripe_trial_end) . "). Estado local para BD será 'trialing'. Estado Stripe original: " . $final_stripe_status);
                } elseif ($final_stripe_status === 'active') {
                    $estado_para_db_despues_reactivar = 'active';
                    custom_log_reactivate("Reactivación: No hay trial activo o ya finalizó, o suscripción no estaba en trial. Estado local para BD será 'active'. Estado Stripe: " . $final_stripe_status);
                } else {
                    // Si Stripe devuelve otro estado (ej. incomplete, past_due), usarlo.
                    // Esto es menos común para una reactivación simple de cancel_at_period_end=false
                    custom_log_reactivate("Reactivación: Estado Stripe es '{$final_stripe_status}'. Estado local para BD será '{$estado_para_db_despues_reactivar}'.");
                }
                // Actualizar el estado en la base de datos
                $updateQuery = "UPDATE suscripciones SET estado = ?, fecha_cancelacion = NULL, fecha_cancelada_en_stripe = NULL, fecha_modificacion = NOW() WHERE stripe_subscription_id = ?";
                $updateStmt = $db->prepare($updateQuery);
                if ($updateStmt) {
                    $updateStmt->bind_param("ss", $estado_para_db_despues_reactivar, $stripeSubscriptionId);
                    $updateStmt->execute();
                    $update_affected_rows = $updateStmt->affected_rows;
                    $updateStmt->close();
                    custom_log_reactivate("Suscripción reactivada con éxito en Stripe y actualizada en BD. Estado DB: {$estado_para_db_despues_reactivar}, fecha_cancelacion y fecha_cancelada_en_stripe puestas a NULL. Filas afectadas: {$update_affected_rows}");
                    // Actualizar clientes_valorador.activo
                    // $agencyId ya está disponible desde la decodificación del token y verificación de owner
                    if ($agencyId && $update_affected_rows > 0) {
                        $valorador_activo_flag = (in_array(strtolower($estado_para_db_despues_reactivar), ['active', 'trialing'])) ? 1 : 0;
                        custom_log_reactivate("Actualizando clientes_valorador.activo a {$valorador_activo_flag} para agency_id: {$agencyId} debido a reactivación/estado: {$estado_para_db_despues_reactivar}");
                        $stmt_update_valorador = $db->prepare("UPDATE clientes_valorador SET activo = ? WHERE agency_id = ?");
                        if ($stmt_update_valorador) {
                            $stmt_update_valorador->bind_param("ii", $valorador_activo_flag, $agencyId);
                            $update_v_success = $stmt_update_valorador->execute();
                            if ($update_v_success) {
                                custom_log_reactivate("clientes_valorador.activo actualizado tras reactivación. Filas afectadas: " . $stmt_update_valorador->affected_rows);
                            } else {
                                custom_log_reactivate("ERROR al actualizar clientes_valorador.activo tras reactivación: " . $stmt_update_valorador->error);
                            }
                            $stmt_update_valorador->close();
                        } else {
                            custom_log_reactivate("ERROR preparando statement para actualizar clientes_valorador.activo tras reactivación: " . $db->error);
                        }
                    } elseif (!$agencyId) {
                        custom_log_reactivate("ADVERTENCIA: agencyId no disponible, no se pudo actualizar clientes_valorador.activo tras reactivación.");
                    }
                } else {
                     custom_log_reactivate("Error al preparar statement para actualizar BD después de reactivar: " . $db->error);
                }
                echo json_encode([
                    'success' => true, 
                    'message' => 'Tu suscripción ha sido reactivada correctamente.',
                    'subscription_status' => $updatedSubscription->status,
                    'cancel_at_period_end' => $updatedSubscription->cancel_at_period_end
                ]);
            } else if ($subscription->status === 'canceled'){
                custom_log_reactivate("La suscripción con ID {$stripeSubscriptionId} ya está cancelada (status: canceled). No se puede reactivar con esta acción. Se requeriría crear una nueva suscripción.");
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'La suscripción ya está cancelada y no puede ser reactivada de esta forma. Por favor, crea una nueva suscripción.']);
            } else {
                // MODIFICADO: Mejorar log y mensaje de error JSON con detalles de Stripe
                $stripe_status_val = $subscription->status;
                $stripe_cancel_at_period_end_val = $subscription->cancel_at_period_end ? 'true' : 'false';
                custom_log_reactivate(
                    "REACTIVATION_CONDITION_FAILED: Stripe Status='{$stripe_status_val}', Stripe cancel_at_period_end='{$stripe_cancel_at_period_end_val}'. " .
                    "DB Status='{$estadoDb}', DB fecha_cancelacion='{$fechaCancelacionDb}'."
                );
                http_response_code(400);
                echo json_encode([
                    'success' => false, 
                    // 'message' => 'La suscripción no está actualmente programada para cancelarse de una manera reactivable o ya está completamente activa sin cancelación pendiente.',
                    'message' => "No se puede reactivar. Stripe(status: {$stripe_status_val}, cancel_at_period_end: {$stripe_cancel_at_period_end_val}) BD(estado: {$estadoDb}, fecha_cancelacion: {$fechaCancelacionDb})",
                    'debug_stripe_status' => $stripe_status_val,
                    'debug_stripe_cancel_at_period_end' => $subscription->cancel_at_period_end // bool
                ]);
            }
        } catch (Exception $e) {
            custom_log_reactivate("Error al reactivar suscripción en Stripe: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Error al reactivar la suscripción: ' . $e->getMessage()]);
        } finally {
            if (isset($stmt)) $stmt->close();
            $db->close();
            exit(); // Asegurarse de que el script termina después de enviar la respuesta JSON
        }
    } else {
        custom_log_reactivate("No se encontró una suscripción activa o en trial con cancelación programada (estado IN ('active','trialing'), fecha_cancelacion futura) para el usuario ID: " . $userId);
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'No se encontró una suscripción activa o en periodo de prueba con cancelación programada que pueda ser reactivada.']);
        if (isset($stmt)) $stmt->close();
        $db->close();
        exit();
    }
} else {
    custom_log_reactivate("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}
// ... (código anterior)
custom_log_reactivate("Suscripción recuperada de Stripe después de la reactivación. ID: " . $reactivated_subscription_stripe->id . ", Estado Stripe: " . $reactivated_subscription_stripe->status . ", Trial End: " . ($reactivated_subscription_stripe->trial_end ? date('Y-m-d H:i:s', $reactivated_subscription_stripe->trial_end) : 'N/A') );
$new_status_from_stripe = $reactivated_subscription_stripe->status;
$trial_end_from_stripe = $reactivated_subscription_stripe->trial_end;
$current_db_status_after_reactivate_check = $new_status_from_stripe; // Estado por defecto
if ($trial_end_from_stripe && $trial_end_from_stripe > time() && $new_status_from_stripe !== 'canceled') {
    // Si hay un periodo de prueba activo y no ha terminado, forzar estado local a 'trialing'
    $current_db_status_after_reactivate_check = 'trialing';
    custom_log_reactivate("Trial en curso (fin: " . date('Y-m-d H:i:s', $trial_end_from_stripe) . "). Estado local forzado a 'trialing'.");
} else {
    custom_log_reactivate("No hay trial en curso o ya finalizó (fin: " . ($trial_end_from_stripe ? date('Y-m-d H:i:s', $trial_end_from_stripe) : 'N/A') . "). Estado local será el de Stripe: " . $new_status_from_stripe);
}
// Si Stripe la devuelve como 'canceled' (lo cual no debería pasar en una reactivación exitosa),
// entonces sería 'canceled'. Pero una reactivación exitosa usualmente la pone active o trialing.
if ($new_status_from_stripe === 'canceled') {
    $current_db_status_after_reactivate_check = 'canceled';
    custom_log_reactivate("Estado de Stripe es 'canceled', estado local forzado a 'canceled'.");
}
// Actualizar la base de datos local
$fecha_fin_periodo_actual_reactivado_formateada = date('Y-m-d H:i:s', $reactivated_subscription_stripe->current_period_end);
$fecha_reactivacion_formateada = date('Y-m-d H:i:s'); // Fecha actual como fecha de reactivación
custom_log_reactivate("Preparando para actualizar BD local. Nuevo estado local: {$current_db_status_after_reactivate_check}, Fecha fin periodo: {$fecha_fin_periodo_actual_reactivado_formateada}, Fecha reactivación: {$fecha_reactivacion_formateada}");
$stmt_update = $db->prepare("UPDATE suscripciones SET estado = ?, fecha_fin_periodo_actual = ?, fecha_modificacion = ?, cancel_at_period_end_on_stripe = 0, fecha_cancelacion = NULL WHERE stripe_subscription_id = ? AND user_id = ?");
if (!$stmt_update) {
    custom_log_reactivate("Error preparando UPDATE para BD local: " . $db->error);
    throw new Exception("Error al preparar la actualización de la BD local: " . $db->error);
}
$stmt_update->bind_param("ssssi", $current_db_status_after_reactivate_check, $fecha_fin_periodo_actual_reactivado_formateada, $fecha_reactivacion_formateada, $stripe_subscription_id, $user_id);
// ... (resto del código)
?>
