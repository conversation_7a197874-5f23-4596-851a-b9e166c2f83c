<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

use Ramsey\Uuid\Uuid;

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit;
}

// Validation based on database schema
$required_fields = ['sequence_id', 'name', 'email_subject', 'prompt_template', 'delay_days', 'step_order'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || $input[$field] === '') {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => "Campo requerido: {$field}"]);
        exit;
    }
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit();
}

// Validar plantilla de email si se proporciona
if (!empty($input['email_template_id'])) {
    $stmt = $pdo->prepare('SELECT id FROM email_plantillas_html WHERE id = ? AND activa = 1');
    $stmt->execute([$input['email_template_id']]);
    if (!$stmt->fetch()) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Plantilla de email no válida']);
        exit;
    }
}

$uuid = Uuid::uuid4()->toString();
$sequence_id = (int)$input['sequence_id'];
$name = trim($input['name']);
$email_subject = trim($input['email_subject']);
$prompt_template = trim($input['prompt_template']);
$delay_days = (int)$input['delay_days'];
$step_order = (int)$input['step_order'];
$email_template_id = !empty($input['email_template_id']) ? (int)$input['email_template_id'] : null;
$is_active = isset($input['is_active']) ? (bool)$input['is_active'] : true;
$ai_model = $input['ai_model'] ?? 'openai';
$use_web_search = isset($input['use_web_search']) ? (bool)$input['use_web_search'] : false;

try {
    $sql = "INSERT INTO sequence_steps (uuid, sequence_id, name, email_subject, prompt_template, delay_days, step_order, email_template_id, is_active, ai_model, use_web_search, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
    $stmt = $pdo->prepare($sql);

    $stmt->execute([$uuid, $sequence_id, $name, $email_subject, $prompt_template, $delay_days, $step_order, $email_template_id, $is_active, $ai_model, $use_web_search]);

    $new_step_id = $pdo->lastInsertId();
    $new_step_data = [
        'id' => $new_step_id,
        'uuid' => $uuid,
        'sequence_id' => $sequence_id,
        'name' => $name,
        'email_subject' => $email_subject,
        'prompt_template' => $prompt_template,
        'delay_days' => $delay_days,
        'step_order' => $step_order,
        'email_template_id' => $email_template_id,
        'is_active' => $is_active,
        'ai_model' => $ai_model,
        'use_web_search' => $use_web_search
    ];
    http_response_code(201);
    echo json_encode(['success' => true, 'message' => 'Sequence step created successfully.', 'data' => $new_step_data]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database operation failed: ' . $e->getMessage()]);
    exit;
}
