<?php
declare(strict_types=1);

require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

// --- Función de log local ---
if (!function_exists('custom_log_cancel_email')) {
    function custom_log_cancel_email($message) {
        $logFile = __DIR__ . '/logs/debug_cancel_email.log';
        if (!is_dir(__DIR__ . '/logs')) {
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                error_log("Advertencia [cancel_email]: No se pudo crear el directorio de logs.");
                return;
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [cancel_email] {$message}\n", 3, $logFile);
    }
}

// --- Función para obtener y decodificar el token JWT ---
function get_decoded_jwt_payload_cancel_email() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded;
            } catch (\Throwable $e) {
                custom_log_cancel_email("JWT Decode Error: " . $e->getMessage());
                if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                    ini_set('display_errors', $previous_display_errors);
                }
            }
        }
    }
    return null;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'Método no permitido. Se esperaba POST.']);
    exit;
}

$decoded_payload = get_decoded_jwt_payload_cancel_email();
$user_id = $decoded_payload->user_id ?? null;
$agency_id_from_jwt = $decoded_payload->agency_id ?? null;
$roles = $decoded_payload->roles ?? [];
$is_super_admin = in_array('admin', (array)$roles);

if (!$user_id) {
    http_response_code(401);
    custom_log_cancel_email("Acceso no autorizado: Usuario no identificado.");
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado.']);
    exit;
}

$input_data = json_decode(file_get_contents('php://input'), true);
$historial_id = $input_data['historial_id'] ?? null;

if (!$historial_id || !is_numeric($historial_id)) {
    http_response_code(400); // Bad Request
    custom_log_cancel_email("Entrada inválida: historial_id es requerido y debe ser numérico. Recibido: " . print_r($historial_id, true));
    echo json_encode(['success' => false, 'message' => 'ID de historial de email inválido.']);
    exit;
}

try {
    $pdo = Database::getInstance()->getConnection();

    // Obtener el email y verificar permisos y estado
    $sql_check = "SELECT leh.id, leh.estado_envio, vl.cliente_valorador_id, cv.agency_id
                  FROM lead_emails_historial leh
                  JOIN valorador_leads vl ON leh.lead_id = vl.id
                  LEFT JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                  WHERE leh.id = :historial_id";
    
    $stmt_check = $pdo->prepare($sql_check);
    $stmt_check->bindParam(':historial_id', $historial_id, PDO::PARAM_INT);
    $stmt_check->execute();
    $email_data = $stmt_check->fetch(PDO::FETCH_ASSOC);

    if (!$email_data) {
        http_response_code(404); // Not Found
        custom_log_cancel_email("Email no encontrado. ID: {$historial_id}");
        echo json_encode(['success' => false, 'message' => 'Email no encontrado.']);
        exit;
    }

    // Verificar permisos de agencia si no es superadmin
    if (!$is_super_admin) {
        if (!$agency_id_from_jwt || $email_data['agency_id'] != $agency_id_from_jwt) {
            http_response_code(403); // Forbidden
            custom_log_cancel_email("Acceso denegado para cancelar email. User ID: {$user_id}, Agency JWT: {$agency_id_from_jwt}, Email Agency: {$email_data['agency_id']}");
            echo json_encode(['success' => false, 'message' => 'No tienes permiso para cancelar este email.']);
            exit;
        }
    }

    if ($email_data['estado_envio'] !== 'scheduled') {
        http_response_code(400); // Bad Request
        custom_log_cancel_email("Intento de cancelar email no programado. ID: {$historial_id}, Estado: {$email_data['estado_envio']}");
        echo json_encode(['success' => false, 'message' => 'Este email no está programado o ya ha sido procesado. Estado actual: ' . $email_data['estado_envio']]);
        exit;
    }

    // Actualizar el estado a 'cancelado'
        $sql_update = "UPDATE lead_emails_historial SET estado_envio = 'cancelled' WHERE id = :historial_id";
    $stmt_update = $pdo->prepare($sql_update);
    $stmt_update->bindParam(':historial_id', $historial_id, PDO::PARAM_INT);
    
    if ($stmt_update->execute()) {
        if ($stmt_update->rowCount() > 0) {
            custom_log_cancel_email("Email cancelado exitosamente. ID: {$historial_id}, User ID: {$user_id}");
            echo json_encode(['success' => true, 'message' => 'Envío programado cancelado exitosamente.', 'new_status' => 'cancelado']);
        } else {
            custom_log_cancel_email("Email ID {$historial_id} no requirió actualización (quizás ya estaba cancelado o no se encontró en el update).");
            echo json_encode(['success' => false, 'message' => 'El email no requirió actualización.']); // Podría ser true si se considera estado idempotente
        }
    } else {
        throw new PDOException("Error al ejecutar la actualización del estado del email.");
    }

} catch (PDOException $e) {
    custom_log_cancel_email("Error PDO: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB) al cancelar el email.']);
} catch (Exception $e) {
    custom_log_cancel_email("Error general: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al cancelar el email.']);
}

?> 