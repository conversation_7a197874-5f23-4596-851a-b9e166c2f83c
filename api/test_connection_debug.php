<?php
declare(strict_types=1);

require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;

echo "=== DIAGNÓSTICO DE CONEXIÓN A BASE DE DATOS ===\n\n";

// 1. Verificar constantes
echo "1. CONSTANTES DEFINIDAS:\n";
echo "   DB_HOST: " . (defined('DB_HOST') ? DB_HOST : 'NO DEFINIDO') . "\n";
echo "   DB_PORT: " . (defined('DB_PORT') ? DB_PORT : 'NO DEFINIDO') . "\n";
echo "   DB_SOCKET: " . (defined('DB_SOCKET') ? DB_SOCKET : 'NO DEFINIDO') . "\n";
echo "   DB_DATABASE: " . (defined('DB_DATABASE') ? DB_DATABASE : 'NO DEFINIDO') . "\n";
echo "   DB_USERNAME: " . (defined('DB_USERNAME') ? DB_USERNAME : 'NO DEFINIDO') . "\n";
echo "   DB_PASSWORD: " . (defined('DB_PASSWORD') ? (strlen(DB_PASSWORD) > 0 ? '[DEFINIDO]' : '[VACÍO]') : 'NO DEFINIDO') . "\n";
echo "   DB_CHARSET: " . (defined('DB_CHARSET') ? DB_CHARSET : 'NO DEFINIDO') . "\n\n";

// 2. Verificar entorno
echo "2. ENTORNO:\n";
echo "   K_SERVICE: " . (getenv('K_SERVICE') ? getenv('K_SERVICE') : 'NO DEFINIDO') . "\n";
echo "   Es producción: " . (getenv('K_SERVICE') !== false ? 'SÍ' : 'NO') . "\n\n";

// 3. Probar conexión con Database class
echo "3. PRUEBA DE CONEXIÓN CON Database::getInstance():\n";
try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    echo "   ✅ Conexión exitosa\n";
    
    // Probar una consulta simple
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    echo "   ✅ Consulta de prueba exitosa: " . $result['test'] . "\n";
    
    // Verificar tablas críticas
    $tables = ['usuarios', 'agencies', 'suscripciones', 'valorador_valoraciones'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table LIMIT 1");
            $result = $stmt->fetch();
            echo "   ✅ Tabla '$table' accesible: {$result['count']} registros\n";
        } catch (Exception $e) {
            echo "   ❌ Error accediendo tabla '$table': " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Error de conexión: " . $e->getMessage() . "\n";
    echo "   Código de error: " . $e->getCode() . "\n";
}

echo "\n=== FIN DEL DIAGNÓSTICO ===\n";
?>
