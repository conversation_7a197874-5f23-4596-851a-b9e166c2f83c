<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// api/admin_get_valoraciones.php
// error_reporting(E_ALL); // Manejado por bootstrap.php
// ini_set('display_errors', 1); // Manejado por bootstrap.php
 // <--- INTEGRAR BOOTSTRAP
require_once __DIR__ . '/utils/auth_check.php'; // Para verifyTokenAndAdminRole
// Use Firebase\JWT\JWT; // auth_check.php o bootstrap.php manejan esto
// Use Firebase\JWT\Key;
header('Content-Type: application/json');
// --- Carga de .env y autoload manejada por bootstrap.php ---
// try { $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); $dotenv->load(); } catch ... (eliminado)
// --- Constantes de BD y JWT_SECRET vienen de bootstrap.php ---
// $dbHost = $_ENV['DB_HOST']; (eliminado)
// ... (eliminación de otras asignaciones de variables de entorno)
// auth_check.php ahora usará JWT_SECRET de bootstrap.php
$authResult = verifyTokenAndAdminRole(); // <--- SIN ARGUMENTOS
if (!$authResult['success']) {
    http_response_code($authResult['status_code']);
    echo json_encode($authResult); // authResult ya tiene 'success' => false y 'message'
    exit();
}

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error de conexión a BD en admin_get_valoraciones: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos.']);
    exit();
}

try {
    // Parámetros de paginación y filtro
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;
    $searchQuery = $_GET['search'] ?? '';
    $filterTipo = $_GET['tipo'] ?? ''; // tipo_principal de la valoración
    $filterValoradorId = $_GET['valorador_id'] ?? ''; // cliente_valorador_id de clientes_valorador

    $baseQuery = "FROM valorador_valoraciones v
                  LEFT JOIN clientes_valorador cv ON v.cliente_valorador_id = cv.id
                  LEFT JOIN usuarios u ON cv.user_id = u.id
                  LEFT JOIN valorador_leads vl ON v.lead_id = vl.id";
    $whereClauses = [];
    $params = [];

    if (!empty($searchQuery)) {
        $searchTerm = '%' . $searchQuery . '%';
        $whereClauses[] = "(v.direccion LIKE ? OR v.referencia_catastral LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    if (!empty($filterTipo)) {
        $whereClauses[] = "v.tipo_principal = ?";
        $params[] = $filterTipo;
    }
    if (!empty($filterValoradorId)) {
        $whereClauses[] = "cv.id = ?";
        $params[] = $filterValoradorId;
    }

    $whereSql = "";
    if (!empty($whereClauses)) {
        $whereSql = " WHERE " . implode(" AND ", $whereClauses);
    }

    // Contar total de items para paginación
    $countSql = "SELECT COUNT(v.id) " . $baseQuery . $whereSql;
    $stmtTotal = $pdo->prepare($countSql);
    $stmtTotal->execute($params);
    $totalItems = (int)$stmtTotal->fetchColumn();
    $totalPages = (int)ceil($totalItems / $limit);

    // Consulta principal
    $sql = "SELECT v.id,
                   cv.user_id AS user_id,
                   v.cliente_valorador_id,
                   v.lead_id, 
                   v.direccion, 
                   v.referencia_catastral, 
                   v.tipo_principal, 
                   v.subtipo, 
                   v.superficie AS superficie_construida,
                   v.habitaciones AS num_habitaciones,
                   v.banos AS num_banos,
                   v.valor_estimado_min, 
                   v.valor_estimado_max, 
                   v.estado AS estado_conservacion,
                   v.datos_adicionales AS json_prompt_details,
                   v.fecha_creacion AS fecha_creacion_valoracion,
                   u.nombre_completo AS nombre_usuario_solicitante, 
                   u.email AS email_usuario_solicitante,
                   cv.nombre_display AS nombre_valorador, 
                   vl.nombre AS nombre_lead, 
                   vl.email AS email_lead, 
                   vl.estado AS estado_lead
            " . $baseQuery . $whereSql . " ORDER BY v.fecha_creacion DESC LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $valoraciones = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'data' => $valoraciones,
        'pagination' => [
            'totalItems' => $totalItems,
            'totalPages' => $totalPages,
            'currentPage' => $page,
            'limit' => $limit
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al obtener valoraciones: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error al obtener las valoraciones.']);
    exit();
}