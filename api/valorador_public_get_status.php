<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// Endpoint público para verificar el estado de un valorador específico.
header('Content-Type: application/json; charset=utf-8');
$response_payload = ['success' => false, 'data' => ['isActive' => false, 'message' => 'Error desconocido.']];
$db = null;
// --- Logger específico para este script ---
if (!function_exists('custom_log_valorador_status')) {
    function custom_log_valorador_status($message) {
        $logFile = __DIR__ . '/debug_valorador_status.log';
        $timestamp = date('Y-m-d H:i:s');
        // Usar @ para suprimir errores si el log no es escribible
        @file_put_contents($logFile, "[$timestamp] [valorador-status-LOG] " . print_r($message, true) . PHP_EOL, FILE_APPEND);
        error_log("[valorador-status-SCRIPT-LOG] " . print_r($message, true));
    }
}
custom_log_valorador_status("--- [valorador_public_get_status.php] INICIADO ---");
try {
    $client_identifier = $_GET['client_identifier'] ?? null;
    if (empty($client_identifier)) {
        custom_log_valorador_status("Error: client_identifier no proporcionado.");
        http_response_code(400);
        $response_payload['data']['message'] = 'El identificador del cliente es requerido.';
        echo json_encode($response_payload);
        exit;
    }
    custom_log_valorador_status("Buscando estado para client_identifier: " . $client_identifier);
    $db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
    if ($db->connect_error) {
        throw new Exception("Error de conexión a BD: " . $db->connect_error);
    }
    $db->set_charset(DB_CHARSET);
    custom_log_valorador_status("Conexión mysqli a BD establecida.");
    // 1. Obtener agency_id de clientes_valorador
    $stmt_valorador_config = $db->prepare("SELECT agency_id, activo FROM clientes_valorador WHERE client_identifier = ? LIMIT 1");
    if (!$stmt_valorador_config) throw new Exception("Error preparando consulta clientes_valorador: " . $db->error);
    $stmt_valorador_config->bind_param("s", $client_identifier);
    $stmt_valorador_config->execute();
    $valorador_config_result = $stmt_valorador_config->get_result();
    $valorador_config = $valorador_config_result->fetch_assoc();
    $stmt_valorador_config->close();
    if (!$valorador_config) {
        custom_log_valorador_status("No se encontró configuración para client_identifier: {$client_identifier}. Se considera inactivo.");
        $response_payload['success'] = true; // La consulta fue exitosa, pero el resultado es que no está activo
        $response_payload['data']['isActive'] = false;
        $response_payload['data']['message'] = 'Valorador no encontrado.';
        echo json_encode($response_payload);
        exit;
    }
    $agency_id = $valorador_config['agency_id'];
    // $valorador_db_activo = (bool)$valorador_config['activo']; // Podríamos usarlo como un primer filtro rápido.
    custom_log_valorador_status("Encontrado agency_id: {$agency_id} para client_identifier: {$client_identifier}.");
    // 2. Obtener owner_user_id de la agencia
    $stmt_agency = $db->prepare("SELECT owner_user_id FROM agencies WHERE id = ? LIMIT 1");
    if (!$stmt_agency) throw new Exception("Error preparando consulta agencies: " . $db->error);
    $stmt_agency->bind_param("i", $agency_id);
    $stmt_agency->execute();
    $agency_result = $stmt_agency->get_result();
    $agency_details = $agency_result->fetch_assoc();
    $stmt_agency->close();
    if (!$agency_details || !isset($agency_details['owner_user_id'])) {
        custom_log_valorador_status("No se encontró agencia o owner_user_id para agency_id: {$agency_id}. Se considera inactivo.");
        $response_payload['success'] = true;
        $response_payload['data']['isActive'] = false;
        $response_payload['data']['message'] = 'Agencia propietaria no encontrada.';
        echo json_encode($response_payload);
        exit;
    }
    $owner_user_id = $agency_details['owner_user_id'];
    custom_log_valorador_status("Encontrado owner_user_id: {$owner_user_id} para agency_id: {$agency_id}.");
    // 3. Obtener estado de la suscripción del owner_user_id
    $stmt_subscription = $db->prepare(
        "SELECT estado FROM suscripciones 
         WHERE user_id = ? AND agency_id = ?
         ORDER BY fecha_creacion DESC LIMIT 1"
    );
    if (!$stmt_subscription) throw new Exception("Error preparando consulta suscripciones: " . $db->error);
    $stmt_subscription->bind_param("ii", $owner_user_id, $agency_id);
    $stmt_subscription->execute();
    $subscription_result = $stmt_subscription->get_result();
    $subscription_details = $subscription_result->fetch_assoc();
    $stmt_subscription->close();
    if (!$subscription_details) {
        custom_log_valorador_status("No se encontró suscripción para owner_user_id: {$owner_user_id} y agency_id: {$agency_id}. Se considera inactivo.");
        $response_payload['success'] = true;
        $response_payload['data']['isActive'] = false;
        $response_payload['data']['message'] = 'Suscripción no encontrada.';
        echo json_encode($response_payload);
        exit;
    }
    $subscription_status = $subscription_details['estado'];
    custom_log_valorador_status("Estado de la suscripción: {$subscription_status} para owner_user_id: {$owner_user_id}.");
    $is_active_subscription = in_array(strtolower($subscription_status), ['active', 'trialing']);
    if ($is_active_subscription) {
        $response_payload['success'] = true;
        $response_payload['data']['isActive'] = true;
        $response_payload['data']['message'] = 'Valorador activo.';
    } else {
        $response_payload['success'] = true;
        $response_payload['data']['isActive'] = false;
        $response_payload['data']['message'] = 'La suscripción de la agencia no está activa.';
         // Podríamos personalizar más el mensaje según $subscription_status si fuera necesario
    }
    echo json_encode($response_payload);
} catch (Exception $e) {
    custom_log_valorador_status("EXCEPCIÓN: " . $e->getMessage());
    if (!headers_sent()) {
        http_response_code(500);
    }
    $response_payload['data']['message'] = 'Error interno del servidor: ' . $e->getMessage();
    echo json_encode($response_payload);
} finally {
    if ($db) {
        $db->close();
        custom_log_valorador_status("Conexión a BD cerrada.");
    }
    custom_log_valorador_status("--- [valorador_public_get_status.php] FINALIZADO ---");
}
exit; 