<?php
declare(strict_types=1);

require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;

echo "=== PRUEBA DE update_valoracion_datos_adicionales.php ===\n\n";

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    echo "✅ Conexión a BD establecida\n";
    
    // Verificar si existe alguna valoración para probar
    $stmt = $pdo->query("SELECT id, direccion FROM valorador_valoraciones LIMIT 1");
    $valoracion = $stmt->fetch();
    
    if ($valoracion) {
        echo "✅ Valoración encontrada para prueba: ID {$valoracion['id']} - {$valoracion['direccion']}\n";
        
        // Simular una actualización de datos adicionales
        $valoracion_id = $valoracion['id'];
        $datos_adicionales = ['test' => 'prueba de conexión', 'timestamp' => date('Y-m-d H:i:s')];
        
        $sql_update = "UPDATE valorador_valoraciones SET datos_adicionales = :datos_adicionales, fecha_modificacion = NOW() WHERE id = :valoracion_id";
        $stmt_update = $pdo->prepare($sql_update);
        $stmt_update->bindParam(':datos_adicionales', json_encode($datos_adicionales), PDO::PARAM_STR);
        $stmt_update->bindParam(':valoracion_id', $valoracion_id, PDO::PARAM_INT);
        
        if ($stmt_update->execute()) {
            echo "✅ Actualización exitosa. Filas afectadas: " . $stmt_update->rowCount() . "\n";
            
            // Verificar la actualización
            $stmt_verify = $pdo->prepare("SELECT datos_adicionales FROM valorador_valoraciones WHERE id = ?");
            $stmt_verify->execute([$valoracion_id]);
            $result = $stmt_verify->fetch();
            echo "✅ Datos verificados: " . $result['datos_adicionales'] . "\n";
        } else {
            echo "❌ Error en la actualización\n";
        }
    } else {
        echo "❌ No se encontraron valoraciones para probar\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== FIN DE LA PRUEBA ===\n";
?>
