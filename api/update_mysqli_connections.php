<?php
/**
 * Script para actualizar las conexiones mysqli en el directorio api/
 * Actualiza las conexiones para manejar correctamente las conexiones con y sin socket
 */

$directory = __DIR__;
$pattern = '*.php';
$files = glob($directory . '/' . $pattern);
$files = array_merge($files, glob($directory . '/**/' . $pattern)); // Incluir subdirectorios

// Patrones para buscar conexiones mysqli
$patterns = [
    // Conexión simple sin manejo de socket
    '/new\s+mysqli\s*\(\s*DB_HOST\s*,\s*DB_USERNAME\s*,\s*DB_PASSWORD\s*,\s*DB_DATABASE\s*,\s*DB_PORT\s*\)/' => 
        '// Conexión a través del proxy de Cloud SQL\n$conn = null;\n' .
        'try {\n' .
        '    if (defined(\'DB_SOCKET\') && !empty(DB_SOCKET)) {\n' .
        '        // Conexión usando socket de Unix (producción en Cloud Run)\n' .
        '        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n' .
        '    } else {\n' .
        '        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n' .
        '        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n' .
        '    }\n' .
        '} catch (Exception $e) {\n' .
        '    error_log(\'Error de conexión a la base de datos: \' . $e->getMessage());\n' .
        '    http_response_code(500);\n' .
        '    die(json_encode([\'success\' => false, \'message\' => \'Error al conectar con la base de datos\']));\n' .
        '}\n' .
        '\n' .
        'if ($conn->connect_error) {\n' .
        '    error_log(\'Error de conexión: \' . $conn->connect_error);\n' .
        '    http_response_code(500);\n' .
        '    die(json_encode([\'success\' => false, \'message\' => \'Error de conexión a la base de datos\']));\n' .
        '}\n' .
        '$conn->set_charset(DB_CHARSET);',
    
    // Conexión con socket
    '/new\s+mysqli\s*\(\s*DB_HOST\s*,\s*DB_USERNAME\s*,\s*DB_PASSWORD\s*,\s*DB_DATABASE\s*,\s*DB_PORT\s*,\s*DB_SOCKET\s*\)/' => 
        '// Conexión a través del proxy de Cloud SQL\n$conn = null;\n' .
        'try {\n' .
        '    if (defined(\'DB_SOCKET\') && !empty(DB_SOCKET)) {\n' .
        '        // Conexión usando socket de Unix (producción en Cloud Run)\n' .
        '        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n' .
        '    } else {\n' .
        '        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n' .
        '        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n' .
        '    }\n' .
        '} catch (Exception $e) {\n' .
        '    error_log(\'Error de conexión a la base de datos: \' . $e->getMessage());\n' .
        '    http_response_code(500);\n' .
        '    die(json_encode([\'success\' => false, \'message\' => \'Error al conectar con la base de datos\']));\n' .
        '}\n' .
        '\n' .
        'if ($conn->connect_error) {\n' .
        '    error_log(\'Error de conexión: \' . $conn->connect_error);\n' .
        '    http_response_code(500);\n' .
        '    die(json_encode([\'success\' => false, \'message\' => \'Error de conexión a la base de datos\']));\n' .
        '}\n' .
        '$conn->set_charset(DB_CHARSET);'
];

$updatedFiles = [];

foreach ($files as $file) {
    // Saltar este script y archivos de prueba
    if (in_array(basename($file), ['update_mysqli_connections.php', 'test_prod_connection.php', 'test_db_connection.php'])) {
        continue;
    }
    
    $content = file_get_contents($file);
    $newContent = $content;
    $fileUpdated = false;
    
    foreach ($patterns as $pattern => $replacement) {
        if (preg_match($pattern, $newContent)) {
            $newContent = preg_replace($pattern, $replacement, $newContent);
            $fileUpdated = true;
        }
    }
    
    if ($fileUpdated) {
        // Asegurarse de que el archivo tenga la declaración de tipos estricta
        if (strpos($newContent, '<?php') === 0) {
            $newContent = str_replace('<?php', "<?php\n\ndeclare(strict_types=1);\n\n", $newContent);
        }
        
        file_put_contents($file, $newContent);
        $updatedFiles[] = str_replace($directory . '/', '', $file);
    }
}

if (!empty($updatedFiles)) {
    echo "Archivos actualizados para usar el patrón de conexión mejorado:\n";
    foreach ($updatedFiles as $file) {
        echo "- $file\n";
    }
} else {
    echo "No se encontraron conexiones mysqli para actualizar.\n";
}

echo "\n¡Proceso completado!\n";
echo "\nIMPORTANTE: Por favor, revisa manualmente los archivos actualizados para asegurarte de que las conexiones se hayan actualizado correctamente.\n";
