<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit;
}

// Validation based on database schema
$required_fields = ['uuid', 'name', 'email_subject', 'prompt_template', 'delay_days', 'step_order'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || $input[$field] === '') {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => "Campo requerido: {$field}"]);
        exit;
    }
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}
$conn->set_charset(DB_CHARSET);

// Validar plantilla de email si se proporciona
if (!empty($input['email_template_id'])) {
    $stmt = $conn->prepare('SELECT id FROM email_plantillas_html WHERE id = ? AND activa = 1');
    $stmt->bind_param('i', $input['email_template_id']);
    $stmt->execute();
    if (!$stmt->get_result()->fetch_assoc()) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Plantilla de email no válida']);
        exit;
    }
}

$uuid = $input['uuid'];
$name = trim($input['name']);
$email_subject = trim($input['email_subject']);
$prompt_template = trim($input['prompt_template']);
$delay_days = (int)$input['delay_days'];
$step_order = (int)$input['step_order'];
$email_template_id = !empty($input['email_template_id']) ? (int)$input['email_template_id'] : null;
$is_active = isset($input['is_active']) ? (bool)$input['is_active'] : true;
$ai_model = $input['ai_model'] ?? 'openai';
$use_web_search = isset($input['use_web_search']) ? (bool)$input['use_web_search'] : false;

$sql = "UPDATE sequence_steps SET name = ?, email_subject = ?, prompt_template = ?, delay_days = ?, step_order = ?, email_template_id = ?, is_active = ?, ai_model = ?, use_web_search = ?, updated_at = NOW() WHERE uuid = ?";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Prepare failed: ' . $conn->error]);
    $conn->close();
    exit;
}

$stmt->bind_param('sssiisisis', $name, $email_subject, $prompt_template, $delay_days, $step_order, $email_template_id, $is_active, $ai_model, $use_web_search, $uuid);

if ($stmt->execute()) {
    if ($stmt->affected_rows > 0) {
        $updated_step_data = [
            'uuid' => $uuid,
            'name' => $name,
            'email_subject' => $email_subject,
            'prompt_template' => $prompt_template,
            'delay_days' => $delay_days,
            'step_order' => $step_order,
            'is_active' => $is_active
        ];
        echo json_encode(['success' => true, 'message' => 'Sequence step updated successfully.', 'data' => $updated_step_data]);
    } else {
        echo json_encode(['success' => true, 'message' => 'No changes were made to the sequence step.']);
    }
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Execute failed: ' . $stmt->error]);
}

$stmt->close();
$conn->close();
