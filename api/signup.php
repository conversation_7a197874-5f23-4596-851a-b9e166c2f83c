<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// bootstrap.php se encargará de la configuración de errores.
use Api\lib\Logger;
use Api\lib\BrevoService;
// Usar el logger configurado en bootstrap.php
Logger::info('[signup.php] Script iniciado.');
// Las cabeceras CORS y el manejo de OPTIONS ahora son gestionados por cors.php
header('Content-Type: application/json');
// Incluir el autoloader de Composer es manejado por bootstrap.php
// require_once __DIR__ . '/vendor/autoload.php';
// Importar clases de JWT (necesario para generar token al final)
use Firebase\JWT\JWT;
use Firebase\JWT\Key; // Importar Key para la firma del token
// Cargar variables de entorno es manejado por bootstrap.php
// $dotenv = Dotenv\Dotenv::createImmutable(__DIR__); 
// try {
//     $dotenv->load();
// } catch (\Dotenv\Exception\InvalidPathException | \Dotenv\Exception\InvalidFileException $e) {
//     error_log("Error al cargar .env: " . $e->getMessage());
//     http_response_code(500);
//     echo json_encode(['success' => false, 'message' => 'Error interno del servidor: No se pudo cargar la configuración.']);
//     exit();
// }
// Obtener variables de entorno como constantes desde bootstrap.php
$stripeSecretKey = defined('STRIPE_SECRET_KEY') ? STRIPE_SECRET_KEY : ($_ENV['STRIPE_SECRET_KEY'] ?? null); // Asegurar compatibilidad
// $dbHost, $dbUser, $dbPass, $dbName son ahora DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE
$jwtSecretKey = JWT_SECRET; // Usar constante JWT_SECRET de bootstrap.php
// Validar que las constantes críticas estén definidas (bootstrap.php debería fallar si no)
if (!$stripeSecretKey || !defined('DB_HOST') || !defined('DB_USERNAME') || !defined('DB_DATABASE') || !$jwtSecretKey) {
    $missing_vars = [];
    if (!$stripeSecretKey) $missing_vars[] = 'STRIPE_SECRET_KEY';
    if (!defined('DB_HOST')) $missing_vars[] = 'DB_HOST';
    if (!defined('DB_USERNAME')) $missing_vars[] = 'DB_USERNAME';
    // DB_PASSWORD no se suele loguear por seguridad, pero su ausencia también sería un problema.
    if (!defined('DB_DATABASE')) $missing_vars[] = 'DB_DATABASE';
    if (!$jwtSecretKey) $missing_vars[] = 'JWT_SECRET'; // Cambiado a la constante correcta
    $errorMsg = 'Faltan variables de entorno críticas (constantes): ' . implode(', ', $missing_vars);
    Logger::critical($errorMsg);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor: Configuración incompleta.']);
    exit();
}
// Conexión a la base de datos usando constantes
$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    $errorMsg = 'Error de conexión a la base de datos: ' . $conn->connect_error;
    Logger::critical($errorMsg);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor: No se pudo conectar a la base de datos.']);
    exit();
}
$conn->set_charset(DB_CHARSET); // Usar constante DB_CHARSET de bootstrap.php

// Configurar zona horaria de MySQL para que coincida con PHP
$conn->query("SET time_zone = '+02:00'");
// Configurar Stripe
\Stripe\Stripe::setApiKey($stripeSecretKey);
// Obtener datos del request
$input = json_decode(file_get_contents('php://input'), true);
// Log the raw input for debugging (Considerar usar el Logger de bootstrap si está disponible y configurado)
Logger::debug('[signup.php] Raw input received:', $input);
// Validar campos de entrada
$fullName        = $input['fullName'] ?? null;
$email           = $input['email'] ?? null;
$password        = $input['password'] ?? null;
$plan_slug       = $input['plan_slug'] ?? null;
$billing_cycle   = $input['billing_cycle'] ?? null; // 'monthly' o 'annual'
$couponCode      = trim($input['coupon_code'] ?? '');
$paymentMethodId = $input['paymentMethodId'] ?? null;
$stripeCustomerId = $input['stripeCustomerId'] ?? null; // Nuevo campo esperado
$nombre_agencia_param = trim($input['nombre_agencia'] ?? ''); // NUEVO CAMPO OPCIONAL
// Log the extracted values (Considerar usar el Logger)
// error_log('Extracted values:');
// ... (logs de valores extraídos)
$requiredInputFields = compact('fullName', 'email', 'password', 'plan_slug', 'billing_cycle', 'paymentMethodId', 'stripeCustomerId'); // nombre_agencia_param NO es obligatorio aquí
$missing_fields = array_keys(array_filter($requiredInputFields, function($value) {
    return empty($value);
}));
if (!empty($missing_fields)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Todos los campos son obligatorios.', 'missing_fields' => $missing_fields]);
    Logger::warning('[signup.php] Intento de registro con campos faltantes:', ['missing_fields' => $missing_fields, 'input' => $input]);
    exit();
}
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Formato de email inválido.']);
    Logger::warning('[signup.php] Email inválido proporcionado:', ['email' => $email]);
    exit();
}
if (!in_array($billing_cycle, ['monthly', 'annual'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => "Ciclo de facturación no válido. Debe ser 'monthly' o 'annual'."]);
    Logger::warning('[signup.php] Ciclo de facturación inválido:', ['billing_cycle' => $billing_cycle]);
    exit();
}
// Consultar la tabla plans para obtener el stripe_price_id y el id del plan
$stmt_plan = $conn->prepare("SELECT id, name, stripe_price_id_monthly, stripe_price_id_annual FROM plans WHERE slug = ? AND is_active = 1");
if (!$stmt_plan) {
    Logger::error("[signup.php] Error al preparar consulta de plan: " . $conn->error);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al buscar plan.']);
    exit();
}
$stmt_plan->bind_param("s", $plan_slug);
$stmt_plan->execute();
$result_plan = $stmt_plan->get_result();
if ($result_plan->num_rows === 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => "Plan seleccionado no válido o no activo."]);
    Logger::warning('[signup.php] Plan no válido o inactivo:', ['plan_slug' => $plan_slug]);
    $stmt_plan->close();
    exit();
}
$plan_details = $result_plan->fetch_assoc();
$stmt_plan->close();
$plan_db_id = $plan_details['id'];
$plan_display_name = $plan_details['name'];
$stripe_price_id_to_use_for_stripe = null;
if ($billing_cycle === 'monthly') {
    $stripe_price_id_to_use_for_stripe = $plan_details['stripe_price_id_monthly'];
} elseif ($billing_cycle === 'annual') {
    $stripe_price_id_to_use_for_stripe = $plan_details['stripe_price_id_annual'];
}
if (empty($stripe_price_id_to_use_for_stripe)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => "Ciclo de facturación no disponible para este plan."]);
    Logger::warning('[signup.php] Ciclo de facturación no disponible para el plan:', ['plan_slug' => $plan_slug, 'billing_cycle' => $billing_cycle]);
    exit();
}
// Verificar si el email ya existe
$stmt = $conn->prepare("SELECT id FROM usuarios WHERE email = ?");
if (!$stmt) {
    Logger::error("[signup.php] Error al preparar consulta de email: " . $conn->error);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
    exit();
}
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows > 0) {
    http_response_code(409); // Conflict
    echo json_encode(['success' => false, 'message' => 'El email ya está registrado.']);
    Logger::info('[signup.php] Intento de registro con email ya existente:', ['email' => $email]);
    $stmt->close();
    $stmt = null; // Asegurar que $stmt esté "limpio" si la siguiente operación no lo usa.
    $conn->close();
    exit();
}
$stmt->close();
$stmt = null; // Establecer a null después de cerrar para el manejo en finally
// NO iniciar transacción aquí todavía.
// $conn->begin_transaction();
try {
    // Validar que stripeCustomerId y paymentMethodId no estén vacíos si se proporcionan
    // La validación de existencia ya está cubierta por $requiredInputFields
    if (empty($stripeCustomerId)) {
        throw new Exception("stripeCustomerId es obligatorio.");
    }
    if (empty($paymentMethodId)) {
        throw new Exception("paymentMethodId es obligatorio.");
    }
    // Adjuntar el PaymentMethod al Customer y establecerlo como default
    // Esto reemplaza la creación del cliente y la asignación del payment method en Customer::create
    // Comentado debido al error "Non-static method Stripe\\PaymentMethod::attach() cannot be called statically"
    // Es posible que este paso no sea necesario si el PaymentMethod ya está asociado al Customer
    // desde el SetupIntent y se establece como default_payment_method en Customer::update.
    // \\Stripe\\PaymentMethod::attach($paymentMethodId, ['customer' => $stripeCustomerId]);
    \Stripe\Customer::update($stripeCustomerId, [
        'invoice_settings' => ['default_payment_method' => $paymentMethodId],
        // Opcionalmente, actualizar nombre y email si es la primera vez que se asocian
        // o si queremos asegurar que Stripe tenga los datos más recientes del formulario de signup.
        // Esto es útil si el customer fue creado en un paso previo (ej. con initialize-checkout)
        // y solo tenía un email genérico o estaba vacío.
        'name' => $fullName, 
        'email' => $email
    ]);
    // Crear suscripción en Stripe
    // El customer ya existe y tiene un método de pago por defecto.
    Logger::debug('[signup.php] Datos para crear suscripción en Stripe:', [
        'customer' => $stripeCustomerId,
        'items' => [['price' => $stripe_price_id_to_use_for_stripe]],
        'payment_behavior' => 'default_incomplete', // Esto es clave
        'payment_settings' => [
            'save_default_payment_method' => 'on_subscription',
        ],
        'proration_behavior' => 'create_prorations', 
        'trial_period_days' => DEFAULT_TRIAL_PERIOD_DAYS, // Usar la constante global
        'expand' => ['latest_invoice.payment_intent'], // Expandir para obtener el PI de la factura
        'metadata' => [
            'user_email' => $email,
            'user_fullname' => $fullName,
            'plan_slug' => $plan_slug,
            'billing_cycle' => $billing_cycle,
            'client_app_source' => 'valorador-landing-vue',
            'agency_name_on_signup' => $nombre_agencia_param // NUEVO: Guardar nombre de agencia en metadata
        ]
    ]);
    $subscription_metadata_for_create = [
        'user_email' => $email,
        'user_fullname' => $fullName,
        'plan_slug' => $plan_slug,
        'billing_cycle' => $billing_cycle,
        'client_app_source' => 'valorador-landing-vue',
        'agency_name_on_signup' => $nombre_agencia_param // NUEVO: Guardar nombre de agencia en metadata
    ];
    Logger::debug('[signup.php] Metadata para crear suscripción en Stripe:', $subscription_metadata_for_create);
    // Preparar parámetros de suscripción
    $subscription_params = [
        'customer' => $stripeCustomerId,
        'items' => [
            ['price' => $stripe_price_id_to_use_for_stripe],
        ],
        'payment_behavior' => 'default_incomplete',
        'payment_settings' => [
            'save_default_payment_method' => 'on_subscription',
        ],
        'proration_behavior' => 'create_prorations',
        'trial_period_days' => DEFAULT_TRIAL_PERIOD_DAYS,
        'expand' => ['latest_invoice.payment_intent'],
        'metadata' => $subscription_metadata_for_create
    ];

    // Agregar cupón si se proporciona y es válido
    if (!empty($couponCode)) {
        try {
            $coupon = \Stripe\Coupon::retrieve($couponCode);
            if ($coupon->valid && (!$coupon->redeem_by || $coupon->redeem_by >= time())) {
                $subscription_params['coupon'] = $couponCode;
                Logger::info('[signup.php] Cupón aplicado a la suscripción.', ['coupon_code' => $couponCode]);
            } else {
                Logger::warning('[signup.php] Cupón no válido o expirado.', ['coupon_code' => $couponCode]);
            }
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            Logger::warning('[signup.php] Cupón no encontrado.', ['coupon_code' => $couponCode, 'error' => $e->getMessage()]);
        }
    }

    $subscription = \Stripe\Subscription::create($subscription_params);
    Logger::info('[signup.php] Suscripción creada en Stripe.', ['subscription_id' => $subscription->id]);
    // Si la suscripción está en período de prueba, no se requiere un PaymentIntent inmediato.
    // El estado 'trialing' es suficiente para proceder.
    if ($subscription->status === 'trialing') {
        Logger::info('[signup.php] Suscripción iniciada en período de prueba (trialing). No se requiere PaymentIntent inmediato.', [
            'subscription_id' => $subscription->id,
            'status' => $subscription->status,
            'trial_end' => $subscription->trial_end ? date('Y-m-d H:i:s', $subscription->trial_end) : null
        ]);
        // Proceder directamente a la lógica de creación en BD,
        // saltando la verificación de PaymentIntent que fallaría para 'trialing'.
        // La transacción de BD se inicia más abajo antes de las inserciones.
    } else {
        // Lógica existente para suscripciones que NO están en 'trialing' (requieren pago o acción)
        $payment_intent = null;
        if (isset($subscription->latest_invoice) && $subscription->latest_invoice->payment_intent instanceof \Stripe\PaymentIntent) {
            $payment_intent = $subscription->latest_invoice->payment_intent;
        }
        if (!$payment_intent) {
            Logger::error('[signup.php] No se encontró PaymentIntent en la factura más reciente para suscripción no trialing.', ['subscription_id' => $subscription->id, 'subscription_status' => $subscription->status]);
            try {
                $subscription->cancel();
                Logger::info('[signup.php] Suscripción (no trialing) cancelada en Stripe debido a ausencia de PaymentIntent inicial.', ['subscription_id' => $subscription->id]);
            } catch (Exception $cancelError) {
                Logger::error('[signup.php] Error al intentar cancelar la suscripción (no trialing) en Stripe por PI inicial ausente.', ['subscription_id' => $subscription->id, 'cancel_error' => $cancelError->getMessage()]);
            }
            throw new Exception("No se encontró información de pago para la suscripción inicial (estado: " . $subscription->status . ").");
        }
        Logger::info('[signup.php] PaymentIntent de la factura inicial recuperado para suscripción no trialing.', ['payment_intent_id' => $payment_intent->id, 'status' => $payment_intent->status]);
        if ($payment_intent->status === 'requires_action' || $payment_intent->status === 'requires_confirmation') {
            Logger::info('[signup.php] El PaymentIntent de la factura inicial (no trialing) requiere acción del cliente.', [
                'payment_intent_id' => $payment_intent->id, 
                'status' => $payment_intent->status
            ]);
            $formDataForFinalize = [
                'fullName' => $fullName,
                'email' => $email,
                'plan_slug' => $plan_slug,
                'billing_cycle' => $billing_cycle,
                'nombre_agencia' => $nombre_agencia_param,
            ];
            http_response_code(200);
            echo json_encode([
                'success' => true,
                'status' => 'requires_action',
                'paymentIntentClientSecret' => $payment_intent->client_secret,
                'subscriptionId' => $subscription->id,
                'stripeCustomerId' => $stripeCustomerId,
                'formDataForFinalize' => $formDataForFinalize
            ]);
            if ($conn && $conn instanceof mysqli && $conn->thread_id) { $conn->close(); }
            exit();
        } elseif ($payment_intent->status === 'succeeded') {
            Logger::info('[signup.php] El PaymentIntent de la factura inicial (no trialing) se completó exitosamente (succeeded).', [
                'payment_intent_id' => $payment_intent->id
            ]);
            // Continuar con la creación en BD.
        } else {
            $errorMessage = 'El pago de la suscripción inicial no pudo ser procesado.';
            if (isset($payment_intent->last_payment_error) && !empty($payment_intent->last_payment_error->message)) {
                $errorMessage = $payment_intent->last_payment_error->message;
            } elseif (isset($payment_intent->last_payment_error) && is_string($payment_intent->last_payment_error)) {
                $errorMessage = strval($payment_intent->last_payment_error);
            }
            Logger::error('[signup.php] El PaymentIntent de la factura inicial (no trialing) está en un estado inesperado o fallido.', [
                'payment_intent_id' => $payment_intent->id, 
                'status' => $payment_intent->status,
                'client_message' => $errorMessage
            ]);
            try {
                $subscription->cancel();
                Logger::info('[signup.php] Suscripción (no trialing) cancelada en Stripe debido a fallo en el PaymentIntent inicial.', ['subscription_id' => $subscription->id]);
            } catch (Exception $cancelError) {
                Logger::error('[signup.php] Error al intentar cancelar la suscripción (no trialing) en Stripe después de fallo del PI inicial.', ['subscription_id' => $subscription->id, 'cancel_error' => $cancelError->getMessage()]);
            }
            http_response_code(402);
            echo json_encode(['success' => false, 'message' => $errorMessage, 'payment_intent_status' => $payment_intent->status]);
            if ($conn && $conn instanceof mysqli && $conn->thread_id) { $conn->close(); }
            exit();
        }
    } // Fin del else para suscripciones no 'trialing'
    // Si $subscription->status === 'trialing' O $payment_intent->status === 'succeeded' (para no trialing),
    // la ejecución continúa aquí.
    // Ahora podemos iniciar la transacción de BD.
    $conn->begin_transaction();
    $transaction_started = true;
    // Hashear contraseña
    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
    // Generar UUIDs
    $userUuidResult = $conn->query("SELECT UUID() as uuid");
    if (!$userUuidResult) { throw new Exception("Error al generar UUID de usuario: " . $conn->error); }
    $userUuid = $userUuidResult->fetch_assoc()['uuid'];
    $userUuidResult->free();
    // Insertar usuario (sin agency_id inicialmente)
    $stmt = $conn->prepare("INSERT INTO usuarios (uuid, nombre_completo, email, password_hash, stripe_customer_id, activo, fecha_creacion, fecha_modificacion) VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())");
    if (!$stmt) { throw new Exception("Error al preparar inserción de usuario: " . $conn->error); }
    $stmt->bind_param("sssss", $userUuid, $fullName, $email, $passwordHash, $stripeCustomerId);
    if (!$stmt->execute()) { throw new Exception("Error al crear usuario: " . $stmt->error); }
    $userId = $stmt->insert_id;
    // No cerrar $stmt aquí, se hará en el finally
    // Crear la agencia para el nuevo usuario
    $agencyUuidResult = $conn->query("SELECT UUID() as uuid");
    if (!$agencyUuidResult) { throw new Exception("Error al generar UUID de agencia: " . $conn->error); }
    $agencyUuid = $agencyUuidResult->fetch_assoc()['uuid'];
    $agencyUuidResult->free();
    $agencyName = !empty($nombre_agencia_param) ? $nombre_agencia_param : "Agencia de " . $fullName;
    $stmt_agency = $conn->prepare("INSERT INTO agencies (uuid, name, owner_user_id, fecha_creacion, fecha_modificacion) VALUES (?, ?, ?, NOW(), NOW())");
    if (!$stmt_agency) { throw new Exception("Error al preparar inserción de agencia: " . $conn->error); }
    $stmt_agency->bind_param("ssi", $agencyUuid, $agencyName, $userId);
    if (!$stmt_agency->execute()) { throw new Exception("Error al crear agencia: " . $stmt_agency->error); }
    $new_agency_id = $stmt_agency->insert_id;
    $stmt_agency->close(); // Cerrar stmt_agency aquí
    // Actualizar usuario con el agency_id Y MARCAR COMO PROPIETARIO
    $stmt_update_user = $conn->prepare("UPDATE usuarios SET agency_id = ?, is_agency_owner = 1 WHERE id = ?");
    if (!$stmt_update_user) { throw new Exception("Error al preparar actualización de usuario para agency_id e is_agency_owner (signup): " . $conn->error); }
    $stmt_update_user->bind_param("ii", $new_agency_id, $userId);
    if (!$stmt_update_user->execute()) { throw new Exception("Error al actualizar agency_id e is_agency_owner del usuario (signup): " . $stmt_update_user->error); }
    $stmt_update_user->close(); // Cerrar stmt_update_user aquí
    // $stmt->close(); // Asegurar que $stmt de usuario se cierra antes de reusar la variable o en finally.
                     // Lo dejamos para el finally principal.
    // Insertar suscripción
    $trialStartTimestamp = $subscription->trial_start ? date('Y-m-d H:i:s', $subscription->trial_start) : null;
    $trialEndTimestamp = $subscription->trial_end ? date('Y-m-d H:i:s', $subscription->trial_end) : null;
    $currentPeriodEndTimestamp = date('Y-m-d H:i:s', $subscription->current_period_end);
    $currentPeriodStartTimestamp = date('Y-m-d H:i:s', $subscription->current_period_start);
    $stripeProductIdFromSub = 'default_product_id';
    if (!empty($subscription->items->data) && isset($subscription->items->data[0]->price->product)) {
        $stripeProductIdFromSub = $subscription->items->data[0]->price->product;
    }
    $stripePriceIdFromSub = $stripe_price_id_to_use_for_stripe; // El que usamos para crear
    if (!empty($subscription->items->data) && isset($subscription->items->data[0]->price->id)) {
        $stripePriceIdFromSub = $subscription->items->data[0]->price->id;
    }
    $stripeMetadataJson = json_encode($subscription->metadata);
    $dbSubscriptionStatus = $subscription->status; // Usar el estado real de Stripe
    $subsUuidResult = $conn->query("SELECT UUID() as uuid"); // Mover aquí para que esté dentro de la transacción si no lo estaba antes
    if (!$subsUuidResult) { throw new Exception("Error al generar UUID de suscripción: " . $conn->error); }
    $subsUuid = $subsUuidResult->fetch_assoc()['uuid'];
    $subsUuidResult->free();
    $stmt = $conn->prepare("INSERT INTO suscripciones (uuid, user_id, plan_id, agency_id, stripe_subscription_id, stripe_product_id, stripe_price_id, nombre_plan_display, estado, fecha_inicio_prueba, fecha_fin_prueba, fecha_inicio_periodo_actual, fecha_fin_periodo_actual, billing_cycle, metadata_stripe, fecha_creacion, fecha_modificacion) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
    if (!$stmt) { throw new Exception("Error al preparar inserción de suscripción: " . $conn->error); }
    // La 's' para uuid, 'i' para user_id, 'i' para plan_id, 'i' para agency_id ...
    $stmt->bind_param("siiisssssssssss", $subsUuid, $userId, $plan_db_id, $new_agency_id, $subscription->id, $stripeProductIdFromSub, $stripePriceIdFromSub, $plan_display_name, $dbSubscriptionStatus, $trialStartTimestamp, $trialEndTimestamp, $currentPeriodStartTimestamp, $currentPeriodEndTimestamp, $billing_cycle, $stripeMetadataJson);
    if (!$stmt->execute()) { throw new Exception("Error al crear suscripción DB: " . $stmt->error); }
    $conn->commit();
    Logger::info("[signup.php] Usuario y suscripción creados en BD.", ['user_id' => $userId, 'subscription_uuid' => $subsUuid]);

    // --- INICIO: Enviar email de bienvenida ---
    try {
        $brevoService = new BrevoService();
        $template_name = 'bienvenida_nuevo_cliente';

        $stmt_template = $conn->prepare("SELECT asunto_predeterminado, contenido_html FROM email_plantillas_html WHERE nombre_interno_plantilla = ?");
        if (!$stmt_template) {
            // No lanzar excepción, solo loguear para no romper el flujo principal
            Logger::error("[signup.php] Error al preparar la consulta de la plantilla de email: " . $conn->error, ['user_id' => $userId]);
        } else {
            $stmt_template->bind_param("s", $template_name);
            $stmt_template->execute();
            $result_template = $stmt_template->get_result();

            if ($result_template->num_rows > 0) {
                $template = $result_template->fetch_assoc();
                
                $login_url = rtrim(DASHBOARD_BASE_URL, '/');

                // Definir los placeholders para la plantilla
                $placeholders = [
                    '{{nombre_completo}}' => $fullName,
                    '{{nombre_agencia}}' => $agencyName,
                    '{{nombre_plan}}' => $plan_display_name,
                    '{{url_login}}' => $login_url,
                    '{{email_cliente}}' => $email,
                    '{{current_year}}' => date('Y')
                ];

                $asunto_final = str_replace(array_keys($placeholders), array_values($placeholders), $template['asunto_predeterminado']);
                $email_html_body_final = str_replace(array_keys($placeholders), array_values($placeholders), $template['contenido_html']);

                $emailResult = $brevoService->sendEmail(
                    $email,
                    $fullName,
                    $asunto_final,
                    $email_html_body_final
                );

                if ($emailResult['success']) {
                    Logger::info("[signup.php] Email de bienvenida (plantilla: {$template_name}) enviado a {$email}.", ['user_id' => $userId, 'message_id' => $emailResult['message_id']]);
                } else {
                    Logger::error("[signup.php] Falló el envío del email de bienvenida a {$email}.", ['user_id' => $userId, 'error' => $emailResult['error']]);
                }
            } else {
                Logger::warning("[signup.php] No se encontró la plantilla de email '{$template_name}'. No se envió correo de bienvenida.", ['user_id' => $userId]);
            }
            $stmt_template->close();
        }
    } catch (Exception $emailException) {
        Logger::error("[signup.php] Excepción al intentar enviar email de bienvenida a {$email}: " . $emailException->getMessage(), ['user_id' => $userId]);
    }
    // --- FIN: Enviar email de bienvenida ---

    // Generar JWT para el nuevo usuario
    $issuedAt = time();
    $jwtIssuer = defined('JWT_ISSUER') ? JWT_ISSUER : ($_ENV['JWT_ISSUER'] ?? 'inmoautomation.com');
    $jwtExpire = 60 * 60 * 24 * 7; // 7 días
    $payload = [
        'iat' => $issuedAt,
        'exp' => $issuedAt + $jwtExpire,
        'iss' => $jwtIssuer,
        'sub' => $userUuid,
        'user_id' => $userId,
        'agency_id' => $new_agency_id, // Añadido
        'is_agency_owner' => true,     // Añadido, ya que es el creador
        'email' => $email,
        'nombre_completo' => $fullName,
        'roles' => [],
    ];
    $jwt = JWT::encode($payload, $jwtSecretKey, 'HS256');
    // Enviar respuesta de éxito con el token y datos del usuario
    // Esto SOLO se alcanza si el payment_intent->status fue 'succeeded' directamente.
    http_response_code(201); // Creado
    echo json_encode([
        'success' => true,
        'status' => 'completed', // Estado explícito para el frontend
        'message' => 'Usuario y suscripción creados exitosamente.',
        'token' => $jwt,
        'user' => [ // Devolver el objeto user como se hacía en finalize-registration
            'uuid' => $userUuid,
            'id' => $userId,
            'email' => $email,
            'nombre_completo' => $fullName,
            'roles' => [],
            'agency_id' => $new_agency_id, 
            'is_agency_owner' => true,    
            'agency_name' => $agencyName // Asegurar que $agencyName está definida y accesible aquí
        ],
        'subscription' => [ // Devolver también detalles básicos de la suscripción creada
            'subscription_id_stripe' => $subscription->id,
            'plan_slug' => $plan_slug,
            'status' => $subscription->status, // Debería ser 'trialing' o 'active'
            'trial_end' => $subscription->trial_end ? date('Y-m-d H:i:s', $subscription->trial_end) : null
        ]
    ]);
} catch (\Stripe\Exception\ApiErrorException $e) {
    Logger::error("[signup.php] Error de API de Stripe: " . $e->getMessage(), ['exception_details' => $e->getJsonBody()]);
    // No se debería haber iniciado una transacción si Stripe falla en Subscription::create
    // if (isset($transaction_started) && $transaction_started) { $conn->rollback(); } // Añadir por si acaso
    $errorBody = $e->getJsonBody();
    $stripeErrorMessage = 'Error desconocido de Stripe.';
    if (isset($errorBody['error']['message'])) {
        $stripeErrorMessage = $errorBody['error']['message'];
    } elseif ($e->getMessage()) {
        $stripeErrorMessage = $e->getMessage();
    }
    Logger::error('[signup.php] Error de API de Stripe: ' . $stripeErrorMessage, [
        'exception_message' => $e->getMessage(),
        'stripe_error_code' => $e->getStripeCode(),
        'stripe_error_param' => $e->getStripeParam(),
        'http_status' => $e->getHttpStatus(),
        'json_body' => $errorBody
    ]);
    http_response_code($e->getHttpStatus() ?: 500);
    echo json_encode(['success' => false, 'message' => 'Error al procesar con Stripe: ' . $stripeErrorMessage]);
} catch (Exception $e) {
    Logger::error("[signup.php] Error general en script: " . $e->getMessage(), ['exception' => $e]);
    if (isset($transaction_started) && $transaction_started) { 
        $conn->rollback(); 
        Logger::info("[signup.php] Transacción de BD revertida debido a error general.");
    }
    http_response_code(500);
    // No exponer $e->getMessage() directamente al cliente en producción si puede contener info sensible.
    // Considerar un mensaje genérico o uno sanitizado.
    $clientMessage = 'Error interno del servidor al procesar el registro.';
    // Si $e->getMessage() es seguro de mostrar O es un mensaje específico que hemos lanzado, podemos usarlo.
    // Por ejemplo, si lanzamos new Exception("Mensaje para el cliente"), entonces está bien.
    // Si es un mensaje de error de bajo nivel (ej. SQL), no.
    // Por ahora, para depuración, lo dejamos, pero revisar para producción.
    if ($e instanceof \Stripe\Exception\ApiErrorException || $e->getMessage() === "No se encontró información de pago para la suscripción inicial.") {
         $clientMessage = $e->getMessage(); // Mensajes de Stripe o los nuestros pueden ser más específicos.
    }
    echo json_encode(['success' => false, 'message' => $clientMessage]);
} finally {
    if (isset($stmt) && $stmt instanceof mysqli_stmt) {
        $stmt->close();
    }
    if ($conn && $conn instanceof mysqli && $conn->thread_id) { // Verificar si la conexión es válida y está abierta
        $conn->close();
    }
}
?>