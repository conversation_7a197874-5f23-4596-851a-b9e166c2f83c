<?php
declare(strict_types=1);
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Api\lib\Database;
// Configuración del valorador - migrado a inmoautomation.com
// ini_set('display_errors', 1); // Gestionado por bootstrap.php
// ini_set('display_startup_errors', 1); // Gestionado por bootstrap.php
// error_reporting(E_ALL); // Gestionado por bootstrap.php
// --- Logger Setup ---
if (!function_exists('custom_log_valorador_config')) {
    function custom_log_valorador_config($message) {
        // Considerar usar el Logger centralizado de bootstrap.php
        $logFile = __DIR__ . '/logs/debug_valorador_config.log'; // Escritura en subdirectorio logs
        if (!is_dir(__DIR__ . '/logs')) {
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                error_log("Advertencia [valorador-config]: No se pudo crear el directorio de logs: " . __DIR__ . '/logs');
                return;
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [valorador-config] {$message}\n", 3, $logFile);
    }
}
// custom_log_valorador_config("--- [valorador-config.php] INICIADO ---");
// --- Autoload y Carga de .env ---
// ... (eliminar require_once vendor/autoload.php y carga de Dotenv) ...
// --- Importar clases de JWT ---
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;
// --- Función para obtener y decodificar el token JWT ---
function get_decoded_jwt_payload_valorador_config() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded; // Devuelve el payload del objeto
            } catch (ExpiredException $e) {
                // custom_log_valorador_config("Token expirado: " . $e->getMessage());
            } catch (SignatureInvalidException $e) {
                // custom_log_valorador_config("Firma de token inválida: " . $e->getMessage());
            } catch (BeforeValidException $e) {
                // custom_log_valorador_config("Token no válido todavía: " . $e->getMessage());
            } catch (\Throwable $e) {
                // custom_log_valorador_config("Error decodificando token: " . $e->getMessage());
            }
             // Restaurar display_errors si aún está desactivado tras un error
            if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                ini_set('display_errors', $previous_display_errors);
            }
        } else {
            // custom_log_valorador_config("JWT_SECRET no está configurado.");
        }
    }
    return null;
}
$decoded_payload = get_decoded_jwt_payload_valorador_config();
if (!$decoded_payload) {
    // custom_log_valorador_config("ACCESO NO AUTORIZADO: Payload de JWT no disponible o inválido.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado.']);
    exit;
}
$user_id = $decoded_payload->user_id ?? null;
$agency_id_from_jwt = $decoded_payload->agency_id ?? null; // Clave para el INSERT
$user_email = $decoded_payload->email ?? null; // Para default en email_notificaciones
if (!$user_id) {
    // custom_log_valorador_config("ACCESO NO AUTORIZADO: user_id no encontrado en el token.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado (user_id ausente en token).']);
    exit;
}
// custom_log_valorador_config("Autenticación local de JWT exitosa. User ID: {$user_id}, Agency ID: {$agency_id_from_jwt}");
try {
    // --- Conexión a la Base de Datos (PDO) ---
    $pdo = Database::getInstance()->getConnection();
    // custom_log_valorador_config("Conexión a BBDD (PDO) exitosa.");
    // --- Lógica Principal (GET y POST) ---
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        custom_log_valorador_config("[GET Request] Procesando para agency_id: {$agency_id_from_jwt} (User ID: {$user_id})");
        if (!$agency_id_from_jwt) {
            custom_log_valorador_config("[GET Request] ERROR: agency_id no encontrado en el token para user_id: {$user_id}.");
            http_response_code(403); // O 401 si se considera un fallo de autenticación/autorización
            echo json_encode(['success' => false, 'message' => 'Agencia no identificada en el token.']);
            exit;
        }
        // Buscar la configuración del valorador usando agency_id (incluyendo inactivos)
        $sql = "SELECT client_identifier, uuid, nombre_display, logo_url, color_primario, color_secundario, email_notificaciones_leads, direccion_fisica, telefono_contacto, email_contacto_publico, sitio_web_url, whatsapp_numero, descripcion_breve, cta_contacto_url, dominios_autorizados_json, config_valorador_json, config_email_secuencia_json, activo FROM clientes_valorador WHERE agency_id = :agency_id LIMIT 1";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':agency_id', $agency_id_from_jwt, PDO::PARAM_INT);
        $stmt->execute();
        $config = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($config) {
            // Decodificar campos JSON antes de enviar al cliente
            $config['dominios_autorizados_json'] = isset($config['dominios_autorizados_json']) ? (json_decode($config['dominios_autorizados_json'], true) ?? []) : [];
            // Asegurarse de que el client_identifier no sea nulo si se encontró la configuración
            if (!isset($config['client_identifier'])) {
                 custom_log_valorador_config("[GET Request] ADVERTENCIA: client_identifier es NULL para agency_id: {$agency_id_from_jwt}. Puede indicar un problema de datos.");
            }
            $config['config_valorador_json'] = isset($config['config_valorador_json']) ? (json_decode($config['config_valorador_json'], true) ?? []) : [];
            $config['config_email_secuencia_json'] = isset($config['config_email_secuencia_json']) ? (json_decode($config['config_email_secuencia_json'], true) ?? []) : [];

            // Agregar información de estado para el frontend
            $config['is_active'] = (bool)$config['activo'];
            $config['status'] = $config['activo'] ? 'active' : 'inactive';

            // Determinar la razón de inactividad si aplica
            if (!$config['activo']) {
                // Obtener estado de suscripción para determinar la razón
                $subscription_sql = "SELECT estado FROM suscripciones WHERE user_id = :user_id ORDER BY fecha_creacion DESC LIMIT 1";
                $subscription_stmt = $pdo->prepare($subscription_sql);
                $subscription_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                $subscription_stmt->execute();
                $subscription = $subscription_stmt->fetch(PDO::FETCH_ASSOC);

                if ($subscription) {
                    $subscription_status = strtolower($subscription['estado']);
                    if (in_array($subscription_status, ['past_due', 'unpaid', 'canceled', 'incomplete'])) {
                        $config['inactive_reason'] = 'subscription_issue';
                        $config['subscription_status'] = $subscription_status;
                    } else {
                        $config['inactive_reason'] = 'manually_disabled';
                    }
                } else {
                    $config['inactive_reason'] = 'no_subscription';
                }

                custom_log_valorador_config("[GET Request] Valorador configurado pero inactivo. Razón: " . ($config['inactive_reason'] ?? 'unknown'));
            }

            custom_log_valorador_config("[GET Request] Configuración encontrada. Estado: " . ($config['activo'] ? 'activo' : 'inactivo'));
            echo json_encode(['success' => true, 'config' => $config]);
        } else {
            custom_log_valorador_config("[GET Request] No se encontró configuración para agency_id: {$agency_id_from_jwt}. Valorador nunca configurado.");
            echo json_encode(['success' => true, 'config' => (object)[], 'status' => 'not_configured']);
        }
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // custom_log_valorador_config("[POST Request] Procesando para user_id: {$user_id}, agency_id: {$agency_id_from_jwt}");
        $data = [];
        $new_logo_url = null;
        // ... (Manejo de Content-Type JSON y multipart/form-data sin cambios significativos, 
        // excepto la creación del directorio de uploads si no existe y el guardado de $new_logo_url)
        if (isset($_SERVER['CONTENT_TYPE']) && stripos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
            $data = json_decode(file_get_contents('php://input'), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                http_response_code(400); echo json_encode(['success' => false, 'message' => 'JSON mal formado.']); exit();
            }
        } elseif (isset($_SERVER['CONTENT_TYPE']) && stripos($_SERVER['CONTENT_TYPE'], 'multipart/form-data') !== false) {
            $data = $_POST;
            if (isset($_FILES['agencyLogoFile']) && $_FILES['agencyLogoFile']['error'] === UPLOAD_ERR_OK) {
                $logo_file_info = $_FILES['agencyLogoFile'];
                $uploadDir = __DIR__ . '/uploads/logos/';
                if (!is_dir($uploadDir)) {
                    if (!mkdir($uploadDir, 0775, true) && !is_dir($uploadDir)){
                         custom_log_valorador_config("[POST Request] CRITICAL: No se pudo crear el directorio de uploads/logos.");
                         throw new Exception("No se pudo crear el directorio para subir logos.");
                    }
                }
                $client_identifier_for_filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $data['client_identifier'] ?? ('user_' . $user_id));
                $fileExtension = strtolower(pathinfo($logo_file_info['name'], PATHINFO_EXTENSION));
                $safeFileName = 'logo_' . $client_identifier_for_filename . '_' . time() . '.' . $fileExtension;
                $uploadFilePath = $uploadDir . $safeFileName;
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
                if (!in_array($fileExtension, $allowedExtensions)) {
                    http_response_code(400); echo json_encode(['success' => false, 'message' => 'Tipo de archivo de logo no permitido.']); exit();
                }
                if (move_uploaded_file($logo_file_info['tmp_name'], $uploadFilePath)) {
                    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
                    $relativePath = '/api/uploads/logos/' . $safeFileName;
                    $new_logo_url = $baseUrl . $relativePath;
                } else {
                    http_response_code(500); echo json_encode(['success' => false, 'message' => 'Error al guardar el logo.']); exit();
                }
            } elseif (isset($_FILES['agencyLogoFile']) && $_FILES['agencyLogoFile']['error'] !== UPLOAD_ERR_NO_FILE) {
                http_response_code(400); echo json_encode(['success' => false, 'message' => 'Error al subir el logo: ' . $_FILES['agencyLogoFile']['error']]); exit();
            }
        } else {
            http_response_code(415); echo json_encode(['success' => false, 'message' => 'Tipo de contenido no soportado.']); exit();
        }
        if (empty($data)) {
            http_response_code(400); echo json_encode(['success' => false, 'message' => 'Datos no proporcionados.']); exit();
        }
        $client_identifier = $data['client_identifier'] ?? null;
        $nombre_display = $data['nombre_display'] ?? null;
        $logo_url_from_payload = $data['logo_url'] ?? null;
        $final_logo_url = $new_logo_url ?? $logo_url_from_payload;
        $color_primario = $data['color_primario'] ?? '#051f33';
        $color_secundario = $data['color_secundario'] ?? '#ed8725';
        $email_notificaciones_leads = $data['email_notificaciones_leads'] ?? $user_email;
        $direccion_fisica = $data['direccion_fisica'] ?? null;
        $telefono_contacto = $data['telefono_contacto'] ?? null;
        $email_contacto_publico = $data['email_contacto_publico'] ?? null;
        $sitio_web_url = $data['sitio_web_url'] ?? null;
        $whatsapp_numero = $data['whatsapp_numero'] ?? null;
        $descripcion_breve = $data['descripcion_breve'] ?? null;
        $cta_contacto_url = $data['cta_contacto_url'] ?? null;
        $activo = $data['activo'] ?? 1;
        $dominios_autorizados_json = isset($data['dominios_autorizados_json']) ? json_encode($data['dominios_autorizados_json']) : json_encode([]);
        $config_valorador_json = isset($data['config_valorador_json']) ? json_encode($data['config_valorador_json']) : json_encode([]);
        $config_email_secuencia_json = isset($data['config_email_secuencia_json']) ? json_encode($data['config_email_secuencia_json']) : json_encode([]);
        if ($client_identifier !== null && !preg_match('/^[a-zA-Z0-9_-]+$/', $client_identifier)) {
            http_response_code(400); echo json_encode(['success' => false, 'message' => 'Identificador único (URL) con caracteres no válidos.']); exit();
        }

        // Validar longitud mínima del identificador
        if ($client_identifier !== null && strlen($client_identifier) < 3) {
            http_response_code(400); echo json_encode(['success' => false, 'message' => 'El identificador debe tener al menos 3 caracteres.']); exit();
        }

        // Verificar que el identificador no esté ya en uso por otro usuario
        if ($client_identifier !== null) {
            $sql_check_duplicate = "SELECT COUNT(*) FROM clientes_valorador WHERE client_identifier = :client_id AND user_id != :user_id AND activo = 1";
            $stmt_check_duplicate = $pdo->prepare($sql_check_duplicate);
            $stmt_check_duplicate->bindParam(':client_id', $client_identifier, PDO::PARAM_STR);
            $stmt_check_duplicate->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt_check_duplicate->execute();

            if ($stmt_check_duplicate->fetchColumn() > 0) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'El identificador ya está en uso. Por favor, elige otro.']);
                exit();
            }
        }
        $stmt_check = $pdo->prepare("SELECT id, logo_url as current_logo_url FROM clientes_valorador WHERE user_id = :user_id");
        $stmt_check->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt_check->execute();
        $existing_config = $stmt_check->fetch(PDO::FETCH_ASSOC);
        if (!$new_logo_url && $existing_config) { // Si no se subió un logo nuevo y hay config existente
            if ($logo_url_from_payload === '') { // Se envió string vacío, indica borrar logo
                 $final_logo_url = null;
            } elseif ($logo_url_from_payload === null) { // No se envió el campo logo_url, mantener el actual
                 $final_logo_url = $existing_config['current_logo_url'];
            } // Si $logo_url_from_payload tiene un valor (URL), ya se asignó a $final_logo_url
        }
        if ($existing_config) {
            // UPDATE
            $sql = "UPDATE clientes_valorador SET client_identifier = :cid, nombre_display = :nd, logo_url = :lu, color_primario = :cp, color_secundario = :cs, email_notificaciones_leads = :enl, direccion_fisica = :df, telefono_contacto = :tc, email_contacto_publico = :ecp, sitio_web_url = :swu, whatsapp_numero = :wn, descripcion_breve = :db, cta_contacto_url = :ccu, activo = :act, dominios_autorizados_json = :daj, config_valorador_json = :cvj, config_email_secuencia_json = :cesj, fecha_modificacion = NOW() WHERE user_id = :uid";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':uid', $user_id, PDO::PARAM_INT);
        } else {
            // INSERT
            if (!$agency_id_from_jwt) {
                // custom_log_valorador_config("[POST Request] ERROR: Creando valorador sin agency_id. User ID: {$user_id}");
                http_response_code(403); echo json_encode(['success' => false, 'message' => 'Agencia no identificada para crear valorador.']); exit();
            }
            $uuid_valorador = $pdo->query("SELECT UUID()")->fetchColumn();
            $sql = "INSERT INTO clientes_valorador (user_id, agency_id, client_identifier, uuid, nombre_display, logo_url, color_primario, color_secundario, email_notificaciones_leads, direccion_fisica, telefono_contacto, email_contacto_publico, sitio_web_url, whatsapp_numero, descripcion_breve, cta_contacto_url, activo, dominios_autorizados_json, config_valorador_json, config_email_secuencia_json, fecha_creacion, fecha_modificacion) VALUES (:uid, :aid, :cid, :uuid, :nd, :lu, :cp, :cs, :enl, :df, :tc, :ecp, :swu, :wn, :db, :ccu, :act, :daj, :cvj, :cesj, NOW(), NOW())";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':uid', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':aid', $agency_id_from_jwt, PDO::PARAM_INT);
            $stmt->bindParam(':uuid', $uuid_valorador, PDO::PARAM_STR);
        }
        $stmt->bindParam(':cid', $client_identifier, PDO::PARAM_STR);
        $stmt->bindParam(':nd', $nombre_display, PDO::PARAM_STR);
        $stmt->bindParam(':lu', $final_logo_url, PDO::PARAM_STR);
        $stmt->bindParam(':cp', $color_primario, PDO::PARAM_STR);
        $stmt->bindParam(':cs', $color_secundario, PDO::PARAM_STR);
        $stmt->bindParam(':enl', $email_notificaciones_leads, PDO::PARAM_STR);
        $stmt->bindParam(':df', $direccion_fisica, PDO::PARAM_STR);
        $stmt->bindParam(':tc', $telefono_contacto, PDO::PARAM_STR);
        $stmt->bindParam(':ecp', $email_contacto_publico, PDO::PARAM_STR);
        $stmt->bindParam(':swu', $sitio_web_url, PDO::PARAM_STR);
        $stmt->bindParam(':wn', $whatsapp_numero, PDO::PARAM_STR);
        $stmt->bindParam(':db', $descripcion_breve, PDO::PARAM_STR);
        $stmt->bindParam(':ccu', $cta_contacto_url, PDO::PARAM_STR);
        $stmt->bindParam(':act', $activo, PDO::PARAM_INT);
        $stmt->bindParam(':daj', $dominios_autorizados_json, PDO::PARAM_STR);
        $stmt->bindParam(':cvj', $config_valorador_json, PDO::PARAM_STR);
        $stmt->bindParam(':cesj', $config_email_secuencia_json, PDO::PARAM_STR);
        if ($stmt->execute()) {
            $affected_rows = $stmt->rowCount();
            // Volver a leer la configuración para devolverla con todos los campos actualizados (ej. logo_url, JSONs decodificados)
            $id_to_fetch = $existing_config ? $existing_config['id'] : $pdo->lastInsertId();
            if (!$id_to_fetch && $affected_rows > 0 && $client_identifier) { // Fallback si lastInsertId no funciona pero sí hubo insert
                $stmt_get_by_cid = $pdo->prepare("SELECT id FROM clientes_valorador WHERE client_identifier = :cid AND user_id = :uid ORDER BY id DESC LIMIT 1");
                $stmt_get_by_cid->bindParam(':cid', $client_identifier, PDO::PARAM_STR);
                $stmt_get_by_cid->bindParam(':uid', $user_id, PDO::PARAM_INT);
                $stmt_get_by_cid->execute();
                $id_to_fetch = $stmt_get_by_cid->fetchColumn();
            }
            $saved_config_data = null;
            if ($id_to_fetch) {
                $stmt_fetch_saved = $pdo->prepare("SELECT client_identifier, uuid, nombre_display, logo_url, color_primario, color_secundario, email_notificaciones_leads, direccion_fisica, telefono_contacto, email_contacto_publico, sitio_web_url, whatsapp_numero, descripcion_breve, cta_contacto_url, dominios_autorizados_json, config_valorador_json, config_email_secuencia_json FROM clientes_valorador WHERE id = :id_val");
                $stmt_fetch_saved->bindParam(':id_val', $id_to_fetch, PDO::PARAM_INT);
                $stmt_fetch_saved->execute();
                $saved_config_data = $stmt_fetch_saved->fetch(PDO::FETCH_ASSOC);
                if ($saved_config_data) {
                    $saved_config_data['dominios_autorizados_json'] = isset($saved_config_data['dominios_autorizados_json']) ? (json_decode($saved_config_data['dominios_autorizados_json'], true) ?? []) : [];
                    $saved_config_data['config_valorador_json'] = isset($saved_config_data['config_valorador_json']) ? (json_decode($saved_config_data['config_valorador_json'], true) ?? []) : [];
                    $saved_config_data['config_email_secuencia_json'] = isset($saved_config_data['config_email_secuencia_json']) ? (json_decode($saved_config_data['config_email_secuencia_json'], true) ?? []) : [];
                }
            }
            $message = $existing_config ? ($affected_rows > 0 ? 'Configuración actualizada.' : 'Configuración guardada (sin cambios detectados).') : 'Configuración creada correctamente.';
            echo json_encode(['success' => true, 'message' => $message, 'config' => $saved_config_data ?? (object)[]]);
        } else {
            http_response_code(500); echo json_encode(['success' => false, 'message' => 'Error al guardar la configuración.', 'db_error' => $stmt->errorInfo()]);
        }
    } else {
        http_response_code(405); echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    }
} catch (PDOException $e) {
    custom_log_valorador_config("CRÍTICO PDOException: " . $e->getMessage() . " Trace: " . $e->getTraceAsString());
    error_log("[valorador-config.php] PDOException: " . $e->getMessage());
    http_response_code(500); echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB).']);
} catch (Exception $e) {
    custom_log_valorador_config("CRÍTICO Exception: " . $e->getMessage() . " Trace: " . $e->getTraceAsString());
    error_log("[valorador-config.php] Exception: " . $e->getMessage());
    http_response_code(500); echo json_encode(['success' => false, 'message' => 'Error interno del servidor (General).']);
}
// custom_log_valorador_config("--- [valorador-config.php] FINALIZADO ---");
?>