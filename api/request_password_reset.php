<?php
/**
 * request_password_reset.php
 * Endpoint para iniciar el proceso de restablecimiento de contraseña.
 * POST JSON: { "email": "<EMAIL>" }
 * Devuelve { success: bool, message: string }
 */

declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Api\lib\BrevoService; // ya cargado vía composer autoload

header('Content-Type: application/json');

$input = json_decode(file_get_contents('php://input'), true);
$email = filter_var($input['email'] ?? null, FILTER_VALIDATE_EMAIL);
if (!$email) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Email inválido.']);
    exit;
}

try {
    $pdo = Database::getInstance()->getConnection();

    // Comprobar si existe usuario activo con ese email
    $stmt = $pdo->prepare('SELECT id, nombre_completo, agency_id FROM usuarios WHERE email = :email AND activo = 1 LIMIT 1');
    $stmt->bindParam(':email', $email, PDO::PARAM_STR);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$user) {
        // Para no dar pistas, responder igual
        echo json_encode(['success' => true, 'message' => 'Si el email existe en el sistema, se enviará un enlace de restablecimiento.']);
        exit;
    }

    // Generar token y fecha expiración 60 minutos
    $token = bin2hex(random_bytes(32));
    $expiresAt = (new DateTime('+60 minutes'))->format('Y-m-d H:i:s');

    // Crear tabla si no existe
    $pdo->exec('CREATE TABLE IF NOT EXISTS password_reset_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(128) NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY token_unique (token),
        INDEX expires_at_idx(expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4');

    // Eliminar tokens anteriores del usuario
    $delStmt = $pdo->prepare('DELETE FROM password_reset_tokens WHERE user_id = :uid');
    $delStmt->execute([':uid' => $user['id']]);

    // Insertar nuevo token
    $ins = $pdo->prepare('INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (:uid, :token, :exp)');
    $ins->execute([':uid' => $user['id'], ':token' => $token, ':exp' => $expiresAt]);

    // Enviar email con enlace
    $resetLink = rtrim(DASHBOARD_BASE_URL, '/') . '/reset-password?token=' . urlencode($token);
    $brevo = new BrevoService();
    $subject = 'Restablece tu contraseña';
    $html = "<p>Hola {$user['nombre_completo']},</p><p>Hemos recibido una solicitud para restablecer tu contraseña.</p><p><a href='{$resetLink}'>Haz clic aquí para establecer una nueva contraseña</a>. Este enlace expirará en 60 minutos.</p><p>Si no solicitaste este cambio, puedes ignorar este mensaje.</p>";

    $brevo->sendEmail($email, $user['nombre_completo'], $subject, $html);

    echo json_encode(['success' => true, 'message' => 'Si el email existe en el sistema, se enviará un enlace de restablecimiento.']);
} catch (Throwable $e) {
    error_log('[request_password_reset] ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}
