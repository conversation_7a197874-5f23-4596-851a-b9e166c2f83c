<?php
// api/debug_wkhtmltopdf.php
declare(strict_types=1);

header('Content-Type: application/json');

$debug = [
    'timestamp' => date('Y-m-d H:i:s'),
    'wkhtmltopdf_path' => $_ENV['WKHTMLTOPDF_PATH'] ?? '/usr/local/bin/wkhtmltopdf-wrapper',
    'checks' => []
];

// 1. Verificar si el archivo existe
$wkhtmlPath = $debug['wkhtmltopdf_path'];
$debug['checks']['file_exists'] = file_exists($wkhtmlPath);

// 2. Verificar permisos
if ($debug['checks']['file_exists']) {
    $debug['checks']['is_executable'] = is_executable($wkhtmlPath);
    $debug['checks']['file_permissions'] = substr(sprintf('%o', fileperms($wkhtmlPath)), -4);
} else {
    $debug['checks']['is_executable'] = false;
    $debug['checks']['file_permissions'] = 'N/A';
}

// 3. Intentar ejecutar wkhtmltopdf --version
$output = [];
$returnCode = 0;
exec($wkhtmlPath . ' --version 2>&1', $output, $returnCode);

$debug['checks']['version_command'] = [
    'return_code' => $returnCode,
    'output' => $output,
    'success' => $returnCode === 0
];

// 4. Verificar si la clase WkhtmltopdfGenerator existe
$debug['checks']['class_exists'] = class_exists('Api\utils\WkhtmltopdfGenerator');

// 5. Intentar instanciar la clase
if ($debug['checks']['class_exists']) {
    try {
        $generator = new \Api\utils\WkhtmltopdfGenerator();
        $debug['checks']['class_instantiation'] = [
            'success' => true,
            'version' => $generator->getVersion()
        ];
    } catch (Exception $e) {
        $debug['checks']['class_instantiation'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
} else {
    $debug['checks']['class_instantiation'] = [
        'success' => false,
        'error' => 'Class does not exist'
    ];
}

// 6. Verificar directorio wkhtmltopdf
$debug['checks']['which_wkhtmltopdf'] = [];
exec('which wkhtmltopdf 2>&1', $debug['checks']['which_wkhtmltopdf']);

// 7. Verificar si está en PATH
exec('wkhtmltopdf --version 2>&1', $pathOutput, $pathReturnCode);
$debug['checks']['path_version'] = [
    'return_code' => $pathReturnCode,
    'output' => $pathOutput,
    'success' => $pathReturnCode === 0
];

echo json_encode($debug, JSON_PRETTY_PRINT);
?>
