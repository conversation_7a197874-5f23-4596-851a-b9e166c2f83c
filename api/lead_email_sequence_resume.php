<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

function validate_jwt_token(string $token): ?object {
    if (!defined('JWT_SECRET') || JWT_SECRET === '') {
        return null;
    }
    try {
        return JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
    } catch (\Throwable $e) {
        return null;
    }
}

header("Content-Type: application/json");
// 1. Validar JWT
$auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if (!$auth_header) {
    http_response_code(401);
    echo json_encode(["success" => false, "message" => "No se proporcionó token de autorización."]);
    exit;
}
list($token_type, $token) = explode(' ', $auth_header, 2);
if (strcasecmp($token_type, 'Bearer') !== 0 || !$token) {
    http_response_code(401);
    echo json_encode(["success" => false, "message" => "Formato de token inválido."]);
    exit;
}
$decoded_token = validate_jwt_token($token);
if (!$decoded_token) {
    http_response_code(401);
    echo json_encode(["success" => false, "message" => "Token inválido o expirado."]);
    exit;
}
// $user_client_id = $decoded_token->data->client_id ?? null;
// 2. Obtener y validar lead_id del cuerpo JSON
$data = json_decode(file_get_contents("php://input"));
if (!$data || !isset($data->lead_id)) {
    http_response_code(400);
    echo json_encode(["success" => false, "message" => "Datos JSON inválidos o lead_id faltante."]);
    exit;
}
$lead_id = filter_var($data->lead_id, FILTER_VALIDATE_INT);
if (!$lead_id || $lead_id <= 0) {
    http_response_code(400);
    echo json_encode(["success" => false, "message" => "lead_id debe ser un entero positivo."]);
    exit;
}
// Usar el singleton de Database en lugar de intentar instanciar directamente
$conn = Database::getInstance()->getConnection();
$conn->beginTransaction(); // Iniciar transacción
// 3. (Opcional) Verificar permisos
/*
if ($user_client_id) {
    $stmt_perm = $conn->prepare("SELECT cliente_valorador_id FROM valorador_leads WHERE id = :lead_id");
    $stmt_perm->bindParam(':lead_id', $lead_id);
    $stmt_perm->execute();
    $lead_owner = $stmt_perm->fetch(PDO::FETCH_ASSOC);
    if (!$lead_owner || $lead_owner['cliente_valorador_id'] !== $user_client_id) {
        http_response_code(403);
        echo json_encode(["success" => false, "message" => "No tienes permiso para modificar este lead."]);
        $conn->rollBack();
        exit;
    }
}
*/
// 4. Actualizar estado_secuencia_lead en valorador_leads
try {
    $nuevo_estado_secuencia = 'active';

    // Obtener el estado actual y el paso actual de la secuencia
    $stmt_check = $conn->prepare("SELECT id, status, current_step_id FROM lead_sequence_tracking WHERE lead_id = :lead_id");
    $stmt_check->bindParam(':lead_id', $lead_id, PDO::PARAM_INT);
    $stmt_check->execute();
    $tracking_record = $stmt_check->fetch(PDO::FETCH_ASSOC);

    if (!$tracking_record) {
        throw new Exception("No se encontró un registro de seguimiento de secuencia para este lead.");
    }

    // Solo se puede reanudar si está pausada por el usuario
    if ($tracking_record['status'] !== 'paused_by_user') {
        http_response_code(400);
        echo json_encode(["success" => false, "message" => "La secuencia no puede ser reanudada. Estado actual: " . $tracking_record['status'], "newState" => $tracking_record['status']]);
        exit;
    }

    // Obtener el intervalo de días para el siguiente paso
    $stmt_step = $conn->prepare("SELECT delay_days FROM sequence_steps WHERE id = :step_id");
    $stmt_step->bindParam(':step_id', $tracking_record['current_step_id'], PDO::PARAM_INT);
    $stmt_step->execute();
    $step_info = $stmt_step->fetch(PDO::FETCH_ASSOC);

    if (!$step_info) {
        throw new Exception("No se pudo encontrar la información del paso actual de la secuencia (ID: {$tracking_record['current_step_id']}).");
    }

    // Calcular la nueva fecha de vencimiento y actualizar el registro
    $delay_days = (int)$step_info['delay_days'];
    $next_due_date = (new DateTime())->modify("+{$delay_days} days")->format('Y-m-d H:i:s');

    $stmt_update = $conn->prepare("
        UPDATE lead_sequence_tracking
        SET status = :status, next_due_date = :next_due_date, updated_at = NOW()
        WHERE id = :id
    ");
    $stmt_update->bindParam(':status', $nuevo_estado_secuencia, PDO::PARAM_STR);
    $stmt_update->bindParam(':next_due_date', $next_due_date, PDO::PARAM_STR);
    $stmt_update->bindParam(':id', $tracking_record['id'], PDO::PARAM_INT);
    $stmt_update->execute();

    $conn->commit();

    http_response_code(200);
    echo json_encode(["success" => true, "message" => "Secuencia reanudada. El próximo email se programará para aproximadamente en {$delay_days} día(s).", "newState" => $nuevo_estado_secuencia]);

} catch (PDOException $e) {
    $conn->rollBack();
    http_response_code(500);
    echo json_encode(["success" => false, "message" => "Error en la base de datos: " . $e->getMessage()]);
} catch (Exception $e) {
    $conn->rollBack();
    http_response_code(500);
    echo json_encode(["success" => false, "message" => "Error inesperado: " . $e->getMessage()]);
} finally {
    if ($conn) {
        $conn = null;
    }
}
?> 