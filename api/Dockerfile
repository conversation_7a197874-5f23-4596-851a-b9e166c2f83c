# Usar imagen base PHP con Apache
FROM php:8.2-apache

# Instalar dependencias del sistema y wkhtmltopdf
RUN apt-get update && apt-get install -y --no-install-recommends \
    # Dependencias básicas de PHP
    libzip-dev \
    zip \
    unzip \
    git \
    curl \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libonig-dev \
    libxml2-dev \
    # wkhtmltopdf y dependencias
    wkhtmltopdf \
    xvfb \
    xauth \
    # Fuentes adicionales
    fonts-liberation \
    fonts-dejavu-core \
    fontconfig \
    fonts-noto-color-emoji \
    # Limpiar cache
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Configurar extensiones PHP
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo \
        pdo_mysql \
        mysqli \
        zip \
        gd \
        mbstring \
        xml \
        bcmath

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Habilitar mod_rewrite de Apache
# Habilitar mod_rewrite y env para exponer variables de entorno a PHP
RUN a2enmod rewrite && a2enmod env

# Copiar configuración personalizada de PHP para asegurar la carga de variables de entorno
COPY zz-custom-php.ini /usr/local/etc/php/conf.d/zz-custom-php.ini

# Establecer directorio de trabajo
WORKDIR /var/www/html

# Copiar solo los archivos de Composer primero para cachear las dependencias
COPY composer.json composer.lock* /var/www/html/

# Instalar dependencias PHP
RUN composer install --no-dev --optimize-autoloader



# Copiar el resto de los archivos de la aplicación
COPY . /var/www/html/

# Crear directorio de logs y dar permisos al usuario de Apache
RUN mkdir -p /var/www/logs && chown -R www-data:www-data /var/www/logs

# Crear wrapper para wkhtmltopdf con xvfb
RUN echo '#!/bin/bash\nxvfb-run -a --server-args="-screen 0, 1024x768x24" wkhtmltopdf "$@"' > /usr/local/bin/wkhtmltopdf-wrapper \
    && chmod +x /usr/local/bin/wkhtmltopdf-wrapper 

# Configurar permisos
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Variables de entorno
ENV WKHTMLTOPDF_PATH=/usr/local/bin/wkhtmltopdf-wrapper
# Valor por defecto para evitar fallo de CORS si la variable no se inyecta
ENV ALLOWED_ORIGINS="https://inmoautomation.com,https://app.inmoautomation.com,https://*.inmoautomation.com"

# Configurar Apache para Cloud Run
RUN sed -i 's/Listen 80/Listen 8080/' /etc/apache2/ports.conf
RUN sed -i 's/<VirtualHost \*:80>/<VirtualHost *:8080>/' /etc/apache2/sites-available/000-default.conf
# Pasar explícitamente todas las variables de entorno requeridas al entorno de Apache.
# Esto es más robusto y seguro que usar 'PassEnv *'.
RUN echo "PassEnv APP_ENV API_URL LANDING_PAGE_URL DASHBOARD_URL APP_BASE_URL ALLOWED_ORIGINS DB_HOST DB_SOCKET DB_NAME DB_USER STRIPE_PRICE_ID_MONTHLY STRIPE_PRICE_ID_ANNUAL DEFAULT_SENDER_EMAIL DEFAULT_SENDER_NAME GOOGLE_CLOUD_STORAGE_BUCKET GOOGLE_CLOUD_PROJECT_ID LOGOS_BASE_URL GOOGLE_CLOUD_REPORTS_BUCKET REPORTS_BASE_URL VALORADOR_API_ENDPOINT_URL CLOUD_RUN_INVOKER_SERVICE_ACCOUNT VALORADOR_DB_HOST VALORADOR_DB_NAME VALORADOR_DB_USER STRIPE_SECRET_KEY STRIPE_WEBHOOK_SECRET DB_PASS JWT_SECRET_KEY BREVO_API_KEY GEMINI_API_KEY OPENAI_API_KEY GOOGLE_PLACES_API_KEY VALORADOR_API_SECRET_KEY VALORADOR_DB_PASS" >> /etc/apache2/apache2.conf

# Exponer puerto 8080
EXPOSE 8080

# Comando de inicio
CMD ["apache2-foreground"]
