<?php
// Script para verificar las tablas existentes en la base de datos y su estructura

// Habilitar la visualización de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Configurar encabezados
header('Content-Type: text/html; charset=utf-8');

// Cargar el archivo .env
$autoloadPath = __DIR__ . '/vendor/autoload.php';
if (!file_exists($autoloadPath)) {
    die("Error: vendor/autoload.php no encontrado");
}
require_once $autoloadPath;

try {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
} catch (Exception $e) {
    die("Error al cargar .env: " . $e->getMessage());
}

// Conectar a la base de datos
$db = new mysqli($_ENV['DB_HOST'], $_ENV['DB_USER'], $_ENV['DB_PASS'], $_ENV['DB_NAME']);

if ($db->connect_error) {
    die("Error de conexión: " . $db->connect_error);
}

echo "<h1>Información de la Base de Datos</h1>";
echo "<p>Host: " . htmlspecialchars($_ENV['DB_HOST']) . "</p>";
echo "<p>Base de datos: " . htmlspecialchars($_ENV['DB_NAME']) . "</p>";
echo "<p>Usuario: " . htmlspecialchars($_ENV['DB_USER']) . "</p>";

// Obtener todas las tablas
$result = $db->query("SHOW TABLES");
if (!$result) {
    die("Error al obtener tablas: " . $db->error);
}

echo "<h2>Tablas en la base de datos</h2>";
echo "<ul>";
$tables = [];
while ($row = $result->fetch_row()) {
    $tableName = $row[0];
    $tables[] = $tableName;
    echo "<li><strong>" . htmlspecialchars($tableName) . "</strong></li>";
}
echo "</ul>";

// Mostrar la estructura de cada tabla
echo "<h2>Estructura de las tablas</h2>";
foreach ($tables as $table) {
    $columns = $db->query("SHOW COLUMNS FROM `$table`");
    if (!$columns) {
        echo "<p>Error al obtener columnas de $table: " . $db->error . "</p>";
        continue;
    }
    
    echo "<h3>Tabla: " . htmlspecialchars($table) . "</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>";
    
    while ($column = $columns->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Mostrar algunos datos de ejemplo
    $data = $db->query("SELECT * FROM `$table` LIMIT 5");
    if (!$data) {
        echo "<p>Error al obtener datos de $table: " . $db->error . "</p>";
        continue;
    }
    
    if ($data->num_rows > 0) {
        echo "<h4>Datos de ejemplo (hasta 5 filas)</h4>";
        echo "<table border='1' cellpadding='5'>";
        
        // Encabezados
        echo "<tr>";
        $fields = $data->fetch_fields();
        foreach ($fields as $field) {
            echo "<th>" . htmlspecialchars($field->name) . "</th>";
        }
        echo "</tr>";
        
        // Datos
        $data->data_seek(0); // Reiniciar el puntero
        while ($row = $data->fetch_assoc()) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No hay datos en esta tabla.</p>";
    }
}

// Cerrar la conexión
$db->close();
?>
