<?php
/**
 * perform_password_reset.php
 * Endpoint que consume el token y establece la nueva contraseña.
 * POST JSON: { "token": "...", "password": "nueva" }
 */

declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;

header('Content-Type: application/json');

$input = json_decode(file_get_contents('php://input'), true);
$token = trim($input['token'] ?? '');
$password = $input['password'] ?? '';

if (!$token || strlen($password) < 8) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Datos inválidos.']);
    exit;
}

try {
    $pdo = Database::getInstance()->getConnection();

    // Verificar token válido y no expirado
    $stmt = $pdo->prepare('SELECT prt.user_id, u.email FROM password_reset_tokens prt JOIN usuarios u ON u.id = prt.user_id WHERE prt.token = :token AND prt.expires_at > NOW() LIMIT 1');
    $stmt->bindParam(':token', $token, PDO::PARAM_STR);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$row) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.']);
        exit;
    }

    // Actualizar contraseña
    $hash = password_hash($password, PASSWORD_DEFAULT);
    $upd = $pdo->prepare('UPDATE usuarios SET password_hash = :hash WHERE id = :uid');
    $upd->execute([':hash' => $hash, ':uid' => $row['user_id']]);

    // Eliminar token
    $pdo->prepare('DELETE FROM password_reset_tokens WHERE token = :token')->execute([':token' => $token]);

    echo json_encode(['success' => true, 'message' => 'Contraseña actualizada correctamente.']);
} catch (Throwable $e) {
    error_log('[perform_password_reset] ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}
