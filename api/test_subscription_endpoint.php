<?php
declare(strict_types=1);

// Script para probar subscription.php localmente con un token JWT simulado

require_once __DIR__ . '/config/bootstrap.php';

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

echo "=== PRUEBA DE subscription.php ENDPOINT ===\n\n";

// Crear un token JWT válido para el usuario 192
$payload = [
    'iat' => time(),
    'exp' => time() + 3600,
    'iss' => 'inmoautomation.com',
    'user_id' => 192,
    'agency_id' => 71,
    'is_agency_owner' => true,
    'email' => '<EMAIL>'
];

$token = JWT::encode($payload, JWT_SECRET, 'HS256');
echo "Token JWT generado: " . substr($token, 0, 50) . "...\n\n";

// Simular las variables de entorno HTTP
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer ' . $token;

// Capturar la salida del endpoint
ob_start();

try {
    // Incluir el endpoint subscription.php
    include __DIR__ . '/subscription.php';
    
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "Salida del endpoint:\n";
    echo "Longitud: " . strlen($output) . " bytes\n";
    
    if (strlen($output) > 0) {
        echo "✅ El endpoint generó respuesta\n";
        
        // Verificar si es JSON válido
        $decoded = json_decode($output, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ JSON válido\n";
            echo "Campos principales:\n";
            echo "  - status: " . ($decoded['status'] ?? 'NO DEFINIDO') . "\n";
            echo "  - planName: " . ($decoded['planName'] ?? 'NO DEFINIDO') . "\n";
            echo "  - agency_id: " . ($decoded['agency_id'] ?? 'NO DEFINIDO') . "\n";
        } else {
            echo "❌ JSON inválido: " . json_last_error_msg() . "\n";
            echo "Primeros 200 caracteres:\n" . substr($output, 0, 200) . "\n";
        }
    } else {
        echo "❌ El endpoint no generó respuesta (respuesta vacía)\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Error al ejecutar el endpoint: " . $e->getMessage() . "\n";
}

echo "\n=== FIN DE LA PRUEBA ===\n";
?>
