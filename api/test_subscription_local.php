<?php
declare(strict_types=1);

require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;

echo "=== INVESTIGACIÓN PROFUNDA DE subscription.php ===\n\n";

// Simular los datos del JWT que tendría un usuario real
$test_user_id = 192; // Usuario real: <EMAIL>
$test_agency_id = 71; // Agencia real: Impacto Group Ltd

echo "1. DATOS DE PRUEBA:\n";
echo "   user_id: $test_user_id\n";
echo "   agency_id: $test_agency_id\n\n";

try {
    $pdo = Database::getInstance()->getConnection();
    echo "2. CONEXIÓN A BD: ✅ Establecida\n\n";
    
    // Paso 1: Verificar que la agencia existe y obtener owner_user_id
    echo "3. VERIFICACIÓN DE AGENCIA:\n";
    $stmt_owner = $pdo->prepare("SELECT owner_user_id, name FROM agencies WHERE id = ?");
    $stmt_owner->execute([$test_agency_id]);
    $agency_data = $stmt_owner->fetch();
    
    if (!$agency_data) {
        echo "   ❌ ERROR: No se encontró la agencia con ID $test_agency_id\n";
        exit;
    }
    
    echo "   ✅ Agencia encontrada: {$agency_data['name']}\n";
    echo "   ✅ owner_user_id: {$agency_data['owner_user_id']}\n\n";
    
    $owner_user_id = $agency_data['owner_user_id'];
    
    // Paso 2: Cargar PlanFeatures.php y probar getUserActivePlanDetails
    echo "4. CARGA DE PlanFeatures:\n";
    require_once __DIR__ . '/utils/PlanFeatures.php';
    echo "   ✅ PlanFeatures.php cargado\n";
    
    // Usar la clase PlanFeatures exactamente como lo hace subscription.php
    $planDetailsFromDB = \Api\Utils\PlanFeatures::getUserActivePlanDetails($owner_user_id, $pdo);
    
    if ($planDetailsFromDB) {
        echo "   ✅ Plan encontrado: {$planDetailsFromDB['plan_name']}\n";
        echo "   ✅ Estado: {$planDetailsFromDB['subscription_status']}\n";
        echo "   ✅ Stripe Subscription ID: {$planDetailsFromDB['stripe_subscription_id']}\n";
    } else {
        echo "   ❌ ERROR: No se encontró plan activo para user_id $owner_user_id\n";
        exit;
    }
    
    // Paso 3: Verificar stripe_customer_id
    echo "\n5. VERIFICACIÓN DE STRIPE CUSTOMER:\n";
    $query_user = "SELECT stripe_customer_id FROM usuarios WHERE id = ?";
    $stmt_user = $pdo->prepare($query_user);
    $stmt_user->execute([$owner_user_id]);
    $user_data = $stmt_user->fetch();
    
    if (!$user_data || !$user_data['stripe_customer_id']) {
        echo "   ❌ ERROR: No se encontró stripe_customer_id para user_id $owner_user_id\n";
        exit;
    }
    
    echo "   ✅ stripe_customer_id: {$user_data['stripe_customer_id']}\n";
    
    // Paso 4: Simular la llamada a Stripe (sin hacer la llamada real)
    echo "\n6. SIMULACIÓN DE RESPUESTA STRIPE:\n";
    echo "   ✅ Subscription ID para consultar: {$planDetailsFromDB['stripe_subscription_id']}\n";
    echo "   ✅ Customer ID para consultar: {$user_data['stripe_customer_id']}\n";
    
    // Paso 5: Construir respuesta como lo haría subscription.php
    echo "\n7. CONSTRUCCIÓN DE RESPUESTA:\n";
    
    $response_data = [
        'id' => $owner_user_id,
        'agency_id' => $test_agency_id,
        'is_agency_owner' => true,
        'user_data_response' => [
            'agency_name' => $agency_data['name'],
            'email' => null,
            'nombre_completo' => null,
            'stripe_customer_id' => $user_data['stripe_customer_id']
        ],
        'status' => $planDetailsFromDB['subscription_status'],
        'planId' => $planDetailsFromDB['plan_slug'],
        'planName' => $planDetailsFromDB['plan_name'],
        'price' => $planDetailsFromDB['price_monthly'],
        'billingCycle' => $planDetailsFromDB['billing_cycle'],
        'plan_features' => [
            'id' => $planDetailsFromDB['plan_id'],
            'name' => $planDetailsFromDB['plan_name'],
            'slug' => $planDetailsFromDB['plan_slug'],
            'max_dashboard_users' => (int)$planDetailsFromDB['max_dashboard_users'],
            'allow_multiple_sequences' => (bool)$planDetailsFromDB['allow_multiple_sequences'],
            'allow_custom_domain_email' => (bool)$planDetailsFromDB['allow_custom_domain_email'],
            'max_valoradores' => (int)$planDetailsFromDB['max_valoradores'],
            'has_analytics' => (bool)$planDetailsFromDB['has_analytics'],
            'additional_config' => $planDetailsFromDB['additional_config'] ?? []
        ]
    ];
    
    echo "   ✅ Respuesta construida exitosamente\n";
    echo "   ✅ Tamaño de respuesta: " . strlen(json_encode($response_data)) . " bytes\n";
    
    // Paso 6: Verificar que el JSON es válido
    $json_response = json_encode($response_data);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "   ✅ JSON válido generado\n";
    } else {
        echo "   ❌ ERROR en JSON: " . json_last_error_msg() . "\n";
    }
    
    echo "\n8. RESPUESTA FINAL (primeros 200 caracteres):\n";
    echo "   " . substr($json_response, 0, 200) . "...\n";
    
} catch (Exception $e) {
    echo "❌ ERROR GENERAL: " . $e->getMessage() . "\n";
    echo "   Archivo: " . $e->getFile() . "\n";
    echo "   Línea: " . $e->getLine() . "\n";
}

echo "\n=== FIN DE LA INVESTIGACIÓN ===\n";
?>
