<?php
declare(strict_types=1);
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Api\lib\Database;
// --- Importar clases de JWT ---
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;
// Función de log local (considerar reemplazar con Logger centralizado de bootstrap.php)
if (!function_exists('custom_log_agency_dashboard')) {
    function custom_log_agency_dashboard($message) {
        $logFile = __DIR__ . '/logs/debug_agency_email_dashboard.log';
        if (!is_dir(__DIR__ . '/logs')) {
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                error_log("Advertencia [agency_email_dashboard]: No se pudo crear el directorio de logs: " . __DIR__ . '/logs');
                return;
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [agency_email_dashboard] {$message}\n", 3, $logFile);
    }
}
// --- Función para obtener y decodificar el token JWT localmente ---
function get_decoded_jwt_payload_agency_emails() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded;
            } catch (\Throwable $e) {
                // custom_log_agency_dashboard("JWT Decode Error: " . $e->getMessage());
                if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                    ini_set('display_errors', $previous_display_errors);
                }
            }
        }
    }
    return null;
}
$decoded_payload = get_decoded_jwt_payload_agency_emails();
$user_id = $decoded_payload->user_id ?? null;
$agency_id_from_jwt = $decoded_payload->agency_id ?? null;
$roles = isset($decoded_payload->roles) && is_array($decoded_payload->roles) ? $decoded_payload->roles : (isset($decoded_payload->roles) && is_object($decoded_payload->roles) ? (array)$decoded_payload->roles : []);
$is_super_admin = in_array('admin', $roles);
// custom_log_agency_dashboard("Script iniciado. User ID: {$user_id}, Agency ID: {$agency_id_from_jwt}, SuperAdmin: " . ($is_super_admin ? 'Yes' : 'No'));
if (!$user_id) {
    http_response_code(401);
    // custom_log_agency_dashboard("ERROR: Usuario no identificado. Token inválido o no proporcionado.");
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Usuario no identificado.']);
    exit;
}
// Asegurarse de que los no-admins tengan un agency_id
if (!$is_super_admin && !$agency_id_from_jwt) {
    http_response_code(403);
    custom_log_agency_dashboard("ERROR: Usuario no admin sin agency_id. User ID: {$user_id}");
    echo json_encode(['success' => false, 'message' => 'Acceso denegado: Información de agencia no disponible para el usuario.']);
    exit;
}
// --- INICIO DE NUEVA LÓGICA PARA ESTADÍSTICAS ---
/**
 * Calcula el porcentaje de cambio.
 * @param float|int|null $current El valor actual.
 * @param float|int|null $previous El valor anterior.
 * @return float|null Retorna el porcentaje de cambio o null si no se puede calcular.
 */
function calculate_change_percent($current, $previous): ?float {
    if ($previous === null || $current === null) {
        return null;
    }
    if ($previous == 0) {
        if ($current > 0) {
            return 100.0; 
        }
        return 0.0; 
    }
    $change = (($current - $previous) / $previous) * 100;
    return round($change, 1); // Redondear a 1 decimal
}
/**
 * Obtiene las estadísticas básicas de email para un periodo dado.
 * @param PDO $pdo Conexión PDO.
 * @param int $agency_id ID de la agencia.
 * @param string $period 'current' para mes actual, 'previous' para mes anterior.
 * @return array Array con las estadísticas.
 */
function get_email_stats_for_period(PDO $pdo, int $agency_id, string $period): array {
    $stats = [
        'emailsSentThisMonth' => 0,
        'activeNurturingLeads' => 0,
        'totalScheduledEmails' => 0,
        'uniqueLeadsContactedThisMonth' => 0
    ];
    $date_ref = 'CURDATE()';
    if ($period === 'previous') {
        $date_ref = 'CURDATE() - INTERVAL 1 MONTH';
    }
    // 1. Emails Enviados en el Mes
    $sql_sent = "SELECT COUNT(leh.id)
                 FROM lead_emails_historial leh
                 JOIN valorador_leads vl ON leh.lead_id = vl.id
                 JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                 WHERE leh.estado_envio = 'sent'
                   AND MONTH(leh.fecha_envio_real) = MONTH($date_ref)
                   AND YEAR(leh.fecha_envio_real) = YEAR($date_ref)
                   AND cv.agency_id = :agency_id";
    $stmt_sent = $pdo->prepare($sql_sent);
    $stmt_sent->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
    $stmt_sent->execute();
    $stats['emailsSentThisMonth'] = (int)$stmt_sent->fetchColumn();
    // 2. Leads en Nutrición Activa 
    // Este valor es el actual, no histórico del periodo. La comparación con el "mes anterior" será con el mismo valor actual.
    // Se podría devolver null para changePercent o buscar una lógica de snapshot si fuera necesaria.
    $sql_nurturing = "SELECT COUNT(DISTINCT vl.id)
                      FROM lead_sequence_tracking lst
                      JOIN valorador_leads vl ON lst.lead_id = vl.id
                      JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                      WHERE lst.status = 'active'
                        AND cv.agency_id = :agency_id";
    $stmt_nurturing = $pdo->prepare($sql_nurturing);
    $stmt_nurturing->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
    $stmt_nurturing->execute();
    $stats['activeNurturingLeads'] = (int)$stmt_nurturing->fetchColumn();
    // 3. Total Emails Programados (estado actual)
    // Similar a 'activeNurturingLeads', la comparación mensual se hace con el valor actual.
    $sql_scheduled = "SELECT COUNT(leh.id)
                      FROM lead_emails_historial leh
                      JOIN valorador_leads vl ON leh.lead_id = vl.id
                      JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                      WHERE leh.estado_envio = 'scheduled'
                        AND cv.agency_id = :agency_id";
    $stmt_scheduled = $pdo->prepare($sql_scheduled);
    $stmt_scheduled->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
    $stmt_scheduled->execute();
    $stats['totalScheduledEmails'] = (int)$stmt_scheduled->fetchColumn();
    // 4. Leads Únicos Contactados en el Mes
    $sql_contacted = "SELECT COUNT(DISTINCT leh.lead_id)
                      FROM lead_emails_historial leh
                      JOIN valorador_leads vl ON leh.lead_id = vl.id
                      JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                      WHERE leh.estado_envio = 'sent'
                        AND MONTH(leh.fecha_envio_real) = MONTH($date_ref)
                        AND YEAR(leh.fecha_envio_real) = YEAR($date_ref)
                        AND cv.agency_id = :agency_id";
    $stmt_contacted = $pdo->prepare($sql_contacted);
    $stmt_contacted->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
    $stmt_contacted->execute();
    $stats['uniqueLeadsContactedThisMonth'] = (int)$stmt_contacted->fetchColumn();
    return $stats;
}
$basic_stats_response = [
    'emailsSentThisMonth' => ['value' => 0, 'changePercent' => null],
    'activeNurturingLeads' => ['value' => 0, 'changePercent' => null],
    'totalScheduledEmails' => ['value' => 0, 'changePercent' => null],
    'uniqueLeadsContactedThisMonth' => ['value' => 0, 'changePercent' => null]
];
if ($agency_id_from_jwt && !$is_super_admin) {
    try {
        $pdo_stats = Database::getInstance()->getConnection();
        $current_period_stats = get_email_stats_for_period($pdo_stats, (int)$agency_id_from_jwt, 'current');
        $previous_period_stats = get_email_stats_for_period($pdo_stats, (int)$agency_id_from_jwt, 'previous');
        $basic_stats_response = [
            'emailsSentThisMonth' => [
                'value' => $current_period_stats['emailsSentThisMonth'],
                'changePercent' => calculate_change_percent($current_period_stats['emailsSentThisMonth'], $previous_period_stats['emailsSentThisMonth'])
            ],
            'activeNurturingLeads' => [ // Como se mencionó, la comparación de 'previous' es con el valor actual.
                'value' => $current_period_stats['activeNurturingLeads'],
                'changePercent' => calculate_change_percent($current_period_stats['activeNurturingLeads'], $previous_period_stats['activeNurturingLeads'])
            ],
            'totalScheduledEmails' => [ // Similar para programados.
                'value' => $current_period_stats['totalScheduledEmails'],
                'changePercent' => null
            ],
            'uniqueLeadsContactedThisMonth' => [
                'value' => $current_period_stats['uniqueLeadsContactedThisMonth'],
                'changePercent' => calculate_change_percent($current_period_stats['uniqueLeadsContactedThisMonth'], $previous_period_stats['uniqueLeadsContactedThisMonth'])
            ]
        ];
    } catch (PDOException $e) {
        custom_log_agency_dashboard("PDO EXCEPTION (Stats Calculation): " . $e->getMessage());
        // $basic_stats_response ya tiene valores por defecto
    }
}
// --- FIN DE NUEVA LÓGICA PARA ESTADÍSTICAS ---
try {
    $pdo = Database::getInstance()->getConnection();
    // custom_log_agency_dashboard("Conexión a BD establecida.");
    $client_identifiers_for_query = [];
    $placeholders_client_ids = '';
    if (!$is_super_admin) {
        if (!$agency_id_from_jwt) {
            http_response_code(403);
            // custom_log_agency_dashboard("ERROR: Usuario no admin sin agency_id. User ID: {$user_id}");
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: Información de agencia no disponible.', 'basic_stats' => $basic_stats_response]);
            exit;
        }
        // MODIFICADO: Permitir acceso a datos históricos incluso con valoradores inactivos
        $stmt_clients = $pdo->prepare("SELECT id FROM clientes_valorador WHERE agency_id = :agency_id");
        $stmt_clients->bindParam(':agency_id', $agency_id_from_jwt, PDO::PARAM_INT);
        $stmt_clients->execute();
        $client_identifiers_for_query = $stmt_clients->fetchAll(PDO::FETCH_COLUMN);
        if (empty($client_identifiers_for_query)) {
            // custom_log_agency_dashboard("INFO: Agencia ID {$agency_id_from_jwt} no tiene valoradores activos. Devolviendo lista vacía.");
            echo json_encode([
                'success' => true, 
                'data' => [], 
                'pagination' => ['currentPage' => 1, 'totalPages' => 1, 'totalItems' => 0, 'limit' => 25], // Considerar el limit que llega por GET
                'basic_stats' => $basic_stats_response
            ]);
            exit;
        }
        $placeholders_client_ids = implode(',', array_fill(0, count($client_identifiers_for_query), '?'));
    }
    // custom_log_agency_dashboard("Client identifiers para la consulta: " . implode(', ', $client_identifiers_for_query));
    // Paginación
    $page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT, ['options' => ['default' => 1, 'min_range' => 1]]);
    $limit_param = filter_input(INPUT_GET, 'limit', FILTER_VALIDATE_INT, ['options' => ['default' => 25, 'min_range' => 1, 'max_range' => 100]]);
    $offset = ($page - 1) * $limit_param;
    // custom_log_agency_dashboard("Paginación: Page={$page}, Limit={$limit_param}, Offset={$offset}");
    // Contar total de items para paginación
    $status_filter = filter_input(INPUT_GET, 'status', FILTER_SANITIZE_STRING);
    if ($status_filter === 'all' || empty($status_filter)) {
        $status_filter = null;
    } else {
        $allowed_statuses = ['scheduled', 'sending', 'sent', 'failed', 'cancelled', 'pending_initial'];
        if (!in_array($status_filter, $allowed_statuses)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid status filter value.']);
            exit;
        }
    }

    $sql_total_items = "SELECT COUNT(leh.id) as total
                         FROM lead_emails_historial leh
                         JOIN valorador_leads vl ON leh.lead_id = vl.id";
    
    $where_clauses_total = [];
    $params_total = [];

    if (!$is_super_admin) {
        $where_clauses_total[] = "vl.cliente_valorador_id IN ({$placeholders_client_ids})";
        $params_total = $client_identifiers_for_query;
    }

    if ($status_filter) {
        $where_clauses_total[] = "leh.estado_envio = ?";
        $params_total[] = $status_filter;
    }

    if (!empty($where_clauses_total)) {
        $sql_total_items .= " WHERE " . implode(' AND ', $where_clauses_total);
    }

    $stmt_total = $pdo->prepare($sql_total_items);
    $stmt_total->execute($params_total);
    $total_items = (int)$stmt_total->fetchColumn();
    // custom_log_agency_dashboard("Total items para paginación: {$total_items}");
    $total_pages = $total_items > 0 ? (int)ceil($total_items / $limit_param) : 1;
    if ($page > $total_pages && $total_items > 0) {
        $page = $total_pages;
        $offset = ($page - 1) * $limit_param;
        // custom_log_agency_dashboard("Página ajustada a: {$page}, Offset ajustado a: {$offset}");
    }
    $sql_emails = "SELECT
                        leh.id, leh.uuid, leh.tipo_email,
                        COALESCE(leh.asunto_final, leh.borrador_asunto) as asunto_final,
                        leh.borrador_asunto,
                        leh.borrador_cuerpo_html,
                        leh.cuerpo_final_html,
                        leh.estado_envio,
                        leh.fecha_envio_real,
                        leh.fecha_programada_envio,
                        leh.abierto_timestamp, leh.clickeado_timestamp,
                        vl.nombre AS lead_nombre,
                        vl.email AS lead_email,
                        vl.id AS lead_id,
                        vl.cliente_valorador_id AS valorador_client_id,
                        lst.status AS lead_estado_secuencia,
                        lst.sequence_id AS lead_id_secuencia_asignada,
                        ss.name AS nombre_paso_secuencia_actual,
                        ss.step_order AS orden_paso_secuencia_actual,
                        (SELECT COUNT(*) FROM sequence_steps ss_count WHERE ss_count.sequence_id = lst.sequence_id) AS total_pasos_en_secuencia
                     FROM
                        lead_emails_historial leh
                     JOIN
                        valorador_leads vl ON leh.lead_id = vl.id
                     LEFT JOIN
                        clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                     LEFT JOIN
                        lead_sequence_tracking lst ON vl.id = lst.lead_id
                     LEFT JOIN
                        sequence_steps ss ON lst.current_step_id = ss.id";

    $where_clauses_main = [];
    $params_main = [];

    if (!$is_super_admin) {
        $where_clauses_main[] = "cv.id IN ({$placeholders_client_ids})";
        $params_main = $client_identifiers_for_query;
    }
    
    if ($status_filter) {
        $where_clauses_main[] = "leh.estado_envio = ?";
        $params_main[] = $status_filter;
    }

    if (!empty($where_clauses_main)) {
        $sql_emails .= " WHERE " . implode(' AND ', $where_clauses_main);
    }

    $sql_emails .= " ORDER BY COALESCE(leh.fecha_programada_envio, leh.fecha_envio_real) DESC, leh.id DESC LIMIT ? OFFSET ?";
    $params_main[] = $limit_param;
    $params_main[] = $offset;

    $stmt_emails = $pdo->prepare($sql_emails);
    
    $param_idx = 1;
    foreach ($params_main as $param) {
        $stmt_emails->bindValue($param_idx++, $param, is_int($param) ? PDO::PARAM_INT : PDO::PARAM_STR);
    }

    $stmt_emails->execute();
    $email_items = $stmt_emails->fetchAll(PDO::FETCH_ASSOC);
    // custom_log_agency_dashboard(count($email_items) . " emails recuperados.");
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "data" => $email_items,
        "pagination" => [
            "currentPage" => $page,
            "totalPages" => $total_pages,
            "totalItems" => $total_items,
            "limit" => $limit_param
        ],
        "basic_stats" => $basic_stats_response
    ]);
} catch (PDOException $e) {
    custom_log_agency_dashboard("ERROR PDO (Main Query): " . $e->getMessage() . " SQL: " . ($sql_emails ?? 'No SQL for emails') . " Trace: " . $e->getTraceAsString());
    error_log("[agency_email_dashboard.php] PDOException: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error interno del servidor (DB).',
        'basic_stats' => $basic_stats_response 
    ]);
} catch (Exception $e) {
    custom_log_agency_dashboard("ERROR Exception (Main Processing): " . $e->getMessage() . " Trace: " . $e->getTraceAsString());
    error_log("[agency_email_dashboard.php] Exception: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error interno del servidor.',
        'basic_stats' => $basic_stats_response
    ]);
}
?> 