# ===================================
# ENTORNO Y URLS
# ===================================
# production o development
APP_ENV=

# URLs de la aplicación
API_URL=
LANDING_PAGE_URL=
DASHBOARD_URL=
APP_BASE_URL=

# Orígenes CORS permitidos (separados por comas, sin espacios)
ALLOWED_ORIGINS=

# ===================================
# BASE DE DATOS
# ===================================
# Configuración para desarrollo local con Cloud SQL Proxy
# 1. Instalar el proxy: https://cloud.google.com/sql/docs/mysql/connect-admin-proxy
# 2. Iniciar el proxy: cloud_sql_proxy -instances=inmoautomation:us-central1:inmoautomation-db=tcp:3306
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=s_valorador
DB_USER=inmoautomation-db
DB_PASS=

# Para producción en Cloud Run (usando el conector de Cloud SQL)
# No es necesario configurar nada aquí, se usará el socket proporcionado por Cloud Run
# en /cloudsql/inmoautomation:us-central1:inmoautomation-db

# ===================================
# CLAVES DE APIS Y SERVICIOS
# ===================================
# Stripe
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
STRIPE_PRICE_ID_MONTHLY=
STRIPE_PRICE_ID_ANNUAL=

# Autenticación
JWT_SECRET_KEY=

# Email (Brevo)
BREVO_API_KEY=
DEFAULT_SENDER_EMAIL=
DEFAULT_SENDER_NAME=

# IA (Google & OpenAI)
GEMINI_API_KEY=
OPENAI_API_KEY=

# ===================================
# GOOGLE CLOUD STORAGE
# ===================================
# Bucket para logos
GOOGLE_CLOUD_STORAGE_BUCKET=
LOGOS_BASE_URL=

# Bucket para informes PDF
GOOGLE_CLOUD_REPORTS_BUCKET=
REPORTS_BASE_URL=

# ID del proyecto de GCP
GOOGLE_CLOUD_PROJECT_ID=