<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// /api/update-payment-method.php - Actualiza el método de pago del usuario
// bootstrap.php se encargará de la configuración de errores y carga de dependencias.
// --- Logger Setup (específico para este endpoint, se mantiene por ahora) ---
if (!function_exists('custom_log_payment_method')) {
    function custom_log_payment_method($message) {
        $logFile = __DIR__ . '/debug_payment_method.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_payment_method("--- [update-payment-method.php] INICIADO ---");
// --- Autoload y Carga de .env son manejados por bootstrap.php ---
// ... (código de carga de autoload y .env eliminado)
// --- Verificación de autenticación ---
$userId = null;
$agencyId = null;
$isAgencyOwner = false;
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
custom_log_payment_method("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0');
        try {
            custom_log_payment_method("Intentando decodificar JWT...");
            $key = JWT_SECRET; // Usar constante de bootstrap.php
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            $userId = $decoded->user_id ?? null;
            $agencyId = $decoded->agency_id ?? null;
            $isAgencyOwner = isset($decoded->is_agency_owner) ? (bool)$decoded->is_agency_owner : false;
            if (!$userId || !$agencyId) {
                custom_log_payment_method("Error: user_id o agency_id no encontrado en el token JWT.");
                throw new Exception('user_id o agency_id no encontrado en el token JWT');
            }
            custom_log_payment_method("JWT decodificado correctamente. User ID: {$userId}, Agency ID: {$agencyId}, Is Owner: " . ($isAgencyOwner ? 'Sí' : 'No'));
        } catch (Exception $e) {
            custom_log_payment_method("Error al decodificar JWT: " . $e->getMessage());
            http_response_code(401);
            if (ob_get_length()) ob_end_clean();
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.']);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors);
        }
    } else {
        custom_log_payment_method("Formato de Authorization header incorrecto.");
        http_response_code(401);
        if (ob_get_length()) ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_payment_method("No se proporcionó token de autorización.");
    http_response_code(401);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Verificación de Permiso de Dueño de Agencia ---
if (!$isAgencyOwner) {
    custom_log_payment_method("Acceso denegado: Usuario user_id={$userId} no es dueño de la agencia agency_id={$agencyId}.");
    http_response_code(403);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Acceso denegado. Solo el propietario de la agencia puede actualizar el método de pago.']);
    exit();
}
// --- Conexión a la base de datos (usando constantes de bootstrap.php) ---
$db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($db->connect_error) {
    custom_log_payment_method("Error de conexión a la base de datos: " . $db->connect_error);
    http_response_code(500);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
    exit();
}
$db->set_charset(DB_CHARSET);
custom_log_payment_method("Conexión a la base de datos establecida correctamente.");
// --- Procesar la actualización del método de pago ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    custom_log_payment_method("[POST Request] Procesando para user_id: " . $userId);
    $data = json_decode(file_get_contents('php://input'), true);
    $paymentMethodIdStripe = isset($data['payment_method_id']) ? $data['payment_method_id'] : null; // Renombrado para claridad
    // $subscriptionIdStripe = isset($data['subscription_id']) ? $data['subscription_id'] : null; // El ID de la suscripción se obtiene de la BD
    custom_log_payment_method("Datos recibidos: payment_method_id (Stripe PM ID) = " . $paymentMethodIdStripe);
    if (empty($paymentMethodIdStripe)) { // Solo se necesita el nuevo payment_method_id de Stripe
        custom_log_payment_method("Falta el dato requerido: payment_method_id (Stripe).");
        http_response_code(400);
        if (ob_get_length()) ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Se requiere el ID del método de pago de Stripe (payment_method_id).']);
        exit();
    }
    // Consulta a la base de datos para obtener el stripe_customer_id y el stripe_subscription_id activa del usuario
    $query = "SELECT u.stripe_customer_id, s.stripe_subscription_id 
              FROM usuarios u 
              LEFT JOIN suscripciones s ON u.id = s.user_id 
              WHERE u.id = ? AND (s.estado = 'active' OR s.estado = 'trialing' OR s.estado = 'past_due')
              ORDER BY s.fecha_creacion DESC 
              LIMIT 1";
    $stmt = $db->prepare($query);
    if (!$stmt) {
        custom_log_payment_method("Error al preparar la consulta para obtener cliente/suscripción: " . $db->error);
        http_response_code(500);
        if (ob_get_length()) ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
        exit();
    }
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $stripeCustomerId = $row['stripe_customer_id'];
        $stripeSubscriptionId = $row['stripe_subscription_id']; // Esta es la suscripción activa a actualizar
        if (empty($stripeCustomerId)) {
            custom_log_payment_method("El usuario no tiene un Stripe Customer ID asociado.");
            http_response_code(400);
            if (ob_get_length()) ob_end_clean();
            echo json_encode(['success' => false, 'message' => 'No se encontró información del cliente de Stripe.']);
            $stmt->close(); $db->close(); exit();
        }
        if (empty($stripeSubscriptionId)) {
            custom_log_payment_method("El usuario no tiene una suscripción activa o válida para actualizar.");
            http_response_code(400);
            if (ob_get_length()) ob_end_clean();
            echo json_encode(['success' => false, 'message' => 'No hay una suscripción activa para actualizar el método de pago.']);
            $stmt->close(); $db->close(); exit();
        }
        custom_log_payment_method("Stripe Customer ID: " . $stripeCustomerId . ", Subscription ID activa: " . $stripeSubscriptionId);
        try {
            $stripeSecretKeyConst = STRIPE_SECRET_KEY; // Usar constante de bootstrap.php
            if (empty($stripeSecretKeyConst)) {
                custom_log_payment_method("CRÍTICO: STRIPE_SECRET_KEY no está definida.");
                throw new Exception("Error de configuración de Stripe.");
            }
            \Stripe\Stripe::setApiKey($stripeSecretKeyConst);
            // 1. Adjuntar el nuevo método de pago al cliente de Stripe
            $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodIdStripe);
            $paymentMethod->attach(['customer' => $stripeCustomerId]);
            custom_log_payment_method("Nuevo método de pago {$paymentMethodIdStripe} adjuntado al cliente {$stripeCustomerId}.");
            // 2. Actualizar el cliente para que este sea su método de pago predeterminado para facturas
            \Stripe\Customer::update($stripeCustomerId, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodIdStripe,
                ],
            ]);
            custom_log_payment_method("Cliente {$stripeCustomerId} actualizado para usar {$paymentMethodIdStripe} como predeterminado para facturas.");
            // 3. Actualizar la suscripción activa para usar este nuevo método de pago
            $updatedSubscription = \Stripe\Subscription::update($stripeSubscriptionId, [
                'default_payment_method' => $paymentMethodIdStripe,
            ]);
            custom_log_payment_method("Suscripción {$stripeSubscriptionId} actualizada para usar {$paymentMethodIdStripe}.");
            custom_log_payment_method("Método de pago actualizado correctamente en Stripe.");
            if (ob_get_length()) ob_end_clean();
            echo json_encode([
                'success' => true, 
                'message' => 'Método de pago actualizado correctamente.',
                'payment_method' => [
                    'id' => $paymentMethodIdStripe,
                    'brand' => $paymentMethod->card->brand,
                    'last4' => $paymentMethod->card->last4,
                    'exp_month' => $paymentMethod->card->exp_month,
                    'exp_year' => $paymentMethod->card->exp_year,
                ],
                'subscription_status' => $updatedSubscription->status // Opcional: devolver estado de la suscripción
            ]);
        } catch (Stripe\Exception\ApiErrorException $e) { // Capturar errores de Stripe específicamente primero
            custom_log_payment_method("Stripe API Error: " . $e->getMessage() . " (Type: " . ($e->getError() ? $e->getError()->type : 'N/A') . ", Code: " . ($e->getError() ? $e->getError()->code : 'N/A') . ")");
            http_response_code($e->getHttpStatus() ?: 500);
            if (ob_get_length()) ob_end_clean();
            echo json_encode(['success' => false, 'message' => 'Error de Stripe al actualizar el método de pago: ' . $e->getMessage()]);
        } catch (Throwable $e) { // CAMBIO: Exception a Throwable
            custom_log_payment_method("Error General (Throwable) al actualizar el método de pago: " . $e->getMessage() . " en " . $e->getFile() . ":" . $e->getLine() . "\nStack Trace: " . $e->getTraceAsString());
            if (!headers_sent()) {
            http_response_code(500);
            }
            if (ob_get_length()) ob_end_clean();
            echo json_encode(['success' => false, 'message' => 'Error al actualizar el método de pago: ' . $e->getMessage()]);
        } finally {
            if (isset($stmt)) $stmt->close();
            $db->close();
            exit();
        }
    } else {
        custom_log_payment_method("No se encontró información de cliente/suscripción activa para el usuario ID: " . $userId);
        http_response_code(404);
        if (ob_get_length()) ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'No se encontró información del cliente o suscripción activa.']);
        if (isset($stmt)) $stmt->close();
        $db->close();
        exit();
    }
} else {
    custom_log_payment_method("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    if (ob_get_length()) ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}
?>
