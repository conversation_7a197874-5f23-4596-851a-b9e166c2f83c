<?php
// Desactivar la salida de errores para la respuesta JSON
ini_set('display_errors', 0);
ini_set('log_errors', 1);

echo "=== PRUEBA DE AUTENTICACIÓN ===\n\n";

// Datos de prueba (reemplaza con credenciales válidas)
$testEmail = '<EMAIL>'; // Reemplaza con un email de prueba válido
$testPassword = 'tu_contraseña'; // Reemplaza con la contraseña correcta

// Preparar los datos para la solicitud
$postData = json_encode([
    'email' => $testEmail,
    'password' => $testPassword
]);

// Configurar la solicitud cURL
$ch = curl_init('http://localhost:8000/login.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($postData)
]);

// Ejecutar la solicitud
$startTime = microtime(true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$totalTime = (microtime(true) - $startTime) * 1000; // Tiempo en milisegundos

// Verificar errores
if (curl_errno($ch)) {
    echo "❌ Error en la solicitud cURL: " . curl_error($ch) . "\n";
} else {
    echo "=== RESPUESTA DEL SERVIDOR ===\n";
    echo "Código HTTP: " . $httpCode . "\n";
    echo "Tiempo de respuesta: " . number_format($totalTime, 2) . " ms\n\n";
    
    // Mostrar la respuesta formateada
    $responseData = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        // Ocultar el token JWT completo por seguridad
        if (isset($responseData['token'])) {
            $responseData['token'] = substr($responseData['token'], 0, 30) . '...';
        }
        echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . "\n";
        
        if ($httpCode >= 200 && $httpCode < 300) {
            echo "\n✅ Autenticación exitosa!\n";
        } else {
            echo "\n❌ Error en la autenticación. Verifica las credenciales.\n";
        }
    } else {
        echo "Respuesta del servidor (formato no JSON):\n";
        echo $response . "\n";
    }
}

// Cerrar la conexión cURL
curl_close($ch);

echo "\n=== FIN DE LA PRUEBA ===\n";
