<?php
require_once __DIR__ . '/config/bootstrap.php';

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

header('Content-Type: application/json');
// QUITAR ESTAS LÍNEAS EN PRODUCCIÓN para no mostrar errores detallados
// ini_set('display_errors', '1');
// ini_set('display_startup_errors', '1');
// error_reporting(E_ALL);

$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';

if (!$authHeader || !preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token no proporcionado o formato incorrecto']);
    exit();
}

$token = $matches[1];
$jwtSecretKey = $_ENV['JWT_SECRET_KEY'] ?? null;

if (!$jwtSecretKey) {
    http_response_code(500);
    // No enviar detalles de variables de entorno en producción
    echo json_encode(['success' => false, 'message' => 'Error de configuración del servidor (JWT Secret)']);
    exit();
}

try {
    $decoded = JWT::decode($token, new Key($jwtSecretKey, 'HS256'));
    $userRoles = $decoded->roles ?? [];

} catch (Firebase\JWT\ExpiredException $e) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token Expirado', 'error_type' => 'ExpiredException']);
    exit();
} catch (Firebase\JWT\SignatureInvalidException $e) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token con Firma Inválida', 'error_type' => 'SignatureInvalidException']);
    exit();
} catch (Exception $e) { // Captura genérica para otros errores de JWT (como UnexpectedValueException)
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token inválido (general)', 'error_type' => get_class($e)]);
    exit();
}

if (!is_array($userRoles) || !in_array('admin', $userRoles)) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Acceso denegado. Se requiere rol de administrador.']);
    exit();
}

$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de usuario no proporcionado.']);
    exit();
}

$userIdToUpdate = $data['id'];
$fieldsToUpdate = [];
$params = [];

if (isset($data['nombre_completo'])) {
    $fieldsToUpdate[] = "nombre_completo = ?";
    $params[] = $data['nombre_completo'];
}

if (isset($data['email'])) {
    $fieldsToUpdate[] = "email = ?";
    $params[] = $data['email'];
}

if (isset($data['roles'])) {
    if (is_array($data['roles'])) {
        $fieldsToUpdate[] = "roles = ?";
        $params[] = json_encode($data['roles']); // Convertir array a JSON string
    } else {
        // Opcional: manejar si roles no es un array como se espera
        error_log("Advertencia: roles recibidos no es un array para el usuario ID: " . $userIdToUpdate);
    }
}

if (isset($data['activo'])) {
    $fieldsToUpdate[] = "activo = ?";
    $params[] = $data['activo'] ? 1 : 0; // Convertir booleano a 0/1
}

if (empty($fieldsToUpdate)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'No hay campos para actualizar.']);
    exit();
}

$params[] = $userIdToUpdate; // Añadir ID al final para la cláusula WHERE

try {
    $pdo = Api\lib\Database::getInstance()->getConnection();

    $sql = "UPDATE usuarios SET " . implode(', ', $fieldsToUpdate) . " WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => 'Usuario actualizado correctamente']);
    } else {
        // Esto puede ocurrir si los datos enviados son los mismos que ya existen en la BD
        // o si el ID no existe (aunque el update no fallaría, no afectaría filas)
        echo json_encode(['success' => true, 'message' => 'Usuario actualizado (sin cambios detectados o ID no encontrado).']);
    }

} catch (Exception $e) {
    http_response_code(500);
    error_log("Error al actualizar usuario (ID: $userIdToUpdate): " . $e->getMessage()); // Loggear el error
    echo json_encode(['success' => false, 'message' => 'Error al actualizar el usuario', 'error_detail' => $e->getMessage()]);
    exit();
}
