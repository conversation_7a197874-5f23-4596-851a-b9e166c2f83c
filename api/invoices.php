<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// /api/invoices.php - Obtiene el historial de facturas del usuario
// IMPORTANTE: Evitar cualquier salida antes de establecer los headers
// Desactivar la salida de errores directa, los capturaremos y devolveremos como JSON
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);
// Iniciar buffer de salida para evitar cualquier salida accidental
ob_start();
// --- Logger Setup ---
if (!function_exists('custom_log_invoices')) {
    function custom_log_invoices($message) {
        $logFile = __DIR__ . '/debug_invoices.log';
        $timestamp = date('Y-m-d H:i:s');
        @file_put_contents($logFile, "[$timestamp] " . $message . PHP_EOL, FILE_APPEND);
    }
}
custom_log_invoices("--- [invoices.php] INICIADO ---");
// --- Verificación de autenticación ---
$userId = null;
// Código para permitir pasar el token por URL (solo para desarrollo)
if (isset($_GET['token'])) {
    custom_log_invoices("Token recibido por URL: " . substr($_GET['token'], 0, 10) . '...');
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer ' . $_GET['token'];
}
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : null;
custom_log_invoices("Authorization header: " . ($authHeader ? 'Presente' : 'No presente'));
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        try {
            custom_log_invoices("Intentando decodificar JWT...");
            $key = JWT_SECRET;
            // Versión actualizada de JWT::decode compatible con versiones recientes
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            // Registrar la estructura completa del token decodificado
            custom_log_invoices("Estructura del token: " . json_encode($decoded));
            // Intentar obtener el userId. Puede estar en user_id o como propiedad anidada
            if (isset($decoded->user_id)) {
                $userId = $decoded->user_id;
                custom_log_invoices("JWT decodificado correctamente. User ID: " . $userId);
            } else {
                custom_log_invoices("Error: No se encontró user_id en el token JWT");
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Token inválido: no contiene user_id.']);
                exit();
            }
        } catch (Exception $e) {
            custom_log_invoices("Error al decodificar JWT: " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado.']);
            exit();
        }
    } else {
        custom_log_invoices("Formato de Authorization header incorrecto.");
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Formato de autorización incorrecto.']);
        exit();
    }
} else {
    custom_log_invoices("No se proporcionó token de autorización.");
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']);
    exit();
}
// --- Conexión a la base de datos ---
try {
    custom_log_invoices("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_invoices("Conexión PDO a la base de datos establecida correctamente.");
} catch (Exception $e) {
    custom_log_invoices("EXCEPCIÓN al conectar a la base de datos: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos: ' . $e->getMessage()]);
    exit();
}
// --- Obtener historial de facturas ---
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    custom_log_invoices("[GET Request] Procesando para user_id: " . $userId);
    try {
        // Consulta a la base de datos para obtener el stripe_customer_id
        $query = "SELECT stripe_customer_id FROM usuarios WHERE id = ?";
        custom_log_invoices("Ejecutando consulta: {$query} con user_id={$userId}");
        $stmt = $pdo->prepare($query);
        if (!$stmt) {
            throw new Exception("Error al preparar la consulta: " . $pdo->errorInfo()[2]);
        }
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        if (!$user || empty($user['stripe_customer_id'])) {
            custom_log_invoices("Usuario no encontrado o sin stripe_customer_id para user_id: " . $userId);
            // Si no hay ID de cliente, no hay facturas. Devolver un array vacío.
            echo json_encode(['success' => true, 'invoices' => []]);
            exit();
        }
        $stripeCustomerId = $user['stripe_customer_id'];
        custom_log_invoices("Stripe Customer ID encontrado: " . $stripeCustomerId);
        // Inicializar Stripe
        \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
        \Stripe\Stripe::setApiVersion("2022-11-15");
        custom_log_invoices("Stripe SDK inicializado.");
        // Obtener facturas de Stripe
        $invoices = \Stripe\Invoice::all([
            'customer' => $stripeCustomerId,
            'limit' => 100 // Límite generoso, ajustar si es necesario
        ]);
        custom_log_invoices(count($invoices->data) . " facturas encontradas para el cliente.");
        // Formatear la respuesta
        $formattedInvoices = [];
        foreach ($invoices->data as $invoice) {
            $formattedInvoices[] = [
                'id' => $invoice->id,
                'date' => $invoice->created,
                'total' => $invoice->total / 100, // Convertir de centavos a la unidad principal
                'currency' => strtoupper($invoice->currency),
                'status' => $invoice->status,
                'pdf_url' => $invoice->invoice_pdf,
                'number' => $invoice->number
            ];
        }
        // Limpiar buffer de salida antes de la respuesta final
        ob_end_clean();
        // Enviar respuesta
        echo json_encode(['success' => true, 'invoices' => $formattedInvoices]);
    } catch (\Stripe\Exception\ApiErrorException $e) {
        custom_log_invoices("Error de Stripe: " . $e->getMessage());
        http_response_code(500);
        ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Error al comunicarse con el procesador de pagos.']);
    } catch (Exception $e) {
        custom_log_invoices("Error general: " . $e->getMessage());
        http_response_code(500);
        ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Error interno del servidor: ' . $e->getMessage()]);
    }
} else {
    custom_log_invoices("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405); // Method Not Allowed
    ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
}
?>
