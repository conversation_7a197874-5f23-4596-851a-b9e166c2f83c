<?php
declare(strict_types=1);
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
// Incluir el archivo bootstrap para configuraciones globales y carga automática
use Api\lib\Database;
// --- Importar clases de JWT ---
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;
// --- Función para obtener y decodificar el token JWT localmente ---
function get_decoded_jwt_payload_valoraciones() {
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        if (!empty(JWT_SECRET)) {
            try {
                $previous_display_errors = ini_set('display_errors', '0');
                $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                return $decoded;
            } catch (\Throwable $e) {
                // error_log("JWT Decode Error en get_valoraciones: " . $e->getMessage());
                if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                    ini_set('display_errors', $previous_display_errors);
                }
            }
        }
    }
    return null;
}
$decoded_payload = get_decoded_jwt_payload_valoraciones();
$user_id = $decoded_payload->user_id ?? null;
$agency_id = $decoded_payload->agency_id ?? null;
$roles = isset($decoded_payload->roles) && is_array($decoded_payload->roles) ? $decoded_payload->roles : (isset($decoded_payload->roles) && is_object($decoded_payload->roles) ? (array)$decoded_payload->roles : []);
$is_super_admin = in_array('admin', $roles);
if (!$user_id) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado: Usuario no identificado.']);
    exit;
}
try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : null;
    $sql = "SELECT
                vv.id, vv.uuid, vv.cliente_valorador_id, vv.lead_id,
                vv.referencia_catastral, vv.direccion, vv.latitud, vv.longitud,
                vv.tipo_principal, vv.subtipo, vv.superficie, vv.superficie_parcela,
                vv.habitaciones, vv.banos, vv.estado, vv.planta, vv.extras,
                vv.valor_estimado_min, vv.valor_estimado_max, vv.notas_agente,
                vv.ano_construccion_catastro, vv.precio_m2_promedio, vv.tamano_promedio,
                vv.porcentaje_con_piscina, vv.porcentaje_con_parking, vv.porcentaje_con_ascensor,
                vv.porcentaje_con_terraza, vv.porcentaje_reformadas, vv.numero_propiedades_analizadas,
                vv.distancia_al_centroide_km, vv.precio_vs_zona_porcentaje, vv.tamano_vs_zona_porcentaje,
                vv.fecha_creacion, vv.fecha_modificacion,
                vl.uuid AS lead_uuid,
                vl.nombre AS lead_nombre,
                vl.email AS lead_email,
                vl.telefono AS lead_telefono,
                vl.necesidad AS lead_necesidad,
                vl.notas AS lead_notas,
                vl.fecha_creacion AS lead_fecha_creacion,
                cv.id as cliente_valorador_id_full,
                cv.client_identifier
            FROM valorador_valoraciones vv
            LEFT JOIN valorador_leads vl ON vv.lead_id = vl.id
            LEFT JOIN clientes_valorador cv ON vv.cliente_valorador_id = cv.id";
    $params = [];
    if (!$is_super_admin) {
        if (!$agency_id) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Acceso denegado: No se encontró información de agencia para el usuario.']);
            exit;
        }
        // MODIFICADO: Permitir acceso a datos históricos incluso con valoradores inactivos
        $stmt_clients = $pdo->prepare("SELECT client_identifier FROM clientes_valorador WHERE agency_id = :agency_id");
        $stmt_clients->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
        $stmt_clients->execute();
        $client_identifiers = $stmt_clients->fetchAll(PDO::FETCH_COLUMN);
        if (empty($client_identifiers)) {
            echo json_encode(['success' => true, 'valoraciones' => []]);
            exit;
        }
        $in_placeholders = implode(',', array_fill(0, count($client_identifiers), '?'));
        $sql .= " WHERE cv.client_identifier IN (" . $in_placeholders . ")";
        $params = $client_identifiers;
    }
    $sql .= " ORDER BY vv.fecha_creacion DESC"; // Alias vv.fecha_creacion
    if ($limit && $limit > 0) {
        $sql .= " LIMIT ?";
        if (!$is_super_admin && !empty($client_identifiers)) {
             $params[] = $limit;
        } else if ($is_super_admin) {
             $params = [$limit];
        }
    }
    $stmt_valoraciones = $pdo->prepare($sql);
    $stmt_valoraciones->execute($params);
    $valoraciones_raw = $stmt_valoraciones->fetchAll(PDO::FETCH_ASSOC);

    // Forzar la conversión de latitud y longitud a float para asegurar el tipo de dato correcto.
    $valoraciones = array_map(function($v) {
        if (isset($v['latitud'])) {
            $v['latitud'] = (float) $v['latitud'];
        }
        if (isset($v['longitud'])) {
            $v['longitud'] = (float) $v['longitud'];
        }
        return $v;
    }, $valoraciones_raw);

    echo json_encode(['success' => true, 'valoraciones' => $valoraciones]);
} catch (PDOException $e) {
    error_log("Error de base de datos en get_valoraciones.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor al obtener valoraciones.']);
} catch (Exception $e) {
    error_log("Error general en get_valoraciones.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}
?>
