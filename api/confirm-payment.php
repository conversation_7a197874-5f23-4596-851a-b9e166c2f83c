<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Logger;

header('Content-Type: application/json');

function custom_log_confirm_payment($message) {
    Logger::info("[confirm-payment.php] " . $message);
}

custom_log_confirm_payment("--- [confirm-payment.php] INICIADO ---");

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    exit();
}

// Verificar autenticación JWT
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token de autorización requerido.']);
    exit();
}

$token = $matches[1];

try {
    $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key(JWT_SECRET, 'HS256'));
    $userId = $decoded->user_id ?? null;
    
    if (!$userId) {
        throw new Exception('Token inválido');
    }
    
    custom_log_confirm_payment("Usuario autenticado: ID = $userId");
    
} catch (Exception $e) {
    custom_log_confirm_payment("Error de autenticación: " . $e->getMessage());
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Token inválido.']);
    exit();
}

// Obtener datos del POST
$input = json_decode(file_get_contents('php://input'), true);
$paymentIntentId = $input['payment_intent_id'] ?? null;

if (!$paymentIntentId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'payment_intent_id requerido.']);
    exit();
}

try {
    // Inicializar Stripe
    \Stripe\Stripe::setApiKey(env('STRIPE_SECRET_KEY'));
    
    // Obtener el PaymentIntent
    $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);
    custom_log_confirm_payment("PaymentIntent obtenido: " . $paymentIntent->id . " - Estado: " . $paymentIntent->status);
    
    if ($paymentIntent->status === 'succeeded') {
        // El pago ya fue exitoso, actualizar la suscripción
        $subscriptionId = $paymentIntent->metadata['subscription_id'] ?? null;
        
        if ($subscriptionId) {
            // Conectar a la base de datos
            $db = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
            if ($db->connect_error) {
                throw new Exception("Error de conexión a la base de datos: " . $db->connect_error);
            }
            $db->set_charset('utf8mb4');
            
            // Actualizar estado de la suscripción
            $updateQuery = "UPDATE suscripciones SET estado = 'active' WHERE stripe_subscription_id = ?";
            $updateStmt = $db->prepare($updateQuery);
            $updateStmt->bind_param("s", $subscriptionId);
            $updateStmt->execute();
            $updateStmt->close();
            $db->close();

            custom_log_confirm_payment("Suscripción reactivada después de confirmación 3DS");
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Pago confirmado exitosamente. Tu suscripción ha sido reactivada.',
            'status' => 'succeeded'
        ]);
        
    } else if ($paymentIntent->status === 'requires_action') {
        // Aún requiere acción del usuario
        echo json_encode([
            'success' => false,
            'message' => 'El pago aún requiere autenticación adicional.',
            'status' => 'requires_action',
            'requires_action' => true
        ]);
        
    } else if (in_array($paymentIntent->status, ['canceled', 'failed'])) {
        // El pago falló o fue cancelado
        echo json_encode([
            'success' => false,
            'message' => 'El pago falló o fue cancelado. Intenta con otro método de pago.',
            'status' => $paymentIntent->status
        ]);
        
    } else {
        // Estado desconocido
        echo json_encode([
            'success' => false,
            'message' => 'Estado de pago desconocido: ' . $paymentIntent->status,
            'status' => $paymentIntent->status
        ]);
    }
    
} catch (\Stripe\Exception\ApiErrorException $e) {
    custom_log_confirm_payment("Error de Stripe API: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Error al confirmar el pago: ' . $e->getMessage(),
        'error_type' => 'stripe_error'
    ]);
    
} catch (Exception $e) {
    custom_log_confirm_payment("Error general: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_type' => 'general_error'
    ]);
}

custom_log_confirm_payment("--- [confirm-payment.php] FINALIZADO ---");
?>
