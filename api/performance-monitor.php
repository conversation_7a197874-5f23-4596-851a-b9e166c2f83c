<?php
// api/performance-monitor.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config/config.php';
require_once 'lib/Database.php';

use Api\lib\Database;

try {
    $startTime = microtime(true);
    
    // Test de conexión básica
    $db = Database::getInstance();
    $connectionTime = microtime(true) - $startTime;
    
    // Test de consulta simple
    $queryStartTime = microtime(true);
    $stmt = $db->query("SELECT 1 as test, NOW() as current_time");
    $result = $stmt->fetch();
    $queryTime = microtime(true) - $queryStartTime;
    
    // Test de consulta con datos reales
    $realQueryStartTime = microtime(true);
    $stmt = $db->query("SELECT COUNT(*) as total_users FROM usuarios LIMIT 1");
    $userCount = $stmt->fetch();
    $realQueryTime = microtime(true) - $realQueryStartTime;
    
    // Obtener estadísticas de MySQL
    $statsStartTime = microtime(true);
    $stmt = $db->query("
        SELECT 
            (SELECT VARIABLE_VALUE FROM performance_schema.global_status WHERE VARIABLE_NAME = 'Threads_connected') as threads_connected,
            (SELECT VARIABLE_VALUE FROM performance_schema.global_status WHERE VARIABLE_NAME = 'Aborted_connects') as aborted_connects,
            (SELECT VARIABLE_VALUE FROM performance_schema.global_status WHERE VARIABLE_NAME = 'Max_used_connections') as max_used_connections,
            (SELECT VARIABLE_VALUE FROM performance_schema.global_variables WHERE VARIABLE_NAME = 'max_connections') as max_connections
    ");
    $mysqlStats = $stmt->fetch();
    $statsTime = microtime(true) - $statsStartTime;
    
    $totalTime = microtime(true) - $startTime;
    
    $response = [
        'status' => 'healthy',
        'timestamp' => date('c'),
        'performance' => [
            'total_time_ms' => round($totalTime * 1000, 2),
            'connection_time_ms' => round($connectionTime * 1000, 2),
            'simple_query_time_ms' => round($queryTime * 1000, 2),
            'real_query_time_ms' => round($realQueryTime * 1000, 2),
            'stats_query_time_ms' => round($statsTime * 1000, 2)
        ],
        'database' => [
            'status' => 'connected',
            'current_time' => $result['current_time'],
            'total_users' => (int)$userCount['total_users'],
            'mysql_stats' => [
                'threads_connected' => (int)$mysqlStats['threads_connected'],
                'aborted_connects' => (int)$mysqlStats['aborted_connects'],
                'max_used_connections' => (int)$mysqlStats['max_used_connections'],
                'max_connections' => (int)$mysqlStats['max_connections'],
                'connection_usage_percent' => round(($mysqlStats['threads_connected'] / $mysqlStats['max_connections']) * 100, 2)
            ]
        ],
        'optimizations' => [
            'persistent_connections' => 'enabled',
            'compression' => 'enabled',
            'connection_timeout' => '3s',
            'retry_logic' => 'enabled',
            'mysql_optimized' => 'yes'
        ],
        'benchmarks' => [
            'excellent' => '< 200ms',
            'good' => '200-400ms',
            'acceptable' => '400-600ms',
            'poor' => '> 600ms',
            'current_rating' => $totalTime < 0.2 ? 'excellent' : 
                              ($totalTime < 0.4 ? 'good' : 
                              ($totalTime < 0.6 ? 'acceptable' : 'poor'))
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'timestamp' => date('c'),
        'error' => $e->getMessage(),
        'performance' => [
            'total_time_ms' => round((microtime(true) - $startTime) * 1000, 2)
        ]
    ], JSON_PRETTY_PRINT);
}
?>
