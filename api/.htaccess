Options -Indexes

# Pasar la autenticación básica a PHP
SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1

# La configuración de CORS ahora es manejada por api/config/cors.php

# Asegurar que PHP procese los archivos correctamente
<IfModule mod_php7.c>
    php_flag display_errors off
    php_value error_reporting E_ALL
    php_flag log_errors on
    php_value error_log /var/log/apache2/php_errors.log
</IfModule>

# Proteger archivos sensibles
<FilesMatch "\.(env|json|config|tpl|inc|log|sh|sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Permitir acceso solo a los archivos PHP necesarios
<Files "valorador-config.php">
    Order allow,deny
    Allow from all
</Files>