<?php
declare(strict_types=1);
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;
// --- Helper Functions ---
if (!function_exists('get_decoded_jwt_payload_email_stats')) {
    function get_decoded_jwt_payload_email_stats() {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
        if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
            if (!empty(JWT_SECRET)) {
                try {
                    $previous_display_errors = ini_set('display_errors', '0');
                    $decoded = JWT::decode($token, new Key(JWT_SECRET, 'HS256'));
                    if ($previous_display_errors !== false) ini_set('display_errors', $previous_display_errors);
                    return $decoded;
                } catch (ExpiredException | SignatureInvalidException | BeforeValidException $e) {
                    // Log specific JWT errors if needed
                } catch (\Throwable $e) {
                    // Log general decoding errors
                }
                if (isset($previous_display_errors) && $previous_display_errors !== false && ini_get('display_errors') === '0') {
                    ini_set('display_errors', $previous_display_errors);
                }
            }
        }
        return null;
    }
}
if (!function_exists('custom_log_email_stats')) {
    function custom_log_email_stats($message) {
        $logFile = __DIR__ . '/logs/debug_email_performance_stats.log';
        if (!is_dir(__DIR__ . '/logs')) {
            if (!mkdir(__DIR__ . '/logs', 0775, true) && !is_dir(__DIR__ . '/logs')) {
                error_log("Advertencia: No se pudo crear el directorio de logs: " . __DIR__ . '/logs');
                return;
            }
        }
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] {$message}\n", 3, $logFile);
    }
}
// --- Main Logic ---
$decoded_payload = get_decoded_jwt_payload_email_stats();
$user_id = $decoded_payload->user_id ?? null;
$agency_id = $decoded_payload->agency_id ?? null;
$roles = $decoded_payload->roles ?? [];
$is_super_admin = in_array('admin', $roles);
if (!$user_id) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Acceso no autorizado.']);
    exit;
}
if (!$is_super_admin && !$agency_id) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Acceso denegado: Información de agencia no disponible.']);
    exit;
}
try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    $client_identifiers_for_query = [];
    if (!$is_super_admin) {
        // MODIFICADO: Permitir acceso a datos históricos incluso con valoradores inactivos
        $stmt_clients = $pdo->prepare("SELECT id FROM clientes_valorador WHERE agency_id = :agency_id");
        $stmt_clients->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
        $stmt_clients->execute();
        $client_identifiers_for_query = $stmt_clients->fetchAll(PDO::FETCH_COLUMN);
        if (empty($client_identifiers_for_query)) {
            // Return empty/zeroed stats if no active valoradores for the agency
             echo json_encode(['success' => true, 'data' => [
                'kpis' => [
                    'emailsEnviadosTotal' => 0,
                    'emailsEnviadosPeriodo' => 0,
                    'tendenciaEmailsEnviados' => 0,
                    'tasaAperturaPromedio' => 0,
                    'tendenciaTasaApertura' => 0,
                    'tasaClicsPromedio' => 0,
                    'tendenciaTasaClics' => 0,
                    'leadsEnNutricionActiva' => 0,
                    'insightDestacado' => 'No hay datos de envío de emails para mostrar.'
                ],
                'rendimientoDiario' => [],
                'rendimientoPorSecuencia' => [],
                'proximosPasosIA' => ['mensaje' => 'No hay acciones programadas por la IA actualmente.']
            ]]);
            exit;
        }
    }
    $where_clause_client_id = "";
    $params_client_id = [];
    if (!$is_super_admin && !empty($client_identifiers_for_query)) {
        $in_placeholders_clients = implode(',', array_fill(0, count($client_identifiers_for_query), '?'));
        // Using cliente_valorador_id for proper relationship
        $where_clause_client_id = " AND cv.id IN ($in_placeholders_clients) ";
        $params_client_id = $client_identifiers_for_query;
    }
    // KPIs
    // --- Emails Enviados (Total y Periodo) ---
    $current_month_start = date('Y-m-01 00:00:00');
    $previous_month_start = date('Y-m-01 00:00:00', strtotime('-1 month'));
    $previous_month_end = date('Y-m-t 23:59:59', strtotime('-1 month'));
    $sql_total_enviados = "SELECT COUNT(*) FROM lead_emails_historial leh JOIN valorador_leads vl ON leh.lead_id = vl.id JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id WHERE leh.estado_envio = 'sent' $where_clause_client_id";
    $stmt_total = $pdo->prepare($sql_total_enviados);
    $stmt_total->execute($params_client_id);
    $emailsEnviadosTotal = (int)$stmt_total->fetchColumn();
    $sql_enviados_periodo = "SELECT COUNT(*) FROM lead_emails_historial leh JOIN valorador_leads vl ON leh.lead_id = vl.id JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id WHERE leh.estado_envio = 'sent' AND leh.fecha_envio_real >= ? $where_clause_client_id";
    $stmt_periodo = $pdo->prepare($sql_enviados_periodo);
    $stmt_periodo->execute(array_merge([$current_month_start], $params_client_id));
    $emailsEnviadosPeriodo = (int)$stmt_periodo->fetchColumn();
    $sql_enviados_prev_periodo = "SELECT COUNT(*) FROM lead_emails_historial leh JOIN valorador_leads vl ON leh.lead_id = vl.id JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id WHERE leh.estado_envio = 'sent' AND leh.fecha_envio_real >= ? AND leh.fecha_envio_real <= ? $where_clause_client_id";
    $stmt_prev_periodo = $pdo->prepare($sql_enviados_prev_periodo);
    $stmt_prev_periodo->execute(array_merge([$previous_month_start, $previous_month_end], $params_client_id));
    $emailsEnviadosPrevPeriodo = (int)$stmt_prev_periodo->fetchColumn();
    $tendenciaEmailsEnviados = 0;
    if ($emailsEnviadosPrevPeriodo > 0) {
        $tendenciaEmailsEnviados = round((($emailsEnviadosPeriodo - $emailsEnviadosPrevPeriodo) / $emailsEnviadosPrevPeriodo) * 100, 1);
    } elseif ($emailsEnviadosPeriodo > 0) {
        $tendenciaEmailsEnviados = 100; // Infinite growth if previous was 0
    }
    // --- Tasas de Apertura y Clics (REAL) ---
    function get_rates_for_period($pdo, $start_date, $end_date, $where_clause, $params) {
        $sql = "SELECT 
                    COUNT(CASE WHEN leh.abierto_timestamp IS NOT NULL THEN 1 END) as opens,
                    COUNT(CASE WHEN leh.clickeado_timestamp IS NOT NULL THEN 1 END) as clicks,
                    COUNT(leh.id) as sent
                FROM lead_emails_historial leh
                JOIN valorador_leads vl ON leh.lead_id = vl.id
                JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                WHERE leh.estado_envio = 'sent' AND leh.fecha_envio_real >= ? AND leh.fecha_envio_real <= ? $where_clause";
        $stmt = $pdo->prepare($sql);
        $stmt->execute(array_merge([$start_date, $end_date], $params));
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        $sent = (int)$data['sent'];
        $opens = (int)$data['opens'];
        $clicks = (int)$data['clicks'];
        return [
            'open_rate' => $sent > 0 ? round(($opens / $sent) * 100, 1) : 0,
            'click_rate' => $opens > 0 ? round(($clicks / $opens) * 100, 1) : 0, // Click rate over opens (CTR)
        ];
    }

    $current_rates = get_rates_for_period($pdo, $current_month_start, date('Y-m-d H:i:s'), $where_clause_client_id, $params_client_id);
    $previous_rates = get_rates_for_period($pdo, $previous_month_start, $previous_month_end, $where_clause_client_id, $params_client_id);

    $tasaAperturaPromedio = $current_rates['open_rate'];
    $tasaClicsPromedio = $current_rates['click_rate'];

    $tendenciaTasaApertura = 0;
    if ($previous_rates['open_rate'] > 0) {
        $tendenciaTasaApertura = round((($tasaAperturaPromedio - $previous_rates['open_rate']) / $previous_rates['open_rate']) * 100, 1);
    } elseif ($tasaAperturaPromedio > 0) {
        $tendenciaTasaApertura = 100;
    }

    $tendenciaTasaClics = 0;
    if ($previous_rates['click_rate'] > 0) {
        $tendenciaTasaClics = round((($tasaClicsPromedio - $previous_rates['click_rate']) / $previous_rates['click_rate']) * 100, 1);
    } elseif ($tasaClicsPromedio > 0) {
        $tendenciaTasaClics = 100;
    }
    // --- Leads en Nutrición Activa (REAL) ---
    $sql_nutricion_activa = "SELECT COUNT(DISTINCT lst.lead_id)
                             FROM lead_sequence_tracking lst
                             JOIN valorador_leads vl ON lst.lead_id = vl.id
                             JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                             WHERE lst.status = 'active' $where_clause_client_id";
    $stmt_nutricion = $pdo->prepare($sql_nutricion_activa);
    $stmt_nutricion->execute($params_client_id);
    $leadsEnNutricionActiva = (int)$stmt_nutricion->fetchColumn();
    // Rendimiento Diario (últimos 30 días) - REAL
    $start_date_30_days = date('Y-m-d', strtotime('-29 days'));
    $sql_daily = "SELECT 
                    DATE(fecha_envio_real) as fecha,
                    COUNT(CASE WHEN abierto_timestamp IS NOT NULL THEN 1 END) as aperturas,
                    COUNT(CASE WHEN clickeado_timestamp IS NOT NULL THEN 1 END) as clics
                  FROM lead_emails_historial leh
                  JOIN valorador_leads vl ON leh.lead_id = vl.id
                  JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                  WHERE leh.estado_envio = 'sent' AND fecha_envio_real >= ? $where_clause_client_id
                  GROUP BY fecha
                  ORDER BY fecha ASC";
    $stmt_daily = $pdo->prepare($sql_daily);
    $stmt_daily->execute(array_merge([$start_date_30_days], $params_client_id));
    $daily_data_from_db = $stmt_daily->fetchAll(PDO::FETCH_ASSOC);

    $rendimientoDiario = [];
    $dates_30_days = [];
    for ($i = 29; $i >= 0; $i--) {
        $dates_30_days[date('Y-m-d', strtotime("-{$i} days"))] = ['fecha' => date('Y-m-d', strtotime("-{$i} days")), 'aperturas' => 0, 'clics' => 0];
    }

    foreach ($daily_data_from_db as $row) {
        if (isset($dates_30_days[$row['fecha']])) {
            $dates_30_days[$row['fecha']]['aperturas'] = (int)$row['aperturas'];
            $dates_30_days[$row['fecha']]['clics'] = (int)$row['clics'];
        }
    }
    $rendimientoDiario = array_values($dates_30_days);
    // Rendimiento por Secuencia - REAL
    $sql_sequence_performance = "SELECT 
                                    s.name as nombreSecuencia,
                                    COUNT(leh.id) as totalEnviadosSecuencia,
                                    COUNT(CASE WHEN leh.abierto_timestamp IS NOT NULL THEN 1 END) as totalAperturas,
                                    COUNT(CASE WHEN leh.clickeado_timestamp IS NOT NULL THEN 1 END) as totalClics
                                FROM
                                    lead_emails_historial leh
                                JOIN
                                    sequence_steps ss ON leh.sequence_step_id = ss.id
                                JOIN
                                    sequences s ON ss.sequence_id = s.id
                                JOIN 
                                    valorador_leads vl ON leh.lead_id = vl.id
                                JOIN 
                                    clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                                WHERE leh.estado_envio = 'sent' $where_clause_client_id
                                GROUP BY s.name
                                ORDER BY totalEnviadosSecuencia DESC";
    $stmt_seq = $pdo->prepare($sql_sequence_performance);
    $stmt_seq->execute($params_client_id);
    $sequence_data = $stmt_seq->fetchAll(PDO::FETCH_ASSOC);

    $rendimientoPorSecuencia = array_map(function($row) {
        $enviados = (int)$row['totalEnviadosSecuencia'];
        $aperturas = (int)$row['totalAperturas'];
        return [
            'nombreSecuencia' => $row['nombreSecuencia'],
            'tasaApertura' => $enviados > 0 ? round(($aperturas / $enviados) * 100, 1) : 0,
            'tasaClics' => $aperturas > 0 ? round(((int)$row['totalClics'] / $aperturas) * 100, 1) : 0,
            'totalEnviadosSecuencia' => $enviados
        ];
    }, $sequence_data);
    // Próximos Pasos IA - Placeholder con datos reales
    $sql_scheduled_count = "SELECT COUNT(*) FROM lead_emails_historial leh JOIN valorador_leads vl ON leh.lead_id = vl.id JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id WHERE leh.estado_envio = 'scheduled' $where_clause_client_id";
    $stmt_scheduled = $pdo->prepare($sql_scheduled_count);
    $stmt_scheduled->execute($params_client_id);
    $scheduled_count = (int)$stmt_scheduled->fetchColumn();

    $proximosPasosIA = [];
    if ($scheduled_count > 0) {
        $proximosPasosIA = [
            'mensaje' => "Hay {$scheduled_count} emails programados para ser enviados automáticamente.",
            'optimizacion' => "Monitorizando rendimiento para futuras optimizaciones."
        ];
    } else {
        $proximosPasosIA = ['mensaje' => 'No hay acciones de envío programadas actualmente.'];
    }
    // Insight Destacado - Basado en datos reales
    $insightDestacado = 'No hay datos suficientes para generar un insight.';
    if ($tasaAperturaPromedio > 0) {
        $insightDestacado = "Tu tasa de apertura este mes es del {$tasaAperturaPromedio}%. ";
        if ($tendenciaTasaApertura > 0) {
            $insightDestacado .= "Esto es un {$tendenciaTasaApertura}% más que el mes pasado. ¡Buen trabajo!";
        } elseif ($tendenciaTasaApertura < 0) {
            $insightDestacado .= "Esto es un ".abs($tendenciaTasaApertura)."% menos que el mes pasado. Revisa tus asuntos y horarios de envío.";
        } else {
            $insightDestacado .= "Se mantiene estable respecto al mes anterior.";
        }
    } elseif ($emailsEnviadosTotal > 0) {
        $insightDestacado = 'Los emails enviados este mes aún no registran aperturas.';
    } else {
        $insightDestacado = 'Aún no se han enviado emails este mes.';
    }
    $data = [
        'kpis' => [
            'emailsEnviadosTotal' => $emailsEnviadosTotal,
            'emailsEnviadosPeriodo' => $emailsEnviadosPeriodo,
            'tendenciaEmailsEnviados' => $tendenciaEmailsEnviados,
            'tasaAperturaPromedio' => $tasaAperturaPromedio,
            'tendenciaTasaApertura' => $tendenciaTasaApertura,
            'tasaClicsPromedio' => $tasaClicsPromedio,
            'tendenciaTasaClics' => $tendenciaTasaClics,
            'leadsEnNutricionActiva' => $leadsEnNutricionActiva,
            'insightDestacado' => $insightDestacado
        ],
        'rendimientoDiario' => $rendimientoDiario,
        'rendimientoPorSecuencia' => $rendimientoPorSecuencia,
        'proximosPasosIA' => $proximosPasosIA
    ];
    // custom_log_email_stats("Email performance stats para agency_id: {$agency_id}, user_id: {$user_id} - Data: " . json_encode($data));
    echo json_encode(['success' => true, 'data' => $data]);
} catch (PDOException $e) {
    custom_log_email_stats("Error de BD en get_email_performance_stats: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB).']);
} catch (Exception $e) {
    custom_log_email_stats("Error general en get_email_performance_stats: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}