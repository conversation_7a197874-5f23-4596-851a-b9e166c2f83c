<?php
// api/procesar_informe_task.php
declare(strict_types=1);



require_once __DIR__ . '/config/bootstrap.php';


use Google\Cloud\Tasks\V2\Client\CloudTasksClient;
use Google\Cloud\Tasks\V2\HttpRequest;
use Google\Cloud\Tasks\V2\Task;
use Api\utils\PDFReportGenerator;
use Api\lib\BrevoService;
use Api\lib\Database;
use Api\lib\Logger;

// Habilitar la visualización de errores para depuración en Cloud Run
ini_set('display_errors', '1');
error_reporting(E_ALL);

// --- INICIO: DEPURACIÓN AGRESIVA Y LECTURA DE PAYLOAD ---
error_log('[TASK_HANDLER_DEBUG] ==> Script procesar_informe_task.php INVOCADO.');
error_log('[TASK_HANDLER_DEBUG] Request Method: ' . $_SERVER['REQUEST_METHOD']);

$jsonPayload = file_get_contents('php://input');
error_log('[TASK_HANDLER_DEBUG] Raw Payload: ' . $jsonPayload);

$data = json_decode($jsonPayload, true);
error_log('[TASK_HANDLER_DEBUG] Decoded Payload: ' . print_r($data, true));

$valoracionUuid = $data['valoracion_uuid'] ?? null;
$leadId = $data['lead_id'] ?? null;
error_log("[TASK_HANDLER_DEBUG] Parsed valoracion_uuid: {$valoracionUuid}, lead_id: {$leadId}");
// --- FIN: DEPURACIÓN AGRESIVA Y LECTURA DE PAYLOAD ---

// --- Task Handler Security ---

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log('[TASK_HANDLER_DEBUG] ==> SALIDA PREMATURA: Método no es POST.');
    http_response_code(405);
    echo 'Method Not Allowed';
    exit;
}

if (!$valoracionUuid || !$leadId) {
    http_response_code(400);
    Logger::error('Task Handler: Payload inválido.', ['payload' => $jsonPayload]);
    echo 'Payload inválido.';
    exit;
}

Logger::info("Task Handler: Iniciando proceso para Valoración UUID: {$valoracionUuid}, Lead ID: {$leadId}");

try {
    $dbInstance = Database::getInstance();
    $db = $dbInstance->getConnection();

    // 1. Obtener todos los datos necesarios para el informe y el email
    $stmt = $db->prepare(
        "SELECT 
            vl.id as lead_id_db, 
            vl.nombre as lead_nombre, 
            vl.email as lead_email,
            vv.uuid as valoracion_uuid,
            cv.client_identifier as agencia_identifier,
            cv.nombre_display as agencia_nombre, 
            cv.logo_url as agencia_logo_url, 
            cv.color_primario as agencia_color
        FROM 
            valorador_leads vl
        JOIN 
            valorador_valoraciones vv ON vl.valoracion_id = vv.id
        JOIN
            clientes_valorador cv ON vv.cliente_valorador_id = cv.id
        WHERE 
            vl.id = :lead_id AND vv.uuid = :valoracion_uuid
        LIMIT 1"
    );
    $stmt->bindParam(':lead_id', $leadId, PDO::PARAM_INT);
    $stmt->bindParam(':valoracion_uuid', $valoracionUuid);
    $stmt->execute();
    $leadData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$leadData) {
        throw new Exception("No se encontraron datos para el lead ID {$leadId} y valoración UUID {$valoracionUuid}");
    }

    $pdfGenerator = new PDFReportGenerator($db);
    $brevoService = new BrevoService();
    $estadoFinalParaLead = 'pendiente_informe_inicial'; // Estado inicial

    // 2. Generar PDF
    $pdfPublicUrl = $pdfGenerator->generateAndSaveReport($valoracionUuid);
    if (!$pdfPublicUrl) {
        $estadoFinalParaLead = 'error_pdf_informe';
        throw new Exception("generateAndSaveReport devolvió null o false.");
    }
    Logger::info("Task Handler: PDF generado para Val. UUID {$valoracionUuid} en: {$pdfPublicUrl}");

    // 3. Obtener plantilla de email
    $emailPlantillaNombre = 'informe_valoracion_inicial';
    $stmtPlantilla = $db->prepare("SELECT asunto_predeterminado, contenido_html FROM email_plantillas_html WHERE nombre_interno_plantilla = :nombre_plantilla AND activa = 1 LIMIT 1");
    $stmtPlantilla->bindParam(':nombre_plantilla', $emailPlantillaNombre);
    $stmtPlantilla->execute();
    $plantillaDb = $stmtPlantilla->fetch(PDO::FETCH_ASSOC);
    if (!$plantillaDb) {
        $estadoFinalParaLead = 'error_plantilla_email_no_encontrada';
        throw new Exception("No se encontró la plantilla de email '{$emailPlantillaNombre}'.");
    }

    // 4. Preparar contenido del email
    $cuerpoHtml = $plantillaDb['contenido_html'];
    $params = [
        'nombre_lead' => $leadData['lead_nombre'] ?? 'Estimado Cliente',
        'nombre_agencia' => $leadData['agencia_nombre'] ?? 'Su Agencia Inmobiliaria',
        'logo_agencia_url' => $leadData['agencia_logo_url'] ?? null,
        'color_primario_agencia' => $leadData['agencia_color'] ?? '#053B5E',
        'enlace_descarga_pdf' => $pdfPublicUrl,
        'year' => date('Y')
    ];

    // 4.1. Procesar condicionales como {{#if logo_agencia_url}}...{{/if}}
    $cuerpoHtml = preg_replace_callback(
        '/{{\#if\s+([a-zA-Z0-9_]+)}}(.*?){{\/if}}/s',
        function ($matches) use ($params) {
            $variableName = $matches[1];
            $contentInsideIf = $matches[2];
            if (isset($params[$variableName]) && !empty($params[$variableName])) {
                return $contentInsideIf;
            }
            return '';
        },
        $cuerpoHtml
    );

    // 4.2. Preparar los placeholders para el reemplazo final
    $placeholders = [];
    foreach ($params as $key => $value) {
        $placeholders['{{' . $key . '}}'] = $value;
    }

    // 4.3. Reemplazar todos los placeholders restantes y preparar asunto
    $asunto = str_replace(array_keys($placeholders), array_values($placeholders), $plantillaDb['asunto_predeterminado']);
    $cuerpoHtml = str_replace(array_keys($placeholders), array_values($placeholders), $cuerpoHtml);

    // 4.4. Construir remitente personalizado para el lead (branding agencia)
    $customFromEmail = !empty($leadData['agencia_identifier']) ? $leadData['agencia_identifier'] . '@inmoautomation.com' : null;
    $customFromName  = $leadData['agencia_nombre'] ?? null;

    // 5. Enviar email
    $sendResult = $brevoService->sendEmail(
        $leadData['lead_email'],
        $leadData['lead_nombre'] ?? 'Lead',
        $asunto,
        $cuerpoHtml,
        '', // textBody opcional
        $customFromEmail,
        $customFromName
    );

    if (!$sendResult['success']) {
        $estadoFinalParaLead = 'error_email_informe';
        throw new Exception("BrevoService falló al enviar: " . ($sendResult['error'] ?? 'Error desconocido'));
    }

    $estadoFinalParaLead = 'pending_activation';
    Logger::info("Task Handler: Email enviado a {$leadData['lead_email']}. Message ID: {$sendResult['message_id']}");

    // 6. Actualizar estado y registrar en historial (transacción)
    $db->beginTransaction();
    try {
        // Registrar en historial
                $stmtHistorial = $db->prepare("INSERT INTO lead_emails_historial (uuid, lead_id, tipo_email, asunto_final, cuerpo_final_html, estado_envio, fecha_envio_real, mensaje_id_esp) VALUES (UUID(), :lead_id, 'informe_valoracion', :asunto, :cuerpo, 'sent', NOW(), :msg_id)");
        $stmtHistorial->execute([':lead_id' => $leadId, ':asunto' => $asunto, ':cuerpo' => $cuerpoHtml, ':msg_id' => $sendResult['message_id']]);
        Logger::info("Task Handler: Inserción en lead_emails_historial intentada. Filas afectadas: " . $stmtHistorial->rowCount());

        // Actualizar estado del lead en lead_sequence_tracking
        // Si no existe, se crea. Si existe, se actualiza.
        $stmtCheckTracking = $db->prepare("SELECT id FROM lead_sequence_tracking WHERE lead_id = :lead_id");
        $stmtCheckTracking->bindParam(':lead_id', $leadId, PDO::PARAM_INT);
        $stmtCheckTracking->execute();
        $trackingRecordExists = $stmtCheckTracking->fetchColumn();

        if ($trackingRecordExists) {
            $stmtUpdate = $db->prepare("UPDATE lead_sequence_tracking SET status = :estado WHERE lead_id = :lead_id");
            $stmtUpdate->execute([':estado' => $estadoFinalParaLead, ':lead_id' => $leadId]);
            Logger::info("Task Handler: lead_sequence_tracking actualizado para lead_id {$leadId} a estado {$estadoFinalParaLead}.");
        } else {
            // Asignar una secuencia por defecto si no hay una (obtener la primera secuencia activa)
            $stmtDefaultSeq = $db->prepare("SELECT id FROM sequences WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
            $stmtDefaultSeq->execute();
            $defaultSequenceId = $stmtDefaultSeq->fetchColumn();

            if ($defaultSequenceId) {
                $stmtInsert = $db->prepare("INSERT INTO lead_sequence_tracking (lead_id, sequence_id, status, created_at, updated_at) VALUES (:lead_id, :sequence_id, :estado, NOW(), NOW())");
                $stmtInsert->execute([':lead_id' => $leadId, ':sequence_id' => $defaultSequenceId, ':estado' => $estadoFinalParaLead]);
                Logger::info("Task Handler: Nueva entrada en lead_sequence_tracking creada para lead_id {$leadId} con secuencia {$defaultSequenceId} y estado {$estadoFinalParaLead}.");
            } else {
                Logger::warning("Task Handler: No se encontró ninguna secuencia activa para asignar al lead_id {$leadId}.");
            }
        }

        $db->commit();
        Logger::info("Task Handler: Transacción completada para Lead {$leadId}.");

    } catch (Exception $e) {
        $db->rollBack();
        throw $e; // Re-lanzar para el catch principal
    }

    http_response_code(200);
    echo "Proceso completado para Lead ID: {$leadId}";

} catch (Exception $e) {
    Logger::error("Task Handler: Error procesando Lead ID {$leadId}. Error: " . $e->getMessage());
    // Si hay un error, actualizamos el estado del lead en lead_sequence_tracking para reflejarlo
    if (isset($db) && isset($estadoFinalParaLead) && $estadoFinalParaLead !== 'pendiente_informe_inicial') {
        try {
            // Verificar si ya existe un registro en lead_sequence_tracking para este lead
            $stmtCheckTracking = $db->prepare("SELECT id FROM lead_sequence_tracking WHERE lead_id = :lead_id");
            $stmtCheckTracking->bindParam(':lead_id', $leadId, PDO::PARAM_INT);
            $stmtCheckTracking->execute();
            $trackingRecordExists = $stmtCheckTracking->fetchColumn();

            if ($trackingRecordExists) {
                $stmtUpdate = $db->prepare("UPDATE lead_sequence_tracking SET status = :estado WHERE lead_id = :lead_id");
                $stmtUpdate->execute([':estado' => $estadoFinalParaLead, ':lead_id' => $leadId]);
            } else {
                // Asignar una secuencia por defecto si no hay una (obtener la primera secuencia activa)
                $stmtDefaultSeq = $db->prepare("SELECT id FROM sequences WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
                $stmtDefaultSeq->execute();
                $defaultSequenceId = $stmtDefaultSeq->fetchColumn();

                if ($defaultSequenceId) {
                    $stmtInsert = $db->prepare("INSERT INTO lead_sequence_tracking (lead_id, sequence_id, status, created_at, updated_at) VALUES (:lead_id, :sequence_id, :estado, NOW(), NOW())");
                    $stmtInsert->execute([':lead_id' => $leadId, ':sequence_id' => $defaultSequenceId, ':estado' => $estadoFinalParaLead]);
                }
            }
        } catch (Exception $dbE) {
            Logger::error("Task Handler: Fallo CRÍTICO al intentar actualizar estado de error para Lead ID {$leadId}. Error: " . $dbE->getMessage());
        }
    }
    http_response_code(500); // Indicar a Cloud Tasks que la tarea falló
    echo "Error: " . $e->getMessage();
}
