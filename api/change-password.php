<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
ob_start();
 // <--- INTEGRAR BOOTSTRAP
 // Usar config central de CORS
// ENCABEZADOS CORS Y MANEJO DE OPTIONS AHORA EN cors.php
header('Content-Type: application/json; charset=utf-8');
if (!function_exists('custom_log_change_password')) {
    function custom_log_change_password($message) {
        $logFile = __DIR__ . '/debug_profile.log'; // Usamos el mismo log de perfil
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [change-password-LOG] " . $message . PHP_EOL);
    }
}
custom_log_change_password("--- [change-password.php] INICIADO ---");
if (ob_get_length()) ob_clean();
// --- Autenticación por Token JWT (igual que en otros scripts) ---
$user_id_from_token = null;
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if ($authHeader) {
    list($type, $token) = explode(' ', $authHeader, 2);
    if (strcasecmp($type, 'Bearer') == 0 && $token) {
        $previous_display_errors = ini_set('display_errors', '0'); 
        try {
            if (empty(JWT_SECRET)) { 
                throw new Exception('JWT_SECRET no configurada (constante).');
            }
            $decoded = Firebase\JWT\JWT::decode($token, new Firebase\JWT\Key(JWT_SECRET, 'HS256'));
            if (isset($decoded->user_id)) {
                 $user_id_from_token = $decoded->user_id;
                 custom_log_change_password("JWT User ID: $user_id_from_token");
            } else { 
                throw new Exception('Token JWT inválido: no contiene user_id.');
            }
        } catch (Exception $e) {
            custom_log_change_password("JWT Error: " . $e->getMessage());
            http_response_code(401);
            if(ob_get_length()) ob_clean(); 
            echo json_encode(['success' => false, 'message' => 'Token inválido o expirado: ' . $e->getMessage()]);
            exit();
        } finally {
            ini_set('display_errors', $previous_display_errors);
        }
    } else {
        http_response_code(401); 
        if(ob_get_length()) ob_clean();
        echo json_encode(['success' => false, 'message' => 'Formato de auth incorrecto.']); exit();
    }
} else {
    http_response_code(401); 
    if(ob_get_length()) ob_clean();
    echo json_encode(['success' => false, 'message' => 'Se requiere autenticación.']); exit();
}
if (empty($user_id_from_token)) {
    http_response_code(401); 
    if(ob_get_length()) ob_clean();
    echo json_encode(['success' => false, 'message' => 'Autenticación fallida.']); exit();
}
$user_id = $user_id_from_token;
// --- Fin Autenticación ---
$response = ['success' => false, 'message' => 'Error desconocido al cambiar contraseña.'];

try {
    custom_log_change_password("Intentando conectar a la base de datos usando Api\\lib\\Database...");
    $pdo = Api\lib\Database::getInstance()->getConnection();
    custom_log_change_password("Conexión PDO establecida a través de la clase Database.");

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        custom_log_change_password("[POST Request] User ID: " . $user_id);
        $input = json_decode(file_get_contents('php://input'), true);
        custom_log_change_password("Input: " . print_r($input, true));
        $currentPassword = $input['currentPassword'] ?? null;
        $newPassword = $input['newPassword'] ?? null;
        if (empty($currentPassword) || empty($newPassword)) {
            http_response_code(400);
            $response['message'] = 'La contraseña actual y la nueva son requeridas.';
            custom_log_change_password("Error validación: " . $response['message']);
            echo json_encode($response); exit;
        }
        if (strlen($newPassword) < 8) {
            http_response_code(400);
            $response['message'] = 'La nueva contraseña debe tener al menos 8 caracteres.';
            custom_log_change_password("Error validación: " . $response['message']);
            echo json_encode($response); exit;
        }
        // Obtener el hash de la contraseña actual del usuario
        $stmt_get_pass = $pdo->prepare("SELECT password_hash FROM usuarios WHERE id = :user_id");
        $stmt_get_pass->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt_get_pass->execute();
        $user_data = $stmt_get_pass->fetch(PDO::FETCH_ASSOC);
        if (!$user_data || empty($user_data['password_hash'])) {
            custom_log_change_password("Usuario no encontrado o sin hash de contraseña para ID: " . $user_id);
            http_response_code(404);
            $response['message'] = 'Error al verificar la cuenta.';
            echo json_encode($response); exit;
        }
        // Verificar la contraseña actual
        if (!password_verify($currentPassword, $user_data['password_hash'])) {
            custom_log_change_password("Contraseña actual incorrecta para user_id: " . $user_id);
            http_response_code(400); // O 401/403 si se prefiere para error de credencial
            $response['message'] = 'La contraseña actual es incorrecta.';
            echo json_encode($response); exit;
        }
        // Generar hash para la nueva contraseña
        $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        if ($newPasswordHash === false) {
            custom_log_change_password("Error al generar el hash de la nueva contraseña.");
            throw new Exception('No se pudo procesar la nueva contraseña.');
        }
        // Actualizar la contraseña en la base de datos
        $sql_update_pass = "UPDATE usuarios SET password_hash = :password_hash, fecha_modificacion = NOW() WHERE id = :user_id";
        $stmt_update_pass = $pdo->prepare($sql_update_pass);
        $stmt_update_pass->bindParam(':password_hash', $newPasswordHash, PDO::PARAM_STR);
        $stmt_update_pass->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt_update_pass->execute();
        if ($stmt_update_pass->rowCount() > 0) {
            custom_log_change_password("Contraseña actualizada para user_id: " . $user_id);
            $response['success'] = true;
            $response['message'] = 'Contraseña actualizada correctamente.';
        } else {
            // Esto es poco probable si la verificación fue exitosa y el ID es correcto, pero podría ser un error de BD no capturado.
            custom_log_change_password("No se afectaron filas al actualizar contraseña para user_id: " . $user_id);
            // No necesariamente un error si el hash nuevo fuera idéntico al viejo (extremadamente improbable con BCRYPT)
            $response['message'] = 'No se pudo actualizar la contraseña en este momento.'; 
        }
        echo json_encode($response);
    } else {
        custom_log_change_password("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Método no permitido.']);
    }
} catch (PDOException $e) {
    custom_log_change_password("PDOException: " . $e->getMessage());
    http_response_code(500);
    if(ob_get_length()) ob_clean();
    echo json_encode(['success' => false, 'message' => 'Error de BD: ' . $e->getMessage()]);
    exit();
} catch (Exception $e) {
    custom_log_change_password("General Exception: " . $e->getMessage());
    $http_code = 500;
    if (strpos($e->getMessage(), 'Token expirado') !== false || strpos($e->getMessage(), 'Token inválido') !== false) {
        $http_code = 401;
    }
    http_response_code($http_code);
    if(ob_get_length()) ob_clean();
    echo json_encode(['success' => false, 'message' => 'Error del servidor: ' . $e->getMessage()]);
    exit();
}
custom_log_change_password("--- [change-password.php] FINALIZADO ---");
ob_end_flush();
exit;
?>