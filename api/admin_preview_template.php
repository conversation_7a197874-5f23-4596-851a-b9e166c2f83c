<?php
require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

use Api\lib\EmailTemplateProcessor;

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$templateId = $_GET['template_id'] ?? null;

if (!$templateId || !is_numeric($templateId)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID de plantilla requerido']);
    exit;
}

try {
    // Generar vista previa con datos de ejemplo
    $previewResult = EmailTemplateProcessor::generatePreview((int)$templateId);
    
    if (!$previewResult['success']) {
        throw new Exception($previewResult['error']);
    }
    
    // Obtener información adicional de la plantilla
    $templateInfo = EmailTemplateProcessor::getTemplateInfo((int)$templateId);
    
    if (!$templateInfo) {
        throw new Exception('Plantilla no encontrada');
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'html' => $previewResult['html'],
            'template_info' => [
                'id' => $templateInfo['id'],
                'name' => $templateInfo['nombre_interno_plantilla'],
                'description' => $templateInfo['descripcion'],
                'type' => $templateInfo['tipo_plantilla'],
                'active' => (bool)$templateInfo['activa']
            ],
            'preview_note' => 'Esta es una vista previa con datos de ejemplo. Los datos reales variarán según el lead.'
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error generando vista previa: ' . $e->getMessage()
    ]);
}
?>
