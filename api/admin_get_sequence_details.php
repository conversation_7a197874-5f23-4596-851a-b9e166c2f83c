<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

// 1. Verify Token and Admin Role
$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

// 2. Validate Input
$uuid = $_GET['uuid'] ?? null;
if (!$uuid) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Sequence UUID is required.']);
    exit;
}

// 3. Database Logic
$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit;
}
$conn->set_charset(DB_CHARSET);

// Get Sequence Details
$stmt = $conn->prepare("SELECT id, uuid, name, description, is_active, trigger_event FROM sequences WHERE uuid = ?");
$stmt->bind_param('s', $uuid);
$stmt->execute();
$result = $stmt->get_result();
$sequence = $result->fetch_assoc();

if (!$sequence) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'Sequence not found.']);
    $stmt->close();
    $conn->close();
    exit;
}

// Cast boolean to boolean type for JSON consistency
$sequence['is_active'] = (bool)$sequence['is_active'];

// Get Sequence Steps with template information
$stmt_steps = $conn->prepare("
    SELECT
        ss.id, ss.uuid, ss.name, ss.email_subject, ss.prompt_template,
        ss.delay_days, ss.step_order, ss.is_active, ss.email_template_id,
        eph.nombre_interno_plantilla as template_name,
        eph.descripcion as template_description
    FROM sequence_steps ss
    LEFT JOIN email_plantillas_html eph ON ss.email_template_id = eph.id
    WHERE ss.sequence_id = ?
    ORDER BY ss.step_order ASC
");
$stmt_steps->bind_param('i', $sequence['id']);
$stmt_steps->execute();
$steps_result = $stmt_steps->get_result();

$steps = [];
while ($row = $steps_result->fetch_assoc()) {
    $row['is_active'] = (bool)$row['is_active'];
    $row['email_template_id'] = $row['email_template_id'] ? (int)$row['email_template_id'] : null;
    $row['has_template'] = !empty($row['template_name']);
    $steps[] = $row;
}

$sequence['steps'] = $steps;

// 4. Return Response
http_response_code(200);
echo json_encode(['success' => true, 'data' => $sequence]);

$stmt->close();
$stmt_steps->close();
$conn->close();
