<?php
declare(strict_types=1);

require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;

// Función para decodificar JWT
function get_decoded_jwt_payload_activity() {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;
    
    if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        throw new Exception('Token de autorización no encontrado');
    }
    
    $jwt = $matches[1];
    $secret_key = $_ENV['JWT_SECRET_KEY'] ?? 'fallback_secret_key_2024';
    
    try {
        return JWT::decode($jwt, new Key($secret_key, 'HS256'));
    } catch (ExpiredException $e) {
        throw new Exception('Token expirado');
    } catch (SignatureInvalidException $e) {
        throw new Exception('Token inválido');
    } catch (BeforeValidException $e) {
        throw new Exception('Token no válido aún');
    } catch (Exception $e) {
        throw new Exception('Error al decodificar token: ' . $e->getMessage());
    }
}

try {
    $decoded_payload = get_decoded_jwt_payload_activity();
    $user_id = $decoded_payload->user_id ?? null;
    $agency_id = $decoded_payload->agency_id ?? null;
    $roles = $decoded_payload->roles ?? [];
    $is_super_admin = in_array('admin', $roles);

    if (!$user_id) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Usuario no identificado']);
        exit;
    }

    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Obtener client_identifiers para la agencia
    $client_identifiers = [];
    if (!$is_super_admin) {
        if (!$agency_id) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Agencia no identificada']);
            exit;
        }

        $stmt = $pdo->prepare("SELECT client_identifier FROM clientes_valorador WHERE agency_id = :agency_id");
        $stmt->bindParam(':agency_id', $agency_id, PDO::PARAM_INT);
        $stmt->execute();
        $client_identifiers = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($client_identifiers)) {
            echo json_encode(['success' => true, 'data' => [
                'alerts' => [],
                'recent_activity' => [],
                'upcoming_actions' => []
            ]]);
            exit;
        }
    }

    $in_placeholders = !$is_super_admin ? implode(',', array_fill(0, count($client_identifiers), '?')) : '';

    // ACTIVIDADES RECIENTES DETALLADAS (últimas 48h)
    $activities = [];

    // 1. NUEVOS LEADS (últimas 48h)
    $sql_new_leads = "SELECT vl.id, vl.nombre, vl.email, vl.telefono, vl.fecha_creacion,
                             vv.direccion, vv.tipo_principal
                      FROM valorador_leads vl
                      JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                      LEFT JOIN valorador_valoraciones vv ON vl.valoracion_id = vv.id
                      WHERE vl.fecha_creacion >= DATE_SUB(NOW(), INTERVAL 48 HOUR)";
    $params_new_leads = [];
    if (!$is_super_admin) {
        $sql_new_leads .= " AND cv.client_identifier IN ($in_placeholders)";
        $params_new_leads = $client_identifiers;
    }
    $sql_new_leads .= " ORDER BY vl.fecha_creacion DESC LIMIT 10";

    $stmt = $pdo->prepare($sql_new_leads);
    $stmt->execute($params_new_leads);
    $new_leads = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($new_leads as $lead) {
        $time_ago = date('H:i', strtotime($lead['fecha_creacion']));
        $date = date('d/m', strtotime($lead['fecha_creacion']));

        $activities[] = [
            'type' => 'new_lead',
            'icon' => '👤',
            'title' => 'Nuevo lead captado',
            'description' => $lead['nombre'] . ' (' . $lead['email'] . ')',
            'details' => $lead['direccion'] ? substr($lead['direccion'], 0, 40) . '...' : $lead['tipo_principal'],
            'time' => "$date a las $time_ago",
            'action_text' => 'Ver lead',
            'action_url' => '/leads/' . $lead['id'],
            'timestamp' => $lead['fecha_creacion']
        ];
    }

    // 2. EMAILS ENVIADOS (últimas 48h)
    $sql_sent_emails = "SELECT leh.id, leh.asunto_final, leh.fecha_envio_real, leh.abierto_timestamp, leh.clickeado_timestamp,
                               vl.nombre as lead_nombre, vl.email as lead_email
                        FROM lead_emails_historial leh
                        JOIN valorador_leads vl ON leh.lead_id = vl.id
                        JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                        WHERE leh.estado_envio = 'sent' AND leh.fecha_envio_real >= DATE_SUB(NOW(), INTERVAL 48 HOUR)";
    $params_sent_emails = [];
    if (!$is_super_admin) {
        $sql_sent_emails .= " AND cv.client_identifier IN ($in_placeholders)";
        $params_sent_emails = $client_identifiers;
    }
    $sql_sent_emails .= " ORDER BY leh.fecha_envio_real DESC LIMIT 10";

    $stmt = $pdo->prepare($sql_sent_emails);
    $stmt->execute($params_sent_emails);
    $sent_emails = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($sent_emails as $email) {
        $time_ago = date('H:i', strtotime($email['fecha_envio_real']));
        $date = date('d/m', strtotime($email['fecha_envio_real']));

        $status = '';
        if ($email['clickeado_timestamp']) {
            $status = ' • Clickeado ✅';
        } elseif ($email['abierto_timestamp']) {
            $status = ' • Abierto 👁️';
        }

        $activities[] = [
            'type' => 'email_sent',
            'icon' => '📧',
            'title' => 'Email enviado',
            'description' => substr($email['asunto_final'], 0, 40) . '...',
            'details' => 'Para: ' . $email['lead_nombre'] . $status,
            'time' => "$date a las $time_ago",
            'action_text' => 'Ver email',
            'action_url' => '/emails/' . $email['id'],
            'timestamp' => $email['fecha_envio_real']
        ];
    }

    // 3. EMAILS PROGRAMADOS (próximas 48h)
    $sql_scheduled = "SELECT leh.id, COALESCE(leh.asunto_final, leh.borrador_asunto) as asunto, leh.fecha_programada_envio,
                             vl.nombre as lead_nombre, vl.email as lead_email
                      FROM lead_emails_historial leh
                      JOIN valorador_leads vl ON leh.lead_id = vl.id
                      JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                      WHERE leh.estado_envio = 'scheduled' AND leh.fecha_programada_envio <= DATE_ADD(NOW(), INTERVAL 48 HOUR)";
    $params_scheduled = [];
    if (!$is_super_admin) {
        $sql_scheduled .= " AND cv.client_identifier IN ($in_placeholders)";
        $params_scheduled = $client_identifiers;
    }
    $sql_scheduled .= " ORDER BY leh.fecha_programada_envio ASC LIMIT 10";

    $stmt = $pdo->prepare($sql_scheduled);
    $stmt->execute($params_scheduled);
    $scheduled_emails = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($scheduled_emails as $email) {
        $scheduled_time = date('H:i', strtotime($email['fecha_programada_envio']));
        $scheduled_date = date('d/m', strtotime($email['fecha_programada_envio']));

        $activities[] = [
            'type' => 'email_scheduled',
            'icon' => '⏰',
            'title' => 'Email programado',
            'description' => substr($email['asunto'], 0, 40) . '...',
            'details' => 'Para: ' . $email['lead_nombre'] . ' • Programado',
            'time' => "$scheduled_date a las $scheduled_time",
            'action_text' => 'Ver email',
            'action_url' => '/emails/' . $email['id'],
            'timestamp' => $email['fecha_programada_envio']
        ];
    }

    // 4. APERTURAS DE EMAIL RECIENTES (últimas 48h)
    $sql_opens = "SELECT leh.id, leh.asunto_final, leh.abierto_timestamp, leh.lead_id,
                         vl.nombre as lead_nombre, vl.email as lead_email
                  FROM lead_emails_historial leh
                  JOIN valorador_leads vl ON leh.lead_id = vl.id
                  JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
                  WHERE leh.abierto_timestamp >= DATE_SUB(NOW(), INTERVAL 48 HOUR)";
    $params_opens = [];
    if (!$is_super_admin) {
        $sql_opens .= " AND cv.client_identifier IN ($in_placeholders)";
        $params_opens = $client_identifiers;
    }
    $sql_opens .= " ORDER BY leh.abierto_timestamp DESC LIMIT 10";

    $stmt = $pdo->prepare($sql_opens);
    $stmt->execute($params_opens);
    $email_opens = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($email_opens as $open) {
        $time_ago = date('H:i', strtotime($open['abierto_timestamp']));
        $date = date('d/m', strtotime($open['abierto_timestamp']));

        $activities[] = [
            'type' => 'email_opened',
            'icon' => '👁️',
            'title' => 'Email abierto',
            'description' => substr($open['asunto_final'], 0, 40) . '...',
            'details' => 'Por: ' . $open['lead_nombre'] . ' • Interesado',
            'time' => "$date a las $time_ago",
            'action_text' => 'Ver lead',
            'action_url' => '/leads/' . $open['lead_id'],
            'timestamp' => $open['abierto_timestamp']
        ];
    }

    // Ordenar todas las actividades por timestamp descendente
    usort($activities, function($a, $b) {
        return strtotime($b['timestamp']) - strtotime($a['timestamp']);
    });

    // Limitar a las 20 actividades más recientes
    $activities = array_slice($activities, 0, 20);

    echo json_encode([
        'success' => true,
        'data' => [
            'activities' => $activities
        ]
    ]);

} catch (Exception $e) {
    error_log("[get_activity_center_data.php] Error: " . $e->getMessage());
    error_log("[get_activity_center_data.php] Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor',
        'debug' => $_ENV['APP_ENV'] === 'development' ? $e->getMessage() : null
    ]);
}
?>
