<?php
declare(strict_types=1);

header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

require_once __DIR__ . '/config/bootstrap.php';

use Api\lib\Database;
use Api\lib\Auth;
use Api\lib\Logger;
use Api\lib\OpenAIService;

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $auth = new Auth();
    $user_data = $auth->verifyToken();

    if (!$user_data || !$user_data['agency_id']) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Acceso no autorizado.']);
        exit;
    }
    $agency_id = $user_data['agency_id'];

    $json_data = json_decode(file_get_contents('php://input'), true);
    $historial_id = $json_data['historial_id'] ?? null;
    $instructions = $json_data['instructions'] ?? null;

    if (!is_numeric($historial_id) || empty($instructions)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Parámetros historial_id e instructions son requeridos.']);
        exit;
    }

    $pdo = Database::getInstance()->getConnection();

    // 1. Obtener el email y verificar permisos
    $stmt_email = $pdo->prepare("
        SELECT leh.*, cv.agency_id FROM lead_emails_historial leh
        JOIN valorador_leads vl ON leh.lead_id = vl.id
        JOIN clientes_valorador cv ON vl.cliente_valorador_id = cv.id
        WHERE leh.id = :historial_id AND leh.estado_envio = 'scheduled'
    ");
    $stmt_email->bindParam(':historial_id', $historial_id, PDO::PARAM_INT);
    $stmt_email->execute();
    $email_data = $stmt_email->fetch(PDO::FETCH_ASSOC);

    if (!$email_data) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Email programado no encontrado.']);
        exit;
    }

    if ($email_data['agency_id'] != $agency_id) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'No tienes permiso para modificar este email.']);
        exit;
    }

    // 2. Llamar a la IA para mejorar el contenido
    $openAIService = new OpenAIService(OPENAI_API_KEY);
    // NOTA: Se necesita un nuevo método en OpenAIService para esto.
    $improvement_result = $openAIService->improveEmailContent(
        $email_data['borrador_asunto'],
        $email_data['borrador_cuerpo_html'],
        $instructions
    );

    if (empty($improvement_result['success'])) {
        throw new Exception('Fallo al mejorar el contenido con IA: ' . ($improvement_result['error'] ?? 'Error desconocido'));
    }

    // 3. Actualizar el email en la base de datos con la nueva versión
    $stmt_update = $pdo->prepare("
        UPDATE lead_emails_historial
        SET
            borrador_asunto = :new_subject,
            borrador_cuerpo_html = :new_body,
            instrucciones_mejora_ia = :instructions,
            fecha_modificacion = NOW()
        WHERE id = :historial_id
    ");

    $new_instructions = ($email_data['instrucciones_mejora_ia'] ? $email_data['instrucciones_mejora_ia'] . "\n---\n" : '') . $instructions;

    $stmt_update->execute([
        ':new_subject' => $improvement_result['subject'],
        ':new_body' => $improvement_result['body'],
        ':instructions' => $new_instructions,
        ':historial_id' => $historial_id
    ]);

    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => 'Email mejorado con éxito.',
        'data' => [
            'historial_id' => $historial_id,
            'borrador_asunto' => $improvement_result['subject'],
            'borrador_cuerpo_html' => $improvement_result['body']
        ]
    ]);

} catch (Exception $e) {
    Logger::error("API Error (improve_email_with_ai): " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor.']);
}
?>
