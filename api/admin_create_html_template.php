<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Datos JSON inválidos']);
    exit;
}

// Validar campos requeridos
$required_fields = ['nombre_interno_plantilla', 'descripcion', 'html_content', 'tipo_plantilla'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || trim($input[$field]) === '') {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => "Campo requerido: {$field}"]);
        exit;
    }
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}
$conn->set_charset(DB_CHARSET);

try {
    // Validar que el nombre interno no exista
    $check_sql = "SELECT id FROM email_plantillas_html WHERE nombre_interno_plantilla = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param('s', $input['nombre_interno_plantilla']);
    $check_stmt->execute();
    
    if ($check_stmt->get_result()->num_rows > 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Ya existe una plantilla con ese nombre interno']);
        exit;
    }
    
    // Preparar datos
    $nombre_interno = trim($input['nombre_interno_plantilla']);
    $descripcion = trim($input['descripcion']);
    $asunto_predeterminado = trim($input['asunto_predeterminado'] ?? '');
    $html_content = trim($input['html_content']);
    $tipo_plantilla = trim($input['tipo_plantilla']);
    $activa = isset($input['activa']) ? (bool)$input['activa'] : true;
    
    // Validar tipo de plantilla
    $valid_types = ['secuencia_email', 'informe_valoracion', 'general', 'personalizada'];
    if (!in_array($tipo_plantilla, $valid_types)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Tipo de plantilla inválido']);
        exit;
    }
    
    // Validar HTML básico
    if (strlen($html_content) < 50) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'El contenido HTML debe tener al menos 50 caracteres']);
        exit;
    }
    
    // Insertar plantilla
    $sql = "
        INSERT INTO email_plantillas_html
        (nombre_interno_plantilla, descripcion, asunto_predeterminado, contenido_html, tipo_plantilla, activa, fecha_creacion, fecha_modificacion)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sssssi', $nombre_interno, $descripcion, $asunto_predeterminado, $html_content, $tipo_plantilla, $activa);
    
    if (!$stmt->execute()) {
        throw new Exception('Error insertando plantilla: ' . $conn->error);
    }
    
    $new_template_id = $conn->insert_id;
    
    // Obtener la plantilla creada
    $get_sql = "
        SELECT 
            id,
            nombre_interno_plantilla,
            descripcion,
            asunto_predeterminado,
            tipo_plantilla,
            activa,
            fecha_creacion,
            fecha_modificacion,
            CHAR_LENGTH(contenido_html) as html_size
        FROM email_plantillas_html 
        WHERE id = ?
    ";
    
    $get_stmt = $conn->prepare($get_sql);
    $get_stmt->bind_param('i', $new_template_id);
    $get_stmt->execute();
    $created_template = $get_stmt->get_result()->fetch_assoc();
    
    // Analizar variables en el HTML
    $variables_found = [];
    if (preg_match_all('/\{\{([^}]+)\}\}/', $html_content, $matches)) {
        $variables_found = array_unique($matches[1]);
        sort($variables_found);
    }
    
    // Procesar respuesta
    $response_data = [
        'id' => (int)$created_template['id'],
        'nombre_interno_plantilla' => $created_template['nombre_interno_plantilla'],
        'descripcion' => $created_template['descripcion'],
        'asunto_predeterminado' => $created_template['asunto_predeterminado'],
        'tipo_plantilla' => $created_template['tipo_plantilla'],
        'activa' => (bool)$created_template['activa'],
        'fecha_creacion' => $created_template['fecha_creacion'],
        'fecha_modificacion' => $created_template['fecha_modificacion'],
        'html_size' => (int)$created_template['html_size'],
        'variables_found' => $variables_found,
        'usage_count' => 0,
        'can_delete' => true,
        'status' => [
            'text' => (bool)$created_template['activa'] ? 'Activa' : 'Inactiva',
            'color' => (bool)$created_template['activa'] ? 'green' : 'gray'
        ]
    ];
    
    // Agregar información del tipo
    switch($tipo_plantilla) {
        case 'secuencia_email':
            $response_data['type_info'] = [
                'display_name' => 'Secuencia de Email',
                'icon' => '📧',
                'description' => 'Plantilla para emails de secuencias automatizadas'
            ];
            break;
        case 'informe_valoracion':
            $response_data['type_info'] = [
                'display_name' => 'Informe de Valoración',
                'icon' => '📊',
                'description' => 'Plantilla para informes de valoración de propiedades'
            ];
            break;
        case 'general':
            $response_data['type_info'] = [
                'display_name' => 'General',
                'icon' => '📄',
                'description' => 'Plantilla de uso general'
            ];
            break;
        default:
            $response_data['type_info'] = [
                'display_name' => ucfirst(str_replace('_', ' ', $tipo_plantilla)),
                'icon' => '📝',
                'description' => 'Plantilla personalizada'
            ];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Plantilla creada exitosamente',
        'data' => $response_data
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error creando plantilla: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
