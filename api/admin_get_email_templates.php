<?php

declare(strict_types=1);


require_once __DIR__ . '/config/cors.php';
require_once __DIR__ . '/config/bootstrap.php';
require_once __DIR__ . '/utils/auth_check.php';

header('Content-Type: application/json');

$auth_response = verifyTokenAndAdminRole();
if (!$auth_response['success']) {
    http_response_code($auth_response['status_code']);
    echo json_encode(['success' => false, 'message' => $auth_response['message']]);
    exit;
}

$conn = // Conexión a través del proxy de Cloud SQL\n$conn = null;\ntry {\n    if (defined('DB_SOCKET') && !empty(DB_SOCKET)) {\n        // Conexión usando socket de Unix (producción en Cloud Run)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, null, DB_SOCKET);\n    } else {\n        // Conexión usando TCP/IP (desarrollo local con Cloud SQL Proxy)\n        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);\n    }\n} catch (Exception $e) {\n    error_log('Error de conexión a la base de datos: ' . $e->getMessage());\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error al conectar con la base de datos']));\n}\n\nif ($conn->connect_error) {\n    error_log('Error de conexión: ' . $conn->connect_error);\n    http_response_code(500);\n    die(json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']));\n}\n$conn->set_charset(DB_CHARSET);;
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor (DB Connect).']);
    exit();
}
$conn->set_charset(DB_CHARSET);

try {

    // Obtener todas las plantillas disponibles para secuencias
    $sql = '
        SELECT
            id,
            nombre_interno_plantilla,
            descripcion,
            asunto_predeterminado,
            tipo_plantilla,
            activa,
            fecha_creacion,
            fecha_modificacion
        FROM email_plantillas_html
        WHERE tipo_plantilla = "secuencia_email" OR tipo_plantilla = "general"
        ORDER BY
            CASE
                WHEN tipo_plantilla = "secuencia_email" THEN 1
                ELSE 2
            END,
            nombre_interno_plantilla ASC
    ';

    $result = $conn->query($sql);
    if (!$result) {
        throw new Exception('Error en la consulta: ' . $conn->error);
    }

    $templates = [];
    while ($row = $result->fetch_assoc()) {
        $templates[] = $row;
    }
    
    // Enriquecer con información adicional
    foreach ($templates as &$template) {
        $template['activa'] = (bool)$template['activa'];
        $template['is_valid'] = true; // Simplificado por ahora

        // Determinar el tipo de plantilla para mostrar
        switch($template['nombre_interno_plantilla']) {
            case 'secuencia_general':
                $template['display_type'] = 'General/Informativa';
                $template['icon'] = '📄';
                break;
            case 'secuencia_cta':
                $template['display_type'] = 'Llamada a la Acción';
                $template['icon'] = '🎯';
                break;
            default:
                $template['display_type'] = ucfirst(str_replace('_', ' ', $template['nombre_interno_plantilla']));
                $template['icon'] = '📧';
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'templates' => $templates,
            'total_count' => count($templates),
            'active_count' => count(array_filter($templates, fn($t) => $t['activa'])),
            'valid_count' => count(array_filter($templates, fn($t) => $t['is_valid']))
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener plantillas: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
