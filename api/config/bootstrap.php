<?php
declare(strict_types=1);

if (PHP_SAPI !== 'cli') {
    ini_set('display_errors', '0');
    ini_set('log_errors', '1');
    error_reporting(E_ALL & ~E_DEPRECATED & ~E_STRICT);
}
require_once __DIR__ . '/../vendor/autoload.php'; 

try {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
    $loadedVars = $dotenv->load();
    error_log("[DEBUG bootstrap.php] APP_BASE_URL desde .env: " . ($_ENV['APP_BASE_URL'] ?? 'NO ESTABLECIDO'));

} catch (\Dotenv\Exception\InvalidPathException $e) {
    // En Cloud Run, las variables de entorno se pasan directamente, no desde archivo .env
    error_log("[INFO bootstrap.php] No se encontró archivo .env, usando variables de entorno del sistema");
} catch (\Dotenv\Exception\ValidationException $e) {
    error_log("CRITICAL: Error de validación de .env (ValidationException): " . $e->getMessage());
    if (PHP_SAPI !== 'cli') {
        if (!headers_sent()) {
            header('Content-Type: application/json');
            http_response_code(500);
        }
        echo json_encode(['success' => false, 'message' => 'Error interno del servidor (ENV_VALIDATION).']);
        exit;
    }
    die("CRITICAL: Error en el formato del archivo .env: " . $e->getMessage());
} catch (\Throwable $e) { // Captura genérica para cualquier otro error durante carga de .env o bootstrap inicial
    error_log("CRITICAL: Error inesperado durante bootstrap inicial (pre-definición de constantes): " . $e->getMessage() . "\nStack: " . $e->getTraceAsString());
    if (PHP_SAPI !== 'cli') {
        if (!headers_sent()) {
            header('Content-Type: application/json');
            http_response_code(500);
        }
        echo json_encode(['success' => false, 'message' => 'Error interno del servidor (BOOTSTRAP_INIT).']);
        exit;
    }
    die("CRITICAL: Error inesperado durante la inicialización del sistema.");
}

// Definir constantes de base de datos
define('DB_DATABASE', $_ENV['DB_NAME'] ?? $_ENV['DB_DATABASE'] ?? 's_valorador');
define('DB_USERNAME', $_ENV['DB_USER'] ?? $_ENV['DB_USERNAME'] ?? '');
define('DB_PASSWORD', $_ENV['DB_PASS'] ?? $_ENV['DB_PASSWORD'] ?? '');

// --- CONFIGURACIÓN DE CONEXIÓN A LA BASE DE DATOS ---
// Esta configuración será utilizada tanto en desarrollo como en producción
// En producción, Cloud Run inyectará automáticamente el socket de Cloud SQL

// Determinar si estamos en producción (Cloud Run) o desarrollo local
$is_production = (getenv('K_SERVICE') !== false);

if ($is_production) {
    // Configuración para producción en Cloud Run
    // Usamos el socket proporcionado por Cloud Run
    define('DB_HOST', 'localhost');
    define('DB_PORT', null); // No se usa con socket
    define('DB_SOCKET', '/cloudsql/inmoautomation:us-central1:inmoautomation-db');
} else {
    // Configuración para desarrollo local con Cloud SQL Proxy
    define('DB_HOST', '127.0.0.1');
    define('DB_PORT', 3306);
    define('DB_SOCKET', '');
}
// --- FIN CONFIGURACIÓN DE CONEXIÓN A LA BASE DE DATOS ---

define('DB_CHARSET', 'utf8mb4'); // Mantener esta línea

define('JWT_SECRET', $_ENV['JWT_SECRET_KEY'] ?? ''); // Corregido a JWT_SECRET_KEY según tu .env

// Añadir la definición de STRIPE_SECRET_KEY
define('STRIPE_SECRET_KEY', $_ENV['STRIPE_SECRET_KEY'] ?? '');

// Nueva constante para el secreto del endpoint de Webhook de Stripe
define('STRIPE_WEBHOOK_SECRET', $_ENV['STRIPE_WEBHOOK_SECRET'] ?? '');

define('OPENAI_API_KEY', $_ENV['OPENAI_API_KEY'] ?? null);
define('GEMINI_API_KEY', $_ENV['GEMINI_API_KEY'] ?? null);


define('BREVO_API_KEY', $_ENV['BREVO_API_KEY'] ?? null); // Descomentar si se usa
define('DEFAULT_SENDER_EMAIL', $_ENV['DEFAULT_SENDER_EMAIL'] ?? '<EMAIL>');
define('DEFAULT_SENDER_NAME', $_ENV['DEFAULT_SENDER_NAME'] ?? 'Sistema');

define('APP_BASE_URL', $_ENV['APP_BASE_URL'] ?? 'http://localhost:5173'); // URL de la Landing Page
define('DASHBOARD_BASE_URL', $_ENV['DASHBOARD_BASE_URL'] ?? ($_ENV['APP_BASE_URL'] ?? 'https://app.inmoautomation.com')); // URL de la App/Dashboard
define('APP_ENV', $_ENV['APP_ENV'] ?? 'development'); // development, production

define('DEFAULT_TRIAL_PERIOD_DAYS', 7);

$logLevelConfig = strtoupper($_ENV['LOG_LEVEL'] ?? 'DEBUG');
$logLevels = ['DEBUG' => 100, 'INFO' => 200, 'WARNING' => 300, 'ERROR' => 400, 'CRITICAL' => 500];
define('LOG_LEVEL_VALUE', $logLevels[$logLevelConfig] ?? $logLevels['DEBUG']);

// En entornos de contenedores como Cloud Run, la mejor práctica es enviar los logs a stderr.
// Esto permite que la plataforma de hosting capture y gestione los logs automáticamente.
if (PHP_SAPI !== 'cli') {
    ini_set('error_log', '/dev/stderr');
}

date_default_timezone_set('Europe/Madrid'); // O la que necesites

// Helper para obtener variables de entorno con un valor por defecto
if (!function_exists('env')) {
    function env(string $key, $default = null) {
        return $_ENV[$key] ?? $default;
    }
}

// --- JWT Handling Class Definition (NO DECODIFICACIÓN GLOBAL AQUÍ) ---
class DecodedJWT {
    public static ?object $payload = null; // Será populado localmente por cada script
    public static array $errors = [];    // Para errores de decodificación local
}

// Eliminar la etiqueta de cierre de PHP 