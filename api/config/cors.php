<?php
// api/config/cors.php

// Manejar la petición pre-vuelo (preflight) OPTIONS PRIMERO
// Esto es crucial para que las peticiones con 'Authorization' header (JWT) no fallen en la comprobación de CORS.
// El navegador envía una petición OPTIONS sin 'Authorization' para preguntar si puede enviar la petición real.
// El servidor debe responder a esta petición OPTIONS con las cabeceras correctas y un código 204, y terminar la ejecución.
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    // Es importante establecer aquí también el ACAO para la respuesta de pre-vuelo.
    // La lógica de validación de origen puede simplificarse o repetirse aquí.
    $allowed_origins = [
        'http://localhost:5173', // Desarrollo - Landing
        'http://localhost:5174', // Desarrollo - Dashboard
        'https://inmoautomation.com',
        'https://app.inmoautomation.com',
        'https://inmoautomation-landing.web.app', // Firebase Hosting
        'https://inmoautomation-app.web.app' // Firebase Hosting
    ];
    if (in_array($origin, $allowed_origins) || preg_match('/^https:\/\/[a-z0-9-]+\.inmoautomation\.com$/', $origin)) {
        header("Access-Control-Allow-Origin: " . $origin);
    }
    
    header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE, PATCH");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
    header('Access-Control-Allow-Credentials: true');
    http_response_code(204); // No Content
    exit();
}

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
$allowed = false;

// Lista completa de orígenes permitidos
$allowed_origins = [
    // Desarrollo
    'http://localhost:5173', // Landing
    'http://localhost:5174', // Dashboard
    // Producción
    'https://inmoautomation.com',
    'https://app.inmoautomation.com',
    'https://inmoautomation-landing.web.app', // Firebase Hosting
    'https://inmoautomation-app.web.app' // Firebase Hosting
];

// Verificar si el origen está en la lista permitida
if (in_array($origin, $allowed_origins)) {
    $allowed = true;
}

// Permitir cualquier subdominio de inmoautomation.com para los valoradores públicos
if (!$allowed && preg_match('/^https:\/\/[a-z0-9-]+\.inmoautomation\.com$/', $origin)) {
    $allowed = true;
}

if ($allowed) {
    header("Access-Control-Allow-Origin: " . $origin);
}

header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE, PATCH");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Access-Control-Allow-Credentials: true'); 