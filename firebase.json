{"projects": {"default": "inmoautomation-0001"}, "hosting": [{"target": "landing", "public": "dist/landing", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**", "headers": [{"key": "Cache-Control", "value": "max-age=3600"}]}]}, {"target": "app", "public": "dist/app", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}]}