// valorador-landing-vue/vite.config.ts
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools' // Si lo quieres usar
import { fileURLToPath, URL } from 'node:url' // Para alias @

export default defineConfig(({ mode }) => {
  // Carga las variables de entorno para el modo actual.
  // Esto cargará .env, .env.[mode], y .env.[mode].local
  const env = loadEnv(mode, process.cwd(), 'VITE_');

  // Configuración de puertos por modo
  const portConfig = {
    landing: 5173,
    app: 5174
  };

  // Configuración de salida por modo
  const outDirConfig = {
    app: 'dist/app',
    landing: 'dist/landing'
  };

  return {
    base: '/', // Se sirve desde la raíz del subdominio
    plugins: [
      vue(),
      vueDevTools(), // Opcional
    ],
    build: {
      // Define el directorio de salida basado en el modo de construcción
      outDir: outDirConfig[mode as keyof typeof outDirConfig] || 'dist/landing',
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    server: {
      port: portConfig[mode as keyof typeof portConfig] || 5173,
      proxy: {
        // Proxy para que las peticiones a /api/* lleguen a tu backend PHP local
        '/api': {
          target: env.VITE_DEV_API_TARGET || 'http://localhost', // Usa la variable de .env.development
          changeOrigin: true,
          secure: false, // No usar SSL en local
          rewrite: (path) => path.replace(/^\/api/, ''), // Elimina el prefijo /api para que Apache encuentre el archivo PHP
        }
      }
    }
  }
})